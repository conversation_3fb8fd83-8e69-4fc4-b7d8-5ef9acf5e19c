# -*- coding: utf-8 -*-
"""
备份恢复模块 - 核心服务
"""
from flask import Flask, jsonify
from flask_cors import CORS
from datetime import datetime, timezone
import random

# --- In-Memory Database for Backup Records ---
db = {
    "backups": [
        {"id": 1, "filename": "backup-20240814-140000.tar.gz", "size_mb": 128, "status": "completed", "created_at": "2024-08-14T14:00:00Z"}
    ],
    "next_backup_id": 2
}

def create_app():
    """Application factory to create and configure the Flask app."""
    app = Flask(__name__)
    CORS(app)

    def get_utc_now_iso():
        return datetime.now(timezone.utc).isoformat().replace(':', '-').split('.')[0] + 'Z'

    @app.route('/', methods=['GET'])
    def index():
        return jsonify({'service': 'Backup & Restore API', 'status': 'running'})

    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({'status': 'healthy', 'service': 'backup-restore'})

    @app.route('/api/v1/backups', methods=['POST'])
    def create_backup():
        """(Mocked) Creates a new backup record."""

        new_id = db['next_backup_id']
        now = get_utc_now_iso()

        backup_record = {
            'id': new_id,
            'filename': f"backup-{now}.tar.gz",
            'size_mb': random.randint(100, 200), # Simulate a random size
            'status': 'completed',
            'created_at': now
        }

        db['backups'].append(backup_record)
        db['next_backup_id'] += 1

        return jsonify(backup_record), 201

    @app.route('/api/v1/backups', methods=['GET'])
    def get_backups():
        """Retrieves a list of all backup records."""
        return jsonify(sorted(db['backups'], key=lambda x: x['id'], reverse=True))

    return app
