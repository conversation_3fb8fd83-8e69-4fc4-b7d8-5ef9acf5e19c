# -*- coding: utf-8 -*-
"""
实操任务管理模块 - 数据库模型
定义所有数据表结构和数据库操作
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import json
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    数据库管理器
    负责数据库连接、表创建和基础操作
    """
    
    def __init__(self, db_path: str = "practical_tasks.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """
        获取数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        return conn
    
    def init_database(self):
        """
        初始化数据库，创建所有必要的表
        """
        try:
            with self.get_connection() as conn:
                # 创建任务分类表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS task_categories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name VARCHAR(100) NOT NULL UNIQUE,
                        description TEXT,
                        software_type VARCHAR(50) NOT NULL,
                        difficulty_level INTEGER NOT NULL CHECK(difficulty_level BETWEEN 1 AND 5),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建实操任务表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS practical_tasks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name VARCHAR(200) NOT NULL,
                        description TEXT NOT NULL,
                        category_id INTEGER NOT NULL,
                        software_version VARCHAR(100) NOT NULL,
                        difficulty_level INTEGER NOT NULL CHECK(difficulty_level BETWEEN 1 AND 5),
                        estimated_duration INTEGER NOT NULL, -- 预估完成时间(分钟)
                        max_score INTEGER NOT NULL DEFAULT 100,
                        requirements TEXT NOT NULL, -- JSON格式的任务要求
                        evaluation_criteria TEXT NOT NULL, -- JSON格式的评分标准
                        status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'inactive', 'archived')),
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (category_id) REFERENCES task_categories(id)
                    )
                """)
                
                # 创建任务素材表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS task_materials (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id INTEGER NOT NULL,
                        file_name VARCHAR(255) NOT NULL,
                        file_path VARCHAR(500) NOT NULL,
                        file_size INTEGER NOT NULL,
                        file_type VARCHAR(50) NOT NULL,
                        version VARCHAR(20) NOT NULL DEFAULT '1.0',
                        description TEXT,
                        is_required BOOLEAN NOT NULL DEFAULT 1,
                        upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        uploaded_by INTEGER,
                        FOREIGN KEY (task_id) REFERENCES practical_tasks(id) ON DELETE CASCADE
                    )
                """)
                
                # 创建任务执行记录表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS task_executions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id INTEGER NOT NULL,
                        exam_id INTEGER, -- 关联考试ID
                        student_id INTEGER NOT NULL,
                        student_name VARCHAR(100) NOT NULL,
                        start_time TIMESTAMP,
                        end_time TIMESTAMP,
                        duration INTEGER, -- 实际用时(分钟)
                        status VARCHAR(20) NOT NULL DEFAULT 'not_started' CHECK(status IN ('not_started', 'in_progress', 'completed', 'timeout', 'abandoned')),
                        score INTEGER CHECK(score >= 0),
                        max_score INTEGER NOT NULL,
                        evaluation_details TEXT, -- JSON格式的详细评分
                        submission_files TEXT, -- JSON格式的提交文件列表
                        evaluator_id INTEGER,
                        evaluation_time TIMESTAMP,
                        remarks TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (task_id) REFERENCES practical_tasks(id)
                    )
                """)
                
                # 创建评分标准模板表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS scoring_templates (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name VARCHAR(100) NOT NULL,
                        description TEXT,
                        software_type VARCHAR(50) NOT NULL,
                        criteria_json TEXT NOT NULL, -- JSON格式的评分标准
                        total_score INTEGER NOT NULL DEFAULT 100,
                        is_default BOOLEAN NOT NULL DEFAULT 0,
                        created_by INTEGER,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建操作日志表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS operation_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER,
                        user_name VARCHAR(100),
                        operation VARCHAR(50) NOT NULL,
                        target_type VARCHAR(50) NOT NULL, -- task, category, material, execution
                        target_id INTEGER,
                        details TEXT, -- JSON格式的操作详情
                        ip_address VARCHAR(45),
                        user_agent TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引以提高查询性能
                conn.execute("CREATE INDEX IF NOT EXISTS idx_tasks_category ON practical_tasks(category_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON practical_tasks(status)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_materials_task ON task_materials(task_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_task ON task_executions(task_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_student ON task_executions(student_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_executions_exam ON task_executions(exam_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_logs_user ON operation_logs(user_id)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_logs_target ON operation_logs(target_type, target_id)")
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """
        执行查询语句
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict]: 查询结果列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """
        执行更新语句
        
        Args:
            query: SQL更新语句
            params: 更新参数
            
        Returns:
            int: 受影响的行数
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(query, params)
                conn.commit()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"更新执行失败: {e}")
            raise
    
    def get_last_insert_id(self) -> int:
        """
        获取最后插入记录的ID
        
        Returns:
            int: 最后插入的记录ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("SELECT last_insert_rowid()")
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"获取插入ID失败: {e}")
            raise

# 全局数据库管理器实例
db_manager = DatabaseManager()

def safe_print(text: str):
    """
    安全打印函数，处理Windows控制台编码问题
    
    Args:
        text: 要打印的文本
    """
    try:
        print(text)
    except UnicodeEncodeError:
        # 在Windows控制台下，移除或替换不兼容的Unicode字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(f"[编码警告] {safe_text}")