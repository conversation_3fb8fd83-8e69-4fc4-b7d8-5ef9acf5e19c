# -*- coding: utf-8 -*-
"""
成绩查询模块

功能说明:
- 成绩查询和统计分析
- 成绩导出功能
- 通过率统计
- 成绩分布分析
- 支持按考试、学生、时间等条件查询
"""

import os
import sys
import logging
import requests
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, session, render_template, redirect, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from sqlalchemy import func, and_, or_
import json
import io
import csv
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill

# 数据库实例
db = SQLAlchemy()

# 日志配置
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/grade_query.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# CORS配置
cors_config = {
    "origins": ["*"],
    "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    "allow_headers": ["Content-Type", "Authorization"]
}

# 外部服务地址配置
USER_MANAGEMENT_URL = 'http://localhost:5002'
EXAM_MANAGEMENT_URL = 'http://localhost:5003'
QUESTION_BANK_URL = 'http://localhost:5004'
STUDENT_EXAM_URL = 'http://localhost:5005'
SCORING_MANAGEMENT_URL = 'http://localhost:5006'
API_GATEWAY_URL = 'http://localhost:8080'

# ==================== 数据库模型 ====================

class StudentGrade(db.Model):
    """学生成绩表"""
    __tablename__ = 'student_grades'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, nullable=False, comment='学生ID')
    student_name = db.Column(db.String(100), nullable=False, comment='学生姓名')
    student_number = db.Column(db.String(50), nullable=False, comment='学号/工号')
    exam_id = db.Column(db.Integer, nullable=False, comment='考试ID')
    exam_name = db.Column(db.String(200), nullable=False, comment='考试名称')
    exam_date = db.Column(db.DateTime, nullable=False, comment='考试时间')
    
    # 成绩信息
    objective_score = db.Column(db.Float, default=0.0, comment='客观题得分')
    subjective_score = db.Column(db.Float, default=0.0, comment='主观题得分')
    total_score = db.Column(db.Float, default=0.0, comment='总分')
    max_score = db.Column(db.Float, nullable=False, comment='满分')
    pass_score = db.Column(db.Float, nullable=False, comment='及格分')
    
    # 状态信息
    is_passed = db.Column(db.Boolean, default=False, comment='是否及格')
    grade_level = db.Column(db.String(20), comment='等级(优秀/良好/及格/不及格)')
    ranking = db.Column(db.Integer, comment='排名')
    
    # 时间信息
    exam_duration = db.Column(db.Integer, comment='考试时长(分钟)')
    actual_duration = db.Column(db.Integer, comment='实际用时(分钟)')
    submit_time = db.Column(db.DateTime, comment='提交时间')
    
    # 审核信息
    reviewed = db.Column(db.Boolean, default=False, comment='是否已审核')
    reviewer_id = db.Column(db.Integer, comment='审核人ID')
    review_time = db.Column(db.DateTime, comment='审核时间')
    review_comments = db.Column(db.Text, comment='审核备注')
    
    # 系统信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'student_id': self.student_id,
            'student_name': self.student_name,
            'student_number': self.student_number,
            'exam_id': self.exam_id,
            'exam_name': self.exam_name,
            'exam_date': self.exam_date.isoformat() if self.exam_date else None,
            'objective_score': self.objective_score,
            'subjective_score': self.subjective_score,
            'total_score': self.total_score,
            'max_score': self.max_score,
            'pass_score': self.pass_score,
            'is_passed': self.is_passed,
            'grade_level': self.grade_level,
            'ranking': self.ranking,
            'exam_duration': self.exam_duration,
            'actual_duration': self.actual_duration,
            'submit_time': self.submit_time.isoformat() if self.submit_time else None,
            'reviewed': self.reviewed,
            'reviewer_id': self.reviewer_id,
            'review_time': self.review_time.isoformat() if self.review_time else None,
            'review_comments': self.review_comments,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class GradeStatistics(db.Model):
    """成绩统计表"""
    __tablename__ = 'grade_statistics'
    
    id = db.Column(db.Integer, primary_key=True)
    exam_id = db.Column(db.Integer, nullable=False, comment='考试ID')
    exam_name = db.Column(db.String(200), nullable=False, comment='考试名称')
    
    # 统计数据
    total_students = db.Column(db.Integer, default=0, comment='总人数')
    submitted_count = db.Column(db.Integer, default=0, comment='已提交人数')
    passed_count = db.Column(db.Integer, default=0, comment='及格人数')
    failed_count = db.Column(db.Integer, default=0, comment='不及格人数')
    
    # 分数统计
    highest_score = db.Column(db.Float, default=0.0, comment='最高分')
    lowest_score = db.Column(db.Float, default=0.0, comment='最低分')
    average_score = db.Column(db.Float, default=0.0, comment='平均分')
    median_score = db.Column(db.Float, default=0.0, comment='中位数')
    
    # 通过率
    pass_rate = db.Column(db.Float, default=0.0, comment='通过率(%)')
    
    # 等级分布
    excellent_count = db.Column(db.Integer, default=0, comment='优秀人数(90+)')
    good_count = db.Column(db.Integer, default=0, comment='良好人数(80-89)')
    fair_count = db.Column(db.Integer, default=0, comment='及格人数(60-79)')
    poor_count = db.Column(db.Integer, default=0, comment='不及格人数(<60)')
    
    # 时间信息
    statistics_date = db.Column(db.DateTime, default=datetime.utcnow, comment='统计时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'exam_id': self.exam_id,
            'exam_name': self.exam_name,
            'total_students': self.total_students,
            'submitted_count': self.submitted_count,
            'passed_count': self.passed_count,
            'failed_count': self.failed_count,
            'highest_score': self.highest_score,
            'lowest_score': self.lowest_score,
            'average_score': self.average_score,
            'median_score': self.median_score,
            'pass_rate': self.pass_rate,
            'excellent_count': self.excellent_count,
            'good_count': self.good_count,
            'fair_count': self.fair_count,
            'poor_count': self.poor_count,
            'statistics_date': self.statistics_date.isoformat() if self.statistics_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

# ==================== 服务类 ====================

class GradeQueryService:
    """成绩查询服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def sync_grades_from_scoring(self, exam_id=None):
        """从评分管理模块同步成绩数据"""
        try:
            # 构建请求URL
            if exam_id:
                url = f'{SCORING_MANAGEMENT_URL}/api/v1/exams/{exam_id}/scores'
            else:
                url = f'{SCORING_MANAGEMENT_URL}/api/v1/scores/all'
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                scores_data = response.json().get('data', [])
                
                for score_data in scores_data:
                    # 检查是否已存在
                    existing_grade = StudentGrade.query.filter_by(
                        student_id=score_data.get('student_id'),
                        exam_id=score_data.get('exam_id')
                    ).first()
                    
                    if existing_grade:
                        # 更新现有记录
                        self._update_grade_record(existing_grade, score_data)
                    else:
                        # 创建新记录
                        self._create_grade_record(score_data)
                
                db.session.commit()
                self.logger.info(f"成绩同步完成，处理了 {len(scores_data)} 条记录")
                return True
            else:
                self.logger.error(f"获取成绩数据失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"同步成绩数据失败: {e}")
            db.session.rollback()
            return False
    
    def _create_grade_record(self, score_data):
        """创建成绩记录"""
        try:
            # 获取学生信息
            student_info = self._get_student_info(score_data.get('student_id'))
            
            # 获取考试信息
            exam_info = self._get_exam_info(score_data.get('exam_id'))
            
            # 计算等级和是否及格
            total_score = score_data.get('total_score', 0)
            max_score = score_data.get('max_score', 100)
            pass_score = exam_info.get('pass_score', 60)
            
            is_passed = total_score >= pass_score
            grade_level = self._calculate_grade_level(total_score, max_score)
            
            grade = StudentGrade(
                student_id=score_data.get('student_id'),
                student_name=student_info.get('name', ''),
                student_number=student_info.get('student_number', ''),
                exam_id=score_data.get('exam_id'),
                exam_name=exam_info.get('name', ''),
                exam_date=datetime.fromisoformat(exam_info.get('start_time', datetime.utcnow().isoformat())),
                objective_score=score_data.get('objective_score', 0),
                subjective_score=score_data.get('subjective_score', 0),
                total_score=total_score,
                max_score=max_score,
                pass_score=pass_score,
                is_passed=is_passed,
                grade_level=grade_level,
                exam_duration=exam_info.get('duration', 0),
                actual_duration=score_data.get('actual_duration', 0),
                submit_time=datetime.fromisoformat(score_data.get('submit_time')) if score_data.get('submit_time') else None
            )
            
            db.session.add(grade)
            
        except Exception as e:
            self.logger.error(f"创建成绩记录失败: {e}")
    
    def _update_grade_record(self, grade, score_data):
        """更新成绩记录"""
        try:
            grade.objective_score = score_data.get('objective_score', grade.objective_score)
            grade.subjective_score = score_data.get('subjective_score', grade.subjective_score)
            grade.total_score = score_data.get('total_score', grade.total_score)
            grade.is_passed = grade.total_score >= grade.pass_score
            grade.grade_level = self._calculate_grade_level(grade.total_score, grade.max_score)
            grade.updated_at = datetime.utcnow()
            
        except Exception as e:
            self.logger.error(f"更新成绩记录失败: {e}")
    
    def _get_student_info(self, student_id):
        """获取学生信息"""
        try:
            response = requests.get(
                f'{USER_MANAGEMENT_URL}/api/v1/users/{student_id}',
                timeout=5
            )
            if response.status_code == 200:
                return response.json().get('data', {})
        except Exception as e:
            self.logger.error(f"获取学生信息失败: {e}")
        
        return {'name': f'学生{student_id}', 'student_number': str(student_id)}
    
    def _get_exam_info(self, exam_id):
        """获取考试信息"""
        try:
            response = requests.get(
                f'{EXAM_MANAGEMENT_URL}/api/v1/exams/{exam_id}',
                timeout=5
            )
            if response.status_code == 200:
                return response.json().get('data', {})
        except Exception as e:
            self.logger.error(f"获取考试信息失败: {e}")
        
        return {
            'name': f'考试{exam_id}',
            'start_time': datetime.utcnow().isoformat(),
            'duration': 120,
            'pass_score': 60
        }
    
    def _calculate_grade_level(self, score, max_score):
        """计算成绩等级"""
        percentage = (score / max_score) * 100 if max_score > 0 else 0
        
        if percentage >= 90:
            return '优秀'
        elif percentage >= 80:
            return '良好'
        elif percentage >= 60:
            return '及格'
        else:
            return '不及格'
    
    def query_grades(self, filters=None, page=1, per_page=20):
        """查询成绩"""
        try:
            query = StudentGrade.query
            
            # 应用过滤条件
            if filters:
                if filters.get('exam_id'):
                    query = query.filter(StudentGrade.exam_id == filters['exam_id'])
                
                if filters.get('student_id'):
                    query = query.filter(StudentGrade.student_id == filters['student_id'])
                
                if filters.get('student_name'):
                    query = query.filter(StudentGrade.student_name.like(f"%{filters['student_name']}%"))
                
                if filters.get('is_passed') is not None:
                    query = query.filter(StudentGrade.is_passed == filters['is_passed'])
                
                if filters.get('grade_level'):
                    query = query.filter(StudentGrade.grade_level == filters['grade_level'])
                
                if filters.get('start_date'):
                    start_date = datetime.fromisoformat(filters['start_date'])
                    query = query.filter(StudentGrade.exam_date >= start_date)
                
                if filters.get('end_date'):
                    end_date = datetime.fromisoformat(filters['end_date'])
                    query = query.filter(StudentGrade.exam_date <= end_date)
            
            # 排序
            query = query.order_by(StudentGrade.exam_date.desc(), StudentGrade.total_score.desc())
            
            # 分页
            pagination = query.paginate(
                page=page,
                per_page=per_page,
                error_out=False
            )
            
            return {
                'grades': [grade.to_dict() for grade in pagination.items],
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
            
        except Exception as e:
            self.logger.error(f"查询成绩失败: {e}")
            return None
    
    def calculate_statistics(self, exam_id):
        """计算考试统计数据"""
        try:
            # 获取考试的所有成绩
            grades = StudentGrade.query.filter_by(exam_id=exam_id).all()
            
            if not grades:
                return None
            
            # 基本统计
            total_students = len(grades)
            submitted_count = len([g for g in grades if g.submit_time])
            passed_count = len([g for g in grades if g.is_passed])
            failed_count = total_students - passed_count
            
            # 分数统计
            scores = [g.total_score for g in grades]
            highest_score = max(scores) if scores else 0
            lowest_score = min(scores) if scores else 0
            average_score = sum(scores) / len(scores) if scores else 0
            
            # 中位数
            sorted_scores = sorted(scores)
            n = len(sorted_scores)
            if n % 2 == 0:
                median_score = (sorted_scores[n//2-1] + sorted_scores[n//2]) / 2
            else:
                median_score = sorted_scores[n//2]
            
            # 通过率
            pass_rate = (passed_count / total_students * 100) if total_students > 0 else 0
            
            # 等级分布
            excellent_count = len([g for g in grades if g.grade_level == '优秀'])
            good_count = len([g for g in grades if g.grade_level == '良好'])
            fair_count = len([g for g in grades if g.grade_level == '及格'])
            poor_count = len([g for g in grades if g.grade_level == '不及格'])
            
            # 获取考试名称
            exam_name = grades[0].exam_name if grades else f'考试{exam_id}'
            
            # 检查是否已存在统计记录
            existing_stats = GradeStatistics.query.filter_by(exam_id=exam_id).first()
            
            if existing_stats:
                # 更新现有记录
                existing_stats.total_students = total_students
                existing_stats.submitted_count = submitted_count
                existing_stats.passed_count = passed_count
                existing_stats.failed_count = failed_count
                existing_stats.highest_score = highest_score
                existing_stats.lowest_score = lowest_score
                existing_stats.average_score = average_score
                existing_stats.median_score = median_score
                existing_stats.pass_rate = pass_rate
                existing_stats.excellent_count = excellent_count
                existing_stats.good_count = good_count
                existing_stats.fair_count = fair_count
                existing_stats.poor_count = poor_count
                existing_stats.updated_at = datetime.utcnow()
                stats = existing_stats
            else:
                # 创建新记录
                stats = GradeStatistics(
                    exam_id=exam_id,
                    exam_name=exam_name,
                    total_students=total_students,
                    submitted_count=submitted_count,
                    passed_count=passed_count,
                    failed_count=failed_count,
                    highest_score=highest_score,
                    lowest_score=lowest_score,
                    average_score=average_score,
                    median_score=median_score,
                    pass_rate=pass_rate,
                    excellent_count=excellent_count,
                    good_count=good_count,
                    fair_count=fair_count,
                    poor_count=poor_count
                )
                db.session.add(stats)
            
            db.session.commit()
            
            self.logger.info(f"考试 {exam_id} 统计计算完成")
            return stats.to_dict()
            
        except Exception as e:
            self.logger.error(f"计算统计数据失败: {e}")
            db.session.rollback()
            return None
    
    def export_grades_to_excel(self, exam_id=None, filters=None):
        """导出成绩到Excel"""
        try:
            # 获取成绩数据
            query = StudentGrade.query
            
            if exam_id:
                query = query.filter_by(exam_id=exam_id)
            
            if filters:
                # 应用过滤条件（简化版）
                if filters.get('is_passed') is not None:
                    query = query.filter(StudentGrade.is_passed == filters['is_passed'])
            
            grades = query.order_by(StudentGrade.exam_date.desc(), StudentGrade.total_score.desc()).all()
            
            # 创建Excel工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "成绩单"
            
            # 设置标题行
            headers = [
                '学号/工号', '姓名', '考试名称', '考试时间',
                '客观题得分', '主观题得分', '总分', '满分',
                '是否及格', '等级', '排名', '考试时长(分钟)',
                '实际用时(分钟)', '提交时间'
            ]
            
            # 写入标题行
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')
                cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
            
            # 写入数据行
            for row, grade in enumerate(grades, 2):
                ws.cell(row=row, column=1, value=grade.student_number)
                ws.cell(row=row, column=2, value=grade.student_name)
                ws.cell(row=row, column=3, value=grade.exam_name)
                ws.cell(row=row, column=4, value=grade.exam_date.strftime('%Y-%m-%d %H:%M') if grade.exam_date else '')
                ws.cell(row=row, column=5, value=grade.objective_score)
                ws.cell(row=row, column=6, value=grade.subjective_score)
                ws.cell(row=row, column=7, value=grade.total_score)
                ws.cell(row=row, column=8, value=grade.max_score)
                ws.cell(row=row, column=9, value='是' if grade.is_passed else '否')
                ws.cell(row=row, column=10, value=grade.grade_level)
                ws.cell(row=row, column=11, value=grade.ranking)
                ws.cell(row=row, column=12, value=grade.exam_duration)
                ws.cell(row=row, column=13, value=grade.actual_duration)
                ws.cell(row=row, column=14, value=grade.submit_time.strftime('%Y-%m-%d %H:%M') if grade.submit_time else '')
            
            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)
            
            return output
            
        except Exception as e:
            self.logger.error(f"导出Excel失败: {e}")
            return None

# 创建服务实例
grade_query_service = GradeQueryService()

# ==================== 路由定义 ====================
# 所有路由都在register_routes函数中定义

def safe_print(text):
    """安全打印函数，处理Windows控制台编码问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

def create_app(config_class=None):
    """创建Flask应用工厂函数"""
    app = Flask(__name__)
    
    if config_class:
        app.config.from_object(config_class)
    else:
        # 默认配置
        app.secret_key = 'grade_query_secret_key_2024'
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///grade_query.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)
    
    # 初始化扩展
    db.init_app(app)
    CORS(app, supports_credentials=True)
    
    # 注册路由
    register_routes(app)
    
    return app

def register_routes(app):
    """注册所有路由"""
    from flask import redirect
    
    # 页面路由
    @app.route('/')
    def index():
        return render_template('login.html')
    
    @app.route('/grades')
    def grades_page():
        if not session.get('logged_in'):
            return redirect('/')
        return render_template('grades.html')
    
    # API路由
    @app.route('/api/v1/login', methods=['POST'])
    def login():
        """用户登录接口"""
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return jsonify({
                    'code': 400,
                    'msg': '用户名和密码不能为空',
                    'data': {}
                }), 400
            
            # 调用用户管理模块验证用户
            auth_response = requests.post(
                f'{USER_MANAGEMENT_URL}/api/v1/auth/login',
                json={'username': username, 'password': password},
                timeout=10
            )
            
            if auth_response.status_code == 200:
                user_data = auth_response.json().get('data', {})
                
                # 设置会话
                session['logged_in'] = True
                session['user_id'] = user_data.get('user_id')
                session['username'] = user_data.get('username')
                session['role'] = user_data.get('role')
                session.permanent = True
                
                return jsonify({
                    'code': 200,
                    'msg': '登录成功',
                    'data': {
                        'user_id': user_data.get('user_id'),
                        'username': user_data.get('username'),
                        'role': user_data.get('role'),
                        'redirect_url': '/grades'
                    }
                })
            else:
                return jsonify({
                    'code': 401,
                    'msg': '用户名或密码错误',
                    'data': {}
                }), 401
                
        except requests.exceptions.RequestException as e:
            logger.error(f"用户认证请求失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '认证服务暂时不可用',
                'data': {}
            }), 500
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '登录失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/grades/sync', methods=['POST'])
    def sync_grades():
        """同步成绩数据"""
        try:
            if not session.get('logged_in') or session.get('role') not in ['admin', 'teacher']:
                return jsonify({
                    'code': 403,
                    'msg': '权限不足，仅限管理员和教师操作',
                    'data': {}
                }), 403
            
            data = request.get_json() or {}
            exam_id = data.get('exam_id')
            
            success = grade_query_service.sync_grades_from_scoring(exam_id)
            
            if success:
                return jsonify({
                    'code': 200,
                    'msg': '成绩同步成功',
                    'data': {}
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '成绩同步失败',
                    'data': {}
                }), 400
            
        except Exception as e:
            logger.error(f"同步成绩失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '同步成绩失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/grades', methods=['GET'])
    def get_grades():
        """查询成绩列表"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            # 获取查询参数
            filters = {}
            if request.args.get('exam_id'):
                filters['exam_id'] = int(request.args.get('exam_id'))
            if request.args.get('student_id'):
                filters['student_id'] = int(request.args.get('student_id'))
            if request.args.get('student_name'):
                filters['student_name'] = request.args.get('student_name')
            if request.args.get('is_passed'):
                filters['is_passed'] = request.args.get('is_passed').lower() == 'true'
            if request.args.get('grade_level'):
                filters['grade_level'] = request.args.get('grade_level')
            if request.args.get('start_date'):
                filters['start_date'] = request.args.get('start_date')
            if request.args.get('end_date'):
                filters['end_date'] = request.args.get('end_date')
            
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
            
            # 权限检查：学生只能查看自己的成绩
            if session.get('role') == 'student':
                filters['student_id'] = session.get('user_id')
            
            result = grade_query_service.query_grades(filters, page, per_page)
            
            if result:
                return jsonify({
                    'code': 200,
                    'msg': '查询成功',
                    'data': result
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '查询失败',
                    'data': {}
                }), 400
            
        except Exception as e:
            logger.error(f"查询成绩失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '查询成绩失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/exams/<int:exam_id>/statistics', methods=['GET'])
    def get_exam_statistics(exam_id):
        """获取考试统计数据"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            # 权限检查：学生不能查看统计数据
            if session.get('role') == 'student':
                return jsonify({
                    'code': 403,
                    'msg': '权限不足',
                    'data': {}
                }), 403
            
            stats = grade_query_service.calculate_statistics(exam_id)
            
            if stats:
                return jsonify({
                    'code': 200,
                    'msg': '获取成功',
                    'data': stats
                })
            else:
                return jsonify({
                    'code': 404,
                    'msg': '未找到统计数据',
                    'data': {}
                }), 404
            
        except Exception as e:
            logger.error(f"获取统计数据失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '获取统计数据失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/grades/export', methods=['GET'])
    def export_grades():
        """导出成绩"""
        try:
            if not session.get('logged_in') or session.get('role') not in ['admin', 'teacher']:
                return jsonify({
                    'code': 403,
                    'msg': '权限不足，仅限管理员和教师操作',
                    'data': {}
                }), 403
            
            exam_id = request.args.get('exam_id')
            if exam_id:
                exam_id = int(exam_id)
            
            # 构建过滤条件
            filters = {}
            if request.args.get('is_passed'):
                filters['is_passed'] = request.args.get('is_passed').lower() == 'true'
            
            excel_file = grade_query_service.export_grades_to_excel(exam_id, filters)
            
            if excel_file:
                filename = f"成绩单_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                return send_file(
                    excel_file,
                    as_attachment=True,
                    download_name=filename,
                    mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
            else:
                return jsonify({
                    'code': 400,
                    'msg': '导出失败',
                    'data': {}
                }), 400
            
        except Exception as e:
            logger.error(f"导出成绩失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '导出成绩失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/logout', methods=['POST'])
    def logout():
        """退出登录"""
        session.clear()
        return jsonify({
            'code': 200,
            'msg': '退出成功',
            'data': {'redirect_url': '/'}
        })
    
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        return jsonify({
            'status': 'healthy',
            'service': 'grade-query',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        })

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 创建数据库表
    with app.app_context():
        db.create_all()
        safe_print("[*] 数据库表创建完成")
    
    safe_print("[*] 成绩查询模块启动中...")
    safe_print("[*] 服务地址: http://0.0.0.0:5007")
    safe_print("[*] 健康检查: http://0.0.0.0:5007/api/health")
    
    app.run(
        host='0.0.0.0',
        port=5007,
        debug=True,
        use_reloader=False
    )