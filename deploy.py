#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
职业技能等级考试系统部署脚本

提供系统的一键部署、启动、停止、更新等功能
支持开发环境和生产环境的不同配置
"""

import os
import sys
import time
import json
import subprocess
import argparse
from pathlib import Path
from typing import List, Dict, Optional


class SystemDeployer:
    """系统部署器"""
    
    def __init__(self, root_dir: str):
        self.root_dir = Path(root_dir)
        self.compose_file = self.root_dir / "docker-compose.yml"
        self.env_file = self.root_dir / ".env"
        
    def check_prerequisites(self) -> bool:
        """检查部署前置条件"""
        print("🔍 检查部署前置条件...")
        
        # 检查Docker
        if not self._check_command("docker --version"):
            print("❌ Docker未安装或未启动")
            return False
        print("✅ Docker已安装")
        
        # 检查Docker Compose
        if not self._check_command("docker-compose --version"):
            print("❌ Docker Compose未安装")
            return False
        print("✅ Docker Compose已安装")
        
        # 检查端口占用
        ports = [80, 443, 3000, 5432, 6379, 8080, 9090]
        for port in ports:
            if self._is_port_in_use(port):
                print(f"⚠️  端口 {port} 已被占用")
        
        # 检查磁盘空间
        free_space = self._get_free_disk_space()
        if free_space < 5:  # 至少5GB
            print(f"⚠️  磁盘空间不足，当前可用: {free_space:.1f}GB")
        
        print("✅ 前置条件检查完成")
        return True
    
    def setup_environment(self, env_type: str = "development"):
        """设置环境配置"""
        print(f"🔧 设置{env_type}环境配置...")
        
        env_config = self._get_env_config(env_type)
        
        # 写入.env文件
        with open(self.env_file, 'w', encoding='utf-8') as f:
            for key, value in env_config.items():
                f.write(f"{key}={value}\n")
        
        print("✅ 环境配置完成")
    
    def build_images(self, services: Optional[List[str]] = None):
        """构建Docker镜像"""
        print("🏗️  构建Docker镜像...")
        
        cmd = ["docker-compose", "build"]
        if services:
            cmd.extend(services)
        
        result = subprocess.run(cmd, cwd=self.root_dir)
        if result.returncode != 0:
            print("❌ 镜像构建失败")
            return False
        
        print("✅ 镜像构建完成")
        return True
    
    def start_services(self, services: Optional[List[str]] = None, detached: bool = True):
        """启动服务"""
        print("🚀 启动服务...")
        
        cmd = ["docker-compose", "up"]
        if detached:
            cmd.append("-d")
        if services:
            cmd.extend(services)
        
        result = subprocess.run(cmd, cwd=self.root_dir)
        if result.returncode != 0:
            print("❌ 服务启动失败")
            return False
        
        if detached:
            print("✅ 服务启动完成")
            self._wait_for_services()
        
        return True
    
    def stop_services(self, services: Optional[List[str]] = None):
        """停止服务"""
        print("🛑 停止服务...")
        
        cmd = ["docker-compose", "stop"]
        if services:
            cmd.extend(services)
        
        result = subprocess.run(cmd, cwd=self.root_dir)
        if result.returncode != 0:
            print("❌ 服务停止失败")
            return False
        
        print("✅ 服务停止完成")
        return True
    
    def restart_services(self, services: Optional[List[str]] = None):
        """重启服务"""
        print("🔄 重启服务...")
        
        cmd = ["docker-compose", "restart"]
        if services:
            cmd.extend(services)
        
        result = subprocess.run(cmd, cwd=self.root_dir)
        if result.returncode != 0:
            print("❌ 服务重启失败")
            return False
        
        print("✅ 服务重启完成")
        return True
    
    def show_status(self):
        """显示服务状态"""
        print("📊 服务状态:")
        
        result = subprocess.run(
            ["docker-compose", "ps"],
            cwd=self.root_dir,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print("❌ 无法获取服务状态")
    
    def show_logs(self, service: Optional[str] = None, follow: bool = False):
        """显示服务日志"""
        print(f"📋 显示{'所有服务' if not service else service}日志...")
        
        cmd = ["docker-compose", "logs"]
        if follow:
            cmd.append("-f")
        if service:
            cmd.append(service)
        
        subprocess.run(cmd, cwd=self.root_dir)
    
    def cleanup(self, volumes: bool = False):
        """清理资源"""
        print("🧹 清理资源...")
        
        # 停止并删除容器
        subprocess.run(["docker-compose", "down"], cwd=self.root_dir)
        
        if volumes:
            print("🗑️  删除数据卷...")
            subprocess.run(["docker-compose", "down", "-v"], cwd=self.root_dir)
        
        # 清理未使用的镜像
        subprocess.run(["docker", "image", "prune", "-f"])
        
        print("✅ 清理完成")
    
    def backup_data(self, backup_dir: str):
        """备份数据"""
        print(f"💾 备份数据到 {backup_dir}...")
        
        backup_path = Path(backup_dir)
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份数据库
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        db_backup = backup_path / f"database_{timestamp}.sql"
        
        subprocess.run([
            "docker-compose", "exec", "-T", "postgres",
            "pg_dump", "-U", "exam_user", "exam_system"
        ], stdout=open(db_backup, 'w'), cwd=self.root_dir)
        
        print(f"✅ 数据库备份完成: {db_backup}")
        
        # 备份配置文件
        config_backup = backup_path / f"config_{timestamp}.tar.gz"
        subprocess.run([
            "tar", "-czf", str(config_backup),
            "api-gateway/config",
            "user-management/config",
            "question-bank/config"
        ], cwd=self.root_dir)
        
        print(f"✅ 配置备份完成: {config_backup}")
    
    def restore_data(self, backup_file: str):
        """恢复数据"""
        print(f"📥 从 {backup_file} 恢复数据...")
        
        if backup_file.endswith('.sql'):
            # 恢复数据库
            with open(backup_file, 'r') as f:
                subprocess.run([
                    "docker-compose", "exec", "-T", "postgres",
                    "psql", "-U", "exam_user", "exam_system"
                ], stdin=f, cwd=self.root_dir)
            print("✅ 数据库恢复完成")
        
        elif backup_file.endswith('.tar.gz'):
            # 恢复配置
            subprocess.run(["tar", "-xzf", backup_file], cwd=self.root_dir)
            print("✅ 配置恢复完成")
    
    def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        print("🏥 执行健康检查...")
        
        services = {
            "API网关": "http://localhost:8080/api/health",
            "用户管理": "http://localhost:5001/api/health",
            "题库管理": "http://localhost:5002/api/health",
            "考试管理": "http://localhost:5003/api/health",
            "成绩管理": "http://localhost:5004/api/health",
            "监控服务": "http://localhost:5005/api/health",
            "审计服务": "http://localhost:5006/api/health"
        }
        
        results = {}
        for name, url in services.items():
            try:
                import requests
                response = requests.get(url, timeout=5)
                results[name] = response.status_code == 200
                status = "✅" if results[name] else "❌"
                print(f"  {status} {name}: {url}")
            except Exception as e:
                results[name] = False
                print(f"  ❌ {name}: {url} - {e}")
        
        return results
    
    def _check_command(self, command: str) -> bool:
        """检查命令是否可用"""
        try:
            result = subprocess.run(
                command.split(),
                capture_output=True,
                text=True
            )
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def _is_port_in_use(self, port: int) -> bool:
        """检查端口是否被占用"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0
    
    def _get_free_disk_space(self) -> float:
        """获取可用磁盘空间(GB)"""
        import shutil
        total, used, free = shutil.disk_usage(self.root_dir)
        return free / (1024**3)
    
    def _get_env_config(self, env_type: str) -> Dict[str, str]:
        """获取环境配置"""
        base_config = {
            "COMPOSE_PROJECT_NAME": "exam-system",
            "POSTGRES_DB": "exam_system",
            "POSTGRES_USER": "exam_user",
            "POSTGRES_PASSWORD": "exam_password",
            "REDIS_PASSWORD": "",
            "JWT_SECRET_KEY": "your-secret-key-change-in-production"
        }
        
        if env_type == "production":
            base_config.update({
                "FLASK_ENV": "production",
                "POSTGRES_PASSWORD": "strong_production_password",
                "JWT_SECRET_KEY": "very-strong-secret-key-for-production",
                "REDIS_PASSWORD": "redis_production_password"
            })
        else:
            base_config.update({
                "FLASK_ENV": "development"
            })
        
        return base_config
    
    def _wait_for_services(self, timeout: int = 120):
        """等待服务启动"""
        print("⏳ 等待服务启动...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                import requests
                response = requests.get("http://localhost:8080/api/health", timeout=2)
                if response.status_code == 200:
                    print("✅ 服务已就绪")
                    return True
            except:
                pass
            
            time.sleep(5)
            print("⏳ 继续等待...")
        
        print("⚠️  服务启动超时，请检查日志")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="职业技能等级考试系统部署工具")
    parser.add_argument("action", choices=[
        "check", "setup", "build", "start", "stop", "restart",
        "status", "logs", "cleanup", "backup", "restore", "health", "deploy"
    ], help="执行的操作")
    parser.add_argument("--env", choices=["development", "production"], 
                       default="development", help="环境类型")
    parser.add_argument("--services", nargs="*", help="指定服务")
    parser.add_argument("--follow", action="store_true", help="跟踪日志")
    parser.add_argument("--volumes", action="store_true", help="删除数据卷")
    parser.add_argument("--backup-dir", default="./backups", help="备份目录")
    parser.add_argument("--backup-file", help="备份文件")
    
    args = parser.parse_args()
    
    # 获取项目根目录
    root_dir = Path(__file__).parent
    deployer = SystemDeployer(str(root_dir))
    
    print(f"🎯 执行操作: {args.action}")
    print(f"📁 项目目录: {root_dir}")
    print(f"🌍 环境类型: {args.env}")
    print("-" * 50)
    
    try:
        if args.action == "check":
            deployer.check_prerequisites()
        
        elif args.action == "setup":
            deployer.setup_environment(args.env)
        
        elif args.action == "build":
            deployer.build_images(args.services)
        
        elif args.action == "start":
            deployer.start_services(args.services)
        
        elif args.action == "stop":
            deployer.stop_services(args.services)
        
        elif args.action == "restart":
            deployer.restart_services(args.services)
        
        elif args.action == "status":
            deployer.show_status()
        
        elif args.action == "logs":
            service = args.services[0] if args.services else None
            deployer.show_logs(service, args.follow)
        
        elif args.action == "cleanup":
            deployer.cleanup(args.volumes)
        
        elif args.action == "backup":
            deployer.backup_data(args.backup_dir)
        
        elif args.action == "restore":
            if not args.backup_file:
                print("❌ 请指定备份文件: --backup-file")
                return
            deployer.restore_data(args.backup_file)
        
        elif args.action == "health":
            results = deployer.health_check()
            healthy_count = sum(results.values())
            total_count = len(results)
            print(f"\n📊 健康检查结果: {healthy_count}/{total_count} 服务正常")
        
        elif args.action == "deploy":
            # 完整部署流程
            print("🚀 开始完整部署流程...")
            
            if not deployer.check_prerequisites():
                print("❌ 前置条件检查失败")
                return
            
            deployer.setup_environment(args.env)
            
            if not deployer.build_images():
                print("❌ 镜像构建失败")
                return
            
            if not deployer.start_services():
                print("❌ 服务启动失败")
                return
            
            # 健康检查
            time.sleep(10)  # 等待服务完全启动
            results = deployer.health_check()
            
            if all(results.values()):
                print("\n🎉 部署成功！")
                print("\n📋 访问地址:")
                print("  🌐 系统首页: http://localhost")
                print("  🔧 API网关: http://localhost:8080")
                print("  📊 监控面板: http://localhost:3000 (admin/admin)")
                print("  📈 Prometheus: http://localhost:9090")
            else:
                print("\n⚠️  部署完成，但部分服务异常，请检查日志")
    
    except KeyboardInterrupt:
        print("\n🛑 操作被用户中断")
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()