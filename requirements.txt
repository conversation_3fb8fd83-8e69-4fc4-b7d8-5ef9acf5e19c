# This is a consolidated requirements file for the entire project.

# Web Framework and Extensions
Flask==2.3.3
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3
Flask-Limiter==3.5.0
Flask-Migrate==4.0.5
Flask-RESTX==1.1.0
Flask-SQLAlchemy==3.0.5
Flask-WTF==1.2.1
Werkzeug==3.0.3
Jinja2==3.1.4
itsdangerous==2.2.0
MarkupSafe==2.1.5
click==8.1.7
gunicorn==21.2.0

# Database and ORM
SQLAlchemy==2.0.30
PyMySQL==1.1.0
psycopg2-binary==2.9.7
greenlet==3.2.2

# Data Handling and Processing
pandas==2.3.0
numpy==1.26.4
openpyxl==3.1.4
python-docx==1.1.2
Pillow==11.2.1
et_xmlfile==2.0.0
six==1.16.0

# Security and Authentication
bcrypt==4.1.2
cryptography==41.0.7
PyJWT==2.8.0
argon2-cffi==23.1.0

# API and HTTP
requests==2.31.0

# Validation and Schemas
marshmallow==3.20.1
jsonschema==4.19.1
WTForms==3.1.1
email-validator==2.1.0

# Utilities
psutil==5.9.8
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
PyYAML==6.0.1
blinker==1.9.0
colorama==0.4.6
typing_extensions==4.13.2
pytz==2024.1
tzdata==2024.1
python-magic==0.4.27
filetype==1.2.0

# Logging and Monitoring
structlog==23.1.0
loguru==0.7.2
prometheus-client==0.17.1

# Development and Testing
pytest==7.4.3
pytest-cov==4.1.0
pytest-flask==1.3.0
flake8==6.1.0
black==23.9.1

# Packaging and Distribution
pyinstaller==6.14.1
pyinstaller-hooks-contrib==2025.5
altgraph==0.17.4
pefile==2023.2.7
pywin32-ctypes==0.2.3

# Advanced features dependencies
scikit-learn==1.3.2
jieba==0.42.1
fuzzywuzzy==0.18.0
python-Levenshtein==0.25.1
threadpoolctl==3.2.0
