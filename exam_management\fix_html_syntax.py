# -*- coding: utf-8 -*-
"""
修复HTML模板中的中文字符截断问题
"""

def fix_html_syntax():
    """修复HTML模板中的语法错误"""
    
    # 读取文件
    with open('app.py', 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # HTML模板中的修复映射
    html_fixes = {
        # JavaScript中的错误
        '加载失?': '加载失败',
        '显示参与者列?': '显示参与者列表',
        '暂无参与者数?': '暂无参与者数据',
        '座位号/th>': '座位号</th>',
        '状?/th>': '状态</th>',
        '上一?': '上一页',
        '下一?': '下一页',
        '应用筛?': '应用筛选',
        '添加参与?': '添加参与者',
        '编辑参与?': '编辑参与者',
        '查看参与?': '查看参与者',
        '移除参与?': '移除参与者',
        '更新参与?': '更新参与者',
        '添加参与?': '添加参与者',
        '页面加载完成后执?': '页面加载完成后执行',
        
        # 修复引号问题
        "确定要移除这个参与者吗?)": "确定要移除这个参与者吗?')",
        "添加参与?;": "添加参与者';",
        "编辑参与?;": "编辑参与者';",
        "查看参与?${participantId}": "查看参与者 ${participantId}",
        "移除参与?${participantId}": "移除参与者 ${participantId}",
        
        # 其他HTML错误
        '</div>': '</div>',
    }
    
    # 应用修复
    fixed_count = 0
    for old_text, new_text in html_fixes.items():
        if old_text in content:
            content = content.replace(old_text, new_text)
            fixed_count += 1
            print(f"修复: '{old_text}' -> '{new_text}'")
    
    # 保存修复后的文件
    with open('app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"\nHTML语法错误修复完成！")
    print(f"已修复 {fixed_count} 个错误")

if __name__ == '__main__':
    fix_html_syntax()