# 配置管理规范文档

## 1. 概述

本文档定义了职业技能等级考试系统的配置管理规范，确保设计文档与实际实现的一致性，建立标准化的配置管理流程。

## 2. 配置管理原则

### 2.1 一致性原则
- **设计与实现一致**：配置文件必须与设计文档保持一致
- **命名规范统一**：所有配置项命名遵循统一规范
- **版本同步更新**：配置变更必须同步更新相关文档

### 2.2 标准化原则
- **统一配置格式**：使用YAML格式作为主要配置文件格式
- **标准化端点**：所有模块使用统一的端点规范
- **一致性验证**：建立配置验证机制

### 2.3 可维护性原则
- **模块化配置**：按功能模块组织配置
- **环境分离**：开发、测试、生产环境配置分离
- **配置继承**：支持配置继承和覆盖机制

## 3. 统一端点规范

### 3.1 健康检查端点

**标准端点**：`/api/health`

**适用范围**：所有微服务模块

**响应格式**：
```json
{
  "code": 200,
  "message": "healthy",
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-20T10:30:00Z",
    "version": "1.0.0",
    "dependencies": {
      "database": "healthy",
      "redis": "healthy"
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 3.2 就绪检查端点

**标准端点**：`/api/health/ready`

**用途**：检查服务是否准备好接收请求

### 3.3 存活检查端点

**标准端点**：`/api/health/live`

**用途**：检查服务是否仍在运行

### 3.4 信息端点

**标准端点**：`/api/info`

**用途**：提供服务基本信息

## 4. 配置文件结构规范

### 4.1 目录结构

```
config/
├── base.yaml              # 基础配置
├── development.yaml       # 开发环境配置
├── testing.yaml          # 测试环境配置
├── production.yaml       # 生产环境配置
└── modules/
    ├── user-management.yaml
    ├── question-bank.yaml
    ├── exam-management.yaml
    ├── score-management.yaml
    ├── monitoring.yaml
    └── auditing.yaml
```

### 4.2 基础配置模板

```yaml
# 服务基础配置
service:
  name: "service-name"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 5000
  debug: false

# 健康检查配置
health:
  endpoint: "/api/health"        # 统一健康检查端点
  ready_endpoint: "/api/health/ready"
  live_endpoint: "/api/health/live"
  info_endpoint: "/api/info"
  check_interval: 30
  timeout: 10

# 数据库配置
database:
  type: "sqlite"  # sqlite, mysql, postgresql
  host: "localhost"
  port: 3306
  name: "database_name"
  username: "username"
  password: "password"
  pool_size: 10
  max_overflow: 20

# Redis配置
redis:
  host: "localhost"
  port: 6379
  db: 0
  password: ""
  timeout: 5

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file: "logs/service.log"
  max_size: "100MB"
  backup_count: 5

# 安全配置
security:
  jwt_secret_key: "your-secret-key"
  jwt_access_token_expires: 7200  # 2小时
  jwt_refresh_token_expires: 604800  # 7天
  cors_origins: ["*"]

# 监控配置
monitoring:
  metrics_enabled: true
  metrics_endpoint: "/metrics"
  tracing_enabled: false
```

## 5. 模块配置规范

### 5.1 用户管理模块配置

```yaml
# user-management/config/base.yaml
service:
  name: "user-management"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 5001

health:
  endpoint: "/api/health"  # 统一端点
  dependencies:
    - "database"
    - "redis"

api:
  prefix: "/api/v1/user"
  rate_limit:
    login: "10 per minute"
    register: "5 per minute"
    default: "100 per minute"
```

### 5.2 题库管理模块配置

```yaml
# question-bank/config/base.yaml
service:
  name: "question-bank"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 5002

health:
  endpoint: "/api/health"  # 统一端点
  dependencies:
    - "database"
    - "file_storage"

api:
  prefix: "/api/v1/question"
  upload:
    max_file_size: "50MB"
    allowed_extensions: [".xlsx", ".xls", ".json"]
```

## 6. API网关配置规范

### 6.1 服务发现配置

```yaml
# api-gateway/config/gateway.yaml
modules:
  user_management:
    host: "localhost"
    port: 5001
    health_check: "/api/health"  # 统一健康检查端点
    prefix: "/api/v1/user"
    timeout: 30
    retry: 3
    
  question_bank:
    host: "localhost"
    port: 5002
    health_check: "/api/health"  # 统一健康检查端点
    prefix: "/api/v1/question"
    timeout: 30
    retry: 3
```

### 6.2 健康检查策略

```yaml
health_check:
  strategy: "multi_level"  # 多级检查策略
  levels:
    - endpoint: "/api/health"    # 优先检查健康端点
    - endpoint: "/"             # 备用检查根路径
    - type: "port"              # 最后检查端口可用性
  
  interval: 30
  timeout: 10
  retries: 3
  failure_threshold: 3
  recovery_threshold: 2
```

## 7. 配置验证规范

### 7.1 配置验证脚本

```python
#!/usr/bin/env python3
# scripts/validate_config.py

import yaml
import sys
from pathlib import Path

def validate_health_endpoints():
    """验证所有模块健康检查端点一致性"""
    expected_endpoint = "/api/health"
    errors = []
    
    # 检查API网关配置
    gateway_config = load_config("api-gateway/config/gateway.yaml")
    for module_name, module_config in gateway_config.get("modules", {}).items():
        health_endpoint = module_config.get("health_check")
        if health_endpoint != expected_endpoint:
            errors.append(f"网关配置中模块 {module_name} 健康检查端点不一致: {health_endpoint}")
    
    # 检查各模块配置
    modules = ["user-management", "question-bank", "exam-management"]
    for module in modules:
        config_path = f"{module}/config/base.yaml"
        if Path(config_path).exists():
            module_config = load_config(config_path)
            health_endpoint = module_config.get("health", {}).get("endpoint")
            if health_endpoint != expected_endpoint:
                errors.append(f"模块 {module} 健康检查端点不一致: {health_endpoint}")
    
    return errors

def validate_port_conflicts():
    """验证端口冲突"""
    ports = {}
    errors = []
    
    # 收集所有端口配置
    configs = [
        ("api-gateway", "api-gateway/config/gateway.yaml"),
        ("user-management", "user-management/config/base.yaml"),
        ("question-bank", "question-bank/config/base.yaml")
    ]
    
    for service_name, config_path in configs:
        if Path(config_path).exists():
            config = load_config(config_path)
            port = config.get("service", {}).get("port")
            if port:
                if port in ports:
                    errors.append(f"端口冲突: {service_name} 和 {ports[port]} 都使用端口 {port}")
                else:
                    ports[port] = service_name
    
    return errors

def load_config(config_path):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"加载配置文件失败 {config_path}: {e}")
        return {}

def main():
    """主验证函数"""
    print("开始配置验证...")
    
    all_errors = []
    
    # 验证健康检查端点
    health_errors = validate_health_endpoints()
    all_errors.extend(health_errors)
    
    # 验证端口冲突
    port_errors = validate_port_conflicts()
    all_errors.extend(port_errors)
    
    if all_errors:
        print("\n配置验证失败:")
        for error in all_errors:
            print(f"  ❌ {error}")
        sys.exit(1)
    else:
        print("\n✅ 配置验证通过")
        sys.exit(0)

if __name__ == "__main__":
    main()
```

## 8. 配置管理工作流

### 8.1 配置变更流程

1. **需求分析**：确定配置变更需求
2. **设计文档更新**：先更新相关设计文档
3. **配置文件修改**：根据设计文档修改配置文件
4. **配置验证**：运行配置验证脚本
5. **测试验证**：在测试环境验证配置
6. **代码审查**：配置变更代码审查
7. **部署上线**：生产环境部署

### 8.2 配置同步检查

**每日检查**：
- 运行配置验证脚本
- 检查设计文档与实现一致性
- 验证健康检查端点统一性

**版本发布前检查**：
- 完整配置验证
- 端口冲突检查
- 环境配置一致性检查

## 9. 监控和告警

### 9.1 配置监控指标

- **配置一致性**：设计文档与实现的一致性
- **健康检查成功率**：各模块健康检查成功率
- **配置变更频率**：配置文件变更频率
- **验证失败次数**：配置验证失败次数

### 9.2 告警规则

- **配置不一致告警**：发现设计与实现不一致时告警
- **健康检查失败告警**：健康检查连续失败时告警
- **端口冲突告警**：发现端口冲突时告警

## 10. 最佳实践

### 10.1 配置管理最佳实践

1. **版本控制**：所有配置文件纳入版本控制
2. **环境隔离**：不同环境使用独立配置
3. **敏感信息**：敏感配置使用环境变量或密钥管理
4. **文档同步**：配置变更必须同步更新文档
5. **自动化验证**：集成自动化配置验证

### 10.2 健康检查最佳实践

1. **统一端点**：所有模块使用统一健康检查端点
2. **多级检查**：实现多级健康检查策略
3. **依赖检查**：检查关键依赖服务状态
4. **快速响应**：健康检查接口快速响应
5. **详细信息**：提供详细的健康状态信息

### 10.3 故障处理最佳实践

1. **故障转移**：健康检查失败时的故障转移机制
2. **熔断保护**：实现熔断器模式
3. **重试机制**：合理的重试策略
4. **监控告警**：及时的故障监控和告警
5. **快速恢复**：故障快速恢复机制

## 11. 工具和脚本

### 11.1 配置管理工具

- **配置验证脚本**：`scripts/validate_config.py`
- **配置同步脚本**：`scripts/sync_config.py`
- **健康检查测试**：`scripts/test_health.py`
- **端口检查工具**：`scripts/check_ports.py`

### 11.2 CI/CD集成

```yaml
# .github/workflows/config-validation.yml
name: Configuration Validation

on:
  push:
    paths:
      - 'config/**'
      - '**/config/**'
      - '**/*.yaml'
      - '**/*.yml'

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install PyYAML
      - name: Validate configurations
        run: python scripts/validate_config.py
```

## 12. 总结

本配置管理规范确保了：

1. **一致性保证**：设计文档与实现保持一致
2. **标准化管理**：统一的配置格式和命名规范
3. **自动化验证**：自动化的配置验证和检查
4. **故障预防**：提前发现和预防配置问题
5. **可维护性**：提高系统的可维护性和可靠性

通过严格执行本规范，可以有效避免配置不一致导致的系统问题，提高整体系统的稳定性和可靠性。