#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型模块初始化文件

本模块定义了文件与项目管理工具的所有数据库模型。
包括项目、任务、文件、用户等核心实体的数据结构。

作者: 系统开发团队
创建时间: 2024-01-01
版本: v1.0
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Text, DateTime, Boolean, BigInteger, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

# 导入数据库实例
from app.extensions import db

class BaseModel(db.Model):
    """
    基础模型类
    
    提供所有模型的公共字段和方法，包括主键、创建时间、更新时间等。
    所有业务模型都应该继承此类。
    """
    
    __abstract__ = True
    
    # 主键，使用UUID确保全局唯一性
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    
    # 时间戳字段
    create_time = Column(DateTime, default=datetime.utcnow, nullable=False, comment='创建时间')
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment='更新时间')
    
    # 软删除标记
    is_deleted = Column(Boolean, default=False, nullable=False, comment='是否已删除')
    
    def to_dict(self, exclude_fields=None):
        """
        将模型实例转换为字典
        
        参数:
            exclude_fields (list): 需要排除的字段列表
            
        返回:
            dict: 模型数据字典
            
        示例:
            >>> project = Project(name='测试项目')
            >>> data = project.to_dict(exclude_fields=['is_deleted'])
            >>> print(data['name'])  # 输出: 测试项目
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            field_name = column.name
            if field_name not in exclude_fields:
                value = getattr(self, field_name)
                # 处理datetime类型
                if isinstance(value, datetime):
                    value = value.strftime('%Y-%m-%d %H:%M:%S')
                result[field_name] = value
                
        return result
    
    def update_from_dict(self, data):
        """
        从字典更新模型实例
        
        参数:
            data (dict): 包含更新数据的字典
            
        示例:
            >>> project = Project.query.first()
            >>> project.update_from_dict({'name': '新项目名称'})
            >>> db.session.commit()
        """
        for key, value in data.items():
            if hasattr(self, key) and key not in ['id', 'create_time']:
                setattr(self, key, value)
        
        self.update_time = datetime.utcnow()
    
    def soft_delete(self):
        """
        软删除记录
        
        将is_deleted标记设置为True，而不是物理删除记录。
        
        示例:
            >>> project = Project.query.first()
            >>> project.soft_delete()
            >>> db.session.commit()
        """
        self.is_deleted = True
        self.update_time = datetime.utcnow()

class Project(BaseModel):
    """
    项目模型
    
    存储项目的基本信息，包括项目名称、描述、负责人、时间等。
    一个项目可以包含多个任务和文件。
    """
    
    __tablename__ = 'projects'
    
    # 项目基本信息
    project_name = Column(String(100), nullable=False, comment='项目名称')
    description = Column(Text, comment='项目描述')
    
    # 项目管理信息
    manager_id = Column(String(36), nullable=False, comment='项目负责人ID')
    status = Column(String(20), default='active', nullable=False, comment='项目状态: active, completed, paused, cancelled')
    
    # 项目时间
    start_date = Column(DateTime, comment='项目开始时间')
    end_date = Column(DateTime, comment='项目结束时间')
    
    # 项目统计信息
    total_tasks = Column(Integer, default=0, comment='总任务数')
    completed_tasks = Column(Integer, default=0, comment='已完成任务数')
    total_files = Column(Integer, default=0, comment='总文件数')
    
    # 关联关系
    tasks = relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    files = relationship('File', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    members = relationship('ProjectMember', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Project {self.project_name}>'
    
    def get_progress(self):
        """
        计算项目进度百分比
        
        返回:
            float: 项目完成进度（0-100）
            
        示例:
            >>> project = Project.query.first()
            >>> progress = project.get_progress()
            >>> print(f"项目进度: {progress}%")
        """
        if self.total_tasks == 0:
            return 0.0
        return round((self.completed_tasks / self.total_tasks) * 100, 2)
    
    def update_statistics(self):
        """
        更新项目统计信息
        
        重新计算项目的任务数量、文件数量等统计数据。
        
        示例:
            >>> project = Project.query.first()
            >>> project.update_statistics()
            >>> db.session.commit()
        """
        # 更新任务统计
        self.total_tasks = self.tasks.filter_by(is_deleted=False).count()
        self.completed_tasks = self.tasks.filter_by(is_deleted=False, status='completed').count()
        
        # 更新文件统计
        self.total_files = self.files.filter_by(is_deleted=False).count()
        
        self.update_time = datetime.utcnow()

class Task(BaseModel):
    """
    任务模型
    
    存储项目任务的详细信息，包括任务标题、描述、负责人、状态等。
    任务属于某个项目，可以有前置任务依赖关系。
    """
    
    __tablename__ = 'tasks'
    
    # 任务基本信息
    task_title = Column(String(200), nullable=False, comment='任务标题')
    description = Column(Text, comment='任务描述')
    
    # 任务关联信息
    project_id = Column(String(36), ForeignKey('projects.id'), nullable=False, comment='所属项目ID')
    assignee_id = Column(String(36), comment='任务负责人ID')
    parent_task_id = Column(String(36), ForeignKey('tasks.id'), comment='父任务ID')
    
    # 任务状态和优先级
    status = Column(String(20), default='pending', nullable=False, comment='任务状态: pending, in_progress, completed, cancelled')
    priority = Column(String(10), default='medium', nullable=False, comment='优先级: low, medium, high, urgent')
    progress = Column(Integer, default=0, nullable=False, comment='任务进度百分比')
    
    # 任务时间
    due_date = Column(DateTime, comment='任务截止时间')
    start_date = Column(DateTime, comment='任务开始时间')
    complete_date = Column(DateTime, comment='任务完成时间')
    
    # 任务统计
    estimated_hours = Column(Integer, default=0, comment='预估工时（小时）')
    actual_hours = Column(Integer, default=0, comment='实际工时（小时）')
    
    # 关联关系
    subtasks = relationship('Task', backref=db.backref('parent_task', remote_side='Task.id'), lazy='dynamic')
    task_files = relationship('TaskFile', backref='task', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Task {self.task_title}>'
    
    def mark_completed(self):
        """
        标记任务为已完成
        
        设置任务状态为completed，进度为100%，并记录完成时间。
        
        示例:
            >>> task = Task.query.first()
            >>> task.mark_completed()
            >>> db.session.commit()
        """
        self.status = 'completed'
        self.progress = 100
        self.complete_date = datetime.utcnow()
        self.update_time = datetime.utcnow()
    
    def can_start(self):
        """
        检查任务是否可以开始
        
        检查前置任务是否已完成。
        
        返回:
            bool: 如果可以开始返回True，否则返回False
            
        示例:
            >>> task = Task.query.first()
            >>> if task.can_start():
            ...     task.status = 'in_progress'
        """
        if self.parent_task_id:
            parent_task = Task.query.get(self.parent_task_id)
            return parent_task and parent_task.status == 'completed'
        return True

class File(BaseModel):
    """
    文件模型
    
    存储上传文件的元数据信息，包括文件名、路径、大小、类型等。
    文件可以关联到项目，也可以独立存在。
    """
    
    __tablename__ = 'files'
    
    # 文件基本信息
    file_name = Column(String(255), nullable=False, comment='文件显示名称')
    original_name = Column(String(255), nullable=False, comment='原始文件名')
    file_path = Column(String(500), nullable=False, comment='文件存储路径')
    
    # 文件属性
    file_size = Column(BigInteger, nullable=False, comment='文件大小（字节）')
    file_type = Column(String(50), comment='文件类型扩展名')
    mime_type = Column(String(100), comment='MIME类型')
    
    # 文件关联信息
    project_id = Column(String(36), ForeignKey('projects.id'), comment='所属项目ID')
    folder_id = Column(String(36), ForeignKey('folders.id'), comment='所属文件夹ID')
    uploader_id = Column(String(36), nullable=False, comment='上传者ID')
    
    # 文件统计
    download_count = Column(Integer, default=0, comment='下载次数')
    view_count = Column(Integer, default=0, comment='查看次数')
    
    # 文件状态
    is_public = Column(Boolean, default=False, comment='是否公开')
    is_locked = Column(Boolean, default=False, comment='是否锁定')
    
    # 关联关系
    tags = relationship('FileTag', backref='file', lazy='dynamic', cascade='all, delete-orphan')
    task_files = relationship('TaskFile', backref='file', lazy='dynamic', cascade='all, delete-orphan')
    shares = relationship('FileShare', backref='file', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<File {self.file_name}>'
    
    def get_file_size_human(self):
        """
        获取人类可读的文件大小
        
        返回:
            str: 格式化的文件大小字符串
            
        示例:
            >>> file = File.query.first()
            >>> print(file.get_file_size_human())  # 输出: 1.5 MB
        """
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    def increment_download(self):
        """
        增加下载计数
        
        每次文件被下载时调用此方法。
        
        示例:
            >>> file = File.query.first()
            >>> file.increment_download()
            >>> db.session.commit()
        """
        self.download_count += 1
        self.update_time = datetime.utcnow()

class Folder(BaseModel):
    """
    文件夹模型
    
    用于组织文件的层级结构，支持多级文件夹嵌套。
    """
    
    __tablename__ = 'folders'
    
    # 文件夹基本信息
    folder_name = Column(String(100), nullable=False, comment='文件夹名称')
    description = Column(Text, comment='文件夹描述')
    
    # 文件夹层级关系
    parent_folder_id = Column(String(36), ForeignKey('folders.id'), comment='父文件夹ID')
    project_id = Column(String(36), ForeignKey('projects.id'), comment='所属项目ID')
    
    # 文件夹属性
    is_public = Column(Boolean, default=False, comment='是否公开')
    creator_id = Column(String(36), nullable=False, comment='创建者ID')
    
    # 关联关系
    subfolders = relationship('Folder', backref=db.backref('parent_folder', remote_side='Folder.id'), lazy='dynamic')
    files = relationship('File', backref='folder', lazy='dynamic')
    
    def __repr__(self):
        return f'<Folder {self.folder_name}>'
    
    def get_full_path(self):
        """
        获取文件夹的完整路径
        
        返回:
            str: 从根目录到当前文件夹的完整路径
            
        示例:
            >>> folder = Folder.query.first()
            >>> print(folder.get_full_path())  # 输出: /项目A/文档/设计稿
        """
        path_parts = [self.folder_name]
        current = self
        
        while current.parent_folder_id:
            current = Folder.query.get(current.parent_folder_id)
            if current:
                path_parts.insert(0, current.folder_name)
            else:
                break
        
        return '/' + '/'.join(path_parts)

class ProjectMember(BaseModel):
    """
    项目成员模型
    
    管理项目成员关系和权限。
    """
    
    __tablename__ = 'project_members'
    
    # 关联信息
    project_id = Column(String(36), ForeignKey('projects.id'), nullable=False, comment='项目ID')
    user_id = Column(String(36), nullable=False, comment='用户ID')
    
    # 成员角色和权限
    role = Column(String(20), default='member', nullable=False, comment='角色: owner, manager, member, viewer')
    permissions = Column(Text, comment='权限列表（JSON格式）')
    
    # 成员状态
    status = Column(String(20), default='active', nullable=False, comment='状态: active, inactive, pending')
    join_date = Column(DateTime, default=datetime.utcnow, comment='加入时间')
    
    def __repr__(self):
        return f'<ProjectMember {self.user_id} in {self.project_id}>'

class FileTag(BaseModel):
    """
    文件标签模型
    
    为文件添加标签，便于分类和搜索。
    """
    
    __tablename__ = 'file_tags'
    
    # 关联信息
    file_id = Column(String(36), ForeignKey('files.id'), nullable=False, comment='文件ID')
    tag_name = Column(String(50), nullable=False, comment='标签名称')
    
    # 标签属性
    tag_color = Column(String(7), default='#007bff', comment='标签颜色')
    creator_id = Column(String(36), nullable=False, comment='创建者ID')
    
    def __repr__(self):
        return f'<FileTag {self.tag_name}>'

class TaskFile(BaseModel):
    """
    任务文件关联模型
    
    管理任务和文件之间的关联关系。
    """
    
    __tablename__ = 'task_files'
    
    # 关联信息
    task_id = Column(String(36), ForeignKey('tasks.id'), nullable=False, comment='任务ID')
    file_id = Column(String(36), ForeignKey('files.id'), nullable=False, comment='文件ID')
    
    # 关联属性
    relation_type = Column(String(20), default='attachment', comment='关联类型: attachment, reference, output')
    description = Column(Text, comment='关联描述')
    
    def __repr__(self):
        return f'<TaskFile {self.task_id}-{self.file_id}>'

class FileShare(BaseModel):
    """
    文件分享模型
    
    管理文件的分享链接和权限。
    """
    
    __tablename__ = 'file_shares'
    
    # 关联信息
    file_id = Column(String(36), ForeignKey('files.id'), nullable=False, comment='文件ID')
    sharer_id = Column(String(36), nullable=False, comment='分享者ID')
    
    # 分享配置
    share_token = Column(String(64), unique=True, nullable=False, comment='分享令牌')
    share_password = Column(String(100), comment='分享密码')
    
    # 分享权限
    can_download = Column(Boolean, default=True, comment='允许下载')
    can_view = Column(Boolean, default=True, comment='允许查看')
    
    # 分享时效
    expire_time = Column(DateTime, comment='过期时间')
    max_downloads = Column(Integer, comment='最大下载次数')
    download_count = Column(Integer, default=0, comment='已下载次数')
    
    # 分享状态
    is_active = Column(Boolean, default=True, comment='是否激活')
    
    def __repr__(self):
        return f'<FileShare {self.share_token}>'
    
    def is_valid(self):
        """
        检查分享链接是否有效
        
        返回:
            bool: 如果分享链接有效返回True，否则返回False
            
        示例:
            >>> share = FileShare.query.filter_by(share_token='abc123').first()
            >>> if share.is_valid():
            ...     # 允许访问文件
        """
        if not self.is_active or self.is_deleted:
            return False
        
        # 检查过期时间
        if self.expire_time and datetime.utcnow() > self.expire_time:
            return False
        
        # 检查下载次数限制
        if self.max_downloads and self.download_count >= self.max_downloads:
            return False
        
        return True

# 导出所有模型
__all__ = [
    'db',
    'BaseModel',
    'Project',
    'Task', 
    'File',
    'Folder',
    'ProjectMember',
    'FileTag',
    'TaskFile',
    'FileShare'
]