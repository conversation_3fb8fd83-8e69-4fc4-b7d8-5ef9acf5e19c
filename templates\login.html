{% extends "base.html" %}

{% block title %}登录 - {{ system_config.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6 col-xl-5">
        <div class="card">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-graduation-cap text-primary" style="font-size: 4rem;"></i>
                    <h3 class="mt-3 mb-1">{{ system_config.name }}</h3>
                    <p class="text-muted">Professional Skills Level Examination System</p>
                    <p class="text-muted small">版本 {{ system_config.version }}</p>
                </div>
                
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-1"></i>
                            用户名
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="请输入用户名" required autofocus>
                    </div>
                    
                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>
                            密码
                        </label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="请输入密码" required>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            登录系统
                        </button>
                    </div>
                </form>
                
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-muted mb-3">
                        <i class="fas fa-info-circle me-1"></i>
                        测试账户信息
                    </h6>
                    {% for username, user_info in users.items() %}
                    <div class="row mb-2">
                        <div class="col-3">
                            <small class="text-muted">{{ user_info.role }}:</small>
                        </div>
                        <div class="col-4">
                            <code class="user-account" data-username="{{ username }}">{{ username }}</code>
                        </div>
                        <div class="col-5">
                            <code class="user-password" data-password="{{ user_info.password }}">{{ user_info.password }}</code>
                        </div>
                    </div>
                    {% endfor %}
                    <small class="text-muted d-block mt-2">
                        <i class="fas fa-mouse-pointer me-1"></i>
                        点击账户信息可快速填入
                    </small>
                </div>
                
                <div class="mt-3 text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        首次登录后请及时修改密码
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center mt-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-rocket me-2"></i>
                    系统功能特性
                </h5>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-users text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6>用户管理</h6>
                            <small class="text-muted">多角色用户权限管理</small>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-book text-success mb-2" style="font-size: 2rem;"></i>
                            <h6>题库管理</h6>
                            <small class="text-muted">智能题库编辑与组卷</small>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-clipboard-check text-info mb-2" style="font-size: 2rem;"></i>
                            <h6>考试管理</h6>
                            <small class="text-muted">在线考试监控与管理</small>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-chart-line text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6>成绩分析</h6>
                            <small class="text-muted">智能成绩统计分析</small>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-desktop text-danger mb-2" style="font-size: 2rem;"></i>
                            <h6>系统监控</h6>
                            <small class="text-muted">实时系统状态监控</small>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-network-wired text-secondary mb-2" style="font-size: 2rem;"></i>
                            <h6>API网关</h6>
                            <small class="text-muted">统一API接口管理</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 点击账户信息快速填入
    $('.user-account').on('click', function() {
        const username = $(this).data('username');
        $('#username').val(username);
        $(this).closest('.row').find('.user-password').trigger('click');
    });
    
    $('.user-password').on('click', function() {
        const password = $(this).data('password');
        $('#password').val(password);
    });
    
    // 添加点击效果
    $('.user-account, .user-password').css('cursor', 'pointer').hover(
        function() { $(this).css('background-color', '#e9ecef'); },
        function() { $(this).css('background-color', 'transparent'); }
    );
    
    // 表单提交动画
    $('form').on('submit', function() {
        const btn = $(this).find('button[type="submit"]');
        btn.html('<i class="fas fa-spinner fa-spin me-2"></i>登录中...');
        btn.prop('disabled', true);
    });
});
</script>
{% endblock %}