# 认证授权机制设计文档

## 1. 认证授权架构概述

### 1.1 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    客户端层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │  Web前端     │  │  桌面客户端   │  │  移动端App   │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼ JWT Token
┌─────────────────────────────────────────────────────────┐
│                   API网关层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │  Token验证   │  │  权限检查    │  │  限流控制    │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼ 用户信息
┌─────────────────────────────────────────────────────────┐
│                   业务模块层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │ 用户管理模块  │  │ 题库管理模块  │  │ 考试管理模块  │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
```

### 1.2 核心组件
- **认证中心**：负责用户身份验证和Token颁发
- **权限中心**：负责权限定义、分配和检查
- **Token管理器**：负责JWT Token的生成、验证和刷新
- **会话管理器**：负责用户会话的创建、维护和销毁

### 1.3 设计原则
- **无状态认证**：基于JWT的无状态Token机制
- **细粒度权限**：基于RBAC的资源级权限控制
- **安全第一**：多层安全防护和审计机制
- **高性能**：缓存优化和快速验证
- **易扩展**：支持多种认证方式和权限模型

## 2. JWT Token设计

### 2.1 Token结构设计

#### Access Token结构
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT",
    "kid": "key-001"
  },
  "payload": {
    "user_id": "user-123456",
    "username": "admin",
    "role": "administrator",
    "permissions": [
      "user:*:*",
      "exam:*:*",
      "question:*:*",
      "grade:*:read"
    ],
    "session_id": "session-789",
    "client_type": "web",
    "exp": 1640995200,
    "iat": 1640991600,
    "nbf": 1640991600,
    "iss": "exam-system",
    "aud": "api-gateway",
    "jti": "token-unique-id"
  }
}
```

#### Refresh Token结构
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT",
    "kid": "key-001"
  },
  "payload": {
    "user_id": "user-123456",
    "session_id": "session-789",
    "token_type": "refresh",
    "exp": 1641600000,
    "iat": 1640991600,
    "iss": "exam-system",
    "aud": "api-gateway",
    "jti": "refresh-token-unique-id"
  }
}
```

### 2.2 Token生命周期管理

#### Token有效期设置
```yaml
token_config:
  access_token:
    expires_in: 7200        # 2小时
    refresh_threshold: 300  # 过期前5分钟自动刷新
    
  refresh_token:
    expires_in: 604800      # 7天
    max_refresh_count: 10   # 最大刷新次数
    
  remember_me:
    access_token_expires: 86400   # 24小时
    refresh_token_expires: 2592000 # 30天
```

#### Token刷新机制
```python
class TokenManager:
    def refresh_token(self, refresh_token):
        """
        刷新Access Token
        
        流程：
        1. 验证Refresh Token有效性
        2. 检查用户状态和权限
        3. 生成新的Access Token
        4. 可选：生成新的Refresh Token（滑动窗口）
        5. 记录刷新日志
        """
        # 验证refresh token
        payload = self.verify_token(refresh_token)
        if not payload:
            raise InvalidTokenError("Invalid refresh token")
            
        # 检查用户状态
        user = self.get_user(payload['user_id'])
        if not user or not user.is_active:
            raise UserInactiveError("User is inactive")
            
        # 生成新的access token
        new_access_token = self.generate_access_token(user)
        
        # 可选：滑动窗口刷新refresh token
        new_refresh_token = None
        if self.should_refresh_refresh_token(payload):
            new_refresh_token = self.generate_refresh_token(user)
            
        return {
            'access_token': new_access_token,
            'refresh_token': new_refresh_token or refresh_token,
            'expires_in': self.config.access_token_expires
        }
```

### 2.3 Token安全机制

#### 密钥管理
```yaml
jwt_security:
  algorithm: "HS256"
  secret_key: "${JWT_SECRET_KEY}"  # 环境变量
  key_rotation:
    enabled: true
    rotation_interval: 2592000  # 30天轮换一次
    grace_period: 86400         # 旧密钥保留24小时
    
  signing_keys:
    - kid: "key-001"
      algorithm: "HS256"
      secret: "${JWT_SECRET_KEY_001}"
      created_at: "2025-01-01T00:00:00Z"
      expires_at: "2025-02-01T00:00:00Z"
      
    - kid: "key-002"
      algorithm: "HS256"
      secret: "${JWT_SECRET_KEY_002}"
      created_at: "2025-01-15T00:00:00Z"
      expires_at: "2025-02-15T00:00:00Z"
```

#### Token黑名单机制
```python
class TokenBlacklist:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.blacklist_prefix = "token_blacklist:"
        
    def add_to_blacklist(self, token_jti, expires_at):
        """
        将Token加入黑名单
        
        场景：
        - 用户主动登出
        - 管理员强制下线
        - 检测到异常行为
        - 密码重置后
        """
        key = f"{self.blacklist_prefix}{token_jti}"
        ttl = expires_at - int(time.time())
        if ttl > 0:
            self.redis.setex(key, ttl, "blacklisted")
            
    def is_blacklisted(self, token_jti):
        """检查Token是否在黑名单中"""
        key = f"{self.blacklist_prefix}{token_jti}"
        return self.redis.exists(key)
        
    def blacklist_user_tokens(self, user_id):
        """将用户所有Token加入黑名单"""
        # 从活跃会话中获取用户的所有token
        sessions = self.get_user_sessions(user_id)
        for session in sessions:
            self.add_to_blacklist(session['access_token_jti'], session['access_expires'])
            self.add_to_blacklist(session['refresh_token_jti'], session['refresh_expires'])
```

## 3. 权限模型设计

### 3.1 RBAC权限模型

#### 权限模型结构
```
用户(User) ──→ 角色(Role) ──→ 权限(Permission) ──→ 资源(Resource)
     │                │              │                │
     │                │              │                └── 操作(Action)
     │                │              └── 范围(Scope)
     │                └── 角色继承(Role Inheritance)
     └── 直接权限(Direct Permission)
```

#### 权限定义格式
```
权限格式：{module}:{resource}:{action}:{scope}

示例：
- user:profile:read:own          # 读取自己的用户资料
- user:list:read:all             # 读取所有用户列表
- exam:session:write:department  # 在部门范围内创建考试
- question:bank:delete:own       # 删除自己创建的题库
- grade:result:read:class        # 查看班级成绩
- system:config:write:all        # 修改系统配置
```

### 3.2 角色定义

#### 系统角色
```yaml
roles:
  super_admin:
    name: "超级管理员"
    description: "系统内置超级管理员，拥有所有权限"
    permissions:
      - "*:*:*:*"
    built_in: true
    deletable: false
    
  system_admin:
    name: "系统管理员"
    description: "系统管理员，负责系统配置和用户管理"
    permissions:
      - "user:*:*:all"
      - "role:*:*:all"
      - "system:config:*:all"
      - "audit:log:read:all"
    parent_roles: []
    
  exam_admin:
    name: "考试管理员"
    description: "考试管理员，负责考试相关管理"
    permissions:
      - "exam:*:*:all"
      - "question:*:*:all"
      - "grade:result:read:all"
      - "monitor:session:read:all"
    parent_roles: []
    
  examiner:
    name: "考评员"
    description: "考评员，负责考场监控和管理"
    permissions:
      - "exam:session:read:assigned"
      - "exam:session:manage:assigned"
      - "monitor:session:read:assigned"
      - "monitor:violation:write:assigned"
      - "user:profile:read:own"
    parent_roles: []
    
  teacher:
    name: "教师"
    description: "教师，负责阅卷和成绩管理"
    permissions:
      - "question:bank:read:all"
      - "grade:manual:write:assigned"
      - "grade:result:read:assigned"
      - "user:profile:read:own"
    parent_roles: []
    
  student:
    name: "学生"
    description: "学生，参加考试"
    permissions:
      - "exam:session:join:assigned"
      - "exam:paper:read:own"
      - "exam:answer:write:own"
      - "grade:result:read:own"
      - "user:profile:read:own"
      - "user:profile:write:own"
    parent_roles: []
```

### 3.3 权限检查机制

#### 权限检查算法
```python
class PermissionChecker:
    def __init__(self, user_permissions, resource_permissions):
        self.user_permissions = user_permissions
        self.resource_permissions = resource_permissions
        
    def check_permission(self, required_permission, context=None):
        """
        检查用户是否有指定权限
        
        Args:
            required_permission: 所需权限，格式：module:resource:action:scope
            context: 上下文信息，用于范围检查
            
        Returns:
            bool: 是否有权限
        """
        # 超级管理员拥有所有权限
        if "*:*:*:*" in self.user_permissions:
            return True
            
        # 检查直接匹配的权限
        if required_permission in self.user_permissions:
            return True
            
        # 检查通配符权限
        for user_perm in self.user_permissions:
            if self._permission_match(user_perm, required_permission, context):
                return True
                
        return False
        
    def _permission_match(self, user_perm, required_perm, context):
        """
        权限匹配算法
        
        支持通配符匹配：
        - user:*:*:* 匹配所有用户相关权限
        - user:profile:*:* 匹配用户资料的所有操作
        - user:profile:read:* 匹配用户资料的所有读取权限
        """
        user_parts = user_perm.split(':')
        required_parts = required_perm.split(':')
        
        if len(user_parts) != 4 or len(required_parts) != 4:
            return False
            
        for i, (user_part, required_part) in enumerate(zip(user_parts, required_parts)):
            if user_part == '*':
                continue
            elif user_part != required_part:
                # 特殊处理scope部分
                if i == 3:  # scope部分
                    return self._check_scope(user_part, required_part, context)
                else:
                    return False
                    
        return True
        
    def _check_scope(self, user_scope, required_scope, context):
        """
        检查权限范围
        
        范围层级：all > department > class > own
        """
        scope_hierarchy = {
            'all': 4,
            'department': 3,
            'class': 2,
            'own': 1
        }
        
        user_level = scope_hierarchy.get(user_scope, 0)
        required_level = scope_hierarchy.get(required_scope, 0)
        
        # 用户权限级别必须大于等于所需级别
        if user_level >= required_level:
            # 进一步检查上下文
            return self._check_scope_context(user_scope, required_scope, context)
            
        return False
        
    def _check_scope_context(self, user_scope, required_scope, context):
        """
        基于上下文检查权限范围
        """
        if not context:
            return True
            
        user_id = context.get('user_id')
        target_user_id = context.get('target_user_id')
        user_department = context.get('user_department')
        target_department = context.get('target_department')
        
        if user_scope == 'own':
            return user_id == target_user_id
        elif user_scope == 'department':
            return user_department == target_department
        elif user_scope == 'all':
            return True
            
        return False
```

### 3.4 动态权限控制

#### 基于时间的权限控制
```python
class TimeBasedPermission:
    def __init__(self, permission, start_time, end_time, days_of_week=None, hours_of_day=None):
        self.permission = permission
        self.start_time = start_time
        self.end_time = end_time
        self.days_of_week = days_of_week or []
        self.hours_of_day = hours_of_day or []
        
    def is_valid_at(self, check_time):
        """
        检查权限在指定时间是否有效
        """
        # 检查时间范围
        if check_time < self.start_time or check_time > self.end_time:
            return False
            
        # 检查星期几
        if self.days_of_week and check_time.weekday() not in self.days_of_week:
            return False
            
        # 检查小时范围
        if self.hours_of_day and check_time.hour not in self.hours_of_day:
            return False
            
        return True

# 示例：考试期间的特殊权限
exam_permission = TimeBasedPermission(
    permission="exam:session:join:assigned",
    start_time=datetime(2025, 1, 15, 9, 0),
    end_time=datetime(2025, 1, 15, 11, 0),
    days_of_week=[0, 1, 2, 3, 4],  # 周一到周五
    hours_of_day=list(range(9, 17))  # 9:00-17:00
)
```

#### 基于资源的权限控制
```python
class ResourceBasedPermission:
    def __init__(self, user_id, resource_type, resource_id, permissions):
        self.user_id = user_id
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.permissions = permissions
        
    def check_resource_permission(self, user_id, resource_type, resource_id, action):
        """
        检查用户对特定资源的权限
        
        示例：
        - 用户只能编辑自己创建的题库
        - 教师只能阅卷分配给自己的试卷
        - 学生只能查看自己的成绩
        """
        if self.user_id != user_id:
            return False
            
        if self.resource_type != resource_type:
            return False
            
        if self.resource_id != resource_id:
            return False
            
        return action in self.permissions

# 示例：题库创建者权限
bank_creator_permission = ResourceBasedPermission(
    user_id="teacher-123",
    resource_type="question_bank",
    resource_id="bank-456",
    permissions=["read", "write", "delete"]
)
```

## 4. 认证流程设计

### 4.1 用户登录流程

#### 标准登录流程
```
1. 客户端 → API网关：POST /api/v1/auth/login
   {
     "username": "admin",
     "password": "password123",
     "remember_me": false,
     "client_type": "web"
   }

2. API网关 → 用户管理模块：验证用户凭据
   {
     "username": "admin",
     "password_hash": "bcrypt_hash"
   }

3. 用户管理模块 → API网关：返回用户信息
   {
     "user_id": "user-123",
     "username": "admin",
     "role": "administrator",
     "permissions": [...],
     "status": "active"
   }

4. API网关：生成JWT Token
   - 创建会话记录
   - 生成Access Token和Refresh Token
   - 记录登录日志

5. API网关 → 客户端：返回认证结果
   {
     "code": 200,
     "data": {
       "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
       "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
       "token_type": "Bearer",
       "expires_in": 7200,
       "user": {
         "id": "user-123",
         "username": "admin",
         "role": "administrator"
       }
     }
   }
```

#### 多因素认证流程
```python
class MFAAuthenticator:
    def __init__(self):
        self.mfa_methods = {
            'sms': SMSAuthenticator(),
            'email': EmailAuthenticator(),
            'totp': TOTPAuthenticator(),
            'face': FaceAuthenticator()
        }
        
    def initiate_mfa(self, user_id, method):
        """
        启动多因素认证
        
        流程：
        1. 验证用户基础凭据（用户名/密码）
        2. 检查用户MFA设置
        3. 发送验证码或准备验证
        4. 返回临时Token
        """
        user = self.get_user(user_id)
        if not user.mfa_enabled:
            raise MFANotEnabledError("MFA not enabled for user")
            
        authenticator = self.mfa_methods.get(method)
        if not authenticator:
            raise UnsupportedMFAMethodError(f"Unsupported MFA method: {method}")
            
        # 生成并发送验证码
        challenge = authenticator.generate_challenge(user)
        
        # 生成临时Token
        temp_token = self.generate_temp_token(user_id, challenge['id'])
        
        return {
            'temp_token': temp_token,
            'challenge_id': challenge['id'],
            'method': method,
            'expires_in': 300  # 5分钟有效期
        }
        
    def verify_mfa(self, temp_token, challenge_response):
        """
        验证多因素认证
        
        流程：
        1. 验证临时Token
        2. 验证挑战响应
        3. 生成正式Token
        4. 记录认证日志
        """
        # 验证临时Token
        temp_payload = self.verify_temp_token(temp_token)
        user_id = temp_payload['user_id']
        challenge_id = temp_payload['challenge_id']
        
        # 验证挑战响应
        challenge = self.get_challenge(challenge_id)
        authenticator = self.mfa_methods[challenge['method']]
        
        if not authenticator.verify_response(challenge, challenge_response):
            raise InvalidMFAResponseError("Invalid MFA response")
            
        # 生成正式Token
        user = self.get_user(user_id)
        tokens = self.generate_tokens(user)
        
        # 清理临时数据
        self.cleanup_challenge(challenge_id)
        
        return tokens
```

### 4.2 请求认证流程

#### Token验证中间件
```python
class AuthenticationMiddleware:
    def __init__(self, app, token_manager, permission_checker):
        self.app = app
        self.token_manager = token_manager
        self.permission_checker = permission_checker
        self.exempt_paths = [
            '/api/v1/auth/login',
            '/api/v1/auth/register',
            '/health',
            '/docs'
        ]
        
    def __call__(self, environ, start_response):
        request_path = environ.get('PATH_INFO', '')
        
        # 跳过免认证路径
        if self._is_exempt_path(request_path):
            return self.app(environ, start_response)
            
        try:
            # 提取和验证Token
            token = self._extract_token(environ)
            payload = self.token_manager.verify_token(token)
            
            # 检查Token黑名单
            if self.token_manager.is_blacklisted(payload['jti']):
                return self._unauthorized_response(start_response, "Token is blacklisted")
                
            # 检查用户状态
            user = self._get_user(payload['user_id'])
            if not user or not user.is_active:
                return self._unauthorized_response(start_response, "User is inactive")
                
            # 检查权限
            required_permission = self._get_required_permission(request_path, environ.get('REQUEST_METHOD'))
            if required_permission:
                context = self._build_permission_context(environ, user)
                if not self.permission_checker.check_permission(required_permission, context):
                    return self._forbidden_response(start_response, "Insufficient permissions")
                    
            # 添加用户信息到环境变量
            environ['user_id'] = payload['user_id']
            environ['user_role'] = payload['role']
            environ['user_permissions'] = payload['permissions']
            environ['session_id'] = payload['session_id']
            
            return self.app(environ, start_response)
            
        except InvalidTokenError as e:
            return self._unauthorized_response(start_response, str(e))
        except Exception as e:
            return self._server_error_response(start_response, str(e))
            
    def _extract_token(self, environ):
        """从请求头中提取Token"""
        auth_header = environ.get('HTTP_AUTHORIZATION', '')
        if not auth_header.startswith('Bearer '):
            raise InvalidTokenError("Missing or invalid authorization header")
        return auth_header[7:]
        
    def _get_required_permission(self, path, method):
        """根据路径和方法获取所需权限"""
        # 从路由配置中获取权限要求
        route_config = self._match_route(path)
        if route_config:
            return route_config.get('required_permission')
        return None
        
    def _build_permission_context(self, environ, user):
        """构建权限检查上下文"""
        return {
            'user_id': user.id,
            'user_role': user.role,
            'user_department': user.department_id,
            'request_path': environ.get('PATH_INFO'),
            'request_method': environ.get('REQUEST_METHOD'),
            'client_ip': environ.get('REMOTE_ADDR')
        }
```

## 5. 会话管理

### 5.1 会话生命周期

#### 会话创建
```python
class SessionManager:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.session_prefix = "session:"
        self.user_sessions_prefix = "user_sessions:"
        
    def create_session(self, user_id, client_info):
        """
        创建用户会话
        
        Args:
            user_id: 用户ID
            client_info: 客户端信息
            
        Returns:
            dict: 会话信息
        """
        session_id = self._generate_session_id()
        
        session_data = {
            'session_id': session_id,
            'user_id': user_id,
            'created_at': int(time.time()),
            'last_activity': int(time.time()),
            'client_type': client_info.get('type'),
            'client_version': client_info.get('version'),
            'user_agent': client_info.get('user_agent'),
            'ip_address': client_info.get('ip_address'),
            'access_token_jti': None,
            'refresh_token_jti': None,
            'status': 'active'
        }
        
        # 存储会话数据
        session_key = f"{self.session_prefix}{session_id}"
        self.redis.hmset(session_key, session_data)
        self.redis.expire(session_key, 604800)  # 7天过期
        
        # 添加到用户会话列表
        user_sessions_key = f"{self.user_sessions_prefix}{user_id}"
        self.redis.sadd(user_sessions_key, session_id)
        self.redis.expire(user_sessions_key, 604800)
        
        return session_data
        
    def update_session_tokens(self, session_id, access_token_jti, refresh_token_jti):
        """更新会话中的Token信息"""
        session_key = f"{self.session_prefix}{session_id}"
        self.redis.hmset(session_key, {
            'access_token_jti': access_token_jti,
            'refresh_token_jti': refresh_token_jti,
            'last_activity': int(time.time())
        })
        
    def update_activity(self, session_id):
        """更新会话活动时间"""
        session_key = f"{self.session_prefix}{session_id}"
        self.redis.hset(session_key, 'last_activity', int(time.time()))
        
    def terminate_session(self, session_id):
        """终止会话"""
        session_key = f"{self.session_prefix}{session_id}"
        session_data = self.redis.hgetall(session_key)
        
        if session_data:
            # 将相关Token加入黑名单
            if session_data.get('access_token_jti'):
                self.token_manager.blacklist_token(session_data['access_token_jti'])
            if session_data.get('refresh_token_jti'):
                self.token_manager.blacklist_token(session_data['refresh_token_jti'])
                
            # 从用户会话列表中移除
            user_sessions_key = f"{self.user_sessions_prefix}{session_data['user_id']}"
            self.redis.srem(user_sessions_key, session_id)
            
            # 删除会话数据
            self.redis.delete(session_key)
            
        return True
```

### 5.2 并发会话控制

#### 单点登录控制
```python
class SingleSignOnManager:
    def __init__(self, session_manager, max_sessions_per_user=1):
        self.session_manager = session_manager
        self.max_sessions = max_sessions_per_user
        
    def enforce_sso(self, user_id, new_session_id):
        """
        强制单点登录
        
        策略：
        1. 允许多设备登录但限制同类型设备
        2. 新登录踢掉旧登录
        3. 保留最近活跃的会话
        """
        user_sessions = self.session_manager.get_user_sessions(user_id)
        
        if len(user_sessions) >= self.max_sessions:
            # 按最后活动时间排序，保留最新的
            sorted_sessions = sorted(user_sessions, key=lambda x: x['last_activity'])
            
            # 终止旧会话
            sessions_to_terminate = sorted_sessions[:-self.max_sessions + 1]
            for session in sessions_to_terminate:
                self.session_manager.terminate_session(session['session_id'])
                
                # 发送踢出通知
                self._send_logout_notification(session)
                
    def _send_logout_notification(self, session):
        """发送登出通知"""
        # 通过WebSocket或其他方式通知客户端
        notification = {
            'type': 'force_logout',
            'reason': 'new_login_detected',
            'message': '检测到新的登录，当前会话已被终止',
            'timestamp': int(time.time())
        }
        
        # 发送通知逻辑
        self.notification_service.send_to_session(session['session_id'], notification)
```

## 6. 安全防护机制

### 6.1 防暴力破解

#### 登录尝试限制
```python
class LoginAttemptLimiter:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.attempt_prefix = "login_attempts:"
        self.lockout_prefix = "login_lockout:"
        
    def check_login_attempts(self, identifier, ip_address):
        """
        检查登录尝试次数
        
        Args:
            identifier: 用户标识（用户名或邮箱）
            ip_address: IP地址
            
        Returns:
            dict: 检查结果
        """
        # 检查用户级别的尝试次数
        user_key = f"{self.attempt_prefix}user:{identifier}"
        user_attempts = int(self.redis.get(user_key) or 0)
        
        # 检查IP级别的尝试次数
        ip_key = f"{self.attempt_prefix}ip:{ip_address}"
        ip_attempts = int(self.redis.get(ip_key) or 0)
        
        # 检查是否被锁定
        user_lockout_key = f"{self.lockout_prefix}user:{identifier}"
        ip_lockout_key = f"{self.lockout_prefix}ip:{ip_address}"
        
        user_locked = self.redis.exists(user_lockout_key)
        ip_locked = self.redis.exists(ip_lockout_key)
        
        if user_locked or ip_locked:
            lockout_ttl = max(
                self.redis.ttl(user_lockout_key) if user_locked else 0,
                self.redis.ttl(ip_lockout_key) if ip_locked else 0
            )
            return {
                'allowed': False,
                'reason': 'account_locked',
                'lockout_remaining': lockout_ttl
            }
            
        # 检查尝试次数限制
        max_user_attempts = 5
        max_ip_attempts = 10
        
        if user_attempts >= max_user_attempts or ip_attempts >= max_ip_attempts:
            return {
                'allowed': False,
                'reason': 'too_many_attempts',
                'user_attempts': user_attempts,
                'ip_attempts': ip_attempts
            }
            
        return {
            'allowed': True,
            'user_attempts': user_attempts,
            'ip_attempts': ip_attempts
        }
        
    def record_failed_attempt(self, identifier, ip_address):
        """记录失败的登录尝试"""
        user_key = f"{self.attempt_prefix}user:{identifier}"
        ip_key = f"{self.attempt_prefix}ip:{ip_address}"
        
        # 增加尝试次数
        user_attempts = self.redis.incr(user_key)
        ip_attempts = self.redis.incr(ip_key)
        
        # 设置过期时间（1小时）
        self.redis.expire(user_key, 3600)
        self.redis.expire(ip_key, 3600)
        
        # 检查是否需要锁定
        if user_attempts >= 5:
            self._lockout_user(identifier)
            
        if ip_attempts >= 10:
            self._lockout_ip(ip_address)
            
    def _lockout_user(self, identifier):
        """锁定用户"""
        lockout_key = f"{self.lockout_prefix}user:{identifier}"
        lockout_duration = 1800  # 30分钟
        
        self.redis.setex(lockout_key, lockout_duration, "locked")
        
        # 记录安全事件
        self._log_security_event({
            'type': 'user_lockout',
            'identifier': identifier,
            'duration': lockout_duration,
            'timestamp': int(time.time())
        })
        
    def reset_attempts(self, identifier, ip_address):
        """重置登录尝试次数（登录成功后调用）"""
        user_key = f"{self.attempt_prefix}user:{identifier}"
        ip_key = f"{self.attempt_prefix}ip:{ip_address}"
        
        self.redis.delete(user_key)
        self.redis.delete(ip_key)
```

### 6.2 异常行为检测

#### 登录行为分析
```python
class LoginBehaviorAnalyzer:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.behavior_prefix = "login_behavior:"
        
    def analyze_login_behavior(self, user_id, login_info):
        """
        分析登录行为异常
        
        检测项：
        1. 异常登录时间
        2. 异常登录地点
        3. 异常设备
        4. 登录频率异常
        """
        behavior_key = f"{self.behavior_prefix}{user_id}"
        
        # 获取历史登录行为
        historical_data = self._get_historical_behavior(user_id)
        
        anomalies = []
        
        # 检测异常登录时间
        if self._is_unusual_time(login_info['timestamp'], historical_data):
            anomalies.append({
                'type': 'unusual_time',
                'severity': 'medium',
                'description': '在异常时间段登录'
            })
            
        # 检测异常登录地点
        if self._is_unusual_location(login_info['ip_address'], historical_data):
            anomalies.append({
                'type': 'unusual_location',
                'severity': 'high',
                'description': '从异常地点登录'
            })
            
        # 检测异常设备
        if self._is_unusual_device(login_info['user_agent'], historical_data):
            anomalies.append({
                'type': 'unusual_device',
                'severity': 'high',
                'description': '使用异常设备登录'
            })
            
        # 检测登录频率异常
        if self._is_unusual_frequency(user_id, login_info['timestamp']):
            anomalies.append({
                'type': 'unusual_frequency',
                'severity': 'medium',
                'description': '登录频率异常'
            })
            
        # 更新行为数据
        self._update_behavior_data(user_id, login_info)
        
        return anomalies
        
    def _is_unusual_time(self, timestamp, historical_data):
        """检测异常登录时间"""
        login_hour = datetime.fromtimestamp(timestamp).hour
        
        # 获取历史登录时间分布
        hour_distribution = historical_data.get('hour_distribution', {})
        
        # 如果在从未登录过的时间段登录，且是深夜时间
        if str(login_hour) not in hour_distribution and (login_hour < 6 or login_hour > 23):
            return True
            
        return False
        
    def _is_unusual_location(self, ip_address, historical_data):
        """检测异常登录地点"""
        # 获取IP地理位置
        location = self._get_ip_location(ip_address)
        
        # 获取历史登录地点
        historical_locations = historical_data.get('locations', [])
        
        # 如果是全新的国家/地区
        if location['country'] not in [loc['country'] for loc in historical_locations]:
            return True
            
        return False
        
    def _is_unusual_device(self, user_agent, historical_data):
        """检测异常设备"""
        device_info = self._parse_user_agent(user_agent)
        
        historical_devices = historical_data.get('devices', [])
        
        # 检查设备指纹
        device_fingerprint = f"{device_info['os']}_{device_info['browser']}"
        historical_fingerprints = [d['fingerprint'] for d in historical_devices]
        
        return device_fingerprint not in historical_fingerprints
```

### 6.3 Token安全增强

#### Token指纹验证
```python
class TokenFingerprintValidator:
    def __init__(self):
        self.fingerprint_fields = [
            'user_agent',
            'accept_language',
            'screen_resolution',
            'timezone'
        ]
        
    def generate_fingerprint(self, request_headers, client_info):
        """
        生成客户端指纹
        
        基于多个客户端特征生成唯一指纹
        """
        fingerprint_data = {
            'user_agent': request_headers.get('User-Agent', ''),
            'accept_language': request_headers.get('Accept-Language', ''),
            'accept_encoding': request_headers.get('Accept-Encoding', ''),
            'screen_resolution': client_info.get('screen_resolution', ''),
            'timezone': client_info.get('timezone', ''),
            'color_depth': client_info.get('color_depth', ''),
            'platform': client_info.get('platform', '')
        }
        
        # 生成指纹哈希
        fingerprint_string = '|'.join([
            str(fingerprint_data.get(field, '')) for field in self.fingerprint_fields
        ])
        
        return hashlib.sha256(fingerprint_string.encode()).hexdigest()[:16]
        
    def validate_fingerprint(self, token_fingerprint, current_fingerprint):
        """
        验证Token指纹
        
        允许一定程度的指纹变化（如浏览器更新）
        """
        if token_fingerprint == current_fingerprint:
            return True
            
        # 计算指纹相似度
        similarity = self._calculate_fingerprint_similarity(token_fingerprint, current_fingerprint)
        
        # 相似度阈值
        return similarity > 0.8
        
    def _calculate_fingerprint_similarity(self, fp1, fp2):
        """计算指纹相似度"""
        # 简单的字符串相似度计算
        if len(fp1) != len(fp2):
            return 0.0
            
        matches = sum(c1 == c2 for c1, c2 in zip(fp1, fp2))
        return matches / len(fp1)
```

## 7. 审计和监控

### 7.1 认证审计

#### 认证事件记录
```python
class AuthenticationAuditor:
    def __init__(self, logger, event_store):
        self.logger = logger
        self.event_store = event_store
        
    def log_authentication_event(self, event_type, user_id, details):
        """
        记录认证事件
        
        事件类型：
        - login_success: 登录成功
        - login_failed: 登录失败
        - logout: 登出
        - token_refresh: Token刷新
        - token_revoked: Token撤销
        - mfa_challenge: MFA挑战
        - mfa_success: MFA成功
        - mfa_failed: MFA失败
        - password_change: 密码修改
        - account_locked: 账户锁定
        """
        event = {
            'event_id': str(uuid.uuid4()),
            'event_type': event_type,
            'user_id': user_id,
            'timestamp': datetime.utcnow().isoformat(),
            'ip_address': details.get('ip_address'),
            'user_agent': details.get('user_agent'),
            'session_id': details.get('session_id'),
            'client_type': details.get('client_type'),
            'success': details.get('success', True),
            'error_code': details.get('error_code'),
            'error_message': details.get('error_message'),
            'additional_data': details.get('additional_data', {})
        }
        
        # 记录到日志
        self.logger.info(f"Authentication event: {event_type}", extra=event)
        
        # 存储到事件存储
        self.event_store.store_event(event)
        
        # 检查是否需要告警
        self._check_alert_conditions(event)
        
    def _check_alert_conditions(self, event):
        """检查告警条件"""
        # 连续登录失败告警
        if event['event_type'] == 'login_failed':
            recent_failures = self._count_recent_failures(event['user_id'], minutes=10)
            if recent_failures >= 3:
                self._send_alert('multiple_login_failures', event)
                
        # 异常登录告警
        if event['event_type'] == 'login_success':
            if self._is_suspicious_login(event):
                self._send_alert('suspicious_login', event)
                
        # MFA失败告警
        if event['event_type'] == 'mfa_failed':
            recent_mfa_failures = self._count_recent_mfa_failures(event['user_id'], minutes=5)
            if recent_mfa_failures >= 3:
                self._send_alert('multiple_mfa_failures', event)
```

### 7.2 权限审计

#### 权限变更记录
```python
class PermissionAuditor:
    def __init__(self, logger, event_store):
        self.logger = logger
        self.event_store = event_store
        
    def log_permission_change(self, change_type, target_user_id, operator_id, changes):
        """
        记录权限变更
        
        变更类型：
        - role_assigned: 角色分配
        - role_revoked: 角色撤销
        - permission_granted: 权限授予
        - permission_revoked: 权限撤销
        - role_created: 角色创建
        - role_modified: 角色修改
        - role_deleted: 角色删除
        """
        event = {
            'event_id': str(uuid.uuid4()),
            'event_type': 'permission_change',
            'change_type': change_type,
            'target_user_id': target_user_id,
            'operator_id': operator_id,
            'timestamp': datetime.utcnow().isoformat(),
            'changes': changes,
            'before_state': changes.get('before'),
            'after_state': changes.get('after')
        }
        
        self.logger.info(f"Permission change: {change_type}", extra=event)
        self.event_store.store_event(event)
        
    def log_permission_check(self, user_id, resource, action, result, context=None):
        """
        记录权限检查
        
        用于审计和分析权限使用情况
        """
        event = {
            'event_id': str(uuid.uuid4()),
            'event_type': 'permission_check',
            'user_id': user_id,
            'resource': resource,
            'action': action,
            'result': result,
            'timestamp': datetime.utcnow().isoformat(),
            'context': context or {}
        }
        
        # 只记录拒绝的权限检查和敏感操作
        if not result or self._is_sensitive_operation(resource, action):
            self.logger.info(f"Permission check: {result}", extra=event)
            self.event_store.store_event(event)
```

## 8. 配置和部署

### 8.1 认证配置

#### 配置文件结构
```yaml
# auth_config.yaml
authentication:
  # JWT配置
  jwt:
    algorithm: "HS256"
    secret_key: "${JWT_SECRET_KEY}"
    access_token_expires: 7200      # 2小时
    refresh_token_expires: 604800   # 7天
    issuer: "exam-system"
    audience: "api-gateway"
    
  # 会话配置
  session:
    max_sessions_per_user: 3
    session_timeout: 604800         # 7天
    activity_timeout: 3600          # 1小时无活动超时
    
  # 多因素认证
  mfa:
    enabled: true
    default_method: "sms"
    methods:
      sms:
        enabled: true
        provider: "aliyun"
        template_id: "SMS_123456"
      email:
        enabled: true
        smtp_server: "smtp.example.com"
        smtp_port: 587
      totp:
        enabled: false
        issuer: "ExamSystem"
        
  # 密码策略
  password:
    min_length: 8
    max_length: 128
    require_uppercase: true
    require_lowercase: true
    require_numbers: true
    require_symbols: true
    history_count: 5                # 不能重复最近5个密码
    expire_days: 90                 # 90天过期
    
  # 登录限制
  login_limits:
    max_attempts_per_user: 5
    max_attempts_per_ip: 10
    lockout_duration: 1800          # 30分钟
    attempt_window: 3600            # 1小时窗口
    
  # 安全策略
  security:
    require_https: true
    secure_cookies: true
    same_site_cookies: "Strict"
    csrf_protection: true
    fingerprint_validation: true
    
authorization:
  # 权限模型
  model: "rbac"
  
  # 默认权限
  default_permissions:
    student:
      - "user:profile:read:own"
      - "user:profile:write:own"
      - "exam:session:join:assigned"
      - "grade:result:read:own"
      
  # 权限缓存
  cache:
    enabled: true
    ttl: 300                        # 5分钟
    
  # 权限检查
  enforcement:
    strict_mode: true
    log_denials: true
    
audit:
  # 审计日志
  logging:
    enabled: true
    level: "INFO"
    file: "logs/auth_audit.log"
    max_size: "100MB"
    backup_count: 10
    
  # 事件存储
  event_store:
    type: "database"                # database, elasticsearch, file
    retention_days: 365
    
  # 告警配置
  alerts:
    enabled: true
    channels: ["email", "webhook"]
    thresholds:
      failed_logins: 5
      suspicious_logins: 1
      permission_denials: 10
```

### 8.2 部署配置

#### Docker部署
```dockerfile
# Dockerfile.auth
FROM python:3.9-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY auth/ ./auth/
COPY config/ ./config/
COPY app.py .

# 创建日志目录
RUN mkdir -p logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV FLASK_ENV=production

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# 启动命令
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

#### Kubernetes部署
```yaml
# auth-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  labels:
    app: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: exam-system/auth-service:latest
        ports:
        - containerPort: 5000
        env:
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: jwt-secret
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: database-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 5000
  type: ClusterIP
```

---

## 总结

本认证授权机制设计文档为职业技能等级考试系统提供了完整的安全框架，包括：

### 🔐 **核心特性**
1. **JWT无状态认证**：高性能、可扩展的Token机制
2. **RBAC权限模型**：细粒度的资源级权限控制
3. **多因素认证**：增强的安全验证机制
4. **会话管理**：完整的会话生命周期管理
5. **安全防护**：防暴力破解、异常检测等安全机制

### 🛡️ **安全保障**
- **多层防护**：认证、授权、审计三重保障
- **实时监控**：异常行为检测和告警
- **完整审计**：全面的操作日志和审计追踪
- **灵活配置**：支持多种部署环境和安全策略

### 📋 **实施建议**
1. **分阶段实施**：先实现基础认证，再逐步增加高级功能
2. **安全优先**：在开发过程中始终将安全性放在首位
3. **性能优化**：合理使用缓存和异步处理提升性能
4. **监控告警**：建立完善的监控和告警机制
5. **定期审查**：定期审查和更新安全策略

### 🔄 **后续优化**
- **零信任架构**：逐步向零信任安全模型演进
- **AI安全**：引入机器学习进行异常检测
- **联邦认证**：支持SAML、OAuth2.0等标准协议
- **生物识别**：集成指纹、人脸识别等生物特征认证

本设计文档为系统的安全架构奠定了坚实基础，确保考试系统的数据安全和用户隐私保护。