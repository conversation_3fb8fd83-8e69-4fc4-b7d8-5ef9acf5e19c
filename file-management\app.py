# -*- coding: utf-8 -*-
"""
成果文件管理模块 - 核心服务
"""

import hashlib
from flask import Flask, jsonify, request
from flask_cors import CORS
from datetime import datetime

# --- In-Memory Database ---
# This will be replaced by a proper file storage system (e.g., local storage, S3)
db = {
    "files": {
        301: {
            "id": 301,
            "filename": "student_5_exam_2_promo.mp4",
            "exam_id": 2,
            "student_id": 5,
            "size_bytes": 15728640, # 15 MB
            "upload_timestamp": "2024-09-05T16:05:10Z",
            "storage_path": "/data/exam_files/student_5/exam_2_promo.mp4",
            "hash_sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
        }
    },
    "next_file_id": 302
}

def create_app():
    """Application factory to create and configure the Flask app."""
    app = Flask(__name__)
    CORS(app)

    @app.route('/', methods=['GET'])
    def index():
        return jsonify({'service': 'File Management API', 'status': 'running'})

    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({'status': 'healthy', 'service': 'file_management'})

    @app.route('/api/v1/files/upload', methods=['POST'])
    def upload_file():
        """(Mocked) Upload a new file and create its metadata record."""
        if 'filename' not in request.json or 'exam_id' not in request.json:
            return jsonify({'error': 'Missing required fields'}), 400

        new_file_id = db['next_file_id']
        data = request.json

        # Simulate hashing the file content
        mock_content = (data['filename'] + str(datetime.utcnow())).encode('utf-8')
        file_hash = hashlib.sha256(mock_content).hexdigest()

        new_file_meta = {
            "id": new_file_id,
            "filename": data['filename'],
            "exam_id": data['exam_id'],
            "student_id": data['student_id'],
            "size_bytes": data.get('size_bytes', 0),
            "upload_timestamp": datetime.utcnow().isoformat() + 'Z',
            "storage_path": f"/data/exam_files/student_{data['student_id']}/{data['filename']}",
            "hash_sha256": file_hash
        }

        db['files'][new_file_id] = new_file_meta
        db['next_file_id'] += 1

        return jsonify(new_file_meta), 201

    @app.route('/api/v1/files/<int:file_id>', methods=['GET'])
    def get_file_metadata(file_id):
        """Get metadata for a specific file."""
        file_meta = db['files'].get(file_id)
        if file_meta:
            return jsonify(file_meta)
        return jsonify({'error': 'File not found'}), 404

    @app.route('/api/v1/files/exam/<int:exam_id>', methods=['GET'])
    def get_files_for_exam(exam_id):
        """Get all file metadata for a specific exam."""
        exam_files = [meta for meta in db['files'].values() if meta['exam_id'] == exam_id]
        return jsonify(exam_files)

    @app.route('/api/v1/files/<int:file_id>/download', methods=['GET'])
    def download_file(file_id):
        """(Mocked) Download a file, with permission checks."""
        # In a real app, the API gateway would add user info to headers
        user_role = request.headers.get('X-User-Role', 'student') # Default to 'student' if not provided

        file_meta = db['files'].get(file_id)
        if not file_meta:
            return jsonify({'error': 'File not found'}), 404

        # Mock permission check: only admins and graders can download
        if user_role not in ['admin', 'grader', 'internal_supervisor']:
            return jsonify({
                'error': 'Permission denied',
                'message': f'Your role ("{user_role}") is not authorized to download this file.'
            }), 403

        # In a real app, this would return a file stream.
        # Here, we just return a success message with the file path.
        return jsonify({
            'message': 'Download authorized',
            'file': file_meta['filename'],
            'path': file_meta['storage_path']
        })

    return app
