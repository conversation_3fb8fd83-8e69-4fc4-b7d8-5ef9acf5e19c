# -*- coding: utf-8 -*-
"""
考试编排模块 - 数据库模型
定义考试、参与者、考试状态等数据表结构
"""

import sqlite3
import json
from datetime import datetime, timezone
from enum import Enum
from typing import List, Dict, Optional, Any
import os
from transaction_manager import TransactionManager, transaction

class ExamStatus(Enum):
    """考试状态枚举"""
    DRAFT = "draft"          # 草稿
    PUBLISHED = "published"  # 已发布
    IN_PROGRESS = "in_progress"  # 进行中
    COMPLETED = "completed"  # 已结束
    CANCELLED = "cancelled"  # 已取消

class ExamType(Enum):
    """考试类型枚举"""
    THEORY = "theory"        # 理论考试
    PRACTICAL = "practical"  # 实操考试
    MIXED = "mixed"          # 混合考试

class ParticipantRole(Enum):
    """参与者角色枚举"""
    STUDENT = "student"      # 考生
    GRADER = "grader"        # 考评员
    SUPERVISOR = "supervisor" # 督导员
    ADMIN = "admin"          # 管理员

class DatabaseManager:
    """数据库管理器
    
    负责数据库连接、表创建、数据操作等功能
    """
    
    def __init__(self, db_path: str = "exam_management.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self) -> sqlite3.Connection:
        """
        获取数据库连接
        
        Returns:
            sqlite3.Connection: 数据库连接对象
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以通过列名访问
        return conn
    
    def init_database(self):
        """
        初始化数据库，创建所有必要的表
        """
        with self.get_connection() as conn:
            # 创建考试表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS exams (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    exam_type TEXT NOT NULL DEFAULT 'theory',
                    status TEXT NOT NULL DEFAULT 'draft',
                    start_time TEXT,
                    end_time TEXT,
                    duration_minutes INTEGER DEFAULT 120,
                    total_score REAL DEFAULT 100.0,
                    passing_score REAL DEFAULT 60.0,
                    question_bank_ids TEXT,  -- JSON数组
                    practical_task_ids TEXT, -- JSON数组
                    instructions TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    created_by INTEGER,
                    updated_by INTEGER
                )
            """)
            
            # 创建考试参与者表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS exam_participants (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exam_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    role TEXT NOT NULL DEFAULT 'student',
                    status TEXT NOT NULL DEFAULT 'registered',
                    seat_number TEXT,
                    registered_at TEXT NOT NULL,
                    FOREIGN KEY (exam_id) REFERENCES exams (id) ON DELETE CASCADE,
                    UNIQUE(exam_id, user_id)
                )
            """)
            
            # 创建考试结果表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS exam_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exam_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    score REAL,
                    status TEXT NOT NULL DEFAULT 'not_started',
                    start_time TEXT,
                    submit_time TEXT,
                    answers TEXT,  -- JSON格式存储答案
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (exam_id) REFERENCES exams (id) ON DELETE CASCADE,
                    UNIQUE(exam_id, user_id)
                )
            """)
            
            # 创建考试日志表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS exam_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exam_id INTEGER NOT NULL,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (exam_id) REFERENCES exams (id) ON DELETE CASCADE
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_exams_status ON exams(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_exams_start_time ON exams(start_time)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_participants_exam_id ON exam_participants(exam_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_participants_user_id ON exam_participants(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_results_exam_id ON exam_results(exam_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_results_user_id ON exam_results(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_logs_exam_id ON exam_logs(exam_id)")
            
            conn.commit()
    
    def get_utc_now_iso(self) -> str:
        """
        获取当前UTC时间的ISO格式字符串
        
        Returns:
            str: ISO格式的时间字符串
        """
        return datetime.now(timezone.utc).isoformat()

class ExamModel:
    """考试数据模型
    
    提供考试相关的数据库操作方法
    """
    
    def __init__(self, db_manager: DatabaseManager, transaction_manager: TransactionManager = None):
        """
        初始化考试模型
        
        Args:
            db_manager: 数据库管理器实例
            transaction_manager: 事务管理器实例
        """
        self.db = db_manager
        self.transaction_manager = transaction_manager
    
    def create_exam(self, exam_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建新考试
        
        Args:
            exam_data: 考试数据字典
            
        Returns:
            Dict[str, Any]: 创建的考试数据
        """
        now = self.db.get_utc_now_iso()
        
        if self.transaction_manager:
            with transaction(self.transaction_manager):
                with self.db.get_connection() as conn:
                    cursor = conn.execute("""
                        INSERT INTO exams (
                            name, description, exam_type, status, start_time, end_time,
                            duration_minutes, total_score, passing_score, question_bank_ids,
                            practical_task_ids, instructions, created_at, updated_at,
                            created_by, updated_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        exam_data['name'],
                        exam_data.get('description', ''),
                        exam_data.get('exam_type', ExamType.THEORY.value),
                        exam_data.get('status', ExamStatus.DRAFT.value),
                        exam_data.get('start_time'),
                        exam_data.get('end_time'),
                        exam_data.get('duration_minutes', 120),
                        exam_data.get('total_score', 100.0),
                        exam_data.get('passing_score', 60.0),
                        json.dumps(exam_data.get('question_bank_ids', [])),
                        json.dumps(exam_data.get('practical_task_ids', [])),
                        exam_data.get('instructions', ''),
                        now, now,
                        exam_data.get('created_by'),
                        exam_data.get('updated_by')
                    ))
                    
                    exam_id = cursor.lastrowid
                    conn.commit()
        else:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO exams (
                        name, description, exam_type, status, start_time, end_time,
                        duration_minutes, total_score, passing_score, question_bank_ids,
                        practical_task_ids, instructions, created_at, updated_at,
                        created_by, updated_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    exam_data['name'],
                    exam_data.get('description', ''),
                    exam_data.get('exam_type', ExamType.THEORY.value),
                    exam_data.get('status', ExamStatus.DRAFT.value),
                    exam_data.get('start_time'),
                    exam_data.get('end_time'),
                    exam_data.get('duration_minutes', 120),
                    exam_data.get('total_score', 100.0),
                    exam_data.get('passing_score', 60.0),
                    json.dumps(exam_data.get('question_bank_ids', [])),
                    json.dumps(exam_data.get('practical_task_ids', [])),
                    exam_data.get('instructions', ''),
                    now, now,
                    exam_data.get('created_by'),
                    exam_data.get('updated_by')
                ))
                
                exam_id = cursor.lastrowid
                conn.commit()
            
            return self.get_exam_by_id(exam_id)
    
    def get_exam_by_id(self, exam_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取考试信息
        
        Args:
            exam_id: 考试ID
            
        Returns:
            Optional[Dict[str, Any]]: 考试数据或None
        """
        with self.db.get_connection() as conn:
            cursor = conn.execute("SELECT * FROM exams WHERE id = ?", (exam_id,))
            row = cursor.fetchone()
            
            if row:
                exam = dict(row)
                # 解析JSON字段
                exam['question_bank_ids'] = json.loads(exam['question_bank_ids'] or '[]')
                exam['practical_task_ids'] = json.loads(exam['practical_task_ids'] or '[]')
                return exam
            
            return None
    
    def get_exams(self, filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        获取考试列表
        
        Args:
            filters: 过滤条件字典
            
        Returns:
            List[Dict[str, Any]]: 考试列表
        """
        query = "SELECT * FROM exams"
        params = []
        
        if filters:
            conditions = []
            if 'status' in filters:
                conditions.append("status = ?")
                params.append(filters['status'])
            if 'exam_type' in filters:
                conditions.append("exam_type = ?")
                params.append(filters['exam_type'])
            if 'start_date' in filters:
                conditions.append("DATE(start_time) >= ?")
                params.append(filters['start_date'])
            if 'end_date' in filters:
                conditions.append("DATE(start_time) <= ?")
                params.append(filters['end_date'])
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY created_at DESC"
        
        with self.db.get_connection() as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
            
            exams = []
            for row in rows:
                exam = dict(row)
                # 解析JSON字段
                exam['question_bank_ids'] = json.loads(exam['question_bank_ids'] or '[]')
                exam['practical_task_ids'] = json.loads(exam['practical_task_ids'] or '[]')
                exams.append(exam)
            
            return exams
    
    def update_exam(self, exam_id: int, exam_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        更新考试信息
        
        Args:
            exam_id: 考试ID
            exam_data: 更新的考试数据
            
        Returns:
            Optional[Dict[str, Any]]: 更新后的考试数据或None
        """
        if not self.get_exam_by_id(exam_id):
            return None
        
        now = self.db.get_utc_now_iso()
        
        # 构建更新字段
        update_fields = []
        params = []
        
        for field in ['name', 'description', 'exam_type', 'status', 'start_time', 'end_time',
                     'duration_minutes', 'total_score', 'passing_score', 'instructions', 'updated_by']:
            if field in exam_data:
                update_fields.append(f"{field} = ?")
                params.append(exam_data[field])
        
        # 处理JSON字段
        if 'question_bank_ids' in exam_data:
            update_fields.append("question_bank_ids = ?")
            params.append(json.dumps(exam_data['question_bank_ids']))
        
        if 'practical_task_ids' in exam_data:
            update_fields.append("practical_task_ids = ?")
            params.append(json.dumps(exam_data['practical_task_ids']))
        
        update_fields.append("updated_at = ?")
        params.append(now)
        params.append(exam_id)
        
        if self.transaction_manager:
            with transaction(self.transaction_manager):
                with self.db.get_connection() as conn:
                    conn.execute(
                        f"UPDATE exams SET {', '.join(update_fields)} WHERE id = ?",
                        params
                    )
                    conn.commit()
        else:
            with self.db.get_connection() as conn:
                conn.execute(
                    f"UPDATE exams SET {', '.join(update_fields)} WHERE id = ?",
                    params
                )
                conn.commit()
        
        return self.get_exam_by_id(exam_id)
    
    def update_exam_status(self, exam_id: int, new_status: str) -> bool:
        """
        更新考试状态
        
        Args:
            exam_id: 考试ID
            new_status: 新状态
            
        Returns:
            bool: 更新是否成功
        """
        now = self.db.get_utc_now_iso()
        
        if self.transaction_manager:
            with transaction(self.transaction_manager):
                with self.db.get_connection() as conn:
                    cursor = conn.execute(
                        "UPDATE exams SET status = ?, updated_at = ? WHERE id = ?",
                        (new_status, now, exam_id)
                    )
                    conn.commit()
                    return cursor.rowcount > 0
        else:
            with self.db.get_connection() as conn:
                cursor = conn.execute(
                    "UPDATE exams SET status = ?, updated_at = ? WHERE id = ?",
                    (new_status, now, exam_id)
                )
                conn.commit()
                return cursor.rowcount > 0
    
    def delete_exam(self, exam_id: int) -> bool:
        """
        删除考试
        
        Args:
            exam_id: 考试ID
            
        Returns:
            bool: 删除是否成功
        """
        if self.transaction_manager:
            with transaction(self.transaction_manager):
                with self.db.get_connection() as conn:
                    cursor = conn.execute("DELETE FROM exams WHERE id = ?", (exam_id,))
                    conn.commit()
                    return cursor.rowcount > 0
        else:
            with self.db.get_connection() as conn:
                cursor = conn.execute("DELETE FROM exams WHERE id = ?", (exam_id,))
                conn.commit()
                return cursor.rowcount > 0
    
    def check_time_conflict(self, start_time: str, end_time: str, exclude_exam_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        检查时间冲突
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            exclude_exam_id: 排除的考试ID（用于更新时排除自己）
            
        Returns:
            List[Dict[str, Any]]: 冲突的考试列表
        """
        query = """
            SELECT * FROM exams 
            WHERE status IN ('published', 'in_progress')
            AND (
                (start_time <= ? AND end_time > ?) OR
                (start_time < ? AND end_time >= ?) OR
                (start_time >= ? AND end_time <= ?)
            )
        """
        params = [start_time, start_time, end_time, end_time, start_time, end_time]
        
        if exclude_exam_id:
            query += " AND id != ?"
            params.append(exclude_exam_id)
        
        with self.db.get_connection() as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
            
            return [dict(row) for row in rows]

class ParticipantModel:
    """参与者数据模型
    
    提供考试参与者相关的数据库操作方法
    """
    
    def __init__(self, db_manager: DatabaseManager, transaction_manager: TransactionManager = None):
        """
        初始化参与者模型
        
        Args:
            db_manager: 数据库管理器实例
            transaction_manager: 事务管理器实例
        """
        self.db = db_manager
        self.transaction_manager = transaction_manager
    
    def add_participant(self, exam_id: int, user_id: int, role: str = ParticipantRole.STUDENT.value, seat_number: str = None) -> Dict[str, Any]:
        """
        添加考试参与者
        
        Args:
            exam_id: 考试ID
            user_id: 用户ID
            role: 参与者角色
            seat_number: 座位号
            
        Returns:
            Dict[str, Any]: 参与者数据
        """
        now = self.db.get_utc_now_iso()
        
        if self.transaction_manager:
            with transaction(self.transaction_manager):
                with self.db.get_connection() as conn:
                    cursor = conn.execute("""
                        INSERT OR REPLACE INTO exam_participants 
                        (exam_id, user_id, role, seat_number, registered_at)
                        VALUES (?, ?, ?, ?, ?)
                    """, (exam_id, user_id, role, seat_number, now))
                    
                    participant_id = cursor.lastrowid
                    conn.commit()
                    
                    return self.get_participant_by_id(participant_id)
        else:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT OR REPLACE INTO exam_participants 
                    (exam_id, user_id, role, seat_number, registered_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (exam_id, user_id, role, seat_number, now))
                
                participant_id = cursor.lastrowid
                conn.commit()
                
                return self.get_participant_by_id(participant_id)
    
    def get_participant_by_id(self, participant_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取参与者信息
        
        Args:
            participant_id: 参与者ID
            
        Returns:
            Optional[Dict[str, Any]]: 参与者数据或None
        """
        with self.db.get_connection() as conn:
            cursor = conn.execute("SELECT * FROM exam_participants WHERE id = ?", (participant_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_participant_by_user_id(self, exam_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """
        根据用户ID获取考试参与者信息
        
        Args:
            exam_id: 考试ID
            user_id: 用户ID
            
        Returns:
            Optional[Dict[str, Any]]: 参与者数据或None
        """
        with self.db.get_connection() as conn:
            cursor = conn.execute(
                "SELECT * FROM exam_participants WHERE exam_id = ? AND user_id = ?", 
                (exam_id, user_id)
            )
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_exam_participants(self, exam_id: int, role: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取考试参与者列表
        
        Args:
            exam_id: 考试ID
            role: 参与者角色过滤
            
        Returns:
            List[Dict[str, Any]]: 参与者列表
        """
        query = "SELECT * FROM exam_participants WHERE exam_id = ?"
        params = [exam_id]
        
        if role:
            query += " AND role = ?"
            params.append(role)
        
        query += " ORDER BY registered_at"
        
        with self.db.get_connection() as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    def remove_participant(self, exam_id: int, user_id: int) -> bool:
        """
        移除考试参与者
        
        Args:
            exam_id: 考试ID
            user_id: 用户ID
            
        Returns:
            bool: 移除是否成功
        """
        if self.transaction_manager:
            with transaction(self.transaction_manager):
                with self.db.get_connection() as conn:
                    cursor = conn.execute(
                        "DELETE FROM exam_participants WHERE exam_id = ? AND user_id = ?",
                        (exam_id, user_id)
                    )
                    conn.commit()
                    return cursor.rowcount > 0
        else:
            with self.db.get_connection() as conn:
                cursor = conn.execute(
                    "DELETE FROM exam_participants WHERE exam_id = ? AND user_id = ?",
                    (exam_id, user_id)
                )
                conn.commit()
                return cursor.rowcount > 0

class ExamLogModel:
    """考试日志数据模型
    
    提供考试日志相关的数据库操作方法
    """
    
    def __init__(self, db_manager: DatabaseManager, transaction_manager: TransactionManager = None):
        """
        初始化考试日志模型
        
        Args:
            db_manager: 数据库管理器实例
            transaction_manager: 事务管理器实例
        """
        self.db = db_manager
        self.transaction_manager = transaction_manager
    
    def add_log(self, exam_id: int, action: str, user_id: Optional[int] = None, 
                details: Optional[str] = None, ip_address: Optional[str] = None) -> int:
        """
        添加考试日志
        
        Args:
            exam_id: 考试ID
            action: 操作动作
            user_id: 用户ID
            details: 详细信息
            ip_address: IP地址
            
        Returns:
            int: 日志ID
        """
        now = self.db.get_utc_now_iso()
        
        if self.transaction_manager:
            with transaction(self.transaction_manager):
                with self.db.get_connection() as conn:
                    cursor = conn.execute("""
                        INSERT INTO exam_logs (exam_id, user_id, action, details, ip_address, created_at)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (exam_id, user_id, action, details, ip_address, now))
                    
                    log_id = cursor.lastrowid
                    conn.commit()
                    return log_id
        else:
            with self.db.get_connection() as conn:
                cursor = conn.execute("""
                    INSERT INTO exam_logs (exam_id, user_id, action, details, ip_address, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (exam_id, user_id, action, details, ip_address, now))
                
                log_id = cursor.lastrowid
                conn.commit()
                return log_id
    
    def get_exam_logs(self, exam_id: int, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取考试日志
        
        Args:
            exam_id: 考试ID
            limit: 限制数量
            
        Returns:
            List[Dict[str, Any]]: 日志列表
        """
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM exam_logs 
                WHERE exam_id = ? 
                ORDER BY created_at DESC 
                LIMIT ?
            """, (exam_id, limit))
            
            rows = cursor.fetchall()
            return [dict(row) for row in rows]