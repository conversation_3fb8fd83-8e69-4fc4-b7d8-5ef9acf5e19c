# ================================================================
# 职业技能等级考试系统 - 模块配置管理文件
# 版本: 2.0.0
# 更新日期: 2024-01-15
# 说明: 统一管理所有模块的配置、端口分配、依赖关系和健康检查
# ================================================================

# ==================== 模块配置概览 ====================
modules_overview:
  total_modules: 8
  active_modules: 8
  api_gateway_port: 8080
  main_system_port: 8000
  port_range: "5000-5020"
  discovery_enabled: true
  health_check_enabled: true
  auto_restart: true

# ==================== 核心模块配置 ====================
modules:
  # ==================== API网关模块 ====================
  api_gateway:
    name: "API网关"
    description: "统一API入口，路由分发和负载均衡"
    type: "gateway"
    priority: 1
    
    # 服务配置
    service:
      enabled: true
      host: "127.0.0.1"
      port: 8080
      path: "api-gateway"
      main_file: "app.py"
      
    # 启动配置
    startup:
      auto_start: true
      start_delay: 0
      timeout: 30
      retry_count: 3
      
    # 健康检查
    health_check:
      enabled: true
      endpoint: "/health"
      interval: 30
      timeout: 10
      
    # 依赖关系
    dependencies:
      required: []
      optional: ["user_management", "question_bank", "exam_score_reporting"]
      
    # 环境变量
    environment:
      FLASK_ENV: "development"
      FLASK_DEBUG: "true"
      API_GATEWAY_PORT: "8080"
      
    # 路由配置
    routes:
      - path: "/api/users"
        target: "user_management"
        methods: ["GET", "POST", "PUT", "DELETE"]
      - path: "/api/questions"
        target: "question_bank"
        methods: ["GET", "POST", "PUT", "DELETE"]
      - path: "/api/exams"
        target: "exam_score_reporting"
        methods: ["GET", "POST", "PUT", "DELETE"]
      - path: "/api/files"
        target: "file_project_manager"
        methods: ["GET", "POST", "PUT", "DELETE"]

  # ==================== 用户管理模块 ====================
  user_management:
    name: "用户管理模块"
    description: "用户注册、登录、权限管理"
    type: "service"
    priority: 2
    
    # 服务配置
    service:
      enabled: true
      host: "127.0.0.1"
      port: 5001
      path: "user-management"
      main_file: "app.py"
      
    # 启动配置
    startup:
      auto_start: true
      start_delay: 2
      timeout: 30
      retry_count: 3
      
    # 健康检查
    health_check:
      enabled: true
      endpoint: "/health"
      interval: 30
      timeout: 10
      
    # 依赖关系
    dependencies:
      required: ["database"]
      optional: ["cache", "email"]
      
    # 环境变量
    environment:
      FLASK_ENV: "development"
      FLASK_DEBUG: "true"
      USER_MANAGEMENT_PORT: "5001"
      DATABASE_URL: "mysql://root:123456@127.0.0.1:3306/exam_system"
      
    # API端点
    api_endpoints:
      - "/api/users/register"
      - "/api/users/login"
      - "/api/users/logout"
      - "/api/users/profile"
      - "/api/users/permissions"

  # ==================== 题库管理模块 ====================
  question_bank:
    name: "题库管理模块"
    description: "题目管理、题库维护、试卷生成"
    type: "service"
    priority: 3
    
    # 服务配置
    service:
      enabled: true
      host: "127.0.0.1"
      port: 5002
      path: "question_bank"
      main_file: "simple_run.py"
      
    # 启动配置
    startup:
      auto_start: true
      start_delay: 3
      timeout: 30
      retry_count: 3
      
    # 健康检查
    health_check:
      enabled: true
      endpoint: "/health"
      interval: 30
      timeout: 10
      
    # 依赖关系
    dependencies:
      required: ["database", "user_management"]
      optional: ["file_storage"]
      
    # 环境变量
    environment:
      FLASK_ENV: "development"
      FLASK_DEBUG: "true"
      QUESTION_BANK_PORT: "5002"
      DATABASE_URL: "mysql://root:123456@127.0.0.1:3306/exam_system"
      
    # API端点
    api_endpoints:
      - "/api/questions/list"
      - "/api/questions/create"
      - "/api/questions/update"
      - "/api/questions/delete"
      - "/api/questions/import"
      - "/api/questions/export"

  # ==================== 考试成绩报告模块 ====================
  exam_score_reporting:
    name: "考试成绩报告模块"
    description: "考试管理、成绩统计、报告生成"
    type: "service"
    priority: 4
    
    # 服务配置
    service:
      enabled: true
      host: "127.0.0.1"
      port: 5008
      path: "exam_score_reporting_interface"
      main_file: "app.py"
      
    # 启动配置
    startup:
      auto_start: true
      start_delay: 4
      timeout: 30
      retry_count: 3
      
    # 健康检查
    health_check:
      enabled: true
      endpoint: "/health"
      interval: 30
      timeout: 10
      
    # 依赖关系
    dependencies:
      required: ["database", "user_management", "question_bank"]
      optional: ["file_storage", "email"]
      
    # 环境变量
    environment:
      FLASK_ENV: "development"
      FLASK_DEBUG: "true"
      EXAM_SCORE_PORT: "5008"
      DATABASE_URL: "mysql://root:123456@127.0.0.1:3306/exam_system"
      
    # API端点
    api_endpoints:
      - "/api/exams/create"
      - "/api/exams/start"
      - "/api/exams/submit"
      - "/api/exams/score"
      - "/api/reports/generate"

  # ==================== 文件项目管理模块 ====================
  file_project_manager:
    name: "文件项目管理模块"
    description: "文件上传、项目管理、文档处理"
    type: "service"
    priority: 5
    
    # 服务配置
    service:
      enabled: true
      host: "127.0.0.1"
      port: 5015
      path: "file-project-manager"
      main_file: "app.py"
      
    # 启动配置
    startup:
      auto_start: true
      start_delay: 5
      timeout: 30
      retry_count: 3
      
    # 健康检查
    health_check:
      enabled: true
      endpoint: "/health"
      interval: 30
      timeout: 10
      
    # 依赖关系
    dependencies:
      required: ["database", "user_management"]
      optional: ["file_storage"]
      
    # 环境变量
    environment:
      FLASK_ENV: "development"
      FLASK_DEBUG: "true"
      FILE_MANAGER_PORT: "5015"
      DATABASE_URL: "mysql://root:123456@127.0.0.1:3306/exam_system"
      
    # API端点
    api_endpoints:
      - "/api/files/upload"
      - "/api/files/download"
      - "/api/files/list"
      - "/api/projects/create"
      - "/api/projects/manage"



  # ==================== 主控台模块 ====================
  main_console:
    name: "主控台模块"
    description: "系统主界面、模块管理、系统监控"
    type: "frontend"
    priority: 7
    
    # 服务配置
    service:
      enabled: true
      host: "127.0.0.1"
      port: 8000
      path: "."
      main_file: "main.py"
      
    # 启动配置
    startup:
      auto_start: true
      start_delay: 1
      timeout: 30
      retry_count: 3
      
    # 健康检查
    health_check:
      enabled: true
      endpoint: "/health"
      interval: 30
      timeout: 10
      
    # 依赖关系
    dependencies:
      required: ["api_gateway"]
      optional: ["user_management", "question_bank", "exam_score_reporting"]
      
    # 环境变量
    environment:
      FLASK_ENV: "development"
      FLASK_DEBUG: "true"
      MAIN_CONSOLE_PORT: "8000"
      API_GATEWAY_URL: "http://127.0.0.1:5008"

  # ==================== 系统监控模块 ====================
  system_monitor:
    name: "系统监控模块"
    description: "系统性能监控、日志管理、健康检查"
    type: "utility"
    priority: 8
    
    # 服务配置
    service:
      enabled: true
      host: "127.0.0.1"
      port: 5005
      path: "monitor"
      main_file: "monitor.py"
      
    # 启动配置
    startup:
      auto_start: true
      start_delay: 10
      timeout: 30
      retry_count: 3
      
    # 健康检查
    health_check:
      enabled: true
      endpoint: "/health"
      interval: 15
      timeout: 5
      
    # 依赖关系
    dependencies:
      required: []
      optional: ["api_gateway", "main_console"]
      
    # 环境变量
    environment:
      FLASK_ENV: "development"
      FLASK_DEBUG: "true"
      MONITOR_PORT: "5005"

# ==================== 模块发现配置 ====================
module_discovery:
  # 自动发现设置
  auto_discovery:
    enabled: true
    scan_interval: 60  # 秒
    scan_directories:
      - "."
      - "api-gateway"
      - "user-management"
      - "question_bank"
      - "exam_score_reporting_interface"
      - "file-project-manager"
      - "file_project_management"
      
  # 模块识别规则
  identification_rules:
    # Python Flask应用识别
    flask_app:
      patterns:
        - "app.py"
        - "main.py"
        - "run.py"
      required_imports:
        - "flask"
        - "Flask"
        
    # 配置文件识别
    config_files:
      patterns:
        - "config.py"
        - "settings.py"
        - "*.yaml"
        - "*.yml"
        - "*.json"
        
    # 依赖文件识别
    dependency_files:
      patterns:
        - "requirements.txt"
        - "Pipfile"
        - "pyproject.toml"
        
  # 排除规则
  exclusion_rules:
    directories:
      - "__pycache__"
      - ".git"
      - ".vscode"
      - "node_modules"
      - "venv"
      - "env"
      - "logs"
      - "cache"
      - "backups"
    files:
      - "*.pyc"
      - "*.pyo"
      - "*.log"
      - "*.tmp"

# ==================== 启动顺序配置 ====================
startup_sequence:
  # 启动阶段定义
  phases:
    # 阶段1：基础服务
    phase_1:
      name: "基础服务启动"
      modules:
        - "main_console"
      parallel: false
      timeout: 30
      
    # 阶段2：API网关
    phase_2:
      name: "API网关启动"
      modules:
        - "api_gateway"
      parallel: false
      timeout: 30
      wait_for_previous: true
      
    # 阶段3：核心业务模块
    phase_3:
      name: "核心业务模块启动"
      modules:
        - "user_management"
        - "question_bank"
        - "exam_score_reporting"
        - "file_project_manager"
      parallel: true
      timeout: 45
      wait_for_previous: true
      
    # 阶段4：辅助模块
    phase_4:
      name: "辅助模块启动"
      modules:
        - "system_monitor"
      parallel: false
      timeout: 30
      wait_for_previous: true
      
  # 启动策略
  strategy:
    max_parallel: 4
    retry_failed: true
    continue_on_failure: true
    health_check_before_next: true

# ==================== 负载均衡配置 ====================
load_balancing:
  # 负载均衡策略
  strategy: "round_robin"  # round_robin, least_connections, weighted
  
  # 健康检查配置
  health_check:
    enabled: true
    interval: 30
    timeout: 10
    failure_threshold: 3
    success_threshold: 2
    
  # 故障转移
  failover:
    enabled: true
    max_retries: 3
    retry_delay: 5
    
  # 权重配置（weighted策略使用）
  weights:
    user_management: 1.0
    question_bank: 1.0
    exam_score_reporting: 1.5
    file_project_manager: 0.8

# ==================== 监控和告警配置 ====================
monitoring:
  # 性能监控
  performance:
    enabled: true
    metrics:
      - "cpu_usage"
      - "memory_usage"
      - "response_time"
      - "request_count"
      - "error_rate"
      
  # 告警配置
  alerts:
    enabled: true
    thresholds:
      cpu_usage: 80
      memory_usage: 85
      response_time: 2000  # 毫秒
      error_rate: 5  # 百分比
      
  # 日志聚合
  log_aggregation:
    enabled: true
    level: "INFO"
    format: "json"
    
# ==================== 安全配置 ====================
security:
  # 模块间通信安全
  inter_module_security:
    enabled: true
    authentication: "jwt"
    encryption: false
    
  # API安全
  api_security:
    rate_limiting: true
    cors_enabled: true
    csrf_protection: true
    
  # 访问控制
  access_control:
    enabled: true
    default_policy: "deny"
    
# ==================== 开发配置 ====================
development:
  # 热重载
  hot_reload:
    enabled: true
    watch_files: true
    auto_restart: true
    
  # 调试模式
  debug:
    enabled: true
    verbose_logging: true
    
  # 测试配置
  testing:
    mock_external_services: true
    test_database: "sqlite:///test.db"
    
# ==================== 生产配置 ====================
production:
  # 性能优化
  performance:
    worker_processes: 4
    worker_threads: 2
    
  # 安全增强
  security:
    force_https: true
    hide_server_info: true
    
  # 监控增强
  monitoring:
    detailed_metrics: true
    log_retention: 90  # 天