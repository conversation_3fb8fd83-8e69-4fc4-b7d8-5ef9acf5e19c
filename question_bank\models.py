from sqlalchemy import create_engine, Column, String, Text, CHAR, DateTime, Enum as SAE<PERSON>, Integer, ForeignKey, Float
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.dialects.mysql import INTEGER # For MySQL specific integer types if needed
import datetime
import uuid

Base = declarative_base()

# Define ENUMs for codes if you prefer them over CHARs for better readability
# class QuestionType(SAEnum):
#     SINGLE_CHOICE = 'B'
#     MULTIPLE_CHOICE = 'G'
#     TRUE_FALSE = 'C'
#     FILL_IN_BLANK = 'T'
#     SHORT_ANSWER = 'D'
#     CALCULATION = 'U'
#     ESSAY = 'W'
#     CASE_ANALYSIS = 'E'
#     COMPOSITE = 'F'

# class DifficultyLevel(SAEnum):
#     VERY_EASY = '1'
#     EASY = '2'
#     MEDIUM = '3'
#     HARD = '4'
#     VERY_HARD = '5'

class Question(Base):
    __tablename__ = 'questions'

    id = Column(String(255), primary_key=True, comment="试题ID：题目唯一标识，格式为 'BWGL-3-LL-B-A-B-C-001-002'，共9段，以-分隔。第1-3段为题库名称代码，第4段为题型代码缩写，第5-7段为三级代码，第8段为认定点代码，第9段为顺序号")
    seq_num = Column(String(50), comment="对应Excel中的序号")
    assessment_code = Column(String(100), comment="认定点代码/考核点代码")
    
    question_type_code = Column(String(20), nullable=False, comment="题型代码: B（单选题）, G（多选题）, C（判断题）, T（填空题）, D（简答题）, U（计算题）, W（论述题）, E（案例分析题）, F（综合题）")
    
    question_number = Column(String(50), comment="题号")
    question_stem = Column(Text, nullable=False, comment="试题题干")
    option_a = Column(Text, comment="试题选项A")
    option_b = Column(Text, comment="试题选项B")
    option_c = Column(Text, comment="试题选项C")
    option_d = Column(Text, comment="试题选项D")
    option_e = Column(Text, comment="试题选项E")
    image_location = Column(String(255), comment="【图】及位置，可存储图片路径或描述")
    correct_answer = Column(Text, nullable=False, comment="正确答案")
    
    difficulty_code = Column(String(20), nullable=False, comment="难度代码: 1（很简单）, 2（简单）, 3（中等）, 4（困难）, 5（很难）")
    
    consistency_code = Column(String(20), comment="一致性代码: 1（很低）, 2（低）, 3（中等）, 4（高）, 5（很高）")
    analysis = Column(Text, comment="解析")
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # 新增外键，关联到题库表
    question_bank_id = Column(String(36), ForeignKey('question_banks.id'), nullable=False, index=True)
    
    # 建立关系
    question_bank = relationship("QuestionBank", back_populates="questions")
    paper_questions = relationship("PaperQuestion", back_populates="question", cascade="all, delete-orphan")

    @classmethod
    def validate_id_format(cls, id_str):
        """验证ID字符串格式是否符合规范"""
        if not id_str or not isinstance(id_str, str):
            return False
        parts = id_str.split('-')
        return len(parts) == 9 and all(part for part in parts)

    def is_id_valid(self):
        """实例方法验证当前ID是否有效"""
        return self.validate_id_format(str(self.id))

    def __repr__(self):
        stem = getattr(self, 'question_stem', '')
        stem_preview = stem[:30] if stem else ''
        return f"<Question(id='{self.id}', question_stem_preview='{stem_preview}')>"

class Paper(Base):
    """试卷模型"""
    __tablename__ = 'papers'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="试卷ID")
    name = Column(String(255), nullable=False, comment="试卷名称")
    description = Column(Text, comment="试卷描述")
    total_score = Column(Float, default=100.0, comment="试卷总分")
    duration = Column(Integer, default=120, comment="考试时长（分钟）")
    difficulty_level = Column(String(50), comment="试卷难度等级")
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # 关联试卷题目
    paper_questions = relationship("PaperQuestion", back_populates="paper", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Paper(id='{self.id}', name='{self.name}')>"

class PaperQuestion(Base):
    """试卷题目关联模型"""
    __tablename__ = 'paper_questions'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="关联ID")
    paper_id = Column(String(36), ForeignKey('papers.id'), nullable=False, comment="试卷ID")
    question_id = Column(String(255), ForeignKey('questions.id'), nullable=False, comment="题目ID")
    question_order = Column(Integer, nullable=False, comment="题目在试卷中的顺序")
    score = Column(Float, nullable=False, comment="题目分值")
    section_name = Column(String(100), comment="题目所属章节/部分")
    
    # 关联关系
    paper = relationship("Paper", back_populates="paper_questions")
    question = relationship("Question", back_populates="paper_questions")
    
    def __repr__(self):
        return f"<PaperQuestion(paper_id='{self.paper_id}', question_id='{self.question_id}', order={self.question_order})>"

class QuestionGroup(Base):
    """题目分组模型（用于组题规则）"""
    __tablename__ = 'question_groups'
    
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="分组ID")
    name = Column(String(255), nullable=False, comment="分组名称")
    description = Column(Text, comment="分组描述")
    question_type_code = Column(String(20), comment="题型代码: B（单选题）, G（多选题）, C（判断题）, T（填空题）, D（简答题）, U（计算题）, W（论述题）, E（案例分析题）, F（综合题）")
    difficulty_code = Column(String(20), comment="难度代码: 1（很简单）, 2（简单）, 3（中等）, 4（困难）, 5（很难）")
    min_count = Column(Integer, default=1, comment="最少题目数量")
    max_count = Column(Integer, default=10, comment="最多题目数量")
    score_per_question = Column(Float, default=5.0, comment="每题分值")
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    
    def __repr__(self):
        return f"<QuestionGroup(id='{self.id}', name='{self.name}')>"

class QuestionBank(Base):
    """题库模型"""
    __tablename__ = 'question_banks'
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()), comment="题库ID")
    题库名称 = Column(String(255), unique=True, nullable=False, comment="题库名称")
    created_at = Column(DateTime, default=datetime.datetime.utcnow, comment="创建时间")

    # 新增反向关系
    questions = relationship("Question", back_populates="question_bank", cascade="all, delete-orphan")

    from sqlalchemy.ext.hybrid import hybrid_property

    @hybrid_property
    def name(self):
        """兼容旧代码的题库名称属性"""
        return self.题库名称

    @name.setter
    def name(self, value):
        """设置题库名称"""
        self.题库名称 = value

    @name.expression
    def name(cls):
        """支持SQL查询的表达式"""
        return cls.题库名称

    def __repr__(self):
        return f"<QuestionBank(id='{self.id}', 题库名称='{self.题库名称}')>"

# 示例：如何连接到MySQL数据库并创建表
# Replace with your actual database connection string
# DB_URL = "mysql+mysqlconnector://user:password@host/dbname"
# engine = create_engine(DB_URL, echo=True)

# To create tables in the database (run this once):
# Base.metadata.create_all(engine)

# --- You would also define other tables like Papers, PaperQuestions, Users here ---