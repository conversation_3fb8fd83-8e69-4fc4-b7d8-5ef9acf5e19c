# 文件与项目管理工具模块

一个基于Flask的文件与项目管理系统，专为局域网在线考试系统设计，提供文件上传下载、项目管理和任务跟踪功能。

## 功能特性

### 文件管理
- **文件上传下载**: 支持多种Office文档、PDF、图片、压缩包等格式
- **文件组织**: 文件夹结构管理，支持层级目录
- **文件搜索**: 按文件名、类型、标签等条件搜索
- **文件分享**: 生成分享链接，支持权限控制
- **存储管理**: 文件大小限制、类型验证、安全存储

### 项目管理
- **项目创建**: 创建和管理项目基本信息
- **进度跟踪**: 项目进度可视化显示
- **项目状态**: 支持进行中、已完成、已暂停等状态
- **项目搜索**: 按名称、状态等条件搜索项目

### 任务管理
- **任务创建**: 创建任务并关联到项目
- **任务分配**: 支持任务分配给特定用户
- **优先级管理**: 高、中、低三级优先级
- **状态跟踪**: 待处理、进行中、已完成状态管理
- **截止日期**: 任务截止日期提醒

### 系统特性
- **RESTful API**: 标准化API接口设计
- **响应式界面**: 基于Bootstrap的现代化UI
- **权限控制**: 基于JWT的身份认证
- **数据安全**: SQL注入防护、文件类型验证
- **性能优化**: 分页查询、缓存机制

## 技术栈

### 后端
- **框架**: Flask 2.3+
- **数据库**: SQLite 3
- **ORM**: SQLAlchemy
- **认证**: Flask-JWT-Extended
- **文件处理**: Werkzeug
- **API文档**: Flask-RESTX

### 前端
- **框架**: HTML5 + CSS3 + JavaScript (ES6+)
- **UI库**: Bootstrap 5.3
- **图标**: Font Awesome 6
- **HTTP客户端**: Fetch API

## 安装部署

### 环境要求
- Python 3.8+
- pip 包管理器
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+)

### 快速开始

1. **克隆项目**
```bash
cd D:\60-PHRL_OLE_SYS\file-project-manager
```

2. **创建虚拟环境**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **初始化数据库**
```bash
python init_db.py
```

5. **启动应用**
```bash
python app.py
```

6. **访问系统**
打开浏览器访问: http://localhost:5000

### 高级配置

#### 环境配置

创建 `.env` 文件配置环境变量:
```env
# 应用配置
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=sqlite:///file_project_manager.db

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=3600

# 文件上传配置
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=104857600  # 100MB

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

#### 生产环境部署

1. **使用Gunicorn**
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

2. **使用Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    location /uploads {
        alias /path/to/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## API文档

### 认证
所有API请求需要在请求头中包含JWT令牌:
```
Authorization: Bearer <your-jwt-token>
```

### 文件管理API

#### 上传文件
```http
POST /api/v1/files/upload
Content-Type: multipart/form-data

file: <文件>
folder_id: <文件夹ID> (可选)
description: <文件描述> (可选)
```

#### 获取文件列表
```http
GET /api/v1/files?page=1&per_page=10&search=keyword&type=pdf
```

#### 下载文件
```http
GET /api/v1/files/{file_id}/download
```

#### 删除文件
```http
DELETE /api/v1/files/{file_id}
```

### 项目管理API

#### 创建项目
```http
POST /api/v1/projects
Content-Type: application/json

{
    "name": "项目名称",
    "description": "项目描述",
    "start_date": "2024-01-01",
    "end_date": "2024-12-31",
    "status": "active"
}
```

#### 获取项目列表
```http
GET /api/v1/projects?page=1&per_page=10&search=keyword&status=active
```

#### 更新项目
```http
PUT /api/v1/projects/{project_id}
Content-Type: application/json

{
    "name": "更新的项目名称",
    "progress": 75
}
```

### 任务管理API

#### 创建任务
```http
POST /api/v1/tasks
Content-Type: application/json

{
    "title": "任务标题",
    "description": "任务描述",
    "project_id": 1,
    "priority": "high",
    "due_date": "2024-06-30"
}
```

#### 更新任务状态
```http
PUT /api/v1/tasks/{task_id}/status
Content-Type: application/json

{
    "status": "completed"
}
```

## 目录结构

```
file-project-manager/
├── app/                    # 应用主目录
│   ├── api/               # API接口
│   │   ├── files.py       # 文件管理API
│   │   ├── projects.py    # 项目管理API
│   │   └── tasks.py       # 任务管理API
│   ├── models/            # 数据模型
│   │   └── __init__.py    # 数据库模型定义
│   ├── services/          # 业务逻辑服务
│   └── utils/             # 工具函数
│       └── __init__.py    # 通用工具函数
├── config/                # 配置文件
│   └── config.py          # 应用配置
├── static/                # 静态资源
│   ├── css/              # 样式文件
│   │   └── main.css      # 主样式文件
│   ├── js/               # JavaScript文件
│   │   └── main.js       # 主脚本文件
│   └── images/           # 图片资源
├── templates/             # 模板文件
│   └── index.html        # 主页面模板
├── uploads/               # 文件上传目录
├── logs/                  # 日志文件
├── tests/                 # 测试文件
├── app.py                 # 应用入口
├── init_db.py            # 数据库初始化脚本
├── requirements.txt       # Python依赖
└── README.md             # 项目说明
```

## 开发指南

### 代码规范
- 遵循PEP 8 Python代码规范
- 使用类型注解提高代码可读性
- 编写完整的函数文档字符串
- 保持一致的命名约定

### 数据库操作

#### 添加新模型
1. 在 `app/models/__init__.py` 中定义模型
2. 运行数据库迁移:
```bash
python init_db.py --reset
```

#### 查询示例
```python
# 获取所有活跃项目
active_projects = Project.query.filter_by(status='active').all()

# 分页查询
projects = Project.query.paginate(
    page=1, per_page=10, error_out=False
)

# 复杂查询
tasks = Task.query.join(Project).filter(
    Project.status == 'active',
    Task.due_date < datetime.now()
).all()
```

### 前端开发

#### 添加新页面
1. 在 `templates/` 中创建HTML模板
2. 在 `static/js/main.js` 中添加页面逻辑
3. 在 `static/css/main.css` 中添加样式

#### API调用示例
```javascript
// 获取项目列表
const projects = await API.projects.list({
    page: 1,
    per_page: 10,
    search: 'keyword'
});

// 创建新任务
const task = await API.tasks.create({
    title: '新任务',
    project_id: 1,
    priority: 'high'
});
```

## 测试

### 运行测试
```bash
# 安装测试依赖
pip install pytest pytest-cov

# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app tests/
```

### 测试数据库
```bash
# 创建测试数据库
python init_db.py --env testing
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件路径
   - 确认数据库文件权限
   - 运行 `python init_db.py --check`

2. **文件上传失败**
   - 检查上传目录权限
   - 确认文件大小限制
   - 验证文件类型是否支持

3. **JWT认证错误**
   - 检查JWT密钥配置
   - 确认令牌是否过期
   - 验证请求头格式

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

## 性能优化

### 数据库优化
- 为常用查询字段添加索引
- 使用分页避免大量数据查询
- 定期清理过期数据

### 文件存储优化
- 实现文件去重机制
- 添加文件压缩功能
- 配置CDN加速静态资源

### 缓存策略
- 使用Redis缓存热点数据
- 实现API响应缓存
- 配置浏览器缓存策略

## 安全考虑

### 文件安全
- 严格的文件类型验证
- 文件名安全处理
- 病毒扫描集成

### 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证

### 访问控制
- JWT令牌过期机制
- 角色权限管理
- API访问频率限制

## 扩展功能

### 计划中的功能
- [ ] 文件版本控制
- [ ] 在线文档预览
- [ ] 实时协作编辑
- [ ] 移动端适配
- [ ] 邮件通知系统
- [ ] 数据导出功能

### 集成建议
- 与现有用户管理系统集成
- 添加单点登录(SSO)支持
- 集成第三方存储服务
- 实现微服务架构

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 联系方式

如有问题或建议，请通过以下方式联系:

- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 实现基础文件管理功能
- 实现项目和任务管理功能
- 提供RESTful API接口
- 响应式Web界面

---

**注意**: 本系统专为局域网环境设计，在部署到公网环境时请确保实施适当的安全措施。