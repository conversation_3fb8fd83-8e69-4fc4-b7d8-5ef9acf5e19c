# -*- coding: utf-8 -*-
"""
实操任务管理模块主应用
提供实操任务的创建、管理、执行和评价功能
"""

import os
import sys
from flask import Flask, render_template, jsonify, request, redirect, url_for, g
from flask_cors import CORS
import logging
from datetime import datetime
from functools import wraps

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入模块
from dao import (
    TaskCategoryDAO, PracticalTaskDAO, TaskMaterialDAO, 
    TaskExecutionDAO, OperationLogDAO
)
from services import (
    TaskCategoryService, PracticalTaskService, 
    TaskMaterialService, TaskExecutionService
)
from utils.file_manager import FileManager
from integration.exam_integration import ExamIntegrationService
from auth.permissions import init_auth_system, auth_manager
from auth.audit_log import init_audit_system

# 导入路由蓝图
from routes.categories import categories_bp
from routes.tasks import tasks_bp
from routes.materials import materials_bp
from routes.executions import executions_bp
from routes.auth import auth_bp

def create_app():
    """
    创建Flask应用实例
    
    Returns:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)
    
    # 应用配置
    app.config.update({
        'SECRET_KEY': 'practical-task-management-secret-key-2024',
        'MAX_CONTENT_LENGTH': 50 * 1024 * 1024,  # 50MB文件上传限制
        'UPLOAD_FOLDER': os.path.join(os.path.dirname(__file__), 'uploads'),
        'DATABASE_PATH': os.path.join(os.path.dirname(__file__), 'practical_tasks.db'),
        'JSON_AS_ASCII': False,  # 支持中文JSON响应
        'JSONIFY_PRETTYPRINT_REGULAR': True
    })
    
    # 启用CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": ["*"],
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('practical_task_management.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 创建必要的目录
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.join(os.path.dirname(__file__), 'logs'), exist_ok=True)
    
    # 初始化数据库
    init_database()
    
    # 初始化认证系统
    init_auth_system()
    
    # 初始化审计系统
    init_audit_system()
    
    # 注册蓝图（Blueprint已经包含了url_prefix，无需重复添加）
    app.register_blueprint(auth_bp)
    app.register_blueprint(categories_bp)
    app.register_blueprint(tasks_bp)
    app.register_blueprint(materials_bp)
    app.register_blueprint(executions_bp)
    
    # 注册路由
    register_routes(app)
    
    # 错误处理
    register_error_handlers(app)
    
    return app

def init_database():
    """
    初始化数据库，创建所有必要的表
    """
    try:
        # 数据库已经在models.py中的DatabaseManager初始化时自动创建
        # 这里只需要插入初始数据
        insert_initial_data()
        
        print("[*] 数据库初始化完成")
        
    except Exception as e:
        print(f"[!] 数据库初始化失败: {str(e)}")
        raise

def insert_initial_data():
    """
    插入初始数据
    """
    try:
        # 检查是否已有数据
        categories = TaskCategoryService.get_categories_by_software()
        if categories['success'] and categories['data']['total'] > 0:
            return  # 已有数据，跳过初始化
        
        # 插入默认分类
        default_categories = [
            {
                'name': 'Office办公软件',
                'description': 'Microsoft Office系列软件操作任务',
                'software_type': 'Office',
                'difficulty_level': 2
            },
            {
                'name': 'Photoshop图像处理',
                'description': 'Adobe Photoshop图像编辑和处理任务',
                'software_type': 'Design',
                'difficulty_level': 3
            },
            {
                'name': 'AutoCAD制图',
                'description': 'AutoCAD计算机辅助设计任务',
                'software_type': 'CAD',
                'difficulty_level': 4
            },
            {
                'name': '网页设计',
                'description': 'HTML/CSS/JavaScript网页设计任务',
                'software_type': 'Web',
                'difficulty_level': 3
            }
        ]
        
        for category_data in default_categories:
            result = TaskCategoryService.create_category(
                name=category_data['name'],
                description=category_data['description'],
                software_type=category_data['software_type'],
                difficulty_level=category_data['difficulty_level'],
                user_id=1,  # 系统用户
                user_name='system'
            )
            if not result['success']:
                print(f"[!] 创建分类失败: {result['message']}")
        
        print("[*] 初始数据插入完成")
        
    except Exception as e:
        print(f"[!] 初始数据插入失败: {str(e)}")

def register_routes(app):
    """
    注册主要路由
    
    Args:
        app (Flask): Flask应用实例
    """
    
    # 认证中间件
    def require_auth(f):
        """需要认证的装饰器"""
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 检查是否为API请求
            if request.path.startswith('/api/'):
                return f(*args, **kwargs)
            
            # 检查认证状态
            token = request.cookies.get('auth_token')
            if not token:
                return redirect(url_for('login'))
            
            # 验证Token
            user_data = auth_manager.verify_token(token)
            if not user_data:
                return redirect(url_for('login'))
            
            g.current_user = user_data
            return f(*args, **kwargs)
        return decorated_function
    
    # 登录页面路由
    @app.route('/login')
    def login():
        """登录页面"""
        return render_template('login.html')
    
    @app.route('/')
    @require_auth
    def index():
        """
        首页路由
        """
        try:
            # 获取统计数据
            stats = get_dashboard_stats()
            return render_template('index.html', stats=stats)
        except Exception as e:
            app.logger.error(f"首页加载失败: {str(e)}")
            return render_template('index.html', stats={})
    
    @app.route('/tasks')
    @require_auth
    def tasks_page():
        """
        任务管理页面
        """
        return render_template('tasks.html')
    
    @app.route('/tasks/new')
    def new_task_page():
        """
        新建任务页面
        """
        return render_template('task_form.html', task=None, mode='create')
    
    @app.route('/tasks/<int:task_id>/edit')
    def edit_task_page(task_id):
        """
        编辑任务页面
        
        Args:
            task_id (int): 任务ID
        """
        try:
            task_service = PracticalTaskService()
            result = task_service.get_task(task_id)
            
            if result['code'] == 200:
                return render_template('task_form.html', task=result['data'], mode='edit')
            else:
                return redirect(url_for('tasks_page'))
        except Exception as e:
            app.logger.error(f"任务编辑页面加载失败: {str(e)}")
            return redirect(url_for('tasks_page'))
    
    @app.route('/categories')
    @require_auth
    def categories_page():
        """
        分类管理页面
        """
        return render_template('categories.html')
    
    @app.route('/tasks/<int:task_id>/materials')
    def materials_page(task_id):
        """
        素材管理页面
        
        Args:
            task_id (int): 任务ID
        """
        try:
            task_service = PracticalTaskService()
            result = task_service.get_task(task_id)
            
            if result['code'] == 200:
                return render_template('materials.html', task=result['data'])
            else:
                return redirect(url_for('tasks_page'))
        except Exception as e:
            app.logger.error(f"素材管理页面加载失败: {str(e)}")
            return redirect(url_for('tasks_page'))
    
    @app.route('/executions')
    @require_auth
    def executions_page():
        """
        执行记录页面
        """
        return render_template('executions.html')
    
    @app.route('/users')
    @require_auth
    def users_page():
        """用户管理页面"""
        return render_template('users.html')

    @app.route('/audit-logs')
    @require_auth
    def audit_logs_page():
        """审计日志页面"""
        return render_template('audit_logs.html')
    
    @app.route('/logout')
    def logout():
        """注销"""
        response = redirect(url_for('login'))
        response.set_cookie('auth_token', '', expires=0)
        return response
    
    @app.route('/api/v1/dashboard/stats')
    def dashboard_stats():
        """
        获取仪表板统计数据API
        
        Returns:
            dict: 统计数据
        """
        try:
            stats = get_dashboard_stats()
            return jsonify({
                'code': 200,
                'msg': '获取统计数据成功',
                'data': stats
            })
        except Exception as e:
            app.logger.error(f"获取统计数据失败: {str(e)}")
            return jsonify({
                'code': 500,
                'msg': f'获取统计数据失败: {str(e)}',
                'data': {}
            })
    
    @app.route('/api/v1/health')
    def health_check():
        """
        健康检查API
        
        Returns:
            dict: 健康状态
        """
        return jsonify({
            'code': 200,
            'msg': '服务正常',
            'data': {
                'service': 'practical-task-management',
                'status': 'healthy',
                'timestamp': datetime.now().isoformat()
            }
        })
    
    @app.route('/api/health')
    def health_check_compat():
        """
        健康检查API兼容性路由
        为API网关提供/api/health路径访问
        
        Returns:
            dict: 健康状态
        """
        return jsonify({
            'code': 200,
            'msg': '服务正常',
            'data': {
                'service': 'practical-task-management',
                'status': 'healthy',
                'timestamp': datetime.now().isoformat()
            }
        })
    
    @app.route('/api/v1/config')
    def get_config():
        """
        获取前端配置API
        
        Returns:
            dict: 配置信息
        """
        return jsonify({
            'code': 200,
            'msg': '获取配置成功',
            'data': {
                'upload_max_size': app.config['MAX_CONTENT_LENGTH'],
                'allowed_file_types': {
                    'image': ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
                    'document': ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
                    'archive': ['zip', 'rar', '7z', 'tar', 'gz'],
                    'video': ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
                    'audio': ['mp3', 'wav', 'flac', 'aac', 'ogg']
                },
                'software_types': ['Office', 'Design', 'CAD', 'Web', 'Database', 'Programming'],
                'difficulty_levels': ['beginner', 'intermediate', 'advanced', 'expert']
            }
        })

def get_dashboard_stats():
    """
    获取仪表板统计数据
    
    Returns:
        dict: 统计数据
    """
    try:
        task_service = PracticalTaskService()
        category_service = TaskCategoryService()
        execution_service = TaskExecutionService()
        material_service = TaskMaterialService()
        
        # 获取各项统计
        task_stats = task_service.get_task_statistics()
        category_stats = category_service.get_category_statistics()
        
        # 获取最近任务
        recent_tasks_result = task_service.get_tasks(page=1, page_size=5, sort_by='created_at', sort_order='desc')
        recent_tasks = recent_tasks_result.get('data', {}).get('items', [])
        
        # 获取最近执行记录
        recent_executions_result = execution_service.get_executions(page=1, page_size=5, sort_by='created_at', sort_order='desc')
        recent_executions = recent_executions_result.get('data', {}).get('items', [])
        
        return {
            'total_tasks': task_stats.get('data', {}).get('total_tasks', 0),
            'total_categories': category_stats.get('data', {}).get('total_categories', 0),
            'total_executions': len(recent_executions),
            'total_materials': 0,  # 需要实现材料统计
            'recent_tasks': recent_tasks,
            'recent_executions': recent_executions,
            'task_stats': task_stats.get('data', {}),
            'category_stats': category_stats.get('data', {})
        }
        
    except Exception as e:
        print(f"[!] 获取统计数据失败: {str(e)}")
        return {
            'total_tasks': 0,
            'total_categories': 0,
            'total_executions': 0,
            'total_materials': 0,
            'recent_tasks': [],
            'recent_executions': [],
            'task_stats': {},
            'category_stats': {}
        }

def register_error_handlers(app):
    """
    注册错误处理器
    
    Args:
        app (Flask): Flask应用实例
    """
    
    @app.errorhandler(404)
    def not_found(error):
        """
        404错误处理
        """
        if request.path.startswith('/api/'):
            return jsonify({
                'code': 404,
                'msg': '接口不存在',
                'data': {}
            }), 404
        return render_template('error.html', error_code=404, error_msg='页面不存在'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        """
        500错误处理
        """
        if request.path.startswith('/api/'):
            return jsonify({
                'code': 500,
                'msg': '服务器内部错误',
                'data': {}
            }), 500
        return render_template('error.html', error_code=500, error_msg='服务器内部错误'), 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        """
        文件过大错误处理
        """
        return jsonify({
            'code': 413,
            'msg': '文件大小超过限制（最大50MB）',
            'data': {}
        }), 413

def safe_print(text):
    """
    安全打印函数，处理Windows控制台编码问题
    
    Args:
        text (str): 要打印的文本
    """
    try:
        print(text)
    except UnicodeEncodeError:
        # 在Windows控制台下，移除或替换不兼容的Unicode字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

if __name__ == '__main__':
    try:
        # 设置控制台编码为UTF-8（Windows）
        if sys.platform.startswith('win'):
            os.system('chcp 65001 > nul')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
        
        # 创建应用
        app = create_app()
        
        safe_print("[*] 实操任务管理模块启动中...")
        safe_print("[*] 服务地址: http://localhost:5003")
        safe_print("[*] API文档: http://localhost:5003/api/v1/health")
        safe_print("[*] 按 Ctrl+C 停止服务")
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=5003,
            debug=True,
            threaded=True
        )
        
    except KeyboardInterrupt:
        safe_print("\n[*] 服务已停止")
    except Exception as e:
        safe_print(f"[!] 启动失败: {str(e)}")
        sys.exit(1)