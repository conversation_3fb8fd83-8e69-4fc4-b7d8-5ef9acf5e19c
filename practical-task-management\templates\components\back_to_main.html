<!-- 返回主控台按钮组件 -->
<div class="back-to-main-container">
    <button class="btn btn-outline-secondary back-to-main-btn" onclick="backToMainConsole()" title="返回主控台">
        <i class="bi bi-house-door"></i>
        <span class="d-none d-md-inline">返回主控台</span>
    </button>
</div>

<style>
.back-to-main-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

.back-to-main-btn {
    border-radius: 25px;
    padding: 8px 16px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.back-to-main-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: rgba(255, 255, 255, 1);
}

.back-to-main-btn i {
    margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .back-to-main-container {
        top: 10px;
        right: 10px;
    }
    
    .back-to-main-btn {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .back-to-main-btn {
        background: rgba(33, 37, 41, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    
    .back-to-main-btn:hover {
        background: rgba(33, 37, 41, 1);
        border-color: rgba(255, 255, 255, 0.2);
    }
}
</style>

<script>
/**
 * 返回主控台函数
 * 根据系统配置返回到主控台页面
 */
function backToMainConsole() {
    try {
        // 检查是否在集成环境中
        if (window.parent && window.parent !== window) {
            // 在iframe中，通知父窗口返回主控台
            window.parent.postMessage({
                type: 'BACK_TO_MAIN_CONSOLE',
                source: 'practical-task-management'
            }, '*');
        } else {
            // 独立运行，直接跳转到主控台
            const mainConsoleUrl = getMainConsoleUrl();
            if (mainConsoleUrl) {
                window.location.href = mainConsoleUrl;
            } else {
                // 如果无法确定主控台地址，显示确认对话框
                if (confirm('确定要离开实操任务管理系统吗？')) {
                    // 尝试返回上一页或关闭窗口
                    if (window.history.length > 1) {
                        window.history.back();
                    } else {
                        window.close();
                    }
                }
            }
        }
    } catch (error) {
        console.error('返回主控台失败:', error);
        
        // 降级处理：显示确认对话框
        if (confirm('确定要离开实操任务管理系统吗？')) {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.close();
            }
        }
    }
}

/**
 * 获取主控台URL
 * @returns {string|null} 主控台URL或null
 */
function getMainConsoleUrl() {
    // 从配置中获取主控台地址
    const config = window.APP_CONFIG || {};
    
    if (config.mainConsoleUrl) {
        return config.mainConsoleUrl;
    }
    
    // 尝试从当前URL推断主控台地址
    const currentUrl = window.location.href;
    const urlParts = currentUrl.split('/');
    
    // 如果当前URL包含端口号，尝试推断主控台端口
    if (urlParts[2] && urlParts[2].includes(':')) {
        const [host, port] = urlParts[2].split(':');
        
        // 根据约定，主控台通常在8000端口
        if (port !== '8000') {
            return `${urlParts[0]}//${host}:8000`;
        }
    }
    
    // 如果无法推断，返回null
    return null;
}

/**
 * 监听来自主控台的消息
 */
window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'MAIN_CONSOLE_CONFIG') {
        // 接收主控台配置信息
        window.APP_CONFIG = window.APP_CONFIG || {};
        window.APP_CONFIG.mainConsoleUrl = event.data.mainConsoleUrl;
    }
});

/**
 * 页面加载时请求主控台配置
 */
document.addEventListener('DOMContentLoaded', function() {
    if (window.parent && window.parent !== window) {
        // 向父窗口请求配置信息
        window.parent.postMessage({
            type: 'REQUEST_MAIN_CONSOLE_CONFIG',
            source: 'practical-task-management'
        }, '*');
    }
});
</script>