#!/usr/bin/env python
# -*- coding: utf-8 -*-

from models import QuestionBank
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
import os

# 连接数据库
engine = create_engine(os.getenv('DATABASE_URL', 'sqlite:///local_dev.db'))
Session = sessionmaker(bind=engine)
session = Session()

try:
    # 查找所有无效题库
    invalid_banks = session.query(QuestionBank).filter(
        QuestionBank.name == 'question_bank_name'
    ).all()
    
    if invalid_banks:
        for bank in invalid_banks:
            print(f'找到无效题库: {bank.id} - {bank.name}')
            session.delete(bank)
        session.commit()
        print(f'已删除 {len(invalid_banks)} 个无效题库')
    else:
        print('未找到无效题库')
        
    # 显示当前所有题库
    all_banks = session.query(QuestionBank).all()
    print('\n当前数据库中的题库:')
    for bank in all_banks:
        print(f'ID: {bank.id}, 名称: {repr(bank.name)}')
        
except Exception as e:
    print(f'操作失败: {e}')
    session.rollback()
finally:
    session.close()