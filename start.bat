@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM ================================================================
REM 职业技能等级考试系统 - Windows启动脚本
REM Professional Skills Assessment System - Windows Startup Script
REM 
REM 版本: 2.0.0
REM 作者: 系统架构师
REM 日期: 2024-01-15
REM ================================================================

REM 设置控制台标题
title 职业技能等级考试系统 v2.0

REM 设置颜色 (绿色文字)
color 0A

REM 显示系统横幅
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                职业技能等级考试系统 v2.0                      ║
echo ║                Professional Skills Assessment System          ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🚀 新一代架构 ^| 🔧 模块化设计 ^| ⚡ 高性能启动 ^| 🛡️ 安全可靠  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 获取Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python版本: %PYTHON_VERSION%

REM 检查当前目录是否为项目根目录
if not exist "start.py" (
    echo ❌ 错误: 请在项目根目录下运行此脚本
    echo 当前目录: %CD%
    pause
    exit /b 1
)

REM 检查虚拟环境
echo 🔍 检查虚拟环境...
if exist "venv\Scripts\activate.bat" (
    echo ✅ 发现虚拟环境，正在激活...
    call venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
) else if exist ".venv\Scripts\activate.bat" (
    echo ✅ 发现虚拟环境，正在激活...
    call .venv\Scripts\activate.bat
    echo ✅ 虚拟环境已激活
) else (
    echo ⚠️  未发现虚拟环境，使用系统Python环境
    echo 建议创建虚拟环境以避免依赖冲突
)

REM 检查依赖
echo 🔍 检查系统依赖...
if exist "requirements.txt" (
    echo ✅ 发现依赖文件: requirements.txt
) else (
    echo ⚠️  未发现requirements.txt文件
)

REM 解析命令行参数
set "ARGS="
set "SHOW_HELP=0"
set "DEV_MODE=0"
set "BACKGROUND=0"
set "STATUS_ONLY=0"
set "STOP_ONLY=0"
set "RESTART=0"
set "MODULES="

:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="--help" set "SHOW_HELP=1"
if "%~1"=="-h" set "SHOW_HELP=1"
if "%~1"=="--dev" set "DEV_MODE=1"
if "%~1"=="-d" set "DEV_MODE=1"
if "%~1"=="--background" set "BACKGROUND=1"
if "%~1"=="-b" set "BACKGROUND=1"
if "%~1"=="--status" set "STATUS_ONLY=1"
if "%~1"=="-s" set "STATUS_ONLY=1"
if "%~1"=="--stop" set "STOP_ONLY=1"
if "%~1"=="--restart" set "RESTART=1"
if "%~1"=="-r" set "RESTART=1"
if "%~1"=="--modules" (
    shift
    set "MODULES=%~1"
)
if "%~1"=="-m" (
    shift
    set "MODULES=%~1"
)
set "ARGS=%ARGS% %~1"
shift
goto :parse_args

:args_done

REM 显示帮助信息
if %SHOW_HELP%==1 (
    echo.
    echo 📖 使用说明:
    echo.
    echo   start.bat                    启动所有模块
    echo   start.bat --dev              开发模式启动
    echo   start.bat --modules MODULE   启动指定模块
    echo   start.bat --status           查看系统状态
    echo   start.bat --stop             停止所有服务
    echo   start.bat --restart          重启系统
    echo   start.bat --background       后台模式启动
    echo   start.bat --help             显示此帮助信息
    echo.
    echo 📋 可用模块:
    echo   user_management              用户管理模块
    echo   question_bank                题库管理模块
    echo   exam_management              考试管理模块
    echo   score_management             成绩管理模块
    echo   monitoring                   监控模块
    echo   auditing                     审计模块
    echo   api_gateway                  API网关
    echo.
    echo 💡 示例:
    echo   start.bat --modules user_management,question_bank
    echo   start.bat --dev --modules api_gateway
    echo.
    pause
    exit /b 0
)

REM 构建Python命令
set "PYTHON_CMD=python start.py"

if %DEV_MODE%==1 set "PYTHON_CMD=%PYTHON_CMD% --dev"
if %BACKGROUND%==1 set "PYTHON_CMD=%PYTHON_CMD% --background"
if %STATUS_ONLY%==1 set "PYTHON_CMD=%PYTHON_CMD% --status"
if %STOP_ONLY%==1 set "PYTHON_CMD=%PYTHON_CMD% --stop"
if %RESTART%==1 set "PYTHON_CMD=%PYTHON_CMD% --restart"
if not "%MODULES%"=="" set "PYTHON_CMD=%PYTHON_CMD% --modules %MODULES%"

REM 显示启动信息
echo.
echo 🚀 正在启动系统...
echo 📝 执行命令: %PYTHON_CMD%
echo.

REM 设置环境变量
set "PYTHONPATH=%CD%"
set "PYTHONIOENCODING=utf-8"

REM 执行Python启动脚本
%PYTHON_CMD%
set "EXIT_CODE=%ERRORLEVEL%"

REM 检查执行结果
if %EXIT_CODE%==0 (
    if %STATUS_ONLY%==1 (
        echo.
        echo ✅ 状态查询完成
    ) else if %STOP_ONLY%==1 (
        echo.
        echo ✅ 系统已停止
    ) else if %RESTART%==1 (
        echo.
        echo ✅ 系统重启完成
    ) else if %BACKGROUND%==1 (
        echo.
        echo ✅ 系统已在后台启动
        echo 💡 使用 'start.bat --status' 查看状态
        echo 💡 使用 'start.bat --stop' 停止系统
    ) else (
        echo.
        echo ✅ 系统启动完