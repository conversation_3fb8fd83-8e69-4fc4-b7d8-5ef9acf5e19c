
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正后的Excel导入器 - 严格按照标准模板执行
"""

import pandas as pd
import openpyxl
import os
import datetime
import time
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
from models import Question, QuestionBank

# 从standard_field_config.json加载标准字段映射
import json
with open('standard_field_config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

# 实际Excel文件的列名映射到标准字段名（支持新旧两种格式）
ACTUAL_TO_STANDARD_MAPPING = {
    # 旧格式映射（驼峰式）
    '题库名称': '题库名称\nquestion_bank_name',
    '试题ID': '试题ID\nquestion_id',
    '题型代码': '题型代码\nquestion_type_code',
    '题号': '题号\nquestion_number',
    '试题（题干）': '试题（题干）\nquestion_stem',
    '试题（选项A）': '试题（选项 A）\noption_a',
    '试题（选项B）': '试题（选项 B）\noption_b',
    '试题（选项C）': '试题（选项 C）\noption_c',
    '试题（选项D）': '试题（选项 D）\noption_d',
    '试题（选项E）': '试题（选项 E）\noption_e',
    '【图】及位置': '【图】及位置\nimage_and_position',
    '正确答案': '正确答案\ncorrect_answer',
    '难度代码': '难度代码\ndifficulty_code',
    '一致性代码': '一致性代码\nconsistency_code',
    '解析': '解析\nexplanation',
    # 实际Excel文件的双行表头格式映射
    '题库名称\nquestion_bank_name': '题库名称\nquestion_bank_name',
    '试题ID\nquestion_id': '试题ID\nquestion_id',
    '序号\nserial_number': '序号\nserial_number',
    '认定点代码\nidentification_point_code': '认定点代码\nidentification_point_code',
    '题型代码\nquestion_type_code': '题型代码\nquestion_type_code',
    '题号\nquestion_number': '题号\nquestion_number',
    '试题（题干）\nQuestion (Stem)': '试题（题干）\nquestion_stem',
    '试题（选项 A）\nOption A': '试题（选项 A）\noption_a',
    '试题（选项 B）\nOption B': '试题（选项 B）\noption_b',
    '试题（选项 C）\nOption C': '试题（选项 C）\noption_c',
    '试题（选项 D）\nOption D': '试题（选项 D）\noption_d',
    '试题（选项 E）\nOption E': '试题（选项 E）\noption_e',
    '【图】及位置\nImage and Position': '【图】及位置\nimage_and_position',
    '正确答案\nCorrect Answer': '正确答案\ncorrect_answer',
    '难度代码\nDifficulty Code': '难度代码\ndifficulty_code',
    '一致性代码\nConsistency Code': '一致性代码\nconsistency_code',
    '解析\nExplanation': '解析\nexplanation'
}

# 兼容旧格式的映射（驼峰式英文字段名）
LEGACY_FIELD_MAPPING = {
    'Bank Name': 'question_bank_name',
    'Question ID': 'question_id', 
    'Serial Number': 'serial_number',
    'Certification Point Code': 'identification_point_code',
    'Question Type Code': 'question_type_code',
    'Question Number': 'question_number',
    'Question Stem': 'question_stem',
    'Option A': 'option_a',
    'Option B': 'option_b', 
    'Option C': 'option_c',
    'Option D': 'option_d',
    'Option E': 'option_e',
    'Image Location': 'image_and_position',
    'Image and Position': 'image_and_position',
    'Correct Answer': 'correct_answer',
    'Difficulty Code': 'difficulty_code',
    'Consistency Code': 'consistency_code',
    'Analysis': 'explanation',
    'Question Bank Name': 'question_bank_name',
    'Question Count': 'number_of_questions',
    'Number of Questions': 'number_of_questions',
    'Score Value': 'score_value'
}

STANDARD_EXPECTED_COLUMNS = {}
for std_field, db_field in config['standard_fields'].items():
    # 为每个标准字段创建可能的别名列表
    aliases = [std_field]
    # 添加不带\n的版本
    if '\n' in std_field:
        aliases.append(std_field.split('\n')[0])
    # 添加中文版本
    if '（' in std_field:
        aliases.append(std_field.split('（')[0])
    
    # 添加实际使用的列名映射
    if std_field == 'ID\nID':
        aliases.extend(['试题ID'])
    elif std_field == '题库名称\nBank Name':
        aliases.extend(['题库名称'])
    elif std_field == '题型代码\nQuestion Type':
        aliases.extend(['题型代码'])
    elif std_field == '题号\nQuestion Number':
        aliases.extend(['题号'])
    elif std_field == '试题（题干）\nQuestion Stem':
        aliases.extend(['试题（题干）'])
    elif std_field == '试题（选项A）\nOption A':
        aliases.extend(['试题（选项A）'])
    elif std_field == '试题（选项B）\nOption B':
        aliases.extend(['试题（选项B）'])
    elif std_field == '试题（选项C）\nOption C':
        aliases.extend(['试题（选项C）'])
    elif std_field == '试题（选项D）\nOption D':
        aliases.extend(['试题（选项D）'])
    elif std_field == '试题（选项E）\nOption E':
        aliases.extend(['试题（选项E）'])
    elif std_field == '【图】及位置\nImage Location':
        aliases.extend(['【图】及位置'])
    elif std_field == '正确答案\nCorrect Answer':
        aliases.extend(['正确答案'])
    elif std_field == '难度代码\nDifficulty Code':
        aliases.extend(['难度代码'])
    elif std_field == '一致性代码\nConsistency Code':
        aliases.extend(['一致性代码'])
    elif std_field == '解析\nAnalysis':
        aliases.extend(['解析'])
    
    STANDARD_EXPECTED_COLUMNS[std_field] = aliases

# 使用配置文件中的标准代码映射
STANDARD_QUESTION_TYPES = config['question_types']
STANDARD_DIFFICULTY_CODES = config['difficulty_codes']
STANDARD_CONSISTENCY_CODES = config['consistency_codes']

def is_valid_bank_name(bank_name):
    """验证题库名称是否有效
    
    Args:
        bank_name (str): 题库名称
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not bank_name or bank_name.isspace() or len(bank_name) <= 2:
        return False, "题库名称不能为空或过短"
        
    # 完全匹配的无效模式（模板字段名称）
    invalid_exact_patterns = [
        'question_bank_name', 'bank_name', 'Bank Name', 'BANK_NAME',
        '题库名称', 'QuestionBank', 'question_bank', 'QUESTION_BANK',
        '请输入题库名称', '题库', 'bank', 'name', 'Name', 'NAME'
    ]
    
    if bank_name in invalid_exact_patterns:
        return False, f"检测到模板字段名称 '{bank_name}'。您上传的似乎是模板文件，请下载并填写实际的题库数据后再导入。"
        
    # 包含无效关键词的模式
    invalid_keywords = ['question_bank_name', 'bank_name', 'Bank Name']
    if any(keyword in bank_name for keyword in invalid_keywords):
        return False, f"题库名称 '{bank_name}' 包含模板字段关键词，请填写实际的题库名称"
        
    # 检查是否为纯英文字母的无意义组合
    if bank_name.lower() in ['name', 'bank', 'question', 'template', 'example']:
        return False, f"题库名称 '{bank_name}' 是无效的通用词汇，请填写具体的题库名称"
        
    return True, ""

def smart_field_mapping(df):
    """智能字段映射：自动检测并转换新旧格式"""
    # 创建新的列名映射字典
    column_mapping = {}
    
    for col in df.columns:
        # 首先尝试直接映射（中文字段名）
        if col in ACTUAL_TO_STANDARD_MAPPING:
            column_mapping[col] = ACTUAL_TO_STANDARD_MAPPING[col]
            continue
        
        # 如果是双行表头格式，检查英文部分
        if isinstance(col, str) and '\n' in col:
            chinese_part, english_part = col.split('\n', 1)
            
            # 检查是否是旧格式的英文字段名
            if english_part in LEGACY_FIELD_MAPPING:
                new_english = LEGACY_FIELD_MAPPING[english_part]
                newline = '\n'
                new_col = f"{chinese_part}{newline}{new_english}"
                column_mapping[col] = new_col
                continue
        
        # 如果是纯英文字段名（旧格式）
        if col in LEGACY_FIELD_MAPPING:
            # 需要根据上下文推断中文名称
            new_english = LEGACY_FIELD_MAPPING[col]
            # 查找对应的中文名称
            chinese_name = get_chinese_name_for_field(new_english)
            if chinese_name:
                newline = '\n'
                new_col = f"{chinese_name}{newline}{new_english}"
                column_mapping[col] = new_col
                continue
    
    # 应用映射
    if column_mapping:
        df = df.rename(columns=column_mapping)
        print(f"字段映射完成，转换了 {len(column_mapping)} 个字段")
        for old_col, new_col in column_mapping.items():
            print(f"  {old_col} -> {new_col}")
    
    return df

def get_chinese_name_for_field(english_field):
    """根据英文字段名获取对应的中文名称"""
    field_chinese_mapping = {
        'question_bank_name': '题库名称',
        'question_id': '试题ID',
        'serial_number': '序号',
        'identification_point_code': '认定点代码',
        'question_type_code': '题型代码',
        'question_number': '题号',
        'question_stem': '试题（题干）',
        'question_option_a': '试题（选项 A）',
        'question_option_b': '试题（选项 B）',
        'question_option_c': '试题（选项 C）',
        'question_option_d': '试题（选项 D）',
        'question_option_e': '试题（选项 E）',
        'image_and_position': '【图】及位置',
        'correct_answer': '正确答案',
        'difficulty_code': '难度代码',
        'consistency_code': '一致性代码',
        'explanation': '解析',
        'number_of_questions': '题量',
        'score_value': '分值'
    }
    return field_chinese_mapping.get(english_field)

def validate_field_mapping(df, config):
    """验证Excel列名与配置映射是否匹配"""
    missing_mappings = []
    for std_field in config['standard_fields']:
        # 检查标准字段或其别名是否存在于Excel列中
        aliases = STANDARD_EXPECTED_COLUMNS.get(std_field, [std_field])
        found = any(alias in df.columns for alias in aliases)
        if not found:
            missing_mappings.append(std_field)
    return missing_mappings

def import_questions_from_excel_standard(filepath, db_session, overwrite_duplicates=False):
    """严格按照标准模板导入题目
    
    Args:
        filepath: Excel文件路径
        db_session: 数据库会话
        overwrite_duplicates: 是否覆盖重复的题目ID，默认为False（跳过重复项）
    """
    
    # 加载配置文件
    with open('standard_field_config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    try:
        # 首先检查文件是否存在
        if not os.path.exists(filepath):
            return [], [{"type": "file", "message": f"文件未找到: {filepath}"}]
        
        # 检查文件扩展名
        if not filepath.lower().endswith(('.xlsx', '.xls')):
            return [], [{"type": "file", "message": "文件格式不支持，请使用.xlsx或.xls格式的Excel文件"}]
        
        # 使用pandas读取Excel文件，指定engine参数
        try:
            # 尝试使用openpyxl引擎读取xlsx文件
            if filepath.lower().endswith('.xlsx'):
                # 先读取前几行来检测表头结构
                preview_df = pd.read_excel(filepath, engine='openpyxl', nrows=5, header=None)
                # 检测数据实际开始的行数
                data_start_row = 2  # 默认从第3行开始（索引2）
                for i in range(1, min(5, len(preview_df))):  # 从第2行开始检查
                    row_values = [str(val).strip().lower() for val in preview_df.iloc[i].values if pd.notna(val)]
                    # 如果第i行包含表头关键词，说明还不是数据行
                    # 仅检查英文表头关键词（扩展列表以确保准确识别）
                    header_keywords = ['question_id', 'question_bank_name', 'question_type_code', 'sequence_number', 
                                     'question_stem', 'question_option_a', 'correct_answer', 'difficulty_code']
                    if any(keyword in ' '.join(row_values) for keyword in header_keywords):
                        data_start_row = i + 1  # 数据从下一行开始（i+1是行索引）
                        # 找到表头行后，不再继续检查，直接使用这个数据开始行
                        break
                
                # 读取表头（前两行）
                header_df = pd.read_excel(filepath, engine='openpyxl', nrows=2, header=None)
                # 使用openpyxl直接读取以避免pandas的行数限制问题
                from openpyxl import load_workbook
                wb = load_workbook(filepath, read_only=True)
                ws = wb.active
                
                # 获取所有数据行（从data_start_row+1开始，因为openpyxl是1-based）
                data_rows = []
                for row in ws.iter_rows(min_row=data_start_row+1, values_only=True):
                    data_rows.append(row)
                
                # 转换为DataFrame
                if data_rows:
                    df = pd.DataFrame(data_rows)
                else:
                    df = pd.DataFrame()
                
                wb.close()
            else:
                # 对于xls文件使用xlrd引擎
                preview_df = pd.read_excel(filepath, engine='xlrd', nrows=5, header=None)
                data_start_row = 2  # 默认从第3行开始（索引2）
                for i in range(1, min(5, len(preview_df))):  # 从第2行开始检查
                    row_values = [str(val).strip() for val in preview_df.iloc[i].values if pd.notna(val)]
                    # 如果第i行包含表头关键词，说明还不是数据行
                    # 仅检查英文表头关键词（兼容旧格式，扩展列表以确保准确识别）
                    english_keywords = ['question_id', 'question_bank_name', 'question_type_code', 'sequence_number',
                                      'question_stem', 'question_option_a', 'correct_answer', 'difficulty_code']
                    
                    row_text = ' '.join(row_values)
                    if any(keyword.lower() in row_text.lower() for keyword in english_keywords):
                        data_start_row = i + 1  # 数据从下一行开始（i+1是行索引）
                        # 找到表头行后，不再继续检查，直接使用这个数据开始行
                        break
                
                header_df = pd.read_excel(filepath, engine='xlrd', nrows=2, header=None)
                # 对于xls文件，先读取全部数据然后切片，避免pandas的行数限制问题
                full_df = pd.read_excel(filepath, engine='xlrd', header=None)
                # 从检测到的行开始切片获取数据
                df = full_df.iloc[data_start_row:].reset_index(drop=True)
            
            # 处理表头，合并中英文列名
            if len(header_df) >= 2:
                # 合并中英文列名为标准格式："中文\n英文"
                new_columns = []
                for i in range(len(header_df.columns)):
                    chinese_part = str(header_df.iloc[0, i]).strip() if i < len(header_df.columns) else ''
                    english_part = str(header_df.iloc[1, i]).strip() if i < len(header_df.columns) else ''
                    # 跳过空列
                    if chinese_part == 'nan' or english_part == 'nan' or (not chinese_part and not english_part):
                        continue
                    newline = '\n'
                    new_columns.append(f"{chinese_part}{newline}{english_part}")
                
                # 重新设置列名
                df.columns = new_columns[:len(df.columns)]

            
            # 填充空值
            df = df.fillna('')
            
            # 智能字段映射：支持新旧两种格式
            df = smart_field_mapping(df)
            
        except Exception as e:
            return [], [{"type": "file", "message": f"读取Excel文件失败: {e}"}]
        
        # 检查数据是否为空
        if df.empty:
            return [], [{"type": "file", "message": "Excel文件中没有数据"}]
        
        print(f"使用标准模板读取Excel: 数据行数={len(df)}")
        
    except FileNotFoundError:
        return [], [{"type": "file", "message": f"文件未找到: {filepath}"}]
    except Exception as e:
        return [], [{"type": "file", "message": f"读取Excel文件错误: {e}"}]

    # 验证必需的标准列是否存在（映射后）
    required_standard_columns = [
        '试题ID\nquestion_id', '题库名称\nquestion_bank_name', '题型代码\nquestion_type_code', 
        '试题（题干）\nquestion_stem', '正确答案\ncorrect_answer', 
        '难度代码\ndifficulty_code'
    ]
    missing_columns = [col for col in required_standard_columns if col not in df.columns]
    if missing_columns:
        # 修复f-string语法错误：不能在f-string表达式中直接使用反斜杠
        missing_col_names = [col.split('\n')[0] for col in missing_columns]
        return [], [{
            "type": "validation",
            "message": f"Excel文件缺少必需列: {', '.join(missing_col_names)}"
        }]
    
    # 验证通过，开始处理数据

    questions_to_add = []
    detailed_errors = []
    bank_cache = {}  # 题库名称到ID的缓存

    # 查询数据库已存在ID
    try:
        db_existing_ids = set([row[0] for row in db_session.execute(text('SELECT id FROM questions')).fetchall()])
    except Exception:
        db_existing_ids = set()

    # 查询数据库已存在的试题ID（用于防重检查）
    try:
        existing_question_ids = set([row[0] for row in db_session.execute(text('SELECT id FROM questions')).fetchall()])
    except Exception:
        existing_question_ids = set()

    # 统计重复试题数量
    duplicate_count = 0

    # 自动同步题库表
    all_bank_names = set()
    for index, row in df.iterrows():
        bank_name = str(row.get('题库名称\nquestion_bank_name', '')).strip()
        
        # 使用统一的题库名称验证函数
        is_valid, error_msg = is_valid_bank_name(bank_name)
        if is_valid:
            all_bank_names.add(bank_name)
    if all_bank_names:
        try:
            existing_banks = db_session.query(QuestionBank).filter(QuestionBank.题库名称.in_(all_bank_names)).all()
            existing_bank_names = {b.题库名称 for b in existing_banks}
            bank_cache = {b.题库名称: b.id for b in existing_banks}

            for bank_name in all_bank_names:
                if bank_name and bank_name not in existing_bank_names:
                    # 在创建题库前再次验证题库名称（双重保险）
                    is_valid, error_msg = is_valid_bank_name(bank_name)
                    if not is_valid:
                        detailed_errors.append({"type": "validation", "message": f"跳过创建无效题库名称: '{bank_name}' - {error_msg}"})
                        continue
                        
                    new_bank = QuestionBank(题库名称=bank_name)
                    db_session.add(new_bank)
                    db_session.flush()  # 刷新以获取ID
                    bank_cache[bank_name] = new_bank.id
                    existing_bank_names.add(bank_name)
            db_session.commit()
        except Exception as e:
            db_session.rollback()
            detailed_errors.append({"type": "db_sync", "message": f"同步题库表失败: {e}"})

    for index, row in df.iterrows():
        try:
            row_num = int(str(index)) + 3  # 双重转换确保类型安全
        except (ValueError, TypeError):
            row_num = 0  # 如果转换失败使用默认值
        try:
            # 基本字段验证
            question_id_str = str(row.get('试题ID\nquestion_id', '')).strip()
            bank_name = str(row.get('题库名称\nquestion_bank_name', '')).strip()

            if not question_id_str:
                detailed_errors.append({
                    "row": row_num,
                    "试题ID": question_id_str,
                    "type": "validation",
                    "message": "试题ID不能为空"
                })
                continue
            
            # 使用统一的题库名称验证函数
            is_valid, error_msg = is_valid_bank_name(bank_name)
            if not is_valid:
                detailed_errors.append({
                    "row": row_num,
                    "试题ID": question_id_str,
                    "type": "validation",
                    "message": error_msg
                })
                continue
            
            # 从缓存获取 bank_id
            bank_id = bank_cache.get(bank_name)
            if not bank_id:
                # 再次检查题库名称是否为模板字段，提供更友好的错误提示
                _, template_error_msg = is_valid_bank_name(bank_name)
                if "模板字段" in template_error_msg or "模板文件" in template_error_msg:
                    error_message = template_error_msg
                else:
                    error_message = f"题库名称 '{bank_name}' 验证失败，请检查是否为有效的题库名称"
                
                detailed_errors.append({
                    "row": row_num,
                    "试题ID": question_id_str,
                    "type": "validation",
                    "message": error_message
                })
                continue
            
            # 验证题型代码（支持两种格式：'B' 或 'B（单选题）'）
            question_type_raw = str(row.get('题型代码\nquestion_type_code', '')).strip()
            question_type = None
            
            # 首先尝试直接匹配完整格式
            if question_type_raw in STANDARD_QUESTION_TYPES.values():
                question_type = question_type_raw
            # 然后尝试匹配简单代码格式
            elif question_type_raw in STANDARD_QUESTION_TYPES:
                question_type = STANDARD_QUESTION_TYPES[question_type_raw]
            # 最后尝试提取括号前的代码进行匹配
            else:
                clean_code = question_type_raw.split('（')[0].strip()
                if clean_code in STANDARD_QUESTION_TYPES:
                    question_type = STANDARD_QUESTION_TYPES[clean_code]
            
            if not question_type:
                detailed_errors.append({
                    "row": row_num,
                    "试题ID": question_id_str,
                    "type": "validation",
                    "message": f"无效的题型代码 '{question_type_raw}'，应为: B（单选题）、G（多选题）、C（判断题）、T（填空题）、D（简答题）、U（计算题）、W（论述题）、E（案例分析题）、F（综合题）"
                })
                continue

            # 验证难度代码（支持两种格式：'1' 或 '1（很简单）'）
            difficulty_code_raw = str(row.get('难度代码\ndifficulty_code', '')).strip()
            difficulty_code = None
            
            # 首先尝试直接匹配完整格式
            if difficulty_code_raw in STANDARD_DIFFICULTY_CODES.values():
                difficulty_code = difficulty_code_raw
            # 然后尝试匹配简单代码格式
            elif difficulty_code_raw in STANDARD_DIFFICULTY_CODES:
                difficulty_code = STANDARD_DIFFICULTY_CODES[difficulty_code_raw]
            # 最后尝试提取括号前的代码进行匹配
            else:
                clean_code = difficulty_code_raw.split('（')[0].strip()
                if clean_code in STANDARD_DIFFICULTY_CODES:
                    difficulty_code = STANDARD_DIFFICULTY_CODES[clean_code]
            
            if not difficulty_code:
                detailed_errors.append({
                    "row": row_num,
                    "试题ID": question_id_str,
                    "type": "validation",
                    "message": f"无效的难度代码 '{difficulty_code_raw}'，应为: 1（很简单）、2（简单）、3（中等）、4（困难）、5（很难）"
                })
                continue

            # 验证一致性代码（支持两种格式：'1' 或 '1（很低）'，允许为空）
            consistency_code_raw = str(row.get('一致性代码\nconsistency_code', '')).strip()
            consistency_code = consistency_code_raw  # 默认保持原值
            
            # 如果不为空，则进行验证和转换
            if consistency_code_raw:
                # 首先尝试直接匹配完整格式
                if consistency_code_raw in STANDARD_CONSISTENCY_CODES.values():
                    consistency_code = consistency_code_raw
                # 然后尝试匹配简单代码格式
                elif consistency_code_raw in STANDARD_CONSISTENCY_CODES:
                    consistency_code = STANDARD_CONSISTENCY_CODES[consistency_code_raw]
                # 最后尝试提取括号前的代码进行匹配
                else:
                    clean_code = consistency_code_raw.split('（')[0].strip()
                    if clean_code in STANDARD_CONSISTENCY_CODES:
                        consistency_code = STANDARD_CONSISTENCY_CODES[clean_code]

            # 检查试题ID是否重复
            if question_id_str in existing_question_ids:
                if overwrite_duplicates:
                    # 如果允许覆盖，删除现有的重复题目
                    try:
                        existing_question = db_session.query(Question).filter_by(question_id=question_id_str).first()
                        if existing_question:
                            db_session.delete(existing_question)
                            db_session.flush()  # 立即执行删除操作
                            # 从现有ID集合中移除，允许重新导入
                            existing_question_ids.discard(question_id_str)
                    except Exception as e:
                        detailed_errors.append({
                            "row": row_num,
                            "试题ID": question_id_str,
                            "type": "database",
                            "message": f"删除重复题目时出错: {e}"
                        })
                        continue
                    # 覆盖成功，继续处理这个题目
                else:
                    # 如果不允许覆盖，跳过重复项
                    duplicate_count += 1
                    detailed_errors.append({
                        "row": row_num,
                        "试题ID": question_id_str,
                        "type": "duplicate",
                        "message": f"检测到重复试题ID，已跳过导入（试题ID: {question_id_str}）"
                    })
                    continue
            
            # 将当前试题ID添加到已存在集合中
            existing_question_ids.add(question_id_str)

            # 创建问题数据（使用Question模型的实际字段名）
            question_data = {
                'id': question_id_str,
                'question_bank_id': bank_id,
                'question_type_code': question_type,
                'question_number': str(row.get('题号\nquestion_number', '')).strip(),
                'question_stem': str(row.get('试题（题干）\nquestion_stem', '')).strip(),
                'option_a': str(row.get('试题（选项 A）\nquestion_option_a', '')).strip(),
                'option_b': str(row.get('试题（选项 B）\nquestion_option_b', '')).strip(),
                'option_c': str(row.get('试题（选项 C）\nquestion_option_c', '')).strip(),
                'option_d': str(row.get('试题（选项 D）\nquestion_option_d', '')).strip(),
                'option_e': str(row.get('试题（选项 E）\nquestion_option_e', '')).strip(),
                'image_location': str(row.get('【图】及位置\nimage_and_position', '')).strip(),
                'correct_answer': str(row.get('正确答案\ncorrect_answer', '')).strip(),
                'difficulty_code': difficulty_code,
                'consistency_code': consistency_code,
                'analysis': str(row.get('解析\nexplanation', '')).strip(),
            }

            # 必填字段验证
            if not question_data['question_stem']:
                detailed_errors.append({
                    "row": row_num,
                    "试题ID": question_id_str,
                    "type": "validation",
                    "message": "试题（题干）不能为空"
                })
                continue
                
            if not question_data['correct_answer']:
                detailed_errors.append({
                    "row": row_num,
                    "试题ID": question_id_str,
                    "type": "validation",
                    "message": "正确答案不能为空"
                })
                continue

            questions_to_add.append(question_data)
            print(f"标准导入ID: {question_id_str}")

        except Exception as e:
            detailed_errors.append({
                "row": row_num,
                "试题ID": str(row.get('ID\nID', '')).strip(),
                "type": "processing",
                "message": f"处理行数据时出错: {e}"
            })

    # 批量插入数据库
    if questions_to_add:
        try:
            for question_data in questions_to_add:
                question = Question(**question_data)
                db_session.add(question)
            db_session.commit()
            # 导入完成
        except Exception as e:
            db_session.rollback()
            detailed_errors.append({"type": "db_insert", "message": f"批量插入失败: {e}"})
            return [], detailed_errors
    
    # 添加重复试题统计信息到结果中
    if duplicate_count > 0:
        detailed_errors.insert(0, {
            "type": "info", 
            "message": f"共跳过 {duplicate_count} 道重复试题（基于试题ID判断）"
        })

    return questions_to_add, detailed_errors

def export_error_report(errors, original_filename):
    """
    导出错误报告到Excel文件
    
    Args:
        errors: 错误列表
        original_filename: 原始文件名
    
    Returns:
        str: 错误报告文件路径
    """
    import os
    from datetime import datetime
    
    # 生成错误报告文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    base_name = os.path.splitext(original_filename)[0]
    error_filename = f"error_report_{base_name}_{timestamp}.txt"
    error_filepath = os.path.join('error_reports', error_filename)
    
    # 确保error_reports目录存在
    os.makedirs('error_reports', exist_ok=True)
    
    # 写入错误报告
    newline = '\n'
    with open(error_filepath, 'w', encoding='utf-8') as f:
        f.write(f"错误报告 - {original_filename}{newline}")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{newline}")
        f.write("=" * 50 + f"{newline}{newline}")
        
        for i, error in enumerate(errors, 1):
            f.write(f"错误 {i}:{newline}")
            if isinstance(error, dict):
                if 'row' in error:
                    f.write(f"  行号: {error['row']}{newline}")
                if '试题ID' in error:
                    f.write(f"  题目ID: {error['试题ID']}{newline}")
                if 'type' in error:
                    f.write(f"  错误类型: {error['type']}{newline}")
                if 'message' in error:
                    f.write(f"  错误信息: {error['message']}{newline}")
            else:
                f.write(f"  错误信息: {str(error)}{newline}")
            f.write(newline)
    
    return error_filepath
