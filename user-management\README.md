# 用户管理模块

## 项目简介

用户管理模块是职业技能等级考试系统的核心组件，负责用户认证、授权和用户信息管理。

## 功能特性

- 用户认证（登录/登出）
- JWT令牌管理
- 用户信息管理（增删改查）
- 角色权限管理
- 密码重置
- 健康检查
- API文档自动生成

## 技术栈

- Flask 2.3.3
- Flask-RESTX 1.1.0
- Flask-SQLAlchemy 3.0.5
- Flask-JWT-Extended 4.5.2
- SQLite/PostgreSQL

## 用户角色

- `admin`: 系统管理员
- `teacher`: 教师
- `student`: 学生
- `examiner`: 考官
- `guest`: 访客

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env` 文件并根据需要修改配置。

### 3. 启动服务

```bash
python run.py
```

服务将在 `http://localhost:5001` 启动。

### 4. 访问API文档

访问 `http://localhost:5001/docs/` 查看完整的API文档。

## API接口

### 认证接口

- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `GET /api/v1/auth/me` - 获取当前用户信息
- `GET /api/v1/auth/health` - 健康检查

### 用户管理接口

- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/{id}` - 获取用户详情
- `PUT /api/v1/users/{id}` - 更新用户信息
- `DELETE /api/v1/users/{id}` - 删除用户
- `POST /api/v1/users/{id}/reset-password` - 重置用户密码

## 默认测试账户

系统启动时会自动创建以下测试账户：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 系统管理员 |
| teacher001 | teacher123 | 教师 | 测试教师账户 |
| student001 | student123 | 学生 | 测试学生账户 |
| examiner001 | examiner123 | 考官 | 测试考官账户 |
| guest001 | guest123 | 访客 | 测试访客账户 |

## 项目结构

```
user-management/
├── app/
│   ├── __init__.py          # 应用工厂
│   ├── api/                 # API接口
│   │   ├── __init__.py
│   │   ├── auth.py          # 认证接口
│   │   └── users.py         # 用户管理接口
│   ├── config/              # 配置文件
│   │   └── config.py
│   ├── models/              # 数据模型
│   │   ├── __init__.py
│   │   └── user.py
│   └── utils/               # 工具类
│       ├── __init__.py
│       ├── response.py      # 响应格式化
│       └── validators.py    # 数据验证
├── tests/                   # 测试文件
├── .env                     # 环境配置
├── requirements.txt         # 依赖包
├── run.py                   # 启动文件
└── README.md               # 项目文档
```

## 开发说明

### 数据库迁移

```bash
# 初始化迁移
flask db init

# 生成迁移文件
flask db migrate -m "Initial migration"

# 应用迁移
flask db upgrade
```

### 测试

```bash
python -m pytest tests/
```

## 部署

### 使用Gunicorn

```bash
gunicorn -w 4 -b 0.0.0.0:5001 run:app
```

### 使用Docker

```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5001
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5001", "run:app"]
```

## 注意事项

1. 生产环境请修改默认的密钥配置
2. 建议使用PostgreSQL作为生产数据库
3. 启用HTTPS以保护JWT令牌传输
4. 定期备份数据库
5. 监控服务健康状态