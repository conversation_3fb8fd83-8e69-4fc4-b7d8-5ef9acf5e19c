# -*- coding: utf-8 -*-
"""
实操任务管理模块 - 业务逻辑层
封装任务管理的核心业务逻辑和规则
"""

from typing import List, Dict, Optional, Tuple
from datetime import datetime
import os
import json
import logging
from werkzeug.utils import secure_filename
from dao import TaskCategoryDAO, PracticalTaskDAO, TaskMaterialDAO, TaskExecutionDAO, OperationLogDAO
from models import safe_print

logger = logging.getLogger(__name__)

class TaskCategoryService:
    """
    任务分类业务逻辑服务
    """
    
    @staticmethod
    def create_category(name: str, description: str, software_type: str, 
                       difficulty_level: int, user_id: int, user_name: str) -> Dict:
        """
        创建任务分类
        
        Args:
            name: 分类名称
            description: 分类描述
            software_type: 软件类型
            difficulty_level: 难度等级(1-5)
            user_id: 操作用户ID
            user_name: 操作用户名
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 验证输入
            if not name or not name.strip():
                return {"success": False, "message": "分类名称不能为空"}
            
            if difficulty_level < 1 or difficulty_level > 5:
                return {"success": False, "message": "难度等级必须在1-5之间"}
            
            # 检查名称是否已存在
            existing_categories = TaskCategoryDAO.get_all()
            for category in existing_categories:
                if category['name'].lower() == name.lower().strip():
                    return {"success": False, "message": "分类名称已存在"}
            
            # 创建分类
            category_id = TaskCategoryDAO.create(
                name.strip(), description.strip(), software_type, difficulty_level
            )
            
            # 记录操作日志
            OperationLogDAO.log_operation(
                user_id, user_name, "CREATE", "task_category", category_id,
                {"name": name, "software_type": software_type, "difficulty_level": difficulty_level}
            )
            
            return {
                "success": True,
                "message": "分类创建成功",
                "data": {"category_id": category_id}
            }
            
        except Exception as e:
            logger.error(f"创建分类失败: {str(e)}")
            return {"success": False, "message": f"创建分类失败: {str(e)}"}
    
    @staticmethod
    def get_categories_by_software(software_type: str = None) -> Dict:
        """
        获取分类列表
        
        Args:
            software_type: 软件类型过滤
            
        Returns:
            Dict: 分类列表
        """
        try:
            if software_type:
                categories = TaskCategoryDAO.get_by_software_type(software_type)
            else:
                categories = TaskCategoryDAO.get_all()
            
            # 按软件类型分组
            grouped_categories = {}
            for category in categories:
                software = category['software_type']
                if software not in grouped_categories:
                    grouped_categories[software] = []
                grouped_categories[software].append(category)
            
            return {
                "success": True,
                "data": {
                    "categories": categories,
                    "grouped": grouped_categories,
                    "total": len(categories)
                }
            }
            
        except Exception as e:
            logger.error(f"获取分类列表失败: {str(e)}")
            return {"success": False, "message": f"获取分类列表失败: {str(e)}"}
    
    @staticmethod
    def update_category(category_id: int, updates: Dict, user_id: int, user_name: str) -> Dict:
        """
        更新分类信息
        
        Args:
            category_id: 分类ID
            updates: 更新数据
            user_id: 操作用户ID
            user_name: 操作用户名
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 检查分类是否存在
            category = TaskCategoryDAO.get_by_id(category_id)
            if not category:
                return {"success": False, "message": "分类不存在"}
            
            # 验证更新数据
            if 'difficulty_level' in updates:
                if updates['difficulty_level'] < 1 or updates['difficulty_level'] > 5:
                    return {"success": False, "message": "难度等级必须在1-5之间"}
            
            # 更新分类
            success = TaskCategoryDAO.update(category_id, **updates)
            
            if success:
                # 记录操作日志
                OperationLogDAO.log_operation(
                    user_id, user_name, "UPDATE", "task_category", category_id,
                    {"updates": updates, "original": category}
                )
                
                return {"success": True, "message": "分类更新成功"}
            else:
                return {"success": False, "message": "分类更新失败"}
                
        except Exception as e:
            logger.error(f"更新分类失败: {str(e)}")
            return {"success": False, "message": f"更新分类失败: {str(e)}"}
    
    @staticmethod
    def delete_category(category_id: int, user_id: int, user_name: str) -> Dict:
        """
        删除分类
        
        Args:
            category_id: 分类ID
            user_id: 操作用户ID
            user_name: 操作用户名
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 检查分类是否存在
            category = TaskCategoryDAO.get_by_id(category_id)
            if not category:
                return {"success": False, "message": "分类不存在"}
            
            # 检查是否有关联的任务
            tasks = PracticalTaskDAO.get_by_category(category_id)
            if tasks:
                return {"success": False, "message": f"该分类下还有{len(tasks)}个任务，无法删除"}
            
            # 删除分类
            success = TaskCategoryDAO.delete(category_id)
            
            if success:
                # 记录操作日志
                OperationLogDAO.log_operation(
                    user_id, user_name, "DELETE", "task_category", category_id,
                    {"deleted_category": category}
                )
                
                return {"success": True, "message": "分类删除成功"}
            else:
                return {"success": False, "message": "分类删除失败"}
                
        except Exception as e:
            logger.error(f"删除分类失败: {str(e)}")
            return {"success": False, "message": f"删除分类失败: {str(e)}"}

class PracticalTaskService:
    """
    实操任务业务逻辑服务
    """
    
    @staticmethod
    def create_task(task_data: Dict, user_id: int, user_name: str) -> Dict:
        """
        创建实操任务
        
        Args:
            task_data: 任务数据
            user_id: 创建者ID
            user_name: 创建者名称
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 验证必需字段
            required_fields = ['name', 'description', 'category_id', 'software_version', 
                             'difficulty_level', 'estimated_duration', 'max_score']
            
            for field in required_fields:
                if field not in task_data or not task_data[field]:
                    return {"success": False, "message": f"缺少必需字段: {field}"}
            
            # 验证分类是否存在
            category = TaskCategoryDAO.get_by_id(task_data['category_id'])
            if not category:
                return {"success": False, "message": "指定的分类不存在"}
            
            # 验证数据范围
            if task_data['difficulty_level'] < 1 or task_data['difficulty_level'] > 5:
                return {"success": False, "message": "难度等级必须在1-5之间"}
            
            if task_data['estimated_duration'] <= 0:
                return {"success": False, "message": "预估时长必须大于0"}
            
            if task_data['max_score'] <= 0:
                return {"success": False, "message": "最高分数必须大于0"}
            
            # 设置默认值
            requirements = task_data.get('requirements', {})
            evaluation_criteria = task_data.get('evaluation_criteria', {})
            
            # 创建任务
            task_id = PracticalTaskDAO.create(
                name=task_data['name'],
                description=task_data['description'],
                category_id=task_data['category_id'],
                software_version=task_data['software_version'],
                difficulty_level=task_data['difficulty_level'],
                estimated_duration=task_data['estimated_duration'],
                max_score=task_data['max_score'],
                requirements=requirements,
                evaluation_criteria=evaluation_criteria,
                created_by=user_id
            )
            
            # 记录操作日志
            OperationLogDAO.log_operation(
                user_id, user_name, "CREATE", "practical_task", task_id,
                {"task_name": task_data['name'], "category_id": task_data['category_id']}
            )
            
            return {
                "success": True,
                "message": "任务创建成功",
                "data": {"task_id": task_id}
            }
            
        except Exception as e:
            logger.error(f"创建任务失败: {str(e)}")
            return {"success": False, "message": f"创建任务失败: {str(e)}"}
    
    @staticmethod
    def get_tasks(filters: Dict = None) -> Dict:
        """
        获取任务列表
        
        Args:
            filters: 过滤条件
            
        Returns:
            Dict: 任务列表
        """
        try:
            status = filters.get('status') if filters else None
            tasks = PracticalTaskDAO.get_all(status)
            
            # 获取统计信息
            stats = PracticalTaskDAO.get_statistics()
            
            return {
                "success": True,
                "data": {
                    "tasks": tasks,
                    "statistics": stats,
                    "total": len(tasks)
                }
            }
            
        except Exception as e:
            logger.error(f"获取任务列表失败: {str(e)}")
            return {"success": False, "message": f"获取任务列表失败: {str(e)}"}
    
    @staticmethod
    def get_task_detail(task_id: int) -> Dict:
        """
        获取任务详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务详情
        """
        try:
            task = PracticalTaskDAO.get_by_id(task_id)
            if not task:
                return {"success": False, "message": "任务不存在"}
            
            # 获取任务素材
            materials = TaskMaterialDAO.get_by_task(task_id)
            
            # 获取执行统计
            execution_stats = TaskExecutionDAO.get_statistics(task_id=task_id)
            
            return {
                "success": True,
                "data": {
                    "task": task,
                    "materials": materials,
                    "execution_stats": execution_stats
                }
            }
            
        except Exception as e:
            logger.error(f"获取任务详情失败: {str(e)}")
            return {"success": False, "message": f"获取任务详情失败: {str(e)}"}
    
    @staticmethod
    def update_task(task_id: int, updates: Dict, user_id: int, user_name: str) -> Dict:
        """
        更新任务信息
        
        Args:
            task_id: 任务ID
            updates: 更新数据
            user_id: 操作用户ID
            user_name: 操作用户名
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 检查任务是否存在
            task = PracticalTaskDAO.get_by_id(task_id)
            if not task:
                return {"success": False, "message": "任务不存在"}
            
            # 验证更新数据
            if 'difficulty_level' in updates:
                if updates['difficulty_level'] < 1 or updates['difficulty_level'] > 5:
                    return {"success": False, "message": "难度等级必须在1-5之间"}
            
            if 'estimated_duration' in updates and updates['estimated_duration'] <= 0:
                return {"success": False, "message": "预估时长必须大于0"}
            
            if 'max_score' in updates and updates['max_score'] <= 0:
                return {"success": False, "message": "最高分数必须大于0"}
            
            # 更新任务
            success = PracticalTaskDAO.update(task_id, **updates)
            
            if success:
                # 记录操作日志
                OperationLogDAO.log_operation(
                    user_id, user_name, "UPDATE", "practical_task", task_id,
                    {"updates": updates, "task_name": task['name']}
                )
                
                return {"success": True, "message": "任务更新成功"}
            else:
                return {"success": False, "message": "任务更新失败"}
                
        except Exception as e:
            logger.error(f"更新任务失败: {str(e)}")
            return {"success": False, "message": f"更新任务失败: {str(e)}"}
    
    @staticmethod
    def delete_task(task_id: int, user_id: int, user_name: str) -> Dict:
        """
        删除任务
        
        Args:
            task_id: 任务ID
            user_id: 操作用户ID
            user_name: 操作用户名
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 检查任务是否存在
            task = PracticalTaskDAO.get_by_id(task_id)
            if not task:
                return {"success": False, "message": "任务不存在"}
            
            # 检查是否有执行记录
            executions = TaskExecutionDAO.get_by_task(task_id)
            if executions:
                return {"success": False, "message": f"该任务有{len(executions)}条执行记录，无法删除"}
            
            # 删除相关素材文件
            materials = TaskMaterialDAO.get_by_task(task_id)
            for material in materials:
                try:
                    if os.path.exists(material['file_path']):
                        os.remove(material['file_path'])
                except Exception as e:
                    logger.warning(f"删除素材文件失败: {material['file_path']}, {str(e)}")
                
                TaskMaterialDAO.delete(material['id'])
            
            # 删除任务
            success = PracticalTaskDAO.delete(task_id)
            
            if success:
                # 记录操作日志
                OperationLogDAO.log_operation(
                    user_id, user_name, "DELETE", "practical_task", task_id,
                    {"deleted_task": task, "materials_count": len(materials)}
                )
                
                return {"success": True, "message": "任务删除成功"}
            else:
                return {"success": False, "message": "任务删除失败"}
                
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            return {"success": False, "message": f"删除任务失败: {str(e)}"}

class TaskMaterialService:
    """
    任务素材业务逻辑服务
    """
    
    UPLOAD_FOLDER = "uploads/materials"
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 
                         'zip', 'rar', '7z', 'jpg', 'jpeg', 'png', 'gif', 'bmp'}
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    
    @staticmethod
    def _allowed_file(filename: str) -> bool:
        """
        检查文件扩展名是否允许
        
        Args:
            filename: 文件名
            
        Returns:
            bool: 是否允许
        """
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in TaskMaterialService.ALLOWED_EXTENSIONS
    
    @staticmethod
    def upload_material(task_id: int, file, description: str = "", 
                       is_required: bool = True, user_id: int = None, user_name: str = "") -> Dict:
        """
        上传任务素材
        
        Args:
            task_id: 任务ID
            file: 上传的文件对象
            description: 文件描述
            is_required: 是否必需
            user_id: 上传者ID
            user_name: 上传者名称
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 检查任务是否存在
            task = PracticalTaskDAO.get_by_id(task_id)
            if not task:
                return {"success": False, "message": "任务不存在"}
            
            # 检查文件
            if not file or file.filename == '':
                return {"success": False, "message": "未选择文件"}
            
            if not TaskMaterialService._allowed_file(file.filename):
                return {"success": False, "message": "不支持的文件类型"}
            
            # 检查文件大小
            file.seek(0, os.SEEK_END)
            file_size = file.tell()
            file.seek(0)
            
            if file_size > TaskMaterialService.MAX_FILE_SIZE:
                return {"success": False, "message": "文件大小超过限制(50MB)"}
            
            # 创建上传目录
            upload_dir = os.path.join(TaskMaterialService.UPLOAD_FOLDER, str(task_id))
            os.makedirs(upload_dir, exist_ok=True)
            
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            file_path = os.path.join(upload_dir, filename)
            
            # 保存文件
            file.save(file_path)
            
            # 获取文件类型
            file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
            file_type = {
                'txt': 'text', 'pdf': 'document', 'doc': 'document', 'docx': 'document',
                'xls': 'spreadsheet', 'xlsx': 'spreadsheet', 'ppt': 'presentation', 'pptx': 'presentation',
                'zip': 'archive', 'rar': 'archive', '7z': 'archive',
                'jpg': 'image', 'jpeg': 'image', 'png': 'image', 'gif': 'image', 'bmp': 'image'
            }.get(file_ext, 'other')
            
            # 创建素材记录
            material_id = TaskMaterialDAO.create(
                task_id=task_id,
                file_name=file.filename,
                file_path=file_path,
                file_size=file_size,
                file_type=file_type,
                description=description,
                is_required=is_required,
                uploaded_by=user_id
            )
            
            # 记录操作日志
            if user_id:
                OperationLogDAO.log_operation(
                    user_id, user_name, "UPLOAD", "task_material", material_id,
                    {"task_id": task_id, "file_name": file.filename, "file_size": file_size}
                )
            
            return {
                "success": True,
                "message": "文件上传成功",
                "data": {"material_id": material_id, "file_path": file_path}
            }
            
        except Exception as e:
            logger.error(f"上传素材失败: {str(e)}")
            return {"success": False, "message": f"上传素材失败: {str(e)}"}
    
    @staticmethod
    def delete_material(material_id: int, user_id: int, user_name: str) -> Dict:
        """
        删除任务素材
        
        Args:
            material_id: 素材ID
            user_id: 操作用户ID
            user_name: 操作用户名
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 获取素材信息
            material = TaskMaterialDAO.get_by_id(material_id)
            if not material:
                return {"success": False, "message": "素材不存在"}
            
            # 删除文件
            try:
                if os.path.exists(material['file_path']):
                    os.remove(material['file_path'])
            except Exception as e:
                logger.warning(f"删除素材文件失败: {material['file_path']}, {str(e)}")
            
            # 删除数据库记录
            success = TaskMaterialDAO.delete(material_id)
            
            if success:
                # 记录操作日志
                OperationLogDAO.log_operation(
                    user_id, user_name, "DELETE", "task_material", material_id,
                    {"deleted_material": material}
                )
                
                return {"success": True, "message": "素材删除成功"}
            else:
                return {"success": False, "message": "素材删除失败"}
                
        except Exception as e:
            logger.error(f"删除素材失败: {str(e)}")
            return {"success": False, "message": f"删除素材失败: {str(e)}"}

class TaskExecutionService:
    """
    任务执行业务逻辑服务
    """
    
    @staticmethod
    def start_task_execution(task_id: int, student_id: int, student_name: str, 
                           exam_id: int = None) -> Dict:
        """
        开始任务执行
        
        Args:
            task_id: 任务ID
            student_id: 学生ID
            student_name: 学生姓名
            exam_id: 考试ID
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 检查任务是否存在
            task = PracticalTaskDAO.get_by_id(task_id)
            if not task:
                return {"success": False, "message": "任务不存在"}
            
            if task['status'] != 'active':
                return {"success": False, "message": "任务未激活，无法执行"}
            
            # 检查学生是否已有未完成的执行记录
            existing_executions = TaskExecutionDAO.get_by_student(student_id)
            for execution in existing_executions:
                if (execution['task_id'] == task_id and 
                    execution['status'] in ['pending', 'in_progress']):
                    return {"success": False, "message": "该任务已有未完成的执行记录"}
            
            # 创建执行记录
            execution_id = TaskExecutionDAO.create(task_id, student_id, student_name, exam_id)
            
            # 开始执行
            TaskExecutionDAO.start_execution(execution_id)
            
            return {
                "success": True,
                "message": "任务执行已开始",
                "data": {
                    "execution_id": execution_id,
                    "task": task,
                    "estimated_duration": task['estimated_duration']
                }
            }
            
        except Exception as e:
            logger.error(f"开始任务执行失败: {str(e)}")
            return {"success": False, "message": f"开始任务执行失败: {str(e)}"}
    
    @staticmethod
    def submit_task_execution(execution_id: int, submission_files: List[str] = None) -> Dict:
        """
        提交任务执行
        
        Args:
            execution_id: 执行记录ID
            submission_files: 提交的文件列表
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 完成执行
            success = TaskExecutionDAO.complete_execution(execution_id, submission_files)
            
            if success:
                return {"success": True, "message": "任务提交成功"}
            else:
                return {"success": False, "message": "任务提交失败"}
                
        except Exception as e:
            logger.error(f"提交任务执行失败: {str(e)}")
            return {"success": False, "message": f"提交任务执行失败: {str(e)}"}
    
    @staticmethod
    def evaluate_task_execution(execution_id: int, score: int, evaluation_details: Dict,
                              evaluator_id: int, evaluator_name: str, remarks: str = "") -> Dict:
        """
        评价任务执行
        
        Args:
            execution_id: 执行记录ID
            score: 得分
            evaluation_details: 评价详情
            evaluator_id: 评价者ID
            evaluator_name: 评价者名称
            remarks: 备注
            
        Returns:
            Dict: 操作结果
        """
        try:
            # 评价执行
            success = TaskExecutionDAO.evaluate_execution(
                execution_id, score, evaluation_details, evaluator_id, remarks
            )
            
            if success:
                # 记录操作日志
                OperationLogDAO.log_operation(
                    evaluator_id, evaluator_name, "EVALUATE", "task_execution", execution_id,
                    {"score": score, "evaluation_details": evaluation_details}
                )
                
                return {"success": True, "message": "任务评价成功"}
            else:
                return {"success": False, "message": "任务评价失败"}
                
        except Exception as e:
            logger.error(f"评价任务执行失败: {str(e)}")
            return {"success": False, "message": f"评价任务执行失败: {str(e)}"}
    
    @staticmethod
    def get_execution_report(task_id: int = None, exam_id: int = None) -> Dict:
        """
        获取执行报告
        
        Args:
            task_id: 任务ID（可选）
            exam_id: 考试ID（可选）
            
        Returns:
            Dict: 执行报告
        """
        try:
            # 获取统计信息
            stats = TaskExecutionDAO.get_statistics(task_id, exam_id)
            
            # 获取执行记录
            if task_id:
                executions = TaskExecutionDAO.get_by_task(task_id)
            else:
                # 这里需要扩展DAO方法来支持按exam_id查询
                executions = []
            
            return {
                "success": True,
                "data": {
                    "statistics": stats,
                    "executions": executions
                }
            }
            
        except Exception as e:
            logger.error(f"获取执行报告失败: {str(e)}")
            return {"success": False, "message": f"获取执行报告失败: {str(e)}"}