# -*- coding: utf-8 -*-
"""
权限控制模块
实现基于角色的访问控制（RBAC）
"""

import functools
import jwt
import sqlite3
from datetime import datetime, timedelta
from flask import request, jsonify, current_app, g
from typing import List, Dict, Optional, Callable

class Permission:
    """
    权限类，定义系统中的各种权限
    """
    # 任务管理权限
    TASK_VIEW = 'task:view'           # 查看任务
    TASK_CREATE = 'task:create'       # 创建任务
    TASK_EDIT = 'task:edit'           # 编辑任务
    TASK_DELETE = 'task:delete'       # 删除任务
    TASK_EXECUTE = 'task:execute'     # 执行任务
    
    # 分类管理权限
    CATEGORY_VIEW = 'category:view'       # 查看分类
    CATEGORY_CREATE = 'category:create'   # 创建分类
    CATEGORY_EDIT = 'category:edit'       # 编辑分类
    CATEGORY_DELETE = 'category:delete'   # 删除分类
    
    # 素材管理权限
    MATERIAL_VIEW = 'material:view'       # 查看素材
    MATERIAL_UPLOAD = 'material:upload'   # 上传素材
    MATERIAL_DOWNLOAD = 'material:download' # 下载素材
    MATERIAL_DELETE = 'material:delete'   # 删除素材
    
    # 执行记录权限
    EXECUTION_VIEW = 'execution:view'     # 查看执行记录
    EXECUTION_GRADE = 'execution:grade'   # 评分执行记录
    EXECUTION_REPORT = 'execution:report' # 生成报告
    
    # 系统管理权限
    SYSTEM_ADMIN = 'system:admin'         # 系统管理
    USER_MANAGE = 'user:manage'           # 用户管理
    LOG_VIEW = 'log:view'                 # 查看日志
    
    @classmethod
    def get_all_permissions(cls) -> List[str]:
        """
        获取所有权限列表
        
        Returns:
            List[str]: 权限列表
        """
        return [
            cls.TASK_VIEW, cls.TASK_CREATE, cls.TASK_EDIT, cls.TASK_DELETE, cls.TASK_EXECUTE,
            cls.CATEGORY_VIEW, cls.CATEGORY_CREATE, cls.CATEGORY_EDIT, cls.CATEGORY_DELETE,
            cls.MATERIAL_VIEW, cls.MATERIAL_UPLOAD, cls.MATERIAL_DOWNLOAD, cls.MATERIAL_DELETE,
            cls.EXECUTION_VIEW, cls.EXECUTION_GRADE, cls.EXECUTION_REPORT,
            cls.SYSTEM_ADMIN, cls.USER_MANAGE, cls.LOG_VIEW
        ]

class Role:
    """
    角色类，定义系统中的各种角色及其权限
    """
    # 角色定义
    ADMIN = 'admin'           # 系统管理员
    TEACHER = 'teacher'       # 教师
    STUDENT = 'student'       # 学生
    EXAMINER = 'examiner'     # 考官
    
    # 角色权限映射
    ROLE_PERMISSIONS = {
        ADMIN: Permission.get_all_permissions(),  # 管理员拥有所有权限
        TEACHER: [
            Permission.TASK_VIEW, Permission.TASK_CREATE, Permission.TASK_EDIT, Permission.TASK_DELETE,
            Permission.CATEGORY_VIEW, Permission.CATEGORY_CREATE, Permission.CATEGORY_EDIT, Permission.CATEGORY_DELETE,
            Permission.MATERIAL_VIEW, Permission.MATERIAL_UPLOAD, Permission.MATERIAL_DOWNLOAD, Permission.MATERIAL_DELETE,
            Permission.EXECUTION_VIEW, Permission.EXECUTION_GRADE, Permission.EXECUTION_REPORT,
            Permission.LOG_VIEW
        ],
        EXAMINER: [
            Permission.TASK_VIEW, Permission.TASK_EXECUTE,
            Permission.CATEGORY_VIEW,
            Permission.MATERIAL_VIEW, Permission.MATERIAL_DOWNLOAD,
            Permission.EXECUTION_VIEW, Permission.EXECUTION_GRADE, Permission.EXECUTION_REPORT
        ],
        STUDENT: [
            Permission.TASK_VIEW, Permission.TASK_EXECUTE,
            Permission.CATEGORY_VIEW,
            Permission.MATERIAL_VIEW, Permission.MATERIAL_DOWNLOAD,
            Permission.EXECUTION_VIEW
        ]
    }
    
    @classmethod
    def get_role_permissions(cls, role: str) -> List[str]:
        """
        获取角色权限
        
        Args:
            role (str): 角色名称
            
        Returns:
            List[str]: 权限列表
        """
        return cls.ROLE_PERMISSIONS.get(role, [])
    
    @classmethod
    def get_all_roles(cls) -> List[str]:
        """
        获取所有角色列表
        
        Returns:
            List[str]: 角色列表
        """
        return [cls.ADMIN, cls.TEACHER, cls.STUDENT, cls.EXAMINER]

class AuthManager:
    """
    认证管理器
    处理用户认证、权限验证等功能
    """
    
    def __init__(self, db_path: str = 'practical_tasks.db'):
        """
        初始化认证管理器
        
        Args:
            db_path (str): 数据库路径
        """
        self.db_path = db_path
        self.secret_key = 'practical-task-auth-secret-2024'
        self.token_expiry = timedelta(hours=8)  # Token有效期8小时
    
    def create_tables(self):
        """
        创建认证相关的数据表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 创建用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS auth_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    email VARCHAR(100),
                    full_name VARCHAR(100),
                    role VARCHAR(20) NOT NULL DEFAULT 'student',
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            ''')
            
            # 创建会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS auth_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    token_hash VARCHAR(255) NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (user_id) REFERENCES auth_users (id)
                )
            ''')
            
            # 创建权限日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS auth_permission_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    username VARCHAR(50),
                    action VARCHAR(100) NOT NULL,
                    resource VARCHAR(100),
                    permission VARCHAR(50),
                    result VARCHAR(20) NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES auth_users (id)
                )
            ''')
            
            conn.commit()
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def generate_token(self, user_data: Dict) -> str:
        """
        生成JWT Token
        
        Args:
            user_data (Dict): 用户数据
            
        Returns:
            str: JWT Token
        """
        payload = {
            'user_id': user_data['id'],
            'username': user_data['username'],
            'role': user_data['role'],
            'exp': datetime.utcnow() + self.token_expiry,
            'iat': datetime.utcnow()
        }
        
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_token(self, token: str) -> Optional[Dict]:
        """
        验证JWT Token
        
        Args:
            token (str): JWT Token
            
        Returns:
            Optional[Dict]: 用户数据，验证失败返回None
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    def get_user_permissions(self, user_id: int) -> List[str]:
        """
        获取用户权限列表
        
        Args:
            user_id (int): 用户ID
            
        Returns:
            List[str]: 权限列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT role FROM auth_users WHERE id = ? AND is_active = 1', (user_id,))
            result = cursor.fetchone()
            
            if result:
                role = result[0]
                return Role.get_role_permissions(role)
            
            return []
            
        except Exception:
            return []
        finally:
            conn.close()
    
    def has_permission(self, user_id: int, permission: str) -> bool:
        """
        检查用户是否具有指定权限
        
        Args:
            user_id (int): 用户ID
            permission (str): 权限名称
            
        Returns:
            bool: 是否具有权限
        """
        user_permissions = self.get_user_permissions(user_id)
        return permission in user_permissions
    
    def log_permission_check(self, user_id: Optional[int], username: Optional[str], 
                           action: str, resource: str, permission: str, 
                           result: str, ip_address: str = None, user_agent: str = None):
        """
        记录权限检查日志
        
        Args:
            user_id (Optional[int]): 用户ID
            username (Optional[str]): 用户名
            action (str): 操作
            resource (str): 资源
            permission (str): 权限
            result (str): 结果（允许/拒绝）
            ip_address (str): IP地址
            user_agent (str): 用户代理
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO auth_permission_logs 
                (user_id, username, action, resource, permission, result, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, username, action, resource, permission, result, ip_address, user_agent))
            
            conn.commit()
            
        except Exception as e:
            print(f"[!] 记录权限日志失败: {str(e)}")
        finally:
            conn.close()

# 全局认证管理器实例
auth_manager = AuthManager()

def require_permission(permission: str):
    """
    权限装饰器，要求用户具有指定权限
    
    Args:
        permission (str): 所需权限
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 获取Token
            token = None
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
            
            if not token:
                auth_manager.log_permission_check(
                    None, None, request.method, request.path, permission, '拒绝',
                    request.remote_addr, request.headers.get('User-Agent')
                )
                return jsonify({
                    'code': 401,
                    'msg': '未提供认证Token',
                    'data': {}
                }), 401
            
            # 验证Token
            user_data = auth_manager.verify_token(token)
            if not user_data:
                auth_manager.log_permission_check(
                    None, None, request.method, request.path, permission, '拒绝',
                    request.remote_addr, request.headers.get('User-Agent')
                )
                return jsonify({
                    'code': 401,
                    'msg': 'Token无效或已过期',
                    'data': {}
                }), 401
            
            # 检查权限
            user_id = user_data['user_id']
            username = user_data['username']
            
            if not auth_manager.has_permission(user_id, permission):
                auth_manager.log_permission_check(
                    user_id, username, request.method, request.path, permission, '拒绝',
                    request.remote_addr, request.headers.get('User-Agent')
                )
                return jsonify({
                    'code': 403,
                    'msg': f'权限不足，需要权限: {permission}',
                    'data': {}
                }), 403
            
            # 记录成功的权限检查
            auth_manager.log_permission_check(
                user_id, username, request.method, request.path, permission, '允许',
                request.remote_addr, request.headers.get('User-Agent')
            )
            
            # 将用户信息存储到g对象中，供视图函数使用
            g.current_user = user_data
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def require_role(role: str):
    """
    角色装饰器，要求用户具有指定角色
    
    Args:
        role (str): 所需角色
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 获取Token
            token = None
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
            
            if not token:
                return jsonify({
                    'code': 401,
                    'msg': '未提供认证Token',
                    'data': {}
                }), 401
            
            # 验证Token
            user_data = auth_manager.verify_token(token)
            if not user_data:
                return jsonify({
                    'code': 401,
                    'msg': 'Token无效或已过期',
                    'data': {}
                }), 401
            
            # 检查角色
            if user_data['role'] != role:
                return jsonify({
                    'code': 403,
                    'msg': f'角色权限不足，需要角色: {role}',
                    'data': {}
                }), 403
            
            # 将用户信息存储到g对象中
            g.current_user = user_data
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def optional_auth():
    """
    可选认证装饰器，如果提供了Token则验证，否则继续执行
    
    Returns:
        Callable: 装饰器函数
    """
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            # 获取Token
            token = None
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
            
            if token:
                # 验证Token
                user_data = auth_manager.verify_token(token)
                if user_data:
                    g.current_user = user_data
                else:
                    g.current_user = None
            else:
                g.current_user = None
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def get_current_user() -> Optional[Dict]:
    """
    获取当前用户信息
    
    Returns:
        Optional[Dict]: 当前用户信息，未登录返回None
    """
    return getattr(g, 'current_user', None)

def init_auth_system():
    """
    初始化认证系统
    创建必要的数据表和默认用户
    """
    try:
        # 创建表
        auth_manager.create_tables()
        
        # 创建默认管理员用户（如果不存在）
        conn = sqlite3.connect(auth_manager.db_path)
        cursor = conn.cursor()
        
        # 检查是否已有管理员用户
        cursor.execute('SELECT COUNT(*) FROM auth_users WHERE role = ?', (Role.ADMIN,))
        admin_count = cursor.fetchone()[0]
        
        if admin_count == 0:
            # 创建默认管理员（密码需要在实际使用时修改）
            import hashlib
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            
            cursor.execute('''
                INSERT INTO auth_users (username, password_hash, email, full_name, role)
                VALUES (?, ?, ?, ?, ?)
            ''', ('admin', password_hash, '<EMAIL>', '系统管理员', Role.ADMIN))
            
            conn.commit()
            print("[*] 创建默认管理员用户: admin/admin123")
        
        conn.close()
        print("[*] 认证系统初始化完成")
        
    except Exception as e:
        print(f"[!] 认证系统初始化失败: {str(e)}")
        raise