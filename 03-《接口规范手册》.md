## 前言

本手册基于系统基础数据字典（用户表、题库表、试卷表）制定，统一接口设计标准，确保各模块间数据交互规范、高效。所有字段名均采用 “全小写 + 下划线连接” 格式，接口遵循 RESTful 风格，适用于系统内所有模块开发与集成。
### 一、基础数据字典（基于 Excel 表头的标准化定义）

假设 Excel 表头包含以下核心字段（可根据实际表头调整），数据字典需明确字段的类型、约束和业务含义，为接口设计提供基础：

#### （1）用户表（user）

| 字段名                               | 数据类型   | 说明（约束）                                                          |
| --------------------------------- | ------ | ----------------------------------------------------------------- |
| serial_number                     | number | 序号（自增，非唯一）                                                        |
| user_name                         | string | 用户名（主键，登录账号，唯一，支持字母 / 数字 / 下划线，长度 1-50） （必填）       |
| real_name                         | string | 真实姓名（长度 1-50） （必填）                                           |
| id_card                           | string | 身份证号码（18 位，可选，加密存储） （必填）                                   |
| role                              | string | 角色（枚举值：admin = 管理员，expert = 专家，student = 考生，grader = 考评员，internal_supervisor = 内部督导员） （必填） |
| gender                            | string | 性别（枚举值：male = 男，female = 女，unknown = 未知）                          |
| date_of_birth                     | string | 出生日期（格式：yyyy-mm-dd）                                               |
| household_registration_address    | string | 户籍所在地（长度 1-200）                                                   |
| educational_level                 | string | 文化程度（枚举值：博士研究生，硕士研究生，大学本科，大学专科，中等专科，职业高中，技工学校，普通中学，初级中学，小学，高技，其他） |
| major                             | string | 所学专业（长度 1-100，初中、高中、其他无需填）                                 |
| declared_occupation               | string | 申报职业（工种）（长度 1-100）                                                |
| declared_level                    | string | 申报级别（枚举值：五级，四级，三级，二级，一级）                                |
| exam_type                         | string | 考试类型（枚举值：新考，重考，补考）                                           |
| assessment_subject                | string | 考核科目（枚举值：理论，实操，答辩）                                           |
| seat_serial_number                | number | 座次序号（整数）                                                        |
| exam_date                         | string | 考试日期（格式：yyyy-MM-dd）                                               |
| exam_session                      | string | 考试期数（长度 1-20，如 “2025 第 1 期”）                                      |
| seat_number                       | string | 座位号（长度 1-10，如 “A01”）                                              |
| email                             | string | 邮箱（符合邮箱格式，可选）                                                     |
| password                          | string | 密码（加密存储，禁止明文，长度≥6） （必填）                                      |
| phone                             | string | 手机号码（11 位数字，可选）    （必填）                                   |
| company                           | string | 工作单位（长度 1-200，可选）                                                 |
| department                        | string | 部门（长度 1-100，可选）                                                   |
| status                            | string | 状态（枚举值：待审核，审核通过，审核未通过）                                      |
| avatar_photo_link                 | string | 头像照片链接（局域网路径，可选）                                                  |
| id_card_photo_link                | string | 身份证照片链接（局域网路径，可选）                                              |
| graduation_certificate_photo_link | string | 毕业证照片链接（局域网路径，可选）                                              |
| skill_certificate_photo_link      | string | 技能证书照片链接（局域网路径，可选）                                            |
| question_bank_name                | string | 关联题库名称（长度 1-100，可选）                                               |
| test_paper_id                     | string | 关联试卷 ID（长度 1-32，可选）                                               |

### 2. 题库表（question）

| 字段名                       | 数据类型   | 说明（约束）                                                                                                                                                    |
| ------------------------- | ------ | --------------------------------------------------------------------------------------------------------------------------------------------------------- |
| question_bank_name        | string | 题库名称（长度 1-100，如“BWGL-3-LL”，必填）                                                                                                                            |
| question_id               | string | 试题 ID（唯一标识，格式：'BWGL-3-LL-B-A-B-C-001-002'，共9段，以-分隔。第1-3段为题库名称代码（如BWGL-3-LL），第4段为题型代码缩写（如B）， 第5-7段为三级代码（如A-B-C）， 第5-8段为认定点代码（如A-B-C-001），第9段为顺序号（如002），必填） |
| serial_number             | number | 序号（题库内自增，可选）                                                                                                                                              |
| identification_point_code | string | 认定点代码（长度 1-20，可选）                                                                                                                                         |
| question_type_code        | string | 题型代码（枚举值：B（单选题），G（多选题），C（判断题），T（填空题），D（简答题），U（计算题），W（论述题），E（案例分析），F（综合题），必填）                                                                              |
| question_number           | string | 题号（如 “1-1”“3.2”，长度 1-20，可选）                                                                                                                               |
| question_stem             | string | 试题（题干）（文本，支持换行，必填）                                                                                                                                        |
| question_option_a         | string | 试题（选项 A）（客观题 / 主观题必填，文本）                                                                                                                                  |
| question_option_b         | string | 试题（选项 B）（客观题 / 主观题必填，文本）                                                                                                                                  |
| question_option_c         | string | 试题（选项 C）（多选题 / 单选题必填，文本）                                                                                                                                  |
| question_option_d         | string | 试题（选项 D）（多选题 / 单选题必填，文本）                                                                                                                                  |
| question_option_e         | string | 试题（选项 E）（多选题必填，文本）                                                                                                                                        |
| image_and_position        | string | 【图】及位置（格式：“图片路径；插入位置”，如 “/images/q1.png; 题干第 2 行”，可选）                                                                                                     |
| correct_answer            | string | 正确答案（客观题用 ABC 等，无连字符，主观题用文字描述，必填）                                                                                                                         |
| difficulty_code           | string | 难度代码（枚举值：1（较易），2（易），3（中），4（较难），5（难），必填）                                                                                                                   |
| consistency_code          | string | 一致性代码（枚举值：1（差），2（较差），3（中），4（较好），5（好），可选）                                                                                                                  |
| explanation               | string | 解析（文本，可选）                                                                                                                                                 |

### 3. 试卷表（test_paper）

| 字段名                 | 数据类型     | 说明（约束）                                                                       |
| ------------------- | -------- | ---------------------------------------------------------------------------- |
| test_paper_id       | string   | 试卷 ID（唯一标识，格式：题库名称 + _  + 日期 + 序号，如" "BWGL-3-LL_20251101001"，必填）             |
| question_type_code  | string   | 题型代码（枚举值：B（单选题），G（多选题），C（判断题），T（填空题），D（简答题），U（计算题），W（论述题），E（案例分析），F（综合题），必填） |
| question_bank_name  | string   | 题库名称（长度 1-100，如“BWGL-3-LL”，必填）                                               |
| number_of_questions | number   | 题量（整数，≥1，必填）                                                                 |
| score_value         | number   | 分值（小数，保留 2 位，≥0，必填）                                                          |
| level_1_code        | string   | 1 级代码（长度 1-20，可选）                                                            |
| level_1_proportion  | number   | 1 级比重（百分比，小数，0-100，可选）                                                       |
| level_2_code        | string   | 2 级代码（长度 1-20，可选）                                                            |
| level_2_proportion  | number   | 2 级比重（百分比，小数，0-100，可选）                                                       |
| level_3_code        | string   | 3 级代码（长度 1-20，可选）                                                            |
| level_3_proportion  | number   | 3 级比重（百分比，小数，0-100，可选）                                                       |
| create_time         | datetime | 创建时间                                                                         |
| update_time         | datetime | 最后更新时间                                                                       |


### 二、简易《接口规范手册》（基于基础数据字典）

#### 1. 通用规范

- **数据格式**：所有接口请求 / 响应均使用 JSON 格式。
- **编码方式**：UTF-8。
- **URL 命名**：采用 RESTful 风格，格式为`/api/v1/[模块名]/[资源名]/[操作]`，全小写，单词间用连字符（-）分隔（如`/api/v1/user/login`）。
- **请求方法**：
    - GET：查询资源（如获取用户列表）
    - POST：创建资源（如导入用户、新增试题）
    - PUT：更新资源（如修改试卷信息）
    - DELETE：删除资源（如删除试题）
- **认证方式**：
    - 除登录接口外，所有接口需在请求头携带令牌：`Authorization: Bearer [token]`（token 由登录接口返回）。
- **响应格式**：
    
    json
    
    ```json
    {
      "code": 200,  // 状态码（200=成功，4xx=客户端错误，5xx=服务器错误）
      "msg": "操作成功",  // 提示信息
      "data": {}  // 业务数据（成功时返回，失败时可省略）
    }
    ```
    
#### 2. 核心模块接口规范

##### （1）用户管理模块

|接口功能|URL|请求方法|请求参数（JSON）|响应参数（JSON）|
|---|---|---|---|---|
|导入用户（Excel）|`/api/v1/user/import`|POST|`{"file_url": "局域网文件路径"}`|`{"success_count": 50, "fail_count": 2, "fail_list": ["2025003"]}`|
|用户登录|`/api/v1/user/login`|POST|`{"username": "zhangsan", "password": "123456"}`|`{"token": "xxx", "user_id": "2025001", "role": "student"}`|
|查询用户信息|`/api/v1/user/info`|GET|（无，通过 token 识别）|`{"user_id": "2025001", "real_name": "张三", "role": "student", "status": 1}`|

##### （2）题库管理模块

|接口功能|URL|请求方法|请求参数（JSON）|响应参数（JSON）|
|---|---|---|---|---|
|导入试题（Excel）|`/api/v1/question/import`|POST|`{"file_url": "局域网文件路径"}`|`{"success_count": 100, "fail_count": 5}`|
|查询试题列表|`/api/v1/question/list`|GET|`{"type": "single_choice", "difficulty": "easy"}`（可选参数）|`{"total": 50, "list": [{"question_id": "Q20250001", "content": "...", ...}]}`|
|获取单个试题|`/api/v1/question/detail`|GET|`{"question_id": "Q20250001"}`|`{"question_id": "Q20250001", "content": "...", "options": "...", "answer": "..."}`|

##### （3）试卷管理模块

|接口功能|URL|请求方法|请求参数（JSON）|响应参数（JSON）|
|---|---|---|---|---|
|导入试卷（Excel）|`/api/v1/paper/import`|POST|`{"file_url": "局域网文件路径"}`|`{"paper_id": "P2025001", "msg": "导入成功"}`|
|查询试卷列表|`/api/v1/paper/list`|GET|`{"status": "published"}`（可选参数）|`{"total": 10, "list": [{"paper_id": "P2025001", "paper_name": "...", ...}]}`|
|获取试卷详情|`/api/v1/paper/detail`|GET|`{"paper_id": "P2025001"}`|`{"paper_id": "P2025001", "question_ids": "...", "total_score": 100, ...}`|

### 三、实施建议

1. **数据字典校准**：用实际 Excel 表头与上述字典比对，补充缺失字段（如用户表可能有 “部门”“手机号”），删除无关字段。
2. **接口扩展**：根据实际业务增加接口（如用户批量禁用、试题批量导出），但保持通用规范一致。
3. **版本控制**：手册标注版本号（如 V1.0），后续修改时同步更新版本，避免接口混乱。

  

通过以上步骤，可快速建立基础数据标准和接口规则，为 Excel 数据导入和模块开发提供统一依据。