#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块

本模块提供文件与项目管理工具的通用工具函数。
包括文件处理、响应格式化、验证函数等。

作者: 系统开发团队
创建时间: 2024-01-01
版本: v1.0
"""

import os
import uuid
import hashlib
import mimetypes
from datetime import datetime
from functools import wraps
from flask import jsonify, request, current_app
from werkzeug.utils import secure_filename

def success_response(data=None, message="操作成功", code=200):
    """
    成功响应格式化函数
    
    参数:
        data: 响应数据
        message (str): 响应消息
        code (int): 响应状态码
        
    返回:
        Response: JSON格式的成功响应
        
    示例:
        >>> return success_response({'id': 1, 'name': '项目A'}, '获取项目成功')
        # 返回: {"code": 200, "msg": "获取项目成功", "data": {"id": 1, "name": "项目A"}}
    """
    response_data = {
        "code": code,
        "msg": message,
        "data": data if data is not None else {}
    }
    return jsonify(response_data), code

def error_response(message="操作失败", code=400, data=None):
    """
    错误响应格式化函数
    
    参数:
        message (str): 错误消息
        code (int): 错误状态码
        data: 额外的错误数据
        
    返回:
        Response: JSON格式的错误响应
        
    示例:
        >>> return error_response('项目不存在', 404)
        # 返回: {"code": 404, "msg": "项目不存在", "data": {}}
    """
    response_data = {
        "code": code,
        "msg": message,
        "data": data if data is not None else {}
    }
    return jsonify(response_data), code

def paginate_response(query, page=1, per_page=20, **kwargs):
    """
    分页响应格式化函数
    
    参数:
        query: SQLAlchemy查询对象
        page (int): 页码
        per_page (int): 每页数量
        **kwargs: 额外的响应数据
        
    返回:
        Response: JSON格式的分页响应
        
    示例:
        >>> query = Project.query.filter_by(is_deleted=False)
        >>> return paginate_response(query, page=1, per_page=10)
    """
    try:
        # 执行分页查询
        pagination = query.paginate(
            page=page,
            per_page=min(per_page, current_app.config.get('MAX_ITEMS_PER_PAGE', 100)),
            error_out=False
        )
        
        # 构建响应数据
        data = {
            'items': [item.to_dict() if hasattr(item, 'to_dict') else item for item in pagination.items],
            'pagination': {
                'page': pagination.page,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next,
                'prev_num': pagination.prev_num,
                'next_num': pagination.next_num
            }
        }
        
        # 添加额外数据
        data.update(kwargs)
        
        return success_response(data, "获取数据成功")
        
    except Exception as e:
        current_app.logger.error(f"分页查询错误: {str(e)}")
        return error_response("获取数据失败", 500)

def allowed_file(filename, allowed_extensions=None):
    """
    检查文件扩展名是否允许
    
    参数:
        filename (str): 文件名
        allowed_extensions (set): 允许的扩展名集合
        
    返回:
        bool: 如果文件扩展名允许返回True，否则返回False
        
    示例:
        >>> allowed_file('document.pdf')
        True
        >>> allowed_file('script.exe')
        False
    """
    if allowed_extensions is None:
        allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', set())
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def generate_unique_filename(original_filename):
    """
    生成唯一的文件名
    
    参数:
        original_filename (str): 原始文件名
        
    返回:
        str: 唯一的文件名
        
    示例:
        >>> unique_name = generate_unique_filename('document.pdf')
        >>> print(unique_name)  # 输出: 20240101_123456_abc123_document.pdf
    """
    # 获取文件扩展名
    name, ext = os.path.splitext(secure_filename(original_filename))
    
    # 生成时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 生成随机字符串
    random_str = str(uuid.uuid4()).replace('-', '')[:8]
    
    # 组合唯一文件名
    unique_filename = f"{timestamp}_{random_str}_{name}{ext}"
    
    return unique_filename

def get_file_hash(file_path, algorithm='md5'):
    """
    计算文件哈希值
    
    参数:
        file_path (str): 文件路径
        algorithm (str): 哈希算法，支持md5, sha1, sha256
        
    返回:
        str: 文件哈希值
        
    示例:
        >>> hash_value = get_file_hash('/path/to/file.pdf', 'md5')
        >>> print(hash_value)  # 输出: d41d8cd98f00b204e9800998ecf8427e
    """
    hash_func = getattr(hashlib, algorithm.lower(), hashlib.md5)
    hash_obj = hash_func()
    
    try:
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        return hash_obj.hexdigest()
    except Exception as e:
        current_app.logger.error(f"计算文件哈希失败: {str(e)}")
        return None

def get_file_info(file_path):
    """
    获取文件详细信息
    
    参数:
        file_path (str): 文件路径
        
    返回:
        dict: 文件信息字典
        
    示例:
        >>> info = get_file_info('/path/to/document.pdf')
        >>> print(info['size'])  # 输出文件大小
    """
    try:
        stat = os.stat(file_path)
        filename = os.path.basename(file_path)
        name, ext = os.path.splitext(filename)
        
        return {
            'filename': filename,
            'name': name,
            'extension': ext.lstrip('.').lower(),
            'size': stat.st_size,
            'mime_type': mimetypes.guess_type(file_path)[0],
            'created_time': datetime.fromtimestamp(stat.st_ctime),
            'modified_time': datetime.fromtimestamp(stat.st_mtime),
            'hash_md5': get_file_hash(file_path, 'md5')
        }
    except Exception as e:
        current_app.logger.error(f"获取文件信息失败: {str(e)}")
        return None

def format_file_size(size_bytes):
    """
    格式化文件大小为人类可读格式
    
    参数:
        size_bytes (int): 文件大小（字节）
        
    返回:
        str: 格式化的文件大小
        
    示例:
        >>> format_file_size(1536)
        '1.5 KB'
        >>> format_file_size(1048576)
        '1.0 MB'
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)
    
    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1
    
    return f"{size:.1f} {size_names[i]}"

def validate_request_data(required_fields, optional_fields=None):
    """
    验证请求数据装饰器
    
    参数:
        required_fields (list): 必需字段列表
        optional_fields (list): 可选字段列表
        
    返回:
        function: 装饰器函数
        
    示例:
        >>> @validate_request_data(['project_name', 'description'])
        ... def create_project():
        ...     # 处理创建项目逻辑
        ...     pass
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 获取请求数据
            if request.is_json:
                data = request.get_json()
            else:
                data = request.form.to_dict()
            
            if not data:
                return error_response("请求数据不能为空", 400)
            
            # 检查必需字段
            missing_fields = []
            for field in required_fields:
                if field not in data or not data[field]:
                    missing_fields.append(field)
            
            if missing_fields:
                return error_response(f"缺少必需字段: {', '.join(missing_fields)}", 400)
            
            # 过滤有效字段
            valid_fields = set(required_fields)
            if optional_fields:
                valid_fields.update(optional_fields)
            
            filtered_data = {k: v for k, v in data.items() if k in valid_fields}
            
            # 将过滤后的数据添加到kwargs中
            kwargs['validated_data'] = filtered_data
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_auth(f):
    """
    认证装饰器
    
    检查请求头中的Authorization token。
    
    参数:
        f: 被装饰的函数
        
    返回:
        function: 装饰后的函数
        
    示例:
        >>> @require_auth
        ... def get_projects():
        ...     # 需要认证的接口逻辑
        ...     pass
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            return error_response("缺少认证信息", 401)
        
        try:
            # 解析Bearer token
            token_type, token = auth_header.split(' ', 1)
            if token_type.lower() != 'bearer':
                return error_response("认证格式错误", 401)
            
            # 这里应该验证token的有效性
            # 暂时简单验证token不为空
            if not token:
                return error_response("认证令牌无效", 401)
            
            # 将token信息添加到kwargs中
            kwargs['auth_token'] = token
            
            return f(*args, **kwargs)
            
        except ValueError:
            return error_response("认证格式错误", 401)
        except Exception as e:
            current_app.logger.error(f"认证验证失败: {str(e)}")
            return error_response("认证验证失败", 401)
    
    return decorated_function

def generate_share_token(length=32):
    """
    生成分享令牌
    
    参数:
        length (int): 令牌长度
        
    返回:
        str: 分享令牌
        
    示例:
        >>> token = generate_share_token()
        >>> print(len(token))  # 输出: 32
    """
    return str(uuid.uuid4()).replace('-', '')[:length]

def safe_filename(filename):
    """
    安全的文件名处理
    
    参数:
        filename (str): 原始文件名
        
    返回:
        str: 安全的文件名
        
    示例:
        >>> safe_name = safe_filename('../../etc/passwd')
        >>> print(safe_name)  # 输出: etc_passwd
    """
    # 使用werkzeug的secure_filename
    secure_name = secure_filename(filename)
    
    # 如果文件名为空，生成一个默认名称
    if not secure_name:
        secure_name = f"file_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    return secure_name

def parse_pagination_params():
    """
    解析分页参数
    
    从请求参数中解析page和per_page参数。
    
    返回:
        tuple: (page, per_page)
        
    示例:
        >>> page, per_page = parse_pagination_params()
        >>> print(f"页码: {page}, 每页数量: {per_page}")
    """
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', current_app.config.get('ITEMS_PER_PAGE', 20)))
        
        # 限制参数范围
        page = max(1, page)
        per_page = min(max(1, per_page), current_app.config.get('MAX_ITEMS_PER_PAGE', 100))
        
        return page, per_page
        
    except (ValueError, TypeError):
        return 1, current_app.config.get('ITEMS_PER_PAGE', 20)

def parse_search_params():
    """
    解析搜索参数
    
    从请求参数中解析搜索相关参数。
    
    返回:
        dict: 搜索参数字典
        
    示例:
        >>> params = parse_search_params()
        >>> print(params['keyword'])  # 输出搜索关键词
    """
    return {
        'keyword': request.args.get('keyword', '').strip(),
        'sort_by': request.args.get('sort_by', 'create_time'),
        'sort_order': request.args.get('sort_order', 'desc'),
        'status': request.args.get('status', ''),
        'project_id': request.args.get('project_id', ''),
        'file_type': request.args.get('file_type', ''),
        'date_from': request.args.get('date_from', ''),
        'date_to': request.args.get('date_to', '')
    }

# 导出所有工具函数
__all__ = [
    'success_response',
    'error_response', 
    'paginate_response',
    'allowed_file',
    'generate_unique_filename',
    'get_file_hash',
    'get_file_info',
    'format_file_size',
    'validate_request_data',
    'require_auth',
    'generate_share_token',
    'safe_filename',
    'parse_pagination_params',
    'parse_search_params'
]