# -*- coding: utf-8 -*-
"""
评分管理模块 - 主应用文件

功能说明：
- 自动评分（客观题）
- 人工阅卷（主观题）
- 多考评员评分
- 分数校准和统计
- 评分结果管理

作者: SOLO Coding
创建时间: 2024
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from statistics import mean, stdev

from flask import Flask, request, jsonify, render_template, session, redirect, url_for
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import check_password_hash
import requests

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 初始化数据库
db = SQLAlchemy()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scoring_management.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# API网关配置
API_GATEWAY_URL = 'http://localhost:8080'
USER_MANAGEMENT_URL = 'http://localhost:5002'
EXAM_MANAGEMENT_URL = 'http://localhost:5003'
QUESTION_BANK_URL = 'http://localhost:5004'
STUDENT_EXAM_URL = 'http://localhost:5005'

class ExamScore(db.Model):
    """考试成绩表"""
    __tablename__ = 'exam_scores'
    
    id = db.Column(db.Integer, primary_key=True)
    exam_id = db.Column(db.Integer, nullable=False, comment='考试ID')
    student_id = db.Column(db.Integer, nullable=False, comment='学生ID')
    student_name = db.Column(db.String(100), nullable=False, comment='学生姓名')
    objective_score = db.Column(db.Float, default=0.0, comment='客观题得分')
    subjective_score = db.Column(db.Float, default=0.0, comment='主观题得分')
    total_score = db.Column(db.Float, default=0.0, comment='总分')
    max_score = db.Column(db.Float, nullable=False, comment='满分')
    status = db.Column(db.String(20), default='pending', comment='评分状态: pending, grading, completed')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'exam_id': self.exam_id,
            'student_id': self.student_id,
            'student_name': self.student_name,
            'objective_score': self.objective_score,
            'subjective_score': self.subjective_score,
            'total_score': self.total_score,
            'max_score': self.max_score,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class SubjectiveGrading(db.Model):
    """主观题评分表"""
    __tablename__ = 'subjective_gradings'
    
    id = db.Column(db.Integer, primary_key=True)
    score_id = db.Column(db.Integer, db.ForeignKey('exam_scores.id'), nullable=False, comment='成绩ID')
    question_id = db.Column(db.Integer, nullable=False, comment='题目ID')
    grader_id = db.Column(db.Integer, nullable=False, comment='评分员ID')
    grader_name = db.Column(db.String(100), nullable=False, comment='评分员姓名')
    score = db.Column(db.Float, nullable=False, comment='得分')
    max_score = db.Column(db.Float, nullable=False, comment='满分')
    comments = db.Column(db.Text, comment='评分备注')
    grading_round = db.Column(db.Integer, default=1, comment='评分轮次')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='评分时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'score_id': self.score_id,
            'question_id': self.question_id,
            'grader_id': self.grader_id,
            'grader_name': self.grader_name,
            'score': self.score,
            'max_score': self.max_score,
            'comments': self.comments,
            'grading_round': self.grading_round,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class GradingTask(db.Model):
    """评分任务表"""
    __tablename__ = 'grading_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    exam_id = db.Column(db.Integer, nullable=False, comment='考试ID')
    grader_id = db.Column(db.Integer, nullable=False, comment='评分员ID')
    grader_name = db.Column(db.String(100), nullable=False, comment='评分员姓名')
    task_type = db.Column(db.String(20), default='subjective', comment='任务类型: subjective')
    assigned_count = db.Column(db.Integer, default=0, comment='分配的题目数量')
    completed_count = db.Column(db.Integer, default=0, comment='已完成数量')
    status = db.Column(db.String(20), default='assigned', comment='任务状态: assigned, in_progress, completed')
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow, comment='分配时间')
    started_at = db.Column(db.DateTime, comment='开始时间')
    completed_at = db.Column(db.DateTime, comment='完成时间')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='更新时间')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'exam_id': self.exam_id,
            'grader_id': self.grader_id,
            'grader_name': self.grader_name,
            'task_type': self.task_type,
            'assigned_count': self.assigned_count,
            'completed_count': self.completed_count,
            'status': self.status,
            'assigned_at': self.assigned_at.isoformat() if self.assigned_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class ScoringService:
    """评分服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def auto_grade_objective_questions(self, session_id: int) -> bool:
        """
        自动评分客观题
        
        Args:
            session_id: 考试会话ID
            
        Returns:
            评分是否成功
        """
        try:
            # 获取考试会话信息
            session_response = requests.get(
                f'{STUDENT_EXAM_URL}/api/v1/sessions/{session_id}',
                timeout=10
            )
            
            if session_response.status_code != 200:
                self.logger.error(f"获取考试会话失败: {session_id}")
                return False
            
            session_data = session_response.json().get('data', {})
            exam_id = session_data.get('exam_id')
            student_id = session_data.get('student_id')
            answers = session_data.get('answers', {})
            
            # 获取试卷题目和标准答案
            questions_response = requests.get(
                f'{QUESTION_BANK_URL}/api/v1/exams/{exam_id}/questions',
                timeout=10
            )
            
            if questions_response.status_code != 200:
                self.logger.error(f"获取试卷题目失败: {exam_id}")
                return False
            
            questions = questions_response.json().get('data', [])
            
            # 计算客观题得分
            objective_score = 0.0
            total_objective_score = 0.0
            
            for question in questions:
                question_id = str(question.get('id'))
                question_type = question.get('type')
                score = question.get('score', 0)
                correct_answer = question.get('answer')
                
                # 只处理客观题（选择题、判断题等）
                if question_type in ['single_choice', 'multiple_choice', 'true_false']:
                    total_objective_score += score
                    
                    student_answer = answers.get(question_id)
                    if student_answer and self._check_answer(question_type, student_answer, correct_answer):
                        objective_score += score
            
            # 保存或更新成绩记录
            exam_score = ExamScore.query.filter_by(
                exam_id=exam_id,
                student_id=student_id,
                session_id=session_id
            ).first()
            
            if not exam_score:
                exam_score = ExamScore(
                    exam_id=exam_id,
                    student_id=student_id,
                    session_id=session_id,
                    objective_score=objective_score,
                    status='grading',
                    auto_graded_at=datetime.utcnow()
                )
                db.session.add(exam_score)
            else:
                exam_score.objective_score = objective_score
                exam_score.auto_graded_at = datetime.utcnow()
                exam_score.status = 'grading'
            
            db.session.commit()
            
            self.logger.info(f"自动评分完成 - 会话:{session_id}, 客观题得分:{objective_score}/{total_objective_score}")
            return True
            
        except Exception as e:
            self.logger.error(f"自动评分失败: {e}")
            db.session.rollback()
            return False
    
    def _check_answer(self, question_type: str, student_answer: Any, correct_answer: Any) -> bool:
        """
        检查答案是否正确
        
        Args:
            question_type: 题目类型
            student_answer: 学生答案
            correct_answer: 标准答案
            
        Returns:
            是否正确
        """
        if question_type == 'single_choice':
            return str(student_answer).strip().upper() == str(correct_answer).strip().upper()
        elif question_type == 'multiple_choice':
            if isinstance(student_answer, list) and isinstance(correct_answer, list):
                return set(student_answer) == set(correct_answer)
            return False
        elif question_type == 'true_false':
            return str(student_answer).strip().lower() == str(correct_answer).strip().lower()
        
        return False
    
    def assign_grading_tasks(self, exam_id: int, grader_ids: List[int]) -> bool:
        """
        分配评分任务
        
        Args:
            exam_id: 考试ID
            grader_ids: 评分员ID列表
            
        Returns:
            分配是否成功
        """
        try:
            # 获取需要人工评分的成绩记录
            scores = ExamScore.query.filter_by(exam_id=exam_id, status='grading').all()
            
            if not scores:
                self.logger.warning(f"考试 {exam_id} 没有需要评分的记录")
                return True
            
            # 平均分配给评分员
            students_per_grader = len(scores) // len(grader_ids)
            remainder = len(scores) % len(grader_ids)
            
            start_idx = 0
            for i, grader_id in enumerate(grader_ids):
                # 计算当前评分员分配的学生数量
                count = students_per_grader + (1 if i < remainder else 0)
                end_idx = start_idx + count
                
                # 获取分配给当前评分员的学生ID列表
                assigned_students = [score.student_id for score in scores[start_idx:end_idx]]
                
                # 创建评分任务
                task = GradingTask(
                    exam_id=exam_id,
                    grader_id=grader_id,
                    assigned_students=json.dumps(assigned_students)
                )
                db.session.add(task)
                
                start_idx = end_idx
            
            db.session.commit()
            
            self.logger.info(f"评分任务分配完成 - 考试:{exam_id}, 评分员数量:{len(grader_ids)}")
            return True
            
        except Exception as e:
            self.logger.error(f"分配评分任务失败: {e}")
            db.session.rollback()
            return False
    
    def submit_subjective_score(self, score_id: int, question_id: int, grader_id: int, 
                               score: float, max_score: float, comments: str = '') -> bool:
        """
        提交主观题评分
        
        Args:
            score_id: 成绩记录ID
            question_id: 题目ID
            grader_id: 评分员ID
            score: 得分
            max_score: 满分
            comments: 评分备注
            
        Returns:
            提交是否成功
        """
        try:
            # 检查是否已经评分
            existing_grading = SubjectiveGrading.query.filter_by(
                score_id=score_id,
                question_id=question_id,
                grader_id=grader_id
            ).first()
            
            if existing_grading:
                # 更新评分
                existing_grading.score = score
                existing_grading.comments = comments
                existing_grading.grading_time = datetime.utcnow()
            else:
                # 新增评分
                grading = SubjectiveGrading(
                    score_id=score_id,
                    question_id=question_id,
                    grader_id=grader_id,
                    score=score,
                    max_score=max_score,
                    comments=comments
                )
                db.session.add(grading)
            
            db.session.commit()
            
            # 检查是否所有评分员都已评分，如果是则计算最终分数
            self._calculate_final_subjective_score(score_id, question_id)
            
            self.logger.info(f"主观题评分提交成功 - 成绩ID:{score_id}, 题目:{question_id}, 评分员:{grader_id}, 得分:{score}")
            return True
            
        except Exception as e:
            self.logger.error(f"提交主观题评分失败: {e}")
            db.session.rollback()
            return False
    
    def _calculate_final_subjective_score(self, score_id: int, question_id: int):
        """
        计算主观题最终得分（多评分员平均）
        
        Args:
            score_id: 成绩记录ID
            question_id: 题目ID
        """
        try:
            # 获取该题目的所有评分
            gradings = SubjectiveGrading.query.filter_by(
                score_id=score_id,
                question_id=question_id
            ).all()
            
            if len(gradings) < 3:  # 需要3个评分员都评分
                return
            
            scores = [g.score for g in gradings]
            
            # 检查分数差异是否过大
            if len(scores) > 1:
                score_std = stdev(scores)
                score_mean = mean(scores)
                
                # 如果标准差超过平均分的20%，需要重新评阅
                if score_std > score_mean * 0.2:
                    self.logger.warning(f"主观题评分差异过大 - 成绩ID:{score_id}, 题目:{question_id}, 分数:{scores}")
                    # 这里可以触发重新评阅流程
                    return
            
            # 计算平均分作为最终得分
            final_score = mean(scores)
            
            # 更新成绩记录中的主观题得分
            exam_score = ExamScore.query.get(score_id)
            if exam_score:
                # 这里简化处理，实际应该累加所有主观题得分
                exam_score.subjective_score = final_score
                exam_score.total_score = exam_score.objective_score + exam_score.subjective_score
                exam_score.manual_graded_at = datetime.utcnow()
                exam_score.status = 'completed'
                
                db.session.commit()
                
                self.logger.info(f"主观题最终得分计算完成 - 成绩ID:{score_id}, 题目:{question_id}, 最终得分:{final_score}")
            
        except Exception as e:
            self.logger.error(f"计算主观题最终得分失败: {e}")

# 创建服务实例
scoring_service = ScoringService()

# ==================== 路由定义 ====================
# 所有路由都在register_routes函数中定义

def safe_print(text):
    """安全打印函数，处理Windows控制台编码问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

def create_app(config_class=None):
    """创建Flask应用工厂函数"""
    app = Flask(__name__)
    
    if config_class:
        app.config.from_object(config_class)
    else:
        # 默认配置
        app.secret_key = 'scoring_management_secret_key_2024'
        app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///scoring_management.db'
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)
    
    # 初始化扩展
    db.init_app(app)
    CORS(app, supports_credentials=True)
    
    # 注册路由
    register_routes(app)
    
    return app

def register_routes(app):
    """注册所有路由"""
    from flask import redirect
    
    # 页面路由
    @app.route('/')
    def index():
        return render_template('login.html')
    
    @app.route('/grading')
    def grading_page():
        if not session.get('logged_in'):
            return redirect('/')
        return render_template('grading.html')
    
    # API路由
    @app.route('/api/v1/login', methods=['POST'])
    def login():
        """用户登录接口"""
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return jsonify({
                    'code': 400,
                    'msg': '用户名和密码不能为空',
                    'data': {}
                }), 400
            
            # 调用用户管理模块验证用户
            auth_response = requests.post(
                f'{USER_MANAGEMENT_URL}/api/v1/auth/login',
                json={'username': username, 'password': password},
                timeout=10
            )
            
            if auth_response.status_code == 200:
                user_data = auth_response.json().get('data', {})
                
                # 检查用户角色权限（管理员或考评员）
                user_role = user_data.get('role')
                if user_role not in ['admin', 'grader']:
                    return jsonify({
                        'code': 403,
                        'msg': '权限不足，仅限管理员和考评员访问',
                        'data': {}
                    }), 403
                
                # 设置会话
                session['logged_in'] = True
                session['user_id'] = user_data.get('user_id')
                session['username'] = user_data.get('username')
                session['role'] = user_role
                session['grader_id'] = user_data.get('user_id')  # 评分员ID
                session.permanent = True
                
                return jsonify({
                    'code': 200,
                    'msg': '登录成功',
                    'data': {
                        'user_id': user_data.get('user_id'),
                        'username': user_data.get('username'),
                        'role': user_role,
                        'redirect_url': '/grading'
                    }
                })
            else:
                return jsonify({
                    'code': 401,
                    'msg': '用户名或密码错误',
                    'data': {}
                }), 401
                
        except requests.exceptions.RequestException as e:
            logger.error(f"用户认证请求失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '认证服务暂时不可用',
                'data': {}
            }), 500
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '登录失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/exams/<int:exam_id>/auto-score', methods=['POST'])
    def auto_score_exam(exam_id):
        """自动评分接口"""
        try:
            if not session.get('logged_in') or session.get('role') != 'admin':
                return jsonify({
                    'code': 403,
                    'msg': '权限不足，仅限管理员操作',
                    'data': {}
                }), 403
            
            success = scoring_service.auto_score_objective_questions(exam_id)
            
            if success:
                return jsonify({
                    'code': 200,
                    'msg': '自动评分完成',
                    'data': {}
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '自动评分失败',
                    'data': {}
                }), 400
            
        except Exception as e:
            logger.error(f"自动评分失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '自动评分失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/exams/<int:exam_id>/assign-grading', methods=['POST'])
    def assign_grading_tasks(exam_id):
        """分配评分任务"""
        try:
            if not session.get('logged_in') or session.get('role') != 'admin':
                return jsonify({
                    'code': 403,
                    'msg': '权限不足，仅限管理员操作',
                    'data': {}
                }), 403
            
            data = request.get_json()
            grader_ids = data.get('grader_ids', [])
            
            if not grader_ids:
                return jsonify({
                    'code': 400,
                    'msg': '评分员ID列表不能为空',
                    'data': {}
                }), 400
            
            success = scoring_service.assign_grading_tasks(exam_id, grader_ids)
            
            if success:
                return jsonify({
                    'code': 200,
                    'msg': '评分任务分配成功',
                    'data': {}
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '评分任务分配失败',
                    'data': {}
                }), 400
            
        except Exception as e:
            logger.error(f"分配评分任务失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '分配评分任务失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/grading-tasks', methods=['GET'])
    def get_grading_tasks():
        """获取评分任务列表"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            grader_id = session.get('grader_id')
            
            tasks = GradingTask.query.filter_by(grader_id=grader_id).all()
            
            return jsonify({
                'code': 200,
                'msg': '获取成功',
                'data': [task.to_dict() for task in tasks]
            })
            
        except Exception as e:
            logger.error(f"获取评分任务失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '获取评分任务失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/scores/<int:score_id>/subjective', methods=['POST'])
    def submit_subjective_score(score_id):
        """提交主观题评分"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            data = request.get_json()
            question_id = data.get('question_id')
            score = data.get('score')
            max_score = data.get('max_score')
            comments = data.get('comments', '')
            
            if question_id is None or score is None or max_score is None:
                return jsonify({
                    'code': 400,
                    'msg': '题目ID、得分和满分不能为空',
                    'data': {}
                }), 400
            
            grader_id = session.get('grader_id')
            
            success = scoring_service.submit_subjective_score(
                score_id, question_id, grader_id, score, max_score, comments
            )
            
            if success:
                return jsonify({
                    'code': 200,
                    'msg': '评分提交成功',
                    'data': {}
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '评分提交失败',
                    'data': {}
                }), 400
            
        except Exception as e:
            logger.error(f"提交主观题评分失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '提交评分失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/exams/<int:exam_id>/scores', methods=['GET'])
    def get_exam_scores(exam_id):
        """获取考试成绩列表"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            scores = ExamScore.query.filter_by(exam_id=exam_id).all()
            
            return jsonify({
                'code': 200,
                'msg': '获取成功',
                'data': [score.to_dict() for score in scores]
            })
            
        except Exception as e:
            logger.error(f"获取考试成绩失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '获取成绩失败',
                'data': {}
            }), 500
    
    @app.route('/api/v1/logout', methods=['POST'])
    def logout():
        """退出登录"""
        session.clear()
        return jsonify({
            'code': 200,
            'msg': '退出成功',
            'data': {'redirect_url': '/'}
        })
    
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        return jsonify({
            'status': 'healthy',
            'service': 'scoring-management',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        })

# 创建服务实例
scoring_service = ScoringService()

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 创建数据库表
    with app.app_context():
        db.create_all()
        safe_print("[*] 数据库表创建完成")
    
    safe_print("[*] 评分管理模块启动中...")
    safe_print("[*] 服务地址: http://0.0.0.0:5006")
    safe_print("[*] 健康检查: http://0.0.0.0:5006/api/health")
    
    app.run(
        host='0.0.0.0',
        port=5006,
        debug=True,
        use_reloader=False
    )