# -*- coding: utf-8 -*-
"""
考试平台考试成绩上报接口服务
主应用入口文件
"""

from flask import Flask, request, jsonify, send_file
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from config import Config
import logging
from datetime import datetime
import os
import pandas as pd
import json
from pathlib import Path
from werkzeug.utils import secure_filename
import requests
from data_import_tool import DataImportTool

# 初始化扩展
db = SQLAlchemy()
jwt = JWTManager()

def save_result_to_outputs(result_data, data_type, original_data=None, staff_data=None):
    """将处理结果保存到outputs文件夹，按照README.md要求的接口格式
    同时保存系统生成的上传JSON文件和模拟返回的JSON文件
    """
    try:
        # 确保outputs文件夹存在
        outputs_dir = Path('outputs')
        outputs_dir.mkdir(exist_ok=True)
        
        # 生成时间戳
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 1. 保存系统生成的上传JSON文件（发送给API的数据）
        if original_data:
            upload_filename = f"{data_type}_upload_{timestamp}.json"
            upload_file_path = outputs_dir / upload_filename
            
            # 根据README.md要求转换为标准接口格式
            api_format_data = convert_to_api_format(data_type, result_data, original_data, staff_data)
            
            with open(upload_file_path, 'w', encoding='utf-8') as f:
                json.dump(api_format_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"上传JSON文件已保存到: {upload_file_path}")
        
        # 2. 保存模拟返回的JSON文件（API的响应数据）
        response_filename = f"{data_type}_response_{timestamp}.json"
        response_file_path = outputs_dir / response_filename
        
        # 保存完整的处理结果（包含响应信息）
        with open(response_file_path, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"响应JSON文件已保存到: {response_file_path}")
        
        return {
            'upload_file': str(upload_file_path) if original_data else None,
            'response_file': str(response_file_path)
        }
    except Exception as e:
        print(f"保存结果文件失败: {str(e)}")
        return None

def convert_to_api_format(data_type, result, original_data=None, staff_data=None):
    """将处理结果转换为符合README.md要求的API接口格式"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    if data_type == 'evaluation_plan':
        # 评价计划核验服务格式 - 按照README.md要求，只包含BGB117和BGB164字段
        api_data = []
        
        # 如果有原始数据，使用实际数据，但只取前2条
        if original_data and len(original_data) > 0:
            # 限制只处理前2条记录
            records_to_process = original_data[:2]
            
            for record in records_to_process:
                plan_data = {
                    "BGB117": record.get('BGB117', record.get('备案号', 'Y000044002068')),
                    "BGB164": record.get('BGB164', record.get('评价计划编号', '2007000014'))
                }
                api_data.append(plan_data)
        else:
            # 默认数据，只返回2条记录
            for i in range(2):
                api_data.append({
                    "BGB117": "Y000044002068",
                    "BGB164": "2007000014"
                })
        
    elif data_type == 'student_info':
        # 考生信息核验服务格式 - 按照README.md要求，只包含studentList
        student_list = []
        
        # 如果有原始数据，使用实际数据
        if original_data:
            for student in original_data:
                student_data = {
                    "BGC023": student.get('BGC023', student.get('姓名', student.get('name', '未知'))),
                    "BGC118": student.get('BGC118', student.get('证件号码', student.get('id_number', '未知')))
                }
                student_list.append(student_data)
        
        api_data = {
            "studentList": student_list
        }
        
    elif data_type == 'score_report':
        # 考试成绩上报服务格式
        student_list = []
        
        # 如果有原始数据，使用实际数据
        if original_data:
            for student in original_data:
                student_data = {
                    "BGC023": student.get('BGC023', student.get('姓名', student.get('name', '未知'))),
                    "BGC118": student.get('BGC118', student.get('证件号码', student.get('id_number', '未知')))
                }
                
                # 格式化考试类型为两位字符串
                def format_exam_type(exam_type):
                    if not exam_type or str(exam_type).strip() == "":
                        return None
                    exam_type_str = str(exam_type).strip()
                    if exam_type_str == "1":
                        return "01"
                    elif exam_type_str == "2":
                        return "02"
                    elif exam_type_str in ["01", "02"]:
                        return exam_type_str
                    return None
                
                # 添加考试类型字段（只有非空时才添加）
                theory_type = format_exam_type(student.get('考试类型（理论）'))
                if theory_type:
                    student_data["BCEB43"] = theory_type
                    
                practical_type = format_exam_type(student.get('考试类型（实操）'))
                if practical_type:
                    student_data["BCEB45"] = practical_type
                    
                comprehensive_type = format_exam_type(student.get('考试类型（综合评审）'))
                if comprehensive_type:
                    student_data["BCEB47"] = comprehensive_type
                
                # 添加正考计划编号字段（只有非空时才添加，确保是字符串）
                if '正考计划编号（理论）' in student and student['正考计划编号（理论）'] and str(student['正考计划编号（理论）']).strip():
                    plan_num = str(student['正考计划编号（理论）']).strip()
                    if plan_num.endswith('.0'):
                        plan_num = plan_num[:-2]
                    student_data["BCEB44"] = plan_num
                    
                if '正考计划编号（实操）' in student and student['正考计划编号（实操）'] and str(student['正考计划编号（实操）']).strip():
                    plan_num = str(student['正考计划编号（实操）']).strip()
                    if plan_num.endswith('.0'):
                        plan_num = plan_num[:-2]
                    student_data["BCEB46"] = plan_num
                    
                if '正考计划编号（综合）' in student and student['正考计划编号（综合）'] and str(student['正考计划编号（综合）']).strip():
                    plan_num = str(student['正考计划编号（综合）']).strip()
                    if plan_num.endswith('.0'):
                        plan_num = plan_num[:-2]
                    student_data["BCEB48"] = plan_num
                
                # 添加成绩字段（只有非空时才添加）
                if '理论成绩' in student and student['理论成绩'] is not None:
                    student_data["BCEB40"] = float(student['理论成绩'])
                elif 'theory_score' in student and student['theory_score'] is not None:
                    student_data["BCEB40"] = float(student['theory_score'])
                    
                if '实操成绩' in student and student['实操成绩'] is not None:
                    student_data["BCEB41"] = float(student['实操成绩'])
                elif 'practical_score' in student and student['practical_score'] is not None:
                    student_data["BCEB41"] = float(student['practical_score'])
                    
                if '综合评审成绩' in student and student['综合评审成绩'] is not None:
                    student_data["BCEB42"] = float(student['综合评审成绩'])
                elif 'comprehensive_score' in student and student['comprehensive_score'] is not None:
                    student_data["BCEB42"] = float(student['comprehensive_score'])
                
                student_list.append(student_data)
        
        # 默认值
        bgb117 = "Y000044002068"
        bgb164 = "2007000014"
        bgc116 = "未知"
        bgb165 = current_time
        examiner_list = []
        supervisor_list = []
        
        # 从原始数据获取备案号和评价计划编号
        if original_data and len(original_data) > 0:
            first_record = original_data[0]
            bgb117 = first_record.get('备案号', bgb117)
            bgb164 = first_record.get('评价计划编号', bgb164)
        
        # 从考务人员数据获取评价业务负责人、考评员、内督员信息
        if staff_data and str(bgb164) in staff_data:
            plan_data = staff_data[str(bgb164)]
            if plan_data['评价业务负责人']['姓名']:
                bgc116 = plan_data['评价业务负责人']['姓名']
                bgb165 = plan_data['评价业务负责人']['确认时间']
            
            # 过滤考评员和内督员：根据他们负责的考试类型和学生参与的考试类型进行筛选
            # 收集学生参与的考试类型
            student_exam_types = set()
            for student in student_list:
                if student.get('BCEB43') in ['01', '02']:  # 理论考试
                    student_exam_types.add('理论')
                if student.get('BCEB45') in ['01', '02']:  # 实操考试
                    student_exam_types.add('实操')
                if student.get('BCEB47') in ['01', '02']:  # 综合评审
                    student_exam_types.add('综合评审')
            
            # 筛选考评员：只包含负责学生参与考试类型的考评员
            filtered_examiners = []
            for examiner in plan_data['考评员']:
                examiner_types = examiner.get('exam_types', [])
                # 如果考评员负责的考试类型与学生参与的考试类型有交集，则包含该考评员
                if any(exam_type in student_exam_types for exam_type in examiner_types):
                    # 移除exam_types字段，只保留API需要的字段
                    filtered_examiner = {
                        'BGC023': examiner['BGC023'],
                        'BGB165': examiner['BGB165']
                    }
                    filtered_examiners.append(filtered_examiner)
            examiner_list = filtered_examiners
            
            # 筛选内督员：只包含负责学生参与考试类型的内督员
            filtered_supervisors = []
            for supervisor in plan_data['内督员']:
                supervisor_types = supervisor.get('exam_types', [])
                # 如果内督员负责的考试类型与学生参与的考试类型有交集，则包含该内督员
                if any(exam_type in student_exam_types for exam_type in supervisor_types):
                    # 移除exam_types字段，只保留API需要的字段
                    filtered_supervisor = {
                        'BGC023': supervisor['BGC023'],
                        'BGB165': supervisor['BGB165']
                    }
                    filtered_supervisors.append(filtered_supervisor)
            supervisor_list = filtered_supervisors
        else:
            # 如果没有考务人员数据，检查是否有学生参与考试
            has_participating_students = any(
                student.get(field) in ['01', '02'] 
                for student in student_list 
                for field in ['BCEB43', 'BCEB45', 'BCEB47']
            )
            
            if has_participating_students:
                # 使用默认值
                examiner_list = [
                    {"BGC023": "李四", "BGB165": current_time},
                    {"BGC023": "王五", "BGB165": current_time}
                ]
                supervisor_list = [
                    {"BGC023": "赵六", "BGB165": current_time},
                    {"BGC023": "孙七", "BGB165": current_time}
                ]
            else:
                examiner_list = []
                supervisor_list = []
        
        api_data = {
            "BGB117": bgb117,
            "BGB164": bgb164,
            "BGC116": bgc116,
            "BGB165": bgb165,
            "studentList": student_list,
            "examinerList": examiner_list,
            "supervisorList": supervisor_list
        }
    
    else:
        # 默认格式，保持原有结构
        api_data = result
    
    return api_data

def create_app(config_class=Config):
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    CORS(app)
    
    # 配置日志
    setup_logging(app)
    
    # 注册蓝图
    from routes.evaluation_plan import evaluation_plan_bp
    from routes.student_info import student_info_bp
    from routes.score_report import score_report_bp
    from routes.query import query_bp
    from routes.logs import logs_bp
    
    app.register_blueprint(evaluation_plan_bp, url_prefix='/pjjh')
    app.register_blueprint(student_info_bp, url_prefix='/pjjh/ksxx')
    app.register_blueprint(score_report_bp, url_prefix='/pjjh/ksxx/score')
    app.register_blueprint(query_bp, url_prefix='/pjjh/ksxx')
    app.register_blueprint(logs_bp, url_prefix='/admin')
    
    # 注册根路径路由
    register_root_routes(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    return app

def setup_logging(app):
    """配置日志"""
    # 确保outputs文件夹存在
    outputs_dir = Path('outputs')
    outputs_dir.mkdir(exist_ok=True)
    
    # 为所有环境配置日志记录，包括开发环境
    if app.debug:
        # 开发环境：记录到outputs/app.log和控制台
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s %(levelname)s %(name)s %(message)s',
            handlers=[
                logging.FileHandler('outputs/app.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    else:
        # 生产环境：记录到outputs/app.log和控制台
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s %(levelname)s %(name)s %(message)s',
            handlers=[
                logging.FileHandler('outputs/app.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

def register_root_routes(app):
    """注册根路径路由"""
    
    @app.route('/')
    def index():
        """API文档首页"""
        return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试成绩上报接口系统</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .upload-section { margin: 30px 0; padding: 20px; border: 2px dashed #ddd; border-radius: 8px; text-align: center; }
        .upload-section:hover { border-color: #007bff; }
        .file-input { margin: 15px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-primary:hover { background-color: #0056b3; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-secondary:hover { background-color: #545b62; }
        .btn-outline-primary { background-color: transparent; color: #007bff; border: 1px solid #007bff; }
        .btn-outline-primary:hover { background-color: #007bff; color: white; }
        .nav-links { text-align: center; margin: 20px 0; }
        .nav-links a { margin: 0 15px; color: #007bff; text-decoration: none; }
        .nav-links a:hover { text-decoration: underline; }
        .result { margin: 20px 0; padding: 15px; border-radius: 4px; display: none; }
        .result.success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .result.error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .environment-select { margin: 10px 0; }
        select { padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .back-button { margin-bottom: 20px; }
    </style>
</head>
<body>
    <!-- 返回主控台按钮 -->
    <div class="back-button">
        <button class="btn btn-outline-primary" onclick="returnToDashboard()">
            ← 返回主控台
        </button>
    </div>
    
    <div class="container">
        <h1>考试成绩上报接口系统</h1>
        
        <div class="upload-section">
            <h3>📥 模板下载</h3>
            <p>下载标准Excel模板文件，用于数据导入</p>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;">
                <a href="/download-template/evaluation_plan" class="btn btn-secondary">📋 评价计划模板</a>
                <a href="/download-template/student_info" class="btn btn-secondary">👥 考生信息模板</a>
                <a href="/download-template/score_report" class="btn btn-secondary">📊 成绩上报模板</a>
                <a href="/download-template/staff" class="btn btn-secondary">👨‍🏫 考务人员模板</a>
            </div>
        </div>
        
        <div class="upload-section">
            <h3>📁 文件上传处理</h3>
            <p>支持上传Excel文件进行数据处理（.xlsx, .xls格式）</p>
            
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="file-input">
                    <input type="file" id="fileInput" name="file" accept=".xlsx,.xls" required>
                </div>
                
                <div class="environment-select">
                    <label for="environment">环境选择：</label>
                    <select id="environment" name="environment">
                        <option value="local">本地测试</option>
                        <option value="dev">开发环境</option>
                        <option value="prod">生产环境</option>
                    </select>
                </div>
                
                <div>
                    <button type="submit" class="btn btn-primary">🚀 上传并处理</button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">🗑️ 清空</button>
                </div>
            </form>
            
            <div id="result" class="result"></div>
        </div>
        
        <div class="nav-links">
            <a href="/api-list">📋 API接口列表</a> |
            <a href="/log-list">📊 日志查询</a> |
            <a href="/health">💚 健康检查</a>
        </div>
    </div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData();
            const fileInput = document.getElementById('fileInput');
            const environment = document.getElementById('environment').value;
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                showResult('请选择要上传的文件', 'error');
                return;
            }
            
            formData.append('file', fileInput.files[0]);
            formData.append('environment', environment);
            
            try {
                showResult('正在处理文件，请稍候...', 'success');
                
                const response = await fetch('/upload-and-process', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showResult(`✅ 处理成功！${result.message || ''}`, 'success');
                } else {
                    showResult(`❌ 处理失败：${result.message || result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误：${error.message}`, 'error');
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
        
        function clearForm() {
            document.getElementById('uploadForm').reset();
            document.getElementById('result').style.display = 'none';
        }
        
        function returnToDashboard() {
            try {
                // 尝试多种返回方式，确保能够成功返回主控台
                const dashboardUrls = [
                    'http://localhost:8000/dashboard?from_module=score_reporting',
                    '/dashboard?from_module=score_reporting',
                    'http://localhost:8000/dashboard',
                    '/dashboard'
                ];
                
                // 首先尝试使用相对路径
                window.location.href = dashboardUrls[1];
                
                // 如果3秒后还没有跳转，尝试绝对路径
                setTimeout(() => {
                    if (window.location.href.includes('exam_score_reporting_interface')) {
                        console.log('相对路径跳转失败，尝试绝对路径');
                        window.location.href = dashboardUrls[0];
                    }
                }, 3000);
                
            } catch (error) {
                console.error('返回主控台失败:', error);
                // 最后的备用方案
                window.location.href = 'http://localhost:8000/dashboard';
            }
        }
    </script>
</body>
</html>
        '''
    
    @app.route('/favicon.ico')
    def favicon():
        """网站图标"""
        return '', 204
    
    @app.route('/health')
    def health():
        """健康检查接口"""
        return {'status': 'ok', 'message': '服务运行正常'}
    
    @app.route('/download-template/<template_type>')
    def download_template(template_type):
        """下载模板文件 - 动态生成模板，不修改原始文件"""
        from openpyxl import Workbook
        from openpyxl.styles import Font, Alignment, PatternFill
        import tempfile
        
        # 定义模板结构
        template_configs = {
            'evaluation_plan': {
                'filename': '评价计划导入模板.xlsx',
                'columns': ['备案号', '评价计划编号', '评价业务负责人', '姓名', '证件号码', '负责人确认时间']
            },
            'student_info': {
                'filename': '考生信息导入模板.xlsx',
                'columns': ['备案号', '评价计划编号', '姓名', '证件号码', '联系电话', '考试类型（理论）', '考试类型（实操）', '考试类型（综合评审）']
            },
            'score_report': {
                'filename': '成绩上报导入模板.xlsx',
                'columns': ['备案号', '评价计划编号', '姓名', '证件号码', '联系电话', '考试类型（理论）', '考试类型（实操）', '考试类型（综合评审）', '理论成绩', '实操成绩', '综合评审成绩', '总成绩', '等级']
            },
            'staff': {
                'filename': '考务人员导入模板.xlsx',
                'columns': ['备案号', '评价计划编号', '角色', '姓名', '证件号码', '负责人确认时间', '考试类型（理论）', '考试类型（实操）', '考试类型（综合评审）']
            }
        }
        
        if template_type not in template_configs:
            return jsonify({'error': '模板类型不存在'}), 404
            
        config = template_configs[template_type]
        
        # 创建新的Excel文件
        wb = Workbook()
        ws = wb.active
        ws.title = "导入模板"
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 写入表头
        for col_idx, column_name in enumerate(config['columns'], 1):
            cell = ws.cell(row=1, column=col_idx, value=column_name)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
        
        # 设置列宽
        for i in range(1, len(config['columns']) + 1):
            ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = 15
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        wb.save(temp_file.name)
        temp_file.close()
        
        return send_file(
            temp_file.name,
            as_attachment=True,
            download_name=config['filename'],
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    
    # 标准 API 路由补充
    @app.route('/api/evaluation-plan/verify', methods=['POST'])
    def api_evaluation_plan_verify():
        try:
            data = request.get_json(force=True)
            environment = request.args.get('environment', 'local')
            result = process_evaluation_plan_data(data, environment)
            return jsonify(result)
        except Exception as e:
            return jsonify({'success': False, 'message': f'评价计划接口处理失败: {str(e)}'}), 500

    @app.route('/api/student-info/verify', methods=['POST'])
    def api_student_info_verify():
        try:
            data = request.get_json(force=True)
            environment = request.args.get('environment', 'local')
            result = process_student_info_data(data, environment)
            return jsonify(result)
        except Exception as e:
            return jsonify({'success': False, 'message': f'考生信息接口处理失败: {str(e)}'}), 500

    @app.route('/api/score-report/submit', methods=['POST'])
    def api_score_report_submit():
        try:
            data = request.get_json(force=True)
            environment = request.args.get('environment', 'local')
            result = process_score_report_data(data, environment)
            return jsonify(result)
        except Exception as e:
            return jsonify({'success': False, 'message': f'成绩上报接口处理失败: {str(e)}'}), 500

    @app.route('/api/query/records', methods=['POST'])
    def api_query_records():
        try:
            # 这里假设有 query_score_records 业务函数
            from routes.query import query_score_records
            data = request.get_json(force=True)
            environment = request.args.get('environment', 'local')
            result = query_score_records(data, environment)
            return jsonify(result)
        except Exception as e:
            return jsonify({'success': False, 'message': f'查询记录接口处理失败: {str(e)}'}), 500

    @app.route('/upload-and-process', methods=['POST'])
    def upload_and_process():
        """文件上传和数据处理接口"""
        try:
            # 检查是否有文件上传
            if 'file' not in request.files:
                return jsonify({'error': '没有上传文件'}), 400
            
            file = request.files['file']
            environment = request.form.get('environment', 'local')
            
            if file.filename == '':
                return jsonify({'error': '没有选择文件'}), 400
            
            # 检查文件类型
            if not file.filename.lower().endswith(('.xlsx', '.xls')):
                return jsonify({'error': '只支持Excel文件格式(.xlsx, .xls)'}), 400
            
            # 创建上传目录
            upload_dir = Path('uploads')
            upload_dir.mkdir(exist_ok=True)
            
            # 保存文件
            filename = secure_filename(file.filename)
            file_path = upload_dir / filename
            file.save(str(file_path))
            
            # 初始化数据导入工具
            import_tool = DataImportTool()
            
            # 根据文件名判断数据类型并处理
            result = {'success': False, 'message': '', 'data': None}
            print(f"开始处理文件: {filename}")
            
            try:
                if '评价计划' in filename or 'evaluation' in filename.lower():
                    # 处理评价计划数据
                    data = import_tool.import_evaluation_plan_data(str(file_path))
                    if data:
                        result = process_evaluation_plan_data(data, environment)
                elif '考生信息' in filename or 'student' in filename.lower():
                    # 处理考生信息数据
                    data = import_tool.import_student_data(str(file_path))
                    if data:
                        result = process_student_info_data(data, environment)
                elif '成绩' in filename or 'score' in filename.lower():
                    # 处理成绩数据
                    print(f"识别为成绩数据文件: {filename}")
                    # 优先检查uploads文件夹中的测试考务人员数据，否则使用templates中的原始模板
                    test_staff_path = Path('uploads/test_考务人员数据.xlsx')
                    staff_template_path = Path('templates/考务人员导入模板.xlsx')
                    
                    if test_staff_path.exists():
                        staff_file = str(test_staff_path)
                        print(f"使用测试考务人员数据: {staff_file}")
                    elif staff_template_path.exists():
                        staff_file = str(staff_template_path)
                        print(f"使用原始考务人员模板: {staff_file}")
                    else:
                        staff_file = None
                        print("未找到考务人员数据文件")
                    
                    try:
                        data = import_tool.import_score_data(str(file_path), staff_file)
                        if data:
                            result = process_score_report_data(data, environment)
                        else:
                            result = {'success': False, 'message': '成绩数据导入失败，未返回有效数据'}
                    except ValueError as ve:
                        result = {'success': False, 'message': str(ve)}
                    except Exception as e:
                        result = {'success': False, 'message': f'成绩数据处理异常: {str(e)}'}
                else:
                    # 尝试自动识别数据类型
                    df = pd.read_excel(str(file_path))
                    columns = df.columns.tolist()
                    
                    if '姓名' in columns and '证件号码' in columns:
                        if '理论成绩' in columns or '实操成绩' in columns:
                            # 优先检查uploads文件夹中的测试考务人员数据，否则使用templates中的原始模板
                            test_staff_path = Path('uploads/test_考务人员数据.xlsx')
                            staff_template_path = Path('templates/考务人员导入模板.xlsx')
                            
                            if test_staff_path.exists():
                                staff_file = str(test_staff_path)
                                print(f"使用测试考务人员数据: {staff_file}")
                            elif staff_template_path.exists():
                                staff_file = str(staff_template_path)
                                print(f"使用原始考务人员模板: {staff_file}")
                            else:
                                staff_file = None
                                print("未找到考务人员数据文件")
                            
                            data = import_tool.import_score_data(str(file_path), staff_file)
                            if data:
                                result = process_score_report_data(data, environment)
                        else:
                            # 检查是否包含评价计划特有的列
                            if '评价业务负责人' in columns or '负责人确认时间' in columns:
                                data = import_tool.import_evaluation_plan_data(str(file_path))
                                if data:
                                    result = process_evaluation_plan_data(data, environment)
                            else:
                                data = import_tool.import_student_data(str(file_path))
                                if data:
                                    result = process_student_info_data(data, environment)
                    elif '备案号' in columns and '评价计划编号' in columns:
                        data = import_tool.import_evaluation_plan_data(str(file_path))
                        if data:
                            result = process_evaluation_plan_data(data, environment)
                    else:
                        # 提供详细的错误信息和模板要求
                        template_columns = ['备案号', '评价计划编号', '姓名', '证件号码', '考试类型（理论）', '正考计划编号（理论）', '理论成绩', '考试类型（实操）', '正考计划编号（实操）', '实操成绩', '考试类型（综合评审）', '正考计划编号（综合）', '综合评审成绩']
                        user_columns = columns
                        missing_columns = [col for col in ['备案号', '评价计划编号', '姓名', '证件号码'] if col not in user_columns]
                        
                        error_msg = f"文件格式不符合标准模板要求。\n\n"
                        error_msg += f"您的文件列名: {user_columns}\n\n"
                        error_msg += f"标准成绩上报模板需要的列名: {template_columns}\n\n"
                        if missing_columns:
                            error_msg += f"缺少必需的列: {missing_columns}\n\n"
                        error_msg += "请使用 templates/成绩上报导入模板.xlsx 作为标准模板，或确保您的文件包含所有必需的列。"
                        
                        result = {'success': False, 'message': error_msg}
                
            except Exception as e:
                result = {'success': False, 'message': f'数据处理失败: {str(e)}'}
            
            # 清理临时文件
            try:
                os.remove(str(file_path))
            except:
                pass
            
            if result['success']:
                return jsonify(result), 200
            else:
                return jsonify({'success': False, 'message': result['message'], 'data': None}), 400
                
        except Exception as e:
            return jsonify({'success': False, 'message': f'处理失败: {str(e)}', 'data': None}), 500

    @app.route('/test-debug')
    def test_debug():
        print("测试调试信息输出")
        return jsonify({'message': '调试测试成功'})
    
    @app.route('/api-list')
    def api_list():
        """API接口列表页面"""
        return '''<html><head><title>API接口列表</title></head><body><h2>API接口列表</h2><ul><li>考生信息核验: /api/student-info/verify</li><li>成绩上报: /api/score-report/submit</li><li>查询记录: /api/query/records</li></ul></body></html>'''

    @app.route('/log-list')
    def log_list():
        """日志查询接口页面"""
        return '''<html><head><title>日志查询</title></head><body><h2>日志查询</h2><ul><li>API日志: /admin/api-logs</li><li>日志详情: /admin/api-logs/{id}</li><li>日志统计: /admin/api-logs/stats</li></ul></body></html>'''

def process_score_report_data(data, environment):
    """处理成绩上报数据"""
    try:
        # 批量处理，每次最多50条
        batch_size = 50
        results = []
        # 对于local环境，直接模拟成功响应（避免自调用）
        if environment == 'local':
            for i in range(0, len(data), batch_size):
                batch = data[i:i+batch_size]
                results.append({
                    'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                    'status': 200,
                    'response': {
                        'appcode': '200',
                        'msg': '成绩上报成功（本地模拟）',
                        'map': {}
                    }
                })
        else:
            # 获取目标环境的API地址
            api_url = get_api_url(environment)
            if not api_url:
                return {'success': False, 'message': f'未配置{environment}环境的API地址'}
            # 调用成绩上报接口
            endpoint = f"{api_url}/pjjh/ksxx/score/report"
            for i in range(0, len(data), batch_size):
                batch = data[i:i+batch_size]
                try:
                    # 生成签名和请求头
                    headers = generate_api_headers(batch, environment)
                    response = requests.post(endpoint, json=batch, headers=headers, timeout=30)
                    results.append({
                        'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                        'status': response.status_code,
                        'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                    })
                except Exception as e:
                    results.append({
                        'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                        'status': 'error',
                        'response': str(e)
                    })
        # 保存结果到outputs文件夹
        result_data = {
            'success': True,
            'message': f'成绩上报数据处理完成，共处理{len(data)}条记录',
            'data': {
                'type': 'score_report',
                'environment': environment,
                'total_records': len(data),
                'batch_count': len(results),
                'results': results
            }
        }
        # 提取学生数据和考务人员数据用于生成API格式
        all_students = []
        staff_data = {}
        if data and len(data) > 0:
            # 从第一个报告中提取考务人员数据
            first_report = data[0]
            plan_id = first_report.get('BGB164')
            if plan_id:
                staff_data[str(plan_id)] = {
                    '评价业务负责人': {
                        '姓名': first_report.get('BGC116', '未知'),
                        '确认时间': first_report.get('BGB165', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                    },
                    '考评员': first_report.get('examinerList', []),
                    '内督员': first_report.get('supervisorList', [])
                }
            # 提取所有学生数据
            for report in data:
                if 'studentList' in report:
                    for student in report['studentList']:
                        # 将API格式的字段名转换回原始字段名
                        student_data = {
                            '备案号': report.get('BGB117'),
                            '评价计划编号': report.get('BGB164'),
                            '姓名': student.get('BGC023'),
                            '证件号码': student.get('BGC118'),
                            '考试类型（理论）': student.get('BCEB43'),
                            '正考计划编号（理论）': student.get('BCEB44'),
                            '理论成绩': student.get('BCEB40'),
                            '考试类型（实操）': student.get('BCEB45'),
                            '正考计划编号（实操）': student.get('BCEB46'),
                            '实操成绩': student.get('BCEB41'),
                            '考试类型（综合评审）': student.get('BCEB47'),
                            '正考计划编号（综合）': student.get('BCEB48'),
                            '综合评审成绩': student.get('BCEB42')
                        }
                        all_students.append(student_data)
        save_result_to_outputs(result_data, 'score_report', all_students, staff_data)
        return result_data
    except Exception as e:
        return {'success': False, 'message': f'处理成绩上报数据失败: {str(e)}'}

def process_evaluation_plan_data(data, environment):
    """处理评价计划数据"""
    try:
        batch_size = 50
        results = []
        if environment == 'local':
            for i in range(0, len(data), batch_size):
                batch = data[i:i+batch_size]
                results.append({
                    'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                    'status': 200,
                    'response': {
                        'appcode': '200',
                        'msg': '评价计划处理成功（本地模拟）',
                        'map': {}
                    }
                })
        else:
            api_url = get_api_url(environment)
            if not api_url:
                return {'success': False, 'message': f'未配置{environment}环境的API地址'}
            endpoint = f"{api_url}/pjjh/evaluation-plan/report"
            for i in range(0, len(data), batch_size):
                batch = data[i:i+batch_size]
                try:
                    headers = generate_api_headers(batch, environment)
                    response = requests.post(endpoint, json=batch, headers=headers, timeout=30)
                    results.append({
                        'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                        'status': response.status_code,
                        'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                    })
                except Exception as e:
                    results.append({
                        'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                        'status': 'error',
                        'response': str(e)
                    })
        result_data = {
            'success': True,
            'message': f'评价计划数据处理完成，共处理{len(data)}条记录',
            'data': {
                'type': 'evaluation_plan',
                'environment': environment,
                'total_records': len(data),
                'batch_count': len(results),
                'results': results
            }
        }
        save_result_to_outputs(result_data, 'evaluation_plan', data)
        return result_data
    except Exception as e:
        return {'success': False, 'message': f'处理评价计划数据失败: {str(e)}'}

def process_student_info_data(data, environment):
    """处理考生信息数据"""
    try:
        batch_size = 50
        results = []
        if environment == 'local':
            for i in range(0, len(data), batch_size):
                batch = data[i:i+batch_size]
                results.append({
                    'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                    'status': 200,
                    'response': {
                        'appcode': '200',
                        'msg': '考生信息处理成功（本地模拟）',
                        'map': {}
                    }
                })
        else:
            api_url = get_api_url(environment)
            if not api_url:
                return {'success': False, 'message': f'未配置{environment}环境的API地址'}
            endpoint = f"{api_url}/pjjh/student-info/report"
            for i in range(0, len(data), batch_size):
                batch = data[i:i+batch_size]
                try:
                    headers = generate_api_headers(batch, environment)
                    response = requests.post(endpoint, json=batch, headers=headers, timeout=30)
                    results.append({
                        'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                        'status': response.status_code,
                        'response': response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                    })
                except Exception as e:
                    results.append({
                        'batch': f'{i+1}-{min(i+batch_size, len(data))}',
                        'status': 'error',
                        'response': str(e)
                    })
        result_data = {
            'success': True,
            'message': f'考生信息数据处理完成，共处理{len(data)}条记录',
            'data': {
                'type': 'student_info',
                'environment': environment,
                'total_records': len(data),
                'batch_count': len(results),
                'results': results
            }
        }
        save_result_to_outputs(result_data, 'student_info', data)
        return result_data
    except Exception as e:
        return {'success': False, 'message': f'处理考生信息数据失败: {str(e)}'}

def generate_api_headers(data, environment):
    """生成API请求头，包括签名和认证信息"""
    import hashlib
    import hmac
    from flask import current_app
    
    # 根据环境获取appid
    app_configs = {
        'local': 'local_test_app',
        'test': 'test_app_id', 
        'prod': 'prod_app_id'
    }
    
    app_id = app_configs.get(environment, 'local_test_app')
    
    # 使用配置文件中的签名密钥，与认证中间件保持一致
    secret_key = current_app.config['API_SIGNATURE_SECRET']
    
    # 构建签名字符串（与认证中间件逻辑一致）
    if isinstance(data, dict):
        sorted_params = sorted(data.items())
    else:
        sorted_params = []
    
    query_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_string = f'{query_string}&appid={app_id}&secret={secret_key}'
    
    # 生成HMAC-SHA256签名
    signature = hmac.new(
        secret_key.encode('utf-8'),
        sign_string.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()
    
    # 返回请求头
    return {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'Exam-Score-Reporting-System/1.0',
        'sign': signature,
        'appid': app_id
    }

def get_api_url(environment):
    """获取API地址"""
    # API地址配置
    api_urls = {
        'local': 'http://127.0.0.1:5002',
        'test': 'http://test-api.example.com',  # 测试环境API地址
        'prod': 'http://prod-api.example.com'  # 生产环境API地址
    }
    
    return api_urls.get(environment)

def register_error_handlers(app):
    """注册错误处理器"""
    @app.errorhandler(400)
    def bad_request(error):
        return {
            'appcode': '5001',
            'msg': '请求参数错误',
            'map': {}
        }, 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return {
            'appcode': '401',
            'msg': '请求未授权',
            'map': {}
        }, 401
    
    @app.errorhandler(404)
    def not_found(error):
        # 动态获取所有可用的路由
        endpoints = {}
        for rule in app.url_map.iter_rules():
            if rule.endpoint != 'static':
                # 将路由规则转换为更友好的格式
                route_name = rule.endpoint.replace('.', '_')
                endpoints[route_name] = rule.rule
        
        return {
            'appcode': '404',
            'msg': '请求的资源不存在',
            'map': {},
            'available_endpoints': endpoints
        }, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return {
            'appcode': '500',
            'msg': '服务器内部错误',
            'map': {}
        }, 500

if __name__ == '__main__':
    from config import config
    app = create_app(config['development'])
    app.run(debug=True, host='0.0.0.0', port=5008)