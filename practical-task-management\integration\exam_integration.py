# -*- coding: utf-8 -*-
"""
考试编排模块集成接口
提供与考试编排模块的数据交互和业务集成
"""

import requests
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dao import PracticalTaskDAO, TaskExecutionDAO
from models import DatabaseManager

class ExamIntegrationService:
    """
    考试编排模块集成服务
    负责与考试编排模块的数据交互和业务协调
    """
    
    def __init__(self, exam_service_url: str = 'http://localhost:8003'):
        """
        初始化集成服务
        
        参数:
            exam_service_url: 考试编排模块服务地址
        """
        self.exam_service_url = exam_service_url.rstrip('/')
        self.db_manager = DatabaseManager()
        self.task_dao = PracticalTaskDAO(self.db_manager)
        self.execution_dao = TaskExecutionDAO(self.db_manager)
        self.timeout = 30  # 请求超时时间（秒）
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     headers: Optional[Dict] = None) -> Optional[Dict]:
        """
        发送HTTP请求到考试编排模块
        
        参数:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            headers: 请求头
        
        返回:
            响应数据或None
        """
        try:
            url = f"{self.exam_service_url}{endpoint}"
            
            # 设置默认请求头
            default_headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            if headers:
                default_headers.update(headers)
            
            # 发送请求
            if method.upper() == 'GET':
                response = requests.get(url, headers=default_headers, 
                                      params=data, timeout=self.timeout)
            elif method.upper() == 'POST':
                response = requests.post(url, headers=default_headers, 
                                       json=data, timeout=self.timeout)
            elif method.upper() == 'PUT':
                response = requests.put(url, headers=default_headers, 
                                      json=data, timeout=self.timeout)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=default_headers, 
                                         timeout=self.timeout)
            else:
                return None
            
            # 检查响应状态
            if response.status_code == 200:
                return response.json()
            else:
                print(f"请求失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"请求异常: {str(e)}")
            return None
        except Exception as e:
            print(f"处理响应异常: {str(e)}")
            return None
    
    def get_exam_info(self, exam_id: int) -> Optional[Dict]:
        """
        获取考试信息
        
        参数:
            exam_id: 考试ID
        
        返回:
            考试信息字典或None
        """
        endpoint = f"/api/v1/exams/{exam_id}"
        return self._make_request('GET', endpoint)
    
    def get_exam_schedule(self, exam_id: int) -> Optional[Dict]:
        """
        获取考试安排信息
        
        参数:
            exam_id: 考试ID
        
        返回:
            考试安排信息或None
        """
        endpoint = f"/api/v1/exams/{exam_id}/schedule"
        return self._make_request('GET', endpoint)
    
    def get_exam_students(self, exam_id: int) -> List[Dict]:
        """
        获取考试学生列表
        
        参数:
            exam_id: 考试ID
        
        返回:
            学生列表
        """
        endpoint = f"/api/v1/exams/{exam_id}/students"
        response = self._make_request('GET', endpoint)
        
        if response and 'data' in response:
            return response['data']
        return []
    
    def register_practical_tasks_for_exam(self, exam_id: int, task_ids: List[int]) -> bool:
        """
        为考试注册实操任务
        
        参数:
            exam_id: 考试ID
            task_ids: 任务ID列表
        
        返回:
            是否注册成功
        """
        try:
            # 验证任务是否存在
            valid_tasks = []
            for task_id in task_ids:
                task = self.task_dao.get_by_id(task_id)
                if task and task['status'] == 'active':
                    valid_tasks.append({
                        'task_id': task_id,
                        'task_name': task['task_name'],
                        'task_type': task['task_type'],
                        'difficulty_level': task['difficulty_level'],
                        'estimated_duration': task['estimated_duration'],
                        'max_score': task['max_score']
                    })
            
            if not valid_tasks:
                return False
            
            # 向考试编排模块注册任务
            endpoint = f"/api/v1/exams/{exam_id}/practical-tasks"
            data = {
                'tasks': valid_tasks,
                'registration_time': datetime.now().isoformat(),
                'source_module': 'practical-task-management'
            }
            
            response = self._make_request('POST', endpoint, data)
            return response is not None
            
        except Exception as e:
            print(f"注册实操任务失败: {str(e)}")
            return False
    
    def get_exam_practical_tasks(self, exam_id: int) -> List[Dict]:
        """
        获取考试的实操任务列表
        
        参数:
            exam_id: 考试ID
        
        返回:
            实操任务列表
        """
        endpoint = f"/api/v1/exams/{exam_id}/practical-tasks"
        response = self._make_request('GET', endpoint)
        
        if response and 'data' in response:
            return response['data']
        return []
    
    def start_practical_exam_session(self, exam_id: int, student_id: int, 
                                   task_ids: List[int]) -> Optional[Dict]:
        """
        开始实操考试会话
        
        参数:
            exam_id: 考试ID
            student_id: 学生ID
            task_ids: 任务ID列表
        
        返回:
            会话信息或None
        """
        try:
            # 为每个任务创建执行记录
            execution_ids = []
            for task_id in task_ids:
                execution_data = {
                    'task_id': task_id,
                    'student_id': student_id,
                    'exam_id': exam_id,
                    'status': 'pending',
                    'start_time': datetime.now().isoformat(),
                    'environment_info': '{}'
                }
                
                execution_id = self.execution_dao.create(execution_data)
                if execution_id:
                    execution_ids.append(execution_id)
            
            if not execution_ids:
                return None
            
            # 通知考试编排模块会话开始
            endpoint = f"/api/v1/exams/{exam_id}/sessions/start"
            data = {
                'student_id': student_id,
                'session_type': 'practical',
                'task_execution_ids': execution_ids,
                'start_time': datetime.now().isoformat()
            }
            
            response = self._make_request('POST', endpoint, data)
            
            if response:
                return {
                    'session_id': response.get('session_id'),
                    'execution_ids': execution_ids,
                    'start_time': data['start_time']
                }
            
            return None
            
        except Exception as e:
            print(f"开始实操考试会话失败: {str(e)}")
            return None
    
    def submit_practical_exam_results(self, exam_id: int, student_id: int, 
                                    execution_results: List[Dict]) -> bool:
        """
        提交实操考试结果
        
        参数:
            exam_id: 考试ID
            student_id: 学生ID
            execution_results: 执行结果列表
        
        返回:
            是否提交成功
        """
        try:
            # 格式化结果数据
            formatted_results = []
            for result in execution_results:
                formatted_result = {
                    'execution_id': result.get('execution_id'),
                    'task_id': result.get('task_id'),
                    'score': result.get('score', 0),
                    'max_score': result.get('max_score', 100),
                    'completion_status': result.get('status', 'completed'),
                    'submission_time': result.get('submission_time', datetime.now().isoformat()),
                    'evaluation_details': result.get('evaluation_details', {})
                }
                formatted_results.append(formatted_result)
            
            # 提交到考试编排模块
            endpoint = f"/api/v1/exams/{exam_id}/practical-results"
            data = {
                'student_id': student_id,
                'results': formatted_results,
                'submission_time': datetime.now().isoformat(),
                'total_score': sum(r.get('score', 0) for r in formatted_results),
                'max_total_score': sum(r.get('max_score', 100) for r in formatted_results)
            }
            
            response = self._make_request('POST', endpoint, data)
            return response is not None
            
        except Exception as e:
            print(f"提交实操考试结果失败: {str(e)}")
            return False
    
    def get_student_exam_status(self, exam_id: int, student_id: int) -> Optional[Dict]:
        """
        获取学生考试状态
        
        参数:
            exam_id: 考试ID
            student_id: 学生ID
        
        返回:
            考试状态信息或None
        """
        endpoint = f"/api/v1/exams/{exam_id}/students/{student_id}/status"
        return self._make_request('GET', endpoint)
    
    def sync_exam_data(self, exam_id: int) -> Dict:
        """
        同步考试数据
        
        参数:
            exam_id: 考试ID
        
        返回:
            同步结果
        """
        try:
            sync_result = {
                'exam_id': exam_id,
                'sync_time': datetime.now().isoformat(),
                'success': False,
                'synced_data': {},
                'errors': []
            }
            
            # 获取考试信息
            exam_info = self.get_exam_info(exam_id)
            if exam_info:
                sync_result['synced_data']['exam_info'] = exam_info
            else:
                sync_result['errors'].append('无法获取考试信息')
            
            # 获取考试安排
            exam_schedule = self.get_exam_schedule(exam_id)
            if exam_schedule:
                sync_result['synced_data']['exam_schedule'] = exam_schedule
            else:
                sync_result['errors'].append('无法获取考试安排')
            
            # 获取学生列表
            students = self.get_exam_students(exam_id)
            if students:
                sync_result['synced_data']['students'] = students
                sync_result['synced_data']['student_count'] = len(students)
            else:
                sync_result['errors'].append('无法获取学生列表')
            
            # 获取实操任务
            practical_tasks = self.get_exam_practical_tasks(exam_id)
            if practical_tasks:
                sync_result['synced_data']['practical_tasks'] = practical_tasks
                sync_result['synced_data']['task_count'] = len(practical_tasks)
            
            # 判断同步是否成功
            sync_result['success'] = len(sync_result['errors']) == 0
            
            return sync_result
            
        except Exception as e:
            return {
                'exam_id': exam_id,
                'sync_time': datetime.now().isoformat(),
                'success': False,
                'synced_data': {},
                'errors': [f'同步异常: {str(e)}']
            }
    
    def validate_exam_integration(self) -> Dict:
        """
        验证考试编排模块集成状态
        
        返回:
            验证结果
        """
        try:
            validation_result = {
                'integration_status': 'unknown',
                'service_available': False,
                'api_endpoints': {},
                'validation_time': datetime.now().isoformat(),
                'errors': []
            }
            
            # 测试服务连通性
            health_response = self._make_request('GET', '/api/health')
            if health_response:
                validation_result['service_available'] = True
                validation_result['integration_status'] = 'connected'
            else:
                validation_result['errors'].append('考试编排模块服务不可用')
                validation_result['integration_status'] = 'disconnected'
                return validation_result
            
            # 测试关键API端点
            test_endpoints = [
                '/api/v1/exams',
                '/api/v1/exams/1',
                '/api/v1/exams/1/students',
                '/api/v1/exams/1/practical-tasks'
            ]
            
            for endpoint in test_endpoints:
                try:
                    response = self._make_request('GET', endpoint)
                    validation_result['api_endpoints'][endpoint] = {
                        'available': response is not None,
                        'status': 'ok' if response else 'error'
                    }
                except Exception as e:
                    validation_result['api_endpoints'][endpoint] = {
                        'available': False,
                        'status': 'error',
                        'error': str(e)
                    }
            
            # 计算整体状态
            available_count = sum(1 for ep in validation_result['api_endpoints'].values() 
                                if ep['available'])
            total_count = len(validation_result['api_endpoints'])
            
            if available_count == total_count:
                validation_result['integration_status'] = 'fully_integrated'
            elif available_count > 0:
                validation_result['integration_status'] = 'partially_integrated'
            else:
                validation_result['integration_status'] = 'integration_failed'
                validation_result['errors'].append('所有API端点都不可用')
            
            return validation_result
            
        except Exception as e:
            return {
                'integration_status': 'error',
                'service_available': False,
                'api_endpoints': {},
                'validation_time': datetime.now().isoformat(),
                'errors': [f'验证异常: {str(e)}']
            }
    
    def get_integration_statistics(self) -> Dict:
        """
        获取集成统计信息
        
        返回:
            统计信息
        """
        try:
            # 获取本地执行记录统计
            local_stats = self.execution_dao.get_statistics()
            
            # 获取远程考试统计（如果可用）
            remote_stats = {}
            try:
                response = self._make_request('GET', '/api/v1/statistics/practical-exams')
                if response and 'data' in response:
                    remote_stats = response['data']
            except Exception:
                pass
            
            return {
                'local_statistics': local_stats,
                'remote_statistics': remote_stats,
                'integration_health': self.validate_exam_integration(),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'local_statistics': {},
                'remote_statistics': {},
                'integration_health': {'status': 'error', 'error': str(e)},
                'last_updated': datetime.now().isoformat()
            }