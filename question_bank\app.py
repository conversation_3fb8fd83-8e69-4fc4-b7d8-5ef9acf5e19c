from flask import Flask, request, render_template_string, redirect, url_for, flash, jsonify, send_file, send_from_directory
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, joinedload
from werkzeug.utils import secure_filename
from flask_wtf.csrf import CSRFProtect
import os
import pandas as pd
from io import BytesIO
import sqlite3
import mimetypes
import re
import time
from models import Base, Question, Paper, PaperQuestion, QuestionGroup, QuestionBank
from excel_importer import import_questions_from_excel_standard as import_questions_from_excel, export_error_report, STANDARD_EXPECTED_COLUMNS as EXPECTED_COLUMNS
from excel_exporter import export_db_questions_to_excel
from paper_generator import PaperGenerator
import datetime
import json
from openpyxl import Workbook
from docx import Document
from json_importer import import_questions_from_json

app = Flask(__name__)
# 使用与主控台相同的secret_key，确保session能够共享
app.secret_key = 'exam-system-secret-key-2024'

# 配置session支持跨域访问，与主控台保持一致
app.config['SESSION_COOKIE_DOMAIN'] = 'localhost'
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['SESSION_COOKIE_SECURE'] = False
app.config['SESSION_COOKIE_HTTPONLY'] = True

# 启用 CSRF 保护
csrf = CSRFProtect(app)
app.config['UPLOAD_FOLDER'] = 'uploads'
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# 添加CSRF token到模板上下文
@app.context_processor
def inject_csrf_token():
    from flask_wtf.csrf import generate_csrf
    return dict(csrf_token=generate_csrf)

# 数据库配置
# 优先使用环境变量，没有则使用SQLite作为开发数据库
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///local_dev.db')

try:
    engine = create_engine(DATABASE_URL)
    with engine.connect() as connection:
        print("Database connection successful")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
except Exception as e:
    print(f"Database connection failed: {e}")
    raise

# 文件上传白名单
ALLOWED_EXTENSIONS = {'xlsx'}
ALLOWED_MIME_TYPES = {
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
}

# 文件大小限制 (10MB)
MAX_FILE_SIZE = 10 * 1024 * 1024

def allowed_file(filename):
    """检查文件扩展名和MIME类型是否在允许列表中"""
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS)

def cleanup_old_files():
    """清理超过24小时的上传文件"""
    upload_folder = os.path.join(os.getcwd(), 'uploads')
    if not os.path.exists(upload_folder):
        return
    
    current_time = time.time()
    for filename in os.listdir(upload_folder):
        filepath = os.path.join(upload_folder, filename)
        if os.path.isfile(filepath):
            # 检查文件是否超过24小时
            if current_time - os.path.getmtime(filepath) > 24 * 3600:
                try:
                    os.remove(filepath)
                    print(f"已清理旧文件: {filename}")
                except Exception as e:
                    print(f"清理文件失败 {filename}: {e}")

def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        return db
    except Exception as e:
        db.close()
        raise e

def close_db(db):
    """关闭数据库会话"""
    if db:
        db.close()

# SQL注入防护
def sanitize_input(input_str):
    """基本输入清理函数"""
    if not input_str:
        return ""
    # 移除潜在的SQL注入字符
    sanitized = re.sub(r'[;\'"\\]', '', input_str)
    return sanitized.strip()

# 定义内联模板
index_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理系统</title>
    <script>
        function returnToMainConsole() {
            // 尝试通过postMessage与主控台通信
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({type: 'navigate', url: '/dashboard'}, '*');
            } else {
                // 直接跳转到主控台dashboard，避免根路径清除session
                window.location.href = 'http://localhost:8000/dashboard';
            }
        }
    </script>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .flashes { 
            list-style-type: none; 
            padding: 0; 
            margin-bottom: 20px;
        }
        .flashes li { 
            margin-bottom: 10px; 
            padding: 12px; 
            border-radius: 6px; 
            border-left: 4px solid;
        }
        .flashes .error { 
            background-color: #f8d7da; 
            color: #721c24; 
            border-color: #dc3545;
        }
        .flashes .success { 
            background-color: #d4edda; 
            color: #155724; 
            border-color: #28a745;
        }
        .flashes .warning { 
            background-color: #fff3cd; 
            color: #856404; 
            border-color: #ffc107;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #1e7e34;
        }
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background-color: #e0a800;
        }
        .btn-info {
            background-color: #17a2b8;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .btn-primary {
            background-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
        .btn-danger {
            background-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 20px; 
            background: white;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 12px; 
            text-align: left; 
        }
        th { 
            background-color: #f8f9fa; 
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e9ecef;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .module-section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .module-title {
            font-size: 1.5em;
            margin-bottom: 15px;
            color: #343a40;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .module-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 题库管理系统</h1>
            <p>专业的题库导入和管理平台</p>
        </div>
        
        <div class="nav-tabs">
            <a href="javascript:void(0)" onclick="returnToMainConsole()" class="nav-link">🏠 返回主控台</a>
            <a href="http://localhost:5000" class="nav-link active">📚 题库首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/import-excel" class="nav-link">📥 导入题库</a>
    
            <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
            <a href="/upload-paper-rule" class="nav-link">📤 上传组卷规则</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
            <form method="post" action="{{ url_for('restart_system') }}" style="display: inline-block; margin-left: 20px;" 
                  onsubmit="return confirm('确定要重启系统吗？这将重新加载所有配置。')">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <button type="submit" class="nav-link" style="background: #dc3545; color: white; border: none; cursor: pointer;">🔄 系统重启</button>
            </form>
        </div>
        
        <!-- 题库管理模块 -->
        <div class="module-section">
            <h2 class="module-title">📚 题库管理</h2>
            <div class="module-actions">
                <a href="/import-excel" class="btn btn-success">📥 导入题库</a>
                <a href="{{ url_for('download_template') }}" class="btn">📋 下载题库模板</a>
                <a href="{{ url_for('handle_export_excel') }}" class="btn btn-success">📤 导出题库</a>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>题库名称</th>
                        <th>题目数量</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% if banks %}
                        {% for bank in banks %}
                            <tr>
                                <td><a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}" style="color: #007bff; text-decoration: none;">{{ bank['name'] }}</a></td>
                                <td>{{ bank['question_count'] }}</td>
                                <td>
                                    <a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}" class="btn btn-primary" style="padding: 4px 12px; font-size: 0.9em; margin-right: 5px;">📝 管理试题</a>
                                    <form method="post" action="{{ url_for('delete_bank', bank_id=bank['id']) }}" style="display: inline-block;" 
                                          onsubmit="return confirm('确定要删除题库【{{ bank['name'] }}】吗？此操作会同时删除该题库下所有题目！')">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="btn btn-danger" style="padding: 4px 12px; font-size: 0.9em; border: none; border-radius: 4px; cursor: pointer;">🗑️ 删除</button>
                                    </form>
                                </td>
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="3" style="text-align: center; color: #666;">暂无题库</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <!-- 试卷管理模块 -->
        <div class="module-section">
            <h2 class="module-title">📝 试卷管理</h2>
            <div class="module-actions">
        
                <a href="/generate-paper" class="btn btn-warning">🎯 自定义组卷</a>
                <a href="/upload-paper-rule" class="btn btn-info">🗂️ 上传组卷规则</a>
                <a href="/import-paper" class="btn btn-success">📥 导入试卷</a>
                <a href="/papers" class="btn">📋 查看所有试卷</a>
            </div>
        </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
      {% if messages %}
        <ul class=flashes>
        {% for category, message in messages %}
          <li class="{{ category }}">{{ message }}</li>
        {% endfor %}
        </ul>
      {% endif %}
    {% endwith %}

        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{{ total_questions }}</div>
                <div class="stat-label">总题目数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_papers }}</div>
                <div class="stat-label">总试卷数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ total_banks }}</div>
                <div class="stat-label">总题库数</div>
            </div>
        </div>
    </div>
</body>
</html>
"""

import_form_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入Excel题库</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', sans-serif; 
            margin: 20px; 
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        input[type="submit"] {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        input[type="submit"]:hover {
            background-color: #0056b3;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #545b62;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info h4 {
            margin-top: 0;
            color: #0056b3;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📥 导入Excel题库</h1>
            <p>请选择要导入的Excel文件</p>
        </div>
        
        <div class="info">
            <h4>📋 文件要求：</h4>
            <ul>
                <li>文件格式：.xlsx (Excel 2007及以上版本)</li>
                <li>文件大小：不超过10MB</li>
                <li>必需列：ID, 题库名称, 题型代码, 试题（题干）, 正确答案, 难度代码</li>
            </ul>
            <p style="margin-top: 15px;">
                <strong>💡 提示：</strong>如果您不确定Excel文件格式，请先 
                <a href="{{ url_for('download_template') }}" style="color: #007bff; text-decoration: underline;">下载题库模板</a> 
                作为参考。
            </p>
        </div>
        
    <form method="post" enctype="multipart/form-data" action="{{ url_for('handle_import_excel') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="form-group">
                <label for="file">选择Excel文件 (.xlsx):</label>
                <input type="file" id="file" name="file" accept=".xlsx" required>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" name="overwrite_duplicates" value="1" style="margin-right: 8px;">
                    🔄 覆盖重复题目 (如果题目ID已存在，则更新该题目)
                </label>
                <p style="font-size: 0.9em; color: #666; margin-top: 5px;">
                    💡 默认情况下，重复的题目ID会被跳过。勾选此选项将覆盖已存在的题目。
                </p>
            </div>
            <input type="submit" value="📤 上传并导入">
    </form>
        
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/import-excel" class="nav-link active">📥 导入题库</a>

            <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
            <a href="/upload-paper-rule" class="nav-link">📤 上传组卷规则</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
    </div>
</body>
</html>
"""

banks_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>题库管理</title>
    <script>
        function returnToMainConsole() {
            // 尝试通过postMessage与主控台通信
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({type: 'navigate', url: '/dashboard'}, '*');
            } else {
                // 直接跳转到主控台
                window.location.href = 'http://localhost:8000/dashboard';
            }
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        form {
            margin-bottom: 20px;
        }
        input[type="text"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
            margin-right: 10px;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .delete-btn {
            background-color: #dc3545;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .view-btn {
            background-color: #28a745;
        }
        .view-btn:hover {
            background-color: #218838;
        }
        .messages {
            margin-bottom: 20px;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            list-style: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>题库管理</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link active">📚 题库管理</a>
        </div>
        <div class="module-actions" style="margin-bottom: 20px;">
            <a href="/import-excel" class="btn btn-success" style="display:inline-block;padding:8px 16px;margin-right:10px;background-color:#28a745;color:white;text-decoration:none;border-radius:4px;">📥 导入题库</a>
            <a href="{{ url_for('download_template') }}" class="btn" style="display:inline-block;padding:8px 16px;margin-right:10px;background-color:#007bff;color:white;text-decoration:none;border-radius:4px;">📋 下载题库模板</a>
            <a href="{{ url_for('handle_export_excel') }}" class="btn btn-success" style="display:inline-block;padding:8px 16px;margin-right:10px;background-color:#28a745;color:white;text-decoration:none;border-radius:4px;">📤 导出题库</a>
        </div>
        <h2>新增题库</h2>
        <form method="post" action="{{ url_for('manage_banks') }}">
            <input type="text" name="bank_name" required placeholder="输入新题库名称">
            <button type="submit">创建</button>
        </form>
        <h2>现有题库</h2>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="messages">
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message|safe }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        {% if banks %}
            <table>
                <tr>
                    <th>题库名称</th>
                    <th>题目数量</th>
                    <th>操作</th>
                </tr>
                {% for bank in banks %}
                <tr>
                    <td><a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}">{{ bank['name'] }}</a></td>
                    <td>{{ bank['question_count'] }}</td>
                    <td>
                        <a href="{{ url_for('manage_bank_questions', bank_id=bank['id']) }}" class="view-btn" style="display:inline-block;margin-right:5px;">📝 管理试题</a>
                        <form method="post" action="{{ url_for('delete_bank', bank_id=bank['id']) }}" style="display:inline;margin:0;" onsubmit="return confirm('确定要删除该题库吗？此操作会同时删除该题库下所有题目！');">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <button type="submit" class="delete-btn">🗑️ 删除</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </table>
        {% else %}
            <p>暂无题库。</p>
        {% endif %}
    </div>
</body>
</html>
"""

# 题库试题管理模板
bank_questions_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{{ bank.name }} - 试题管理</title>
    <script>
        function returnToMainConsole() {
            // 尝试通过postMessage与主控台通信
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({type: 'navigate', url: '/dashboard'}, '*');
            } else {
                // 直接跳转到主控台
                window.location.href = 'http://localhost:8000/dashboard';
            }
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .messages {
            margin-bottom: 20px;
        }
        .messages li {
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
            list-style: none;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-outline-primary {
            background-color: transparent;
            border: 1px solid #007bff;
            color: #007bff;
            text-decoration: none;
            display: inline-block;
            margin: 0 2px;
        }
        .btn-outline-primary:hover {
            background-color: #007bff;
            color: white;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .pagination-info {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>{{ bank.name }} - 试题管理</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="messages">
                    {% for category, message in messages %}
                        <li class="{{ category }}">{{ message|safe }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        
        <p><a href="{{ url_for('manage_banks') }}" class="btn btn-primary">← 返回题库列表</a></p>
        
        <h2>试题列表 (共 {{ pagination.total }} 道题目)</h2>
        {% if questions %}
            <div class="pagination-info" style="margin-bottom: 15px; color: #666;">
                显示第 {{ (pagination.page - 1) * pagination.per_page + 1 }} - {{ (pagination.page - 1) * pagination.per_page + questions|length }} 条，共 {{ pagination.total }} 条记录
            </div>
            <table>
                <tr>
                    <th>题目ID</th>
                    <th>题型</th>
                    <th>题干</th>
                    <th>正确答案</th>
                    <th>操作</th>
                </tr>
                {% for question in questions %}
                <tr>
                    <td>{{ question.id }}</td>
                    <td>{{ question.question_type_code }}</td>
                <td>{{ question.question_stem[:100] }}{% if question.question_stem|length > 100 %}...{% endif %}</td>
                <td>{{ question.correct_answer }}</td>
                    <td>
                        <a href="{{ url_for('view_question', bank_id=bank.id, question_id=question.id) }}" class="btn btn-primary">查看详情</a>
                    </td>
                </tr>
                {% endfor %}
            </table>
            
            <!-- 分页导航 -->
            {% if pagination.total_pages > 1 %}
            <div class="pagination" style="margin-top: 20px; text-align: center;">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=1) }}" class="btn btn-outline-primary">首页</a>
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=pagination.prev_num) }}" class="btn btn-outline-primary">上一页</a>
                {% endif %}
                
                {% for page_num in range(1, pagination.total_pages + 1) %}
                    {% if page_num == pagination.page %}
                        <span class="btn btn-primary" style="margin: 0 2px;">{{ page_num }}</span>
                    {% elif page_num <= pagination.page + 2 and page_num >= pagination.page - 2 %}
                        <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=page_num) }}" class="btn btn-outline-primary" style="margin: 0 2px;">{{ page_num }}</a>
                    {% elif page_num == 1 or page_num == pagination.total_pages %}
                        <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=page_num) }}" class="btn btn-outline-primary" style="margin: 0 2px;">{{ page_num }}</a>
                    {% elif page_num == pagination.page - 3 or page_num == pagination.page + 3 %}
                        <span style="margin: 0 5px;">...</span>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=pagination.next_num) }}" class="btn btn-outline-primary">下一页</a>
                    <a href="{{ url_for('manage_bank_questions', bank_id=bank.id, page=pagination.total_pages) }}" class="btn btn-outline-primary">末页</a>
                {% endif %}
            </div>
            {% endif %}
        {% else %}
            <p>该题库暂无试题。</p>
        {% endif %}
    </div>
</body>
</html>
"""

# 试题详情模板
question_detail_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>试题详情 - {{ question.id }}</title>
    <script>
        function returnToMainConsole() {
            // 尝试通过postMessage与主控台通信
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({type: 'navigate', url: '/dashboard'}, '*');
            } else {
                // 直接跳转到主控台
                window.location.href = 'http://localhost:8000/dashboard';
            }
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
        }
        h1, h2 {
            color: #333;
            margin-bottom: 20px;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .question-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .question-info p {
            margin: 10px 0;
        }
        .question-info strong {
            color: #495057;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-right: 5px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>试题详情</h1>
        <div class="nav-tabs">
            <a href="{{ url_for('index') }}" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link">📋 试卷管理</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        <p>
            <a href="{{ url_for('manage_banks') }}" class="btn btn-primary">← 返回题库列表</a>
            <a href="{{ url_for('manage_bank_questions', bank_id=bank.id) }}" class="btn btn-primary">← 返回 {{ bank.name }}</a>
        </p>
        
        <div class="question-info">
            <h2>{{ question.id }}</h2>
            <p><strong>题库：</strong>{{ bank.name }}</p>
            <p><strong>题型代码：</strong>{{ question.question_type_code }}</p>
            <p><strong>题干：</strong>{{ question.question_stem }}</p>
            {% if question.option_a %}
                <p><strong>选项A：</strong>{{ question.option_a }}</p>
            {% endif %}
            {% if question.option_b %}
                <p><strong>选项B：</strong>{{ question.option_b }}</p>
            {% endif %}
            {% if question.option_c %}
                <p><strong>选项C：</strong>{{ question.option_c }}</p>
            {% endif %}
            {% if question.option_d %}
                <p><strong>选项D：</strong>{{ question.option_d }}</p>
            {% endif %}
            {% if question.option_e %}
                <p><strong>选项E：</strong>{{ question.option_e }}</p>
            {% endif %}
            <p><strong>正确答案：</strong>{{ question.correct_answer }}</p>
            {% if question.analysis %}
                <p><strong>解析：</strong>{{ question.analysis }}</p>
            {% endif %}
            {% if question.difficulty_code %}
                <p><strong>难度代码：</strong>{{ question.difficulty_code }}</p>
            {% endif %}
            {% if question.image_location %}
                <p><strong>图片信息：</strong>{{ question.image_location }}</p>
            {% endif %}
        </div>
    </div>
</body>
</html>
"""

@app.route('/', methods=['GET'])
def index():
    """主页，显示题库统计和题库列表"""
    db = get_db()
    try:
        # 获取基本统计信息
        total_questions = db.query(Question).count()
        total_papers = db.query(Paper).count()
        total_banks = db.query(QuestionBank).count()
        
        # 获取题库列表及其统计信息
        banks = []
        for bank in db.query(QuestionBank).order_by(QuestionBank.题库名称).all():
            # 获取该题库的题目数量
            question_count = db.query(Question).filter(Question.question_bank_id == bank.id).count()
            
            # 获取该题库的题型种类
            question_types = db.query(Question.question_type_code).filter(
                Question.question_bank_id == bank.id
            ).distinct().all()
            question_types = [qt[0] for qt in question_types]
            
            banks.append({
                'id': bank.id,
                'name': bank.题库名称,
                'question_count': question_count,
                'question_types': question_types
            })
        
        return render_template_string(
            index_template, 
            total_questions=total_questions,
            total_papers=total_papers,
            total_banks=total_banks,
            banks=banks
        )
    except Exception as e:
        flash(f"加载页面失败：{e}", "error")
        return render_template_string(
            index_template,
            total_questions=0,
            total_papers=0,
            total_banks=0,
            banks=[]
        )
    finally:
        close_db(db)

@app.route('/import-json', methods=['GET'])
def handle_import_json():
    """处理从JSON文件导入样例题库的请求"""
    db = get_db()
    json_file_path = os.path.join(os.path.dirname(__file__), 'questions_sample.json')
    
    if not os.path.exists(json_file_path):
        flash(f"错误：样例题库文件 'questions_sample.json' 不存在。", 'error')
        return redirect(url_for('index'))
    
    try:
        success_count, fail_count = import_questions_from_json(json_file_path, db)
        if success_count > 0:
            flash(f"成功导入 {success_count} 道新的样例题目！", 'success')
        else:
            flash("没有新的样例题目需要导入，或所有题目ID已存在。", 'warning')
        if fail_count > 0:
            flash(f"有 {fail_count} 道题目导入失败，请检查服务器日志。", 'error')

    except Exception as e:
        flash(f"导入过程中发生未知错误: {e}", 'error')
    finally:
        close_db(db)
        
    return redirect(url_for('index'))

@app.route('/import-sample', methods=['GET'])
def handle_import_sample():
    """重定向到导入Excel页面，因为功能已整合"""
    return redirect(url_for('handle_import_excel'))


@app.route('/import-excel', methods=['GET', 'POST'])
def handle_import_excel():
    """处理Excel导入"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('没有文件部分', 'error')
            return redirect(request.url)
        file = request.files['file']
        if not file or not file.filename:
            flash('未选择文件', 'warning')
            return redirect(request.url)
        
        if allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            # 获取覆盖选项
            overwrite_duplicates = request.form.get('overwrite_duplicates') == '1'
            
            db_session = get_db()
            try:
                file.save(filepath)
                # 现在这个函数会直接处理数据库提交
                questions_added, errors = import_questions_from_excel(filepath, db_session, overwrite_duplicates=overwrite_duplicates)
                
                if errors:
                    error_report_path = export_error_report(errors, filename)
                    error_link = f'<a href="/download_error_report/{os.path.basename(error_report_path)}" target="_blank">点击查看报告</a>'
                    if questions_added:
                        flash(f'成功导入 {len(questions_added)} 条题目，但有部分数据出错。{error_link}', 'warning')
                    else:
                        flash(f'导入失败，所有条目均有问题。{error_link}', 'error')

                elif questions_added:
                    flash(f'成功导入 {len(questions_added)} 条题目！', 'success')
                else:
                    flash('未在文件中找到可导入的新题目。', 'info')
                
            except Exception as e:
                # 现在的导入函数会自己回滚，这里主要捕获文件保存等其他错误
                flash(f'处理文件时发生严重错误: {e}', 'error')
            finally:
                close_db(db_session)
            
            return redirect(url_for('index'))

    return render_template_string(import_form_template)

@app.route('/download-template', methods=['GET'])
def download_template():
    """下载题库模板"""
    try:
        # 检查模板文件是否存在
        template_path = os.path.join(os.getcwd(), 'templates', '题库模板.xlsx')
        
        if not os.path.exists(template_path):
            # 如果模板文件不存在，则生成一个
            from create_template import create_question_bank_template
            template_path = create_question_bank_template()
        
        # 返回文件
        return send_file(
            template_path,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name='题库模板.xlsx'
        )
        
    except Exception as e:
        flash(f'下载模板文件失败: {e}', 'error')
        return redirect(url_for('index'))

@app.route('/download_error_report/<filename>')
def download_error_report(filename):
    """下载错误报告"""
    if not filename:
        flash("无效的文件名。", "error")
        return redirect(url_for('index'))
    try:
        # 安全地构建文件路径
        safe_filename = secure_filename(filename)
        # 错误报告存储在error_reports目录
        error_reports_dir = os.path.join(os.getcwd(), 'error_reports')
        return send_from_directory(error_reports_dir, safe_filename, as_attachment=True)
    except FileNotFoundError:
        flash("错误报告文件未找到。", "error")
        return redirect(url_for('index'))
    except Exception as e:
        flash(f"下载错误报告失败: {e}", "error")
        return redirect(url_for('index'))

# 组卷功能路由
@app.route('/papers')
def papers():
    """试卷列表页面"""
    db_session = None
    papers_list = []
    
    try:
        db_session = get_db()
        papers_list = db_session.query(Paper).order_by(Paper.name).all()
    except Exception as e:
        flash(f"获取试卷列表失败: {e}", "error")
    finally:
        close_db(db_session)
    
    return render_template_string(papers_template, papers=papers_list)

@app.route('/generate-paper', methods=['GET', 'POST'])
def generate_paper():
    """生成试卷页面"""
    if request.method == 'POST':
        try:
            db_session = get_db()
            generator = PaperGenerator(db_session)
            
            # 获取表单数据
            paper_name = request.form.get('paper_name', '').strip()
            paper_description = request.form.get('paper_description', '').strip()
            total_score = float(request.form.get('total_score', 100))
            duration = int(request.form.get('duration', 120))
            difficulty_level = request.form.get('difficulty_level', '中等')
            
            # 验证必填字段
            if not paper_name:
                flash("试卷名称不能为空", "error")
                return redirect(url_for('generate_paper'))
            
            # 获取组卷规则
            rules = []
            rule_count = int(request.form.get('rule_count', 0))
            
            for i in range(rule_count):
                question_type = request.form.get(f'rule_{i}_type')
                count = int(request.form.get(f'rule_{i}_count', 1))
                score = float(request.form.get(f'rule_{i}_score', 5.0))
                section_name = request.form.get(f'rule_{i}_section', '')
                
                if question_type and count > 0:
                    rules.append({
                        'question_type': question_type,
                        'count': count,
                        'score_per_question': score,
                        'section_name': section_name
                    })
            
            # 生成试卷
            if rules:
                paper = generator.generate_paper_by_rules(
                    paper_name=paper_name,
                    paper_description=paper_description,
                    total_score=total_score,
                    duration=duration,
                    difficulty_level=difficulty_level,
                    rules=rules
                )
                flash(f"试卷 '{paper.name}' 生成成功！", "success")
                return redirect(url_for('view_paper', paper_id=paper.id))
            else:
                flash("请至少添加一条组卷规则", "error")
                
        except ValueError as e:
            flash(f"参数错误: {e}", "error")
        except Exception as e:
            flash(f"生成试卷失败: {e}", "error")
        finally:
            close_db(db_session)
    
    return render_template_string(generate_paper_template)

@app.route('/paper/<paper_id>')
def view_paper(paper_id):
    """查看试卷详情"""
    db_session = None
    paper = None
    paper_questions = []
    stats = {}
    
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        # 获取试卷题目
        paper_questions = db_session.query(PaperQuestion).filter(
            PaperQuestion.paper_id == paper_id
        ).order_by(PaperQuestion.question_order).all()
        
        # 获取统计信息
        generator = PaperGenerator(db_session)
        stats = generator.get_paper_statistics(paper_id)
        
    except Exception as e:
        flash(f"获取试卷详情失败: {e}", "error")
    finally:
        close_db(db_session)
    
    return render_template_string(
        view_paper_template, 
        paper=paper, 
        paper_questions=paper_questions,
        stats=stats
    )

@app.route('/paper/<paper_id>/level3_code_stats')
def paper_level3_code_stats(paper_id):
    """显示试卷的三级代码统计信息"""
    db_session = None
    paper = None
    level3_stats = {}
    
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        # 获取三级代码统计信息
        generator = PaperGenerator(db_session)
        level3_stats = generator.get_level3_code_statistics(paper_id)
        
    except Exception as e:
        flash(f"获取三级代码统计失败: {e}", "error")
    finally:
        close_db(db_session)
    
    # 尝试从最近的组卷规则文件中读取知识点分布配置
    knowledge_distribution = {}
    try:
        import pandas as pd
        import glob
        # 查找最近的组卷规则文件（包括uploads和templates文件夹），排除临时文件
        rule_files = [f for f in glob.glob('uploads/*.xlsx') + glob.glob('templates/*.xlsx') if not os.path.basename(f).startswith('~$')]
        if rule_files:
            # 优先选择组卷规则模板文件
            template_file = None
            for f in rule_files:
                if '组卷规则模板.xlsx' in f:
                    template_file = f
                    break
            latest_file = template_file if template_file else max(rule_files, key=os.path.getctime)
            try:
                df2 = pd.read_excel(latest_file, sheet_name='知识点分布', dtype=str, engine='openpyxl')
                # 处理列名
                df2.columns = [col.split('\n')[0].strip().replace(' ', '').replace('　', '') if isinstance(col, str) and '\n' in col else str(col).strip().replace(' ', '').replace('　', '') for col in df2.columns]
                
                # 标准化列名
                header_map = {
                    '1级代码': ['1级代码', '一级代码', '1级 代码'],
                    '1级比重(%)': ['1级比重(%)', '一级比重(%)', '1级比重%', '一级比重%'],
                    '2级代码': ['2级代码', '二级代码', '2级 代码'],
                    '2级比重(%)': ['2级比重(%)', '二级比重(%)', '2级比重%', '二级比重%'],
                    '3级代码': ['3级代码', '三级代码', '3级 代码'],
                    '3级比重(%)': ['3级比重(%)', '三级比重(%)', '3级比重%', '三级比重%'],
                }
                
                new_columns = {}
                for std_col, aliases in header_map.items():
                    for col in df2.columns:
                        if col in aliases:
                            new_columns[col] = std_col
                df2 = df2.rename(columns=new_columns)
                
                if not df2.empty:
                    for _, row in df2.iterrows():
                        if row.isnull().all():
                            continue
                        
                        # 安全地获取各级代码，处理nan值
                        try:
                            l3_code_raw = str(row.get('3级代码', '')).strip()
                            l3r = row.get('3级比重(%)', 0)
                            
                            # 跳过表头行
                            if l3_code_raw in ['Level 3 Code', '3级代码', 'Level3Code']:
                                continue
                            
                            # 检查三级代码和比重的有效性
                            if (l3_code_raw and l3_code_raw != 'nan' and l3_code_raw != '' and
                                l3r and not pd.isna(l3r)):
                                
                                try:
                                    l3r_float = float(l3r)
                                    if l3r_float > 0:
                                        # 如果三级代码已经是完整格式（如'A-A-A'），直接使用
                                        # 如果不是，则需要从其他列构建
                                        if '-' in l3_code_raw and len(l3_code_raw.split('-')) == 3:
                                            # 已经是完整的三级代码格式
                                            full_l3_code = l3_code_raw
                                        else:
                                            # 尝试从各级代码列构建
                                            l1 = str(row.get('1级代码', '')).strip()
                                            l2 = str(row.get('2级代码', '')).strip()
                                            l3 = l3_code_raw
                                            
                                            if (l1 and l1 != 'nan' and l1 != '' and
                                                l2 and l2 != 'nan' and l2 != '' and
                                                l3 and l3 != 'nan' and l3 != ''):
                                                full_l3_code = f"{l1}-{l2}-{l3}"
                                            else:
                                                continue
                                        
                                        knowledge_distribution[full_l3_code] = l3r_float
                                except (ValueError, TypeError):
                                    # 跳过无法转换为数字的比重值
                                    continue
                        except (ValueError, TypeError) as e:
                            # 跳过无效数据行
                            continue
            except Exception as e:
                print(f"读取组卷规则文件失败: {e}")
    except Exception as e:
        print(f"查找组卷规则文件失败: {e}")
    
    # 只构建第三级代码的统计数据
    stats_table = []
    
    # 直接使用实际存在的三级代码，而不是生成所有可能的组合
    all_level3_codes = set(level3_stats.keys())  # 实际存在的三级代码
    all_level3_codes.update(knowledge_distribution.keys())  # 需求中的三级代码
    
    # 为每个三级代码构建统计数据
    for full_l3_code in sorted(all_level3_codes):
        # 获取实际统计和需求数据
        l3_actual_proportion = level3_stats.get(full_l3_code, 0)  # 这里已经是比例了
        l3_required_proportion = knowledge_distribution.get(full_l3_code, 0)
        
        # 只有当有实际数据或需求数据时才添加
        if l3_actual_proportion > 0 or l3_required_proportion > 0:
            stats_table.append({
                "code": full_l3_code,
                "actual_proportion": l3_actual_proportion,
                "required_proportion": l3_required_proportion,
                "has_requirement": l3_required_proportion > 0
            })
    
    return render_template_string(
        """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>三级代码统计 - {{ paper.name }} - 题库管理系统</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 15px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    overflow: hidden;
                    padding: 30px;
                }
                .header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                    margin: -30px -30px 30px -30px;
                }
                .header h1 {
                    margin: 0;
                    font-size: 2.5em;
                    font-weight: 300;
                }
                .nav {
                    background: #f8f9fa;
                    padding: 15px 30px;
                    border-bottom: 1px solid #e9ecef;
                    margin: -30px -30px 30px -30px;
                }
                .nav a {
                    color: #495057;
                    text-decoration: none;
                    margin-right: 20px;
                    padding: 8px 16px;
                    border-radius: 20px;
                    transition: all 0.3s ease;
                }
                .nav a:hover {
                    background: #007bff;
                    color: white;
                }
                .card {
                    border: 1px solid #e9ecef;
                    border-radius: 10px;
                    margin-bottom: 20px;
                }
                .card-header {
                    background: #f8f9fa;
                    padding: 15px 20px;
                    border-bottom: 1px solid #e9ecef;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .card-body {
                    padding: 20px;
                }
                .btn {
                    padding: 8px 16px;
                    border: none;
                    border-radius: 20px;
                    cursor: pointer;
                    text-decoration: none;
                    display: inline-block;
                    transition: all 0.3s ease;
                }
                .btn-primary {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                }
                .btn-outline-secondary {
                    background: transparent;
                    border: 1px solid #6c757d;
                    color: #6c757d;
                }
                .btn-outline-secondary:hover {
                    background: #6c757d;
                    color: white;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                }
                th, td {
                    border: 1px solid #e9ecef;
                    padding: 12px;
                    text-align: left;
                }
                th {
                    background: #f8f9fa;
                    font-weight: 600;
                }
                tr:nth-child(even) {
                    background: #f8f9fa;
                }
                tr:hover {
                    background: #e9ecef;
                }
                .requirement-row {
                    background-color: #f8f9fa;
                }
                .requirement-cell {
                    background-color: #e3f2fd;
                    font-weight: 600;
                    color: #1976d2;
                }
                .positive-diff {
                    background-color: #e8f5e8;
                    color: #2e7d32;
                    font-weight: 600;
                }
                .negative-diff {
                    background-color: #ffebee;
                    color: #c62828;
                    font-weight: 600;
                }
                .zero-diff {
                    background-color: #f3e5f5;
                    color: #7b1fa2;
                    font-weight: 600;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📊 三级代码统计</h1>
                    <p>{{ paper.name }}</p>
                </div>
                
                <div class="nav">
                    <a href="http://localhost:8000/dashboard">🏠 返回主控台</a>
                    <a href="/papers">📋 试卷管理</a>
                    <a href="/paper/{{ paper.id }}">📝 返回试卷详情</a>
                </div>
                
                <div class="content">
                    <h2>试卷三级代码统计 - {{ paper.name }}</h2>
                    <p>此页面显示试卷中题目的三级代码分布情况，可用于与组卷模板进行比对。</p>
                    
                    <div class="alert alert-info" style="background: #e3f2fd; border: 1px solid #bbdefb; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #1976d2;">📊 颜色说明</h4>
                        <ul style="margin: 0; padding-left: 20px;">
                            <li><span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 3px; font-weight: 600;">蓝色背景</span>：组题需求的三级代码值</li>
                            <li><span style="background: #e8f5e8; color: #2e7d32; padding: 2px 6px; border-radius: 3px; font-weight: 600;">绿色</span>：实际比重高于需求比重</li>
                            <li><span style="background: #ffebee; color: #c62828; padding: 2px 6px; border-radius: 3px; font-weight: 600;">红色</span>：实际比重低于需求比重</li>
                            <li><span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 6px; border-radius: 3px; font-weight: 600;">紫色</span>：实际比重等于需求比重</li>
                        </ul>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 style="margin: 0;">三级代码分布</h3>
                            <a href="/paper/{{ paper.id }}" class="btn btn-outline-secondary">返回试卷详情</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table>
                                    <thead>
                                        <tr>
                                            <th>3级代码<br>Level 3 Code</th>
                                            <th>实际比重(%)<br>Actual Proportion (%)</th>
                                            <th>组题需求(%)<br>Required Proportion (%)</th>
                                            <th>差异<br>Difference</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stats_table %}
                                        <tr {% if item.has_requirement %}class="requirement-row"{% endif %}>
                                            <td>{{ item.code }}</td>
                                            <td>{{ item.actual_proportion }}</td>
                                            <td {% if item.has_requirement %}class="requirement-cell"{% endif %}>
                                                {% if item.required_proportion > 0 %}
                                                    {{ item.required_proportion }}
                                                {% else %}
                                                    0
                                                {% endif %}
                                            </td>
                                            <td class="{% if item.required_proportion > 0 %}{% if item.actual_proportion > item.required_proportion %}positive-diff{% elif item.actual_proportion < item.required_proportion %}negative-diff{% else %}zero-diff{% endif %}{% endif %}">
                                                {% if item.required_proportion > 0 %}
                                                    {{ "%.1f"|format(item.actual_proportion - item.required_proportion) }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """, 
        paper=paper,
        stats_table=stats_table
    )

@app.route('/paper/<paper_id>/export', methods=['GET', 'POST'])
def export_paper(paper_id):
    """导出试卷为 Word 文档"""
    db_session = None
    try:
        db_session = get_db()
        generator = PaperGenerator(db_session)
        
        docx_buffer = generator.export_paper_to_docx(paper_id)
        
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        # 确保即使paper_name为空，也能提供一个安全的文件名
        if paper is not None and paper.name is not None:
            paper_name = str(paper.name)
        else:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            paper_name = f"无标题试卷_{timestamp}"

        safe_paper_name = secure_filename(paper_name)

        return send_file(
            docx_buffer,
            as_attachment=True,
            download_name=f'{safe_paper_name}.docx',
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
    except Exception as e:
        flash(f"导出试卷失败: {e}", "error")
        return redirect(url_for('view_paper', paper_id=paper_id))
    finally:
        close_db(db_session)

@app.route('/paper/<paper_id>/delete', methods=['POST'])
def delete_paper(paper_id):
    """删除试卷"""
    db_session = None
    
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        db_session.delete(paper)
        db_session.commit()
        flash(f"试卷 '{paper.name}' 删除成功", "success")
        
    except Exception as e:
        flash(f"删除试卷失败: {e}", "error")
    finally:
        close_db(db_session)
    
    return redirect(url_for('papers'))



@app.route('/upload-paper-rule', methods=['GET', 'POST'])
def upload_paper_rule():
    """上传试卷规则Excel并自动组卷"""
    import pandas as pd
    from werkzeug.utils import secure_filename
    if request.method == 'POST':
        file = request.files.get('file')
        if not file or not file.filename:
            flash('请上传Excel文件或文件无名称', 'error')
            return redirect(url_for('upload_paper_rule'))
        filename = secure_filename(file.filename)
        filepath = os.path.join('uploads', filename)
        file.save(filepath)
        
        # 新增：获取多套组卷参数
        num_sets = int(request.form.get('num_sets', 1))
        if num_sets < 1:
            num_sets = 1
        if num_sets > 10:
            num_sets = 10
            
        # 获取试题ID重复率限制参数
        repeat_rate_limit = float(request.form.get('repeat_rate_limit', 0.09))
        if repeat_rate_limit < 0:
            repeat_rate_limit = 0
        if repeat_rate_limit > 1:
            repeat_rate_limit = 1
        
        try:
            # 解析Sheet1（题型分布）- 适应新的中英文双行表头格式
            # 读取Excel时使用前两行作为多级表头（第一行中文，第二行英文）
            df1 = pd.read_excel(filepath, sheet_name='题型分布', header=[0, 1], engine='openpyxl')
            
            # 处理多级表头，只使用中文部分作为列名，并去除换行符
            df1.columns = [col[0].split("\n")[0].strip().replace(" ", "").replace("　", "") if isinstance(col[0], str) and "\n" in col[0] else col[0].strip().replace(" ", "").replace("　", "") for col in df1.columns]

            # 定义标准列名和可能的别名
            column_aliases = {
                "题库名称": ["题库名称", "题库名称Question Bank Name", "Question Bank Name", "题库代码", "题库代码/Bank Code"],
                "题型": ["题型", "题型Question Type Code", "Question Type Code", "题型代码", "题型代码/Question Type", "题型\nQuestion Type", "题型代码\nQuestion Type Code"],
                "题量": ["题量", "题量Question Count", "Question Count", "题目数量", "题目数量/Question Count"],
                "分值": ["分值", "分值Score Value", "Score Value", "分数", "分数/Score"]
            }

            # 构建列名映射
            column_mapping = {}
            for std_col, aliases in column_aliases.items():
                for col in df1.columns:
                    if col in aliases:
                        column_mapping[col] = std_col

            # 重命名列
            if column_mapping:
                df1 = df1.rename(columns=column_mapping)

            # 检查必需的列
            required_cols_s1 = {'题库名称', '题型', '题量', '分值'}
            if not required_cols_s1.issubset(df1.columns):
                missing_cols = required_cols_s1 - set(df1.columns)
                flash(f'题型分布表缺少必需列: {", ".join(missing_cols)}', 'error')
                return redirect(url_for('upload_paper_rule'))

            paper_structure = []

            # 从第三行开始读取数据（跳过表头两行）
            for _, row in df1.iterrows():
                if pd.isna(row.get('题型')) or pd.isna(row.get('题量')) or pd.isna(row.get('分值')) or pd.isna(row.get('题库名称')):
                    continue
                qtype = str(row['题型']).split('（')[0]
                paper_structure.append({
                    'question_bank_name': str(row['题库名称']).strip(),
                    'question_type': qtype,
                    'count': int(row['题量']),
                    'score_per_question': float(row['分值'])
                })

            # 解析Sheet2（知识点分布）- 适应新的中英文双行表头格式
            # 读取Excel时使用前两行作为多级表头（第一行中文，第二行英文）
            df2 = pd.read_excel(filepath, sheet_name='知识点分布', header=[0, 1], engine='openpyxl')
            
            # 处理多级表头，只使用中文部分作为列名，并去除换行符
            df2.columns = [col[0].split("\n")[0].strip().replace(" ", "").replace("　", "") if isinstance(col[0], str) and "\n" in col[0] else col[0].strip().replace(" ", "").replace("　", "") for col in df2.columns]
            
            # 定义标准列名和可能的别名
            knowledge_column_aliases = {
                "1级代码": ["1级代码", "1级代码\nLevel 1 Code", "Level 1 Code"],
                "1级比重(%)": ["1级比重(%)", "1级比重(%)\nLevel 1 Proportion (%)", "Level 1 Proportion (%)"],
                "2级代码": ["2级代码", "2级代码\nLevel 2 Code", "Level 2 Code"],
                "2级比重(%)": ["2级比重(%)", "2级比重(%)\nLevel 2 Proportion (%)", "Level 2 Proportion (%)"],
                "3级代码": ["3级代码", "3级代码\nLevel 3 Code", "Level 3 Code"],
                "3级比重(%)": ["3级比重(%)", "3级比重(%)\nLevel 3 Proportion (%)", "Level 3 Proportion (%)"]
            }
            
            # 构建列名映射
            knowledge_column_mapping = {}
            for std_col, aliases in knowledge_column_aliases.items():
                for col in df2.columns:
                    if col in aliases:
                        knowledge_column_mapping[col] = std_col
            
            # 重命名列
            if knowledge_column_mapping:
                df2 = df2.rename(columns=knowledge_column_mapping)
            knowledge_distribution = {}
            if not df2.empty:
                required_cols_s2 = {'1级代码', '1级比重(%)', '2级代码', '2级比重(%)', '3级代码', '3级比重(%)'}
                if not required_cols_s2.issubset(df2.columns):
                    missing_cols = required_cols_s2 - set(df2.columns)
                    flash(f'知识点分布表缺少必需列: {", ".join(missing_cols)}', 'error')
                    return redirect(url_for('upload_paper_rule'))

                # 用于跟踪当前的1级和2级代码
                current_l1 = None
                current_l1r = None
                current_l2 = None
                current_l2r = None
                
                for _, row in df2.iterrows():
                    if row.isnull().all():
                        continue
                    
                    # 更新当前1级代码和比重
                    if pd.notna(row['1级代码']) and pd.notna(row['1级比重(%)']):
                        current_l1 = str(row['1级代码']).strip()
                        try:
                            current_l1r = float(row['1级比重(%)'])
                        except (ValueError, TypeError):
                            continue
                    
                    # 更新当前2级代码和比重
                    if pd.notna(row['2级代码']) and pd.notna(row['2级比重(%)']):
                        current_l2 = str(row['2级代码']).strip()
                        try:
                            current_l2r = float(row['2级比重(%)'])
                        except (ValueError, TypeError):
                            continue
                    
                    # 处理3级代码（必须有值）- 直接使用Excel中的完整三级代码
                    if pd.notna(row['3级代码']) and pd.notna(row['3级比重(%)']):
                        level3_code = str(row['3级代码']).strip()
                        try:
                            level3_ratio = float(row['3级比重(%)'])
                        except (ValueError, TypeError):
                            continue
                        
                        # 直接使用三级代码作为键值，不构建嵌套结构
                        knowledge_distribution[level3_code] = level3_ratio
            
            # 从表单或Excel获取试卷名称
            paper_name = request.form.get('paper_name')
            if not paper_name:
                if paper_structure:
                    paper_name = f"{paper_structure[0]['question_bank_name']} - 自动组卷"
                else:
                    paper_name = f"自动组卷_{int(time.time())}"

            db_session = get_db()
            generator = PaperGenerator(db_session)
            paper_ids = []
            for i in range(num_sets):
                this_paper_name = paper_name if num_sets == 1 else f"{paper_name}_第{i+1}套"
                paper = generator.generate_paper_by_knowledge_distribution(
                    paper_name=this_paper_name,
                    paper_structure=paper_structure,
                    knowledge_distribution=knowledge_distribution,
                    repeat_rate_limit=repeat_rate_limit
                )
                paper_ids.append(paper.id)
            flash(f'成功生成 {num_sets} 套试卷！', 'success')
            return redirect(url_for('papers'))
        except FileNotFoundError:
            flash("上传的文件未找到，请重试。", "error")
        except ValueError as e:
            flash(f"组卷失败，请检查规则配置：{e}", "error")
        except KeyError as e:
            flash(f"Excel文件中缺少必需的列名: {e}，请使用模板文件。", "error")
        except Exception as e:
            flash(f"处理文件时发生未知错误: {e}", "error")
        return redirect(url_for('upload_paper_rule')
    )

    # GET请求返回上传页面，增加多套组卷输入框和重复率限制输入框
    return render_template_string('''
    <h2>上传组卷规则Excel</h2>
    <div class="nav-tabs">
        <a href="/" class="nav-link">🏠 首页</a>
        <a href="/papers" class="nav-link">📋 试卷管理</a>
        <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
        <a href="/upload-paper-rule" class="nav-link active">🗂️ 上传组卷规则</a>
        <a href="/banks" class="nav-link">📚 题库管理</a>
    </div>
    <div style="margin-bottom: 20px;">
        <a href="/download-paper-rule-template" class="btn btn-success" style="margin-bottom:16px;display:inline-block;">📥 下载组卷规则模板</a>
    </div>
    <style>
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
    </style>
    <form method="post" enctype="multipart/form-data">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <div class="form-group" style="margin-bottom: 1rem;">
            <label for="paper_name">试卷名称 (可选, 默认为题库名)</label>
            <input type="text" id="paper_name" name="paper_name" class="form-control" style="width:100%; padding:8px; border-radius:4px; border:1px solid #ccc;">
        </div>
        <div class="form-group" style="margin-bottom: 1rem;">
            <label for="num_sets">生成套数</label>
            <input type="number" id="num_sets" name="num_sets" min="1" max="10" value="1" style="width:100px;">
        </div>
        <div class="form-group" style="margin-bottom: 1rem;">
            <label for="repeat_rate_limit">试题ID重复率限制 (0-1之间，默认0.09)</label>
             <input type="number" id="repeat_rate_limit" name="repeat_rate_limit" min="0" max="1" step="0.01" value="0.09" style="width:100px;">
             <small style="display:block;color:#666;margin-top:5px;">基于试题ID计算重复率，确保任意两套试卷之间的题目重复率低于设定阈值。设置为0表示不允许重复，设置为1表示允许完全重复</small>
        </div>
        <div class="form-group">
            <label for="file">上传组卷规则文件</label>
            <input type="file" id="file" name="file" accept=".xlsx" required>
        </div>
        <button type="submit" style="padding:10px 20px; border-radius:5px; border:none; background-color:#007bff; color:white; cursor:pointer; margin-top:10px;">上传并自动组卷</button>
    </form>
    ''')

# 组卷功能模板
papers_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>试卷管理 - 题库管理系统</title>
    <script>
        function returnToMainConsole() {
            // 尝试通过postMessage与主控台通信
            if (window.parent && window.parent !== window) {
                window.parent.postMessage({type: 'navigate', url: '/dashboard'}, '*');
            } else {
                // 直接跳转到主控台
                window.location.href = 'http://localhost:8000/dashboard';
            }
        }
    </script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            padding: 0.5rem 1rem;
            margin-right: 5px;
            color: #495057;
            text-decoration: none;
            font-weight: 500;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
        }
        .nav-tabs .nav-link.active {
            color: #007bff;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
        .content {
            padding: 30px;
        }
        .actions {
            margin-bottom: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .papers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        .paper-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .paper-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .paper-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .paper-info {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        .paper-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-size: 0.9em;
        }
        .paper-actions {
            display: flex;
            gap: 10px;
        }
        .btn-sm {
            padding: 6px 12px;
            font-size: 14px;
        }
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 试卷管理</h1>
            <p>管理和生成试卷</p>
        </div>
        
        <div class="nav-tabs">
            <a href="http://localhost:5000" class="nav-link">🏠 首页</a>
            <a href="/papers" class="nav-link active">📋 试卷管理</a>
            <a href="/generate-paper" class="nav-link">🎯 自定义组卷</a>
            <a href="/upload-paper-rule" class="nav-link">📤 上传组卷规则</a>
            <a href="/banks" class="nav-link">📚 题库管理</a>
        </div>
        
        <div class="content">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <div class="actions">
                <form id="batchForm" method="post" style="display:inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="paper_ids" id="batchPaperIds">
                    <button type="button" class="btn btn-success" onclick="batchExportExcel()">📊 批量导出Excel</button>
                    <button type="button" class="btn btn-primary" onclick="batchExportWord()">📄 批量导出Word</button>
                    <button type="button" class="btn btn-danger" onclick="batchDelete()">🗑️ 批量删除</button>
                </form>
    
                <a href="/generate-paper" class="btn btn-primary">🎯 自定义组卷</a>
            </div>
            <script>
            function getCheckedPaperIds() {
                let ids = [];
                document.querySelectorAll('.paper-checkbox:checked').forEach(cb => ids.push(cb.value));
                return ids;
            }
            function batchExportExcel() {
                let ids = getCheckedPaperIds();
                if(ids.length===0){alert('请先选择试卷');return;}
                let form = document.getElementById('batchForm');
                form.action = '/export_papers_excel';
                document.getElementById('batchPaperIds').value = ids.join(',');
                form.submit();
            }
            function batchExportWord() {
                let ids = getCheckedPaperIds();
                if(ids.length===0){alert('请先选择试卷');return;}
                let form = document.getElementById('batchForm');
                form.action = '/export_papers_word';
                document.getElementById('batchPaperIds').value = ids.join(',');
                form.submit();
            }
            function batchDelete() {
                let ids = getCheckedPaperIds();
                if(ids.length===0){alert('请先选择试卷');return;}
                if(!confirm('确定要批量删除选中的试卷吗？'))return;
                let form = document.getElementById('batchForm');
                form.action = '/delete_papers';
                document.getElementById('batchPaperIds').value = ids.join(',');
                form.submit();
            }
            function toggleAllPapers(cb){
                document.querySelectorAll('.paper-checkbox').forEach(x=>x.checked=cb.checked);
            }
            </script>
            <div style="margin-bottom:10px;text-align:right;">
                <input type="checkbox" id="checkAll" onclick="toggleAllPapers(this)"> <label for="checkAll">全选</label>
            </div>
            {% if papers %}
            <div class="papers-grid">
                {% for paper in papers %}
                <div class="paper-card">
                    <input type="checkbox" class="paper-checkbox" value="{{ paper.id }}" style="float:right;transform:scale(1.3);margin-top:2px;">
                    <div class="paper-title">{{ paper.name }}</div>
                    <div class="paper-info">
                        {{ paper.description or '暂无描述' }}
                    </div>
                    <div class="paper-stats">
                        <span>📊 总分: {{ paper.total_score }}分</span>
                        <span>⏱️ 时长: {{ paper.duration }}分钟</span>
                    </div>
                    <div class="paper-stats">
                        <span>🎯 难度: {{ paper.difficulty_level or '未设置' }}</span>
                    </div>
                    <div class="paper-actions">
                        <a href="/paper/{{ paper.id }}" class="btn btn-primary btn-sm">👁️ 查看</a>
                        <form method="GET" action="/paper/{{ paper.id }}/export" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-success btn-sm">📥 导出</button>
                        </form>
                        <form method="GET" action="/paper/{{ paper.id }}/export_excel" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-success btn-sm">📊 Excel导出</button>
                        </form>
                        <form method="POST" action="/paper/{{ paper.id }}/delete" style="display: inline;" onsubmit="return confirm('确定要删除这个试卷吗？')">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <button type="submit" class="btn btn-danger btn-sm">🗑️ 删除</button>
                        </form>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <h3>📭 暂无试卷</h3>
                <p>还没有生成任何试卷，点击上方按钮开始创建吧！</p>
            </div>
            {% endif %}
        </div>
    </div>
</body>
</html>
"""

generate_paper_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自定义组卷 - 题库管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .nav-tabs {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .nav-tabs a {
            color: #495057;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px 5px 0 0;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            border-bottom: none;
            position: relative;
            background-color: #f8f9fa;
        }
        .nav-tabs a:hover {
            background: #e9ecef;
            color: #007bff;
            border-color: #dee2e6;
        }
        .nav-tabs a.active {
            background: #fff;
            color: #007bff;
            border-color: #dee2e6;
            border-bottom: 2px solid #fff;
            margin-bottom: -1px;
            font-weight: 500;
        }
        .content {
            padding: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .rules-container {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .rule-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .rule-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .rule-title {
            font-weight: 600;
            color: #333;
        }
        .remove-rule {
            background: #ff416c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-size: 16px;
        }
        .rule-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 自定义组卷</h1>
            <p>根据规则自动生成试卷</p>
        </div>
        
        <div class="nav-tabs">
            <a href="javascript:void(0)" onclick="returnToMainConsole()">🏠 返回主控台</a>
            <a href="/papers">📋 试卷管理</a>

            <a href="/generate-paper" class="active">🎯 自定义组卷</a>
            <a href="/upload-paper-rule">📤 上传组卷规则</a>
            <a href="/banks">📚 题库管理</a>
        </div>
        
        <div class="content">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <form method="POST" id="generateForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="form-row">
                    <div class="form-group">
                        <label for="paper_name">试卷名称 *</label>
                        <input type="text" id="paper_name" name="paper_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="difficulty_level">试卷难度</label>
                        <select id="difficulty_level" name="difficulty_level" class="form-control">
                            <option value="简单">简单</option>
                            <option value="中等" selected>中等</option>
                            <option value="困难">困难</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="total_score">试卷总分</label>
                        <input type="number" id="total_score" name="total_score" class="form-control" value="100" min="1" max="200">
                    </div>
                    <div class="form-group">
                        <label for="duration">考试时长（分钟）</label>
                        <input type="number" id="duration" name="duration" class="form-control" value="120" min="30" max="300">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="paper_description">试卷描述</label>
                    <textarea id="paper_description" name="paper_description" class="form-control" rows="3" placeholder="可选：试卷的详细描述"></textarea>
                </div>
                
                <div class="rules-container">
                    <h3>📋 组卷规则</h3>
                    <div id="rulesList">
                        <!-- 规则项将在这里动态添加 -->
                    </div>
                    <button type="button" class="btn btn-success" onclick="addRule()">➕ 添加规则</button>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">🚀 生成试卷</button>
                    <a href="/papers" class="btn btn-danger">❌ 取消</a>
                </div>
                
                <input type="hidden" id="rule_count" name="rule_count" value="0">
            </form>
        </div>
    </div>
    
    <script>
        let ruleIndex = 0;
        
        function addRule() {
            const rulesList = document.getElementById('rulesList');
            const ruleDiv = document.createElement('div');
            ruleDiv.className = 'rule-item';
            ruleDiv.innerHTML = `
                <div class="rule-header">
                    <span class="rule-title">规则 ${ruleIndex + 1}</span>
                    <button type="button" class="remove-rule" onclick="removeRule(this)">×</button>
                </div>
                <div class="rule-grid">
                    <div class="form-group">
                        <label>题型</label>
                        <select name="rule_${ruleIndex}_type" class="form-control" required>
                            <option value="">请选择题型</option>
                            <option value="B">B（单选题）</option>
                            <option value="G">G（多选题）</option>
                            <option value="C">C（判断题）</option>
                            <option value="T">T（填空题）</option>
                            <option value="D">D（简答题）</option>
                            <option value="U">U（计算题）</option>
                            <option value="W">W（论述题）</option>
                            <option value="E">E（案例分析题）</option>
                            <option value="F">F（综合题）</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>难度</label>
                        <select name="rule_${ruleIndex}_difficulty" class="form-control" required>
                            <option value="">请选择难度</option>
                            <option value="1">1（很简单）</option>
                            <option value="2">2（简单）</option>
                            <option value="3">3（中等）</option>
                            <option value="4">4（困难）</option>
                            <option value="5">5（很难）</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>题目数量</label>
                        <input type="number" name="rule_${ruleIndex}_count" class="form-control" value="1" min="1" max="50" required>
                    </div>
                    <div class="form-group">
                        <label>每题分值</label>
                        <input type="number" name="rule_${ruleIndex}_score" class="form-control" value="5.0" min="0.5" max="50" step="0.5" required>
                    </div>
                    <div class="form-group">
                        <label>章节名称</label>
                        <input type="text" name="rule_${ruleIndex}_section" class="form-control" placeholder="如：单选题、多选题等">
                    </div>
                </div>
            `;
            rulesList.appendChild(ruleDiv);
            ruleIndex++;
            document.getElementById('rule_count').value = ruleIndex;
        }
        
        function removeRule(button) {
            button.parentElement.parentElement.remove();
            updateRuleNumbers();
        }
        
        function updateRuleNumbers() {
            const rules = document.querySelectorAll('.rule-item');
            rules.forEach((rule, index) => {
                rule.querySelector('.rule-title').textContent = `规则 ${index + 1}`;
            });
            ruleIndex = rules.length;
            document.getElementById('rule_count').value = ruleIndex;
        }
        
        // 页面加载时添加一个默认规则
        window.onload = function() {
            addRule();
        };
    </script>
</body>
</html>
"""

view_paper_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ paper.name }} - 题库管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .nav-tabs {
            background: #f8f9fa;
            padding: 15px 30px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .nav-tabs a {
            color: #495057;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px 5px 0 0;
            transition: all 0.3s ease;
            border: 1px solid transparent;
            border-bottom: none;
            position: relative;
            background-color: #f8f9fa;
        }
        .nav-tabs a:hover {
            background: #e9ecef;
            color: #007bff;
            border-color: #dee2e6;
        }
        .nav-tabs a.active {
            background: #fff;
            color: #007bff;
            border-color: #dee2e6;
            border-bottom: 2px solid #fff;
            margin-bottom: -1px;
            font-weight: 500;
        }
        .content {
            padding: 30px;
        }
        .paper-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .paper-title {
            font-size: 2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .paper-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
        }
        .paper-description {
            color: #666;
            font-style: italic;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        .questions-section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }
        .question-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .question-number {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1em;
        }
        .question-score {
            background: #28a745;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .question-stem {
            color: #333;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .question-options {
            margin-left: 20px;
        }
        .option {
            margin-bottom: 8px;
            color: #666;
        }
        .actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .flash-messages {
            margin-bottom: 20px;
        }
        .flash-message {
            padding: 12px 20px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .flash-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .flash-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 {{ paper.name }}</h1>
            <p>试卷详情</p>
        </div>
        
        <div class="nav-tabs">
            <a href="http://localhost:8000/dashboard">🏠 返回主控台</a>
            <a href="/papers">📋 试卷管理</a>
            <a href="/view_paper" class="active">📄 查看试卷</a>
        </div>
        
        <div class="content">
            <div class="flash-messages">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">{{ message }}</div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
            </div>
            
            <div class="actions" style="margin-bottom: 20px;">
                <a href="/paper/{{ paper.id }}/export" class="btn btn-success">📥 导出试卷</a>
                <a href="/paper/{{ paper.id }}/verification-report" class="btn btn-success">📋 下载校核报告</a>
                <a href="/paper/{{ paper.id }}/level3_code_stats" class="btn btn-primary">📊 查看三级代码统计</a>
                <a href="/papers" class="btn btn-primary">📋 返回列表</a>
                <form method="POST" action="/paper/{{ paper.id }}/delete" style="display: inline;" onsubmit="return confirm('确定要删除这个试卷吗？')">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">🗑️ 删除试卷</button>
                </form>
            </div>
            
            <div class="paper-info">
                <div class="paper-title">{{ paper.name }}</div>
                <div class="paper-meta">
                    <div class="meta-item">
                        <span>📊 总分:</span>
                        <span>{{ paper.total_score }}分</span>
                    </div>
                    <div class="meta-item">
                        <span>⏱️ 时长:</span>
                        <span>{{ paper.duration }}分钟</span>
                    </div>
                    <div class="meta-item">
                        <span>🎯 难度:</span>
                        <span>{{ paper.difficulty_level or '未设置' }}</span>
                    </div>

                </div>
                {% if paper.description %}
                <div class="paper-description">{{ paper.description }}</div>
                {% endif %}
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total_questions or 0 }}</div>
                    <div class="stat-label">总题目数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.total_score or 0 }}</div>
                    <div class="stat-label">实际总分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.question_types|length or 0 }}</div>
                    <div class="stat-label">题型种类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ stats.sections|length or 0 }}</div>
                    <div class="stat-label">章节数量</div>
                </div>
            </div>
            
            <div class="questions-section">
                <div class="section-title">📝 题目列表</div>
                {% if paper_questions %}
                    {% for pq in paper_questions %}
                    <div class="question-item">
                        <div class="question-header">
                            <span class="question-number">第{{ pq.question_order }}题</span>
                            <span class="question-score">{{ pq.score }}分</span>
                        </div>
                        <div class="question-stem">{{ pq.question.question_stem }}</div>
                        {% if pq.question.option_a or pq.question.option_b or pq.question.option_c or pq.question.option_d or pq.question.option_e %}
                        <div class="question-options">
                            {% if pq.question.option_a %}<div class="option">A. {{ pq.question.option_a }}</div>{% endif %}
                            {% if pq.question.option_b %}<div class="option">B. {{ pq.question.option_b }}</div>{% endif %}
                            {% if pq.question.option_c %}<div class="option">C. {{ pq.question.option_c }}</div>{% endif %}
                            {% if pq.question.option_d %}<div class="option">D. {{ pq.question.option_d }}</div>{% endif %}
                            {% if pq.question.option_e %}<div class="option">E. {{ pq.question.option_e }}</div>{% endif %}
                        </div>
                        {% endif %}
                        <div style="margin-top: 10px; color: #666; font-size: 0.9em;">
                            <span>题型: {{ pq.question.question_type_code }}</span> | 
                            <span>难度: {{ pq.question.difficulty_code }}</span>
                            {% if pq.section_name %} | <span>章节: {{ pq.section_name }}</span>{% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div style="text-align: center; padding: 40px; color: #6c757d;">
                        <h3>📭 暂无题目</h3>
                        <p>这个试卷还没有添加任何题目。</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</body>
</html>
"""





@app.route('/download-paper-rule-template', methods=['GET'])
def download_paper_rule_template():
    """生成并下载组卷规则Excel模板"""
    import pandas as pd
    output = BytesIO()
    # Sheet1: 题型分布
    df1 = pd.DataFrame([
        ['BWGL-3-LL', 'B（单选题）', 10, 2]
    ])
    df1.columns = ['题库名称\nquestion_bank_name', '题型代码\nquestion_type_code', '题量\nquestion_count', '分值\nscore_value']
    # Sheet2: 知识点分布
    df2 = pd.DataFrame([
        ['A', 50, 'B', 60, 'C', 100]
    ])
    df2.columns = ['1级代码\nlevel_1_code', '1级比重(%)\nlevel_1_weight', '2级代码\nlevel_2_code', '2级比重(%)\nlevel_2_weight', '3级代码\nlevel_3_code', '3级比重(%)\nlevel_3_weight']
    with pd.ExcelWriter(output, engine='openpyxl') as writer: # type: ignore
        df1.to_excel(writer, index=False, sheet_name='题型分布')
        df2.to_excel(writer, index=False, sheet_name='知识点分布')
    output.seek(0)
    return send_file(output, as_attachment=True, download_name='组卷规则模板.xlsx', mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

@app.route('/paper/<paper_id>/verification-report', methods=['GET'])
def generate_paper_verification_report(paper_id):
    """生成试卷校核报告，返回与组卷规则模板相同格式的Excel文件"""
    import pandas as pd
    from utils import parse_question_id
    
    db_session = None
    try:
        db_session = get_db()
        
        # 获取试卷信息
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        
        # 获取试卷题目
        paper_questions = db_session.query(PaperQuestion).filter(
            PaperQuestion.paper_id == paper_id
        ).order_by(PaperQuestion.question_order).all()
        
        if not paper_questions:
            flash("试卷中没有题目", "warning")
            return redirect(url_for('paper_detail', paper_id=paper_id))
        
        # 统计题型分布
        question_type_stats = {}
        total_score = 0
        
        for pq in paper_questions:
            q = pq.question
            qtype = q.question_type_code or 'Unknown'
            score = pq.score or 0
            
            if qtype not in question_type_stats:
                question_type_stats[qtype] = {'count': 0, 'total_score': 0, 'score_per_question': score}
            
            question_type_stats[qtype]['count'] += 1
            question_type_stats[qtype]['total_score'] += score
            total_score += score
        
        # 构建题型分布数据
        type_distribution_data = []
        for qtype, stats in question_type_stats.items():
            # 获取题型名称
            type_name_map = {
                'A': 'A（判断题）',
                'B': 'B（单选题）', 
                'C': 'C（多选题）',
                'D': 'D（填空题）',
                'E': 'E（简答题）',
                'F': 'F（论述题）',
                'G': 'G（计算题）',
                'H': 'H（案例分析题）'
            }
            type_display = type_name_map.get(qtype, f'{qtype}（未知题型）')
            
            type_distribution_data.append([
                paper.name,  # 使用试卷名称作为题库名称
                type_display,
                stats['count'],
                stats['score_per_question']
            ])
        
        # 统计三级代码分布
        level3_code_stats = {}
        level2_code_stats = {}
        level1_code_stats = {}
        total_questions = len(paper_questions)
        
        for pq in paper_questions:
            question = pq.question
            try:
                # 解析题目ID，提取层级代码
                parsed = parse_question_id(question.id)
                level_codes = parsed.get("level_codes", [])
                
                if len(level_codes) >= 3:
                    l1, l2, l3 = level_codes[0], level_codes[1], level_codes[2]
                    
                    # 统计各级代码
                    if l1 not in level1_code_stats:
                        level1_code_stats[l1] = 0
                    level1_code_stats[l1] += 1
                    
                    if l2 not in level2_code_stats:
                        level2_code_stats[l2] = 0
                    level2_code_stats[l2] += 1
                    
                    if l3 not in level3_code_stats:
                        level3_code_stats[l3] = 0
                    level3_code_stats[l3] += 1
                    
            except Exception as e:
                print(f"解析题目ID失败: {question.id}, 错误: {e}")
                continue
        
        # 构建知识点分布数据 - 与需求模板格式完全一致
        knowledge_distribution_data = []
        
        # 统计试卷中实际出现的三级代码组合
        actual_level3_combinations = {}
        for pq in paper_questions:
            try:
                parsed = parse_question_id(pq.question.id)
                level_codes = parsed.get("level_codes", [])
                if len(level_codes) >= 3:
                    l1, l2, l3 = level_codes[0], level_codes[1], level_codes[2]
                    combination = f"{l1}-{l2}-{l3}"
                    if combination not in actual_level3_combinations:
                        actual_level3_combinations[combination] = {
                            'l1': l1, 'l2': l2, 'l3': l3, 'count': 0
                        }
                    actual_level3_combinations[combination]['count'] += 1
            except:
                continue
        
        # 按照需求模板的格式生成数据：每行包含一个完整的三级代码组合
        # 格式：1级代码, 1级比重(%), 2级代码, 2级比重(%), 3级代码, 3级比重(%)
        for combination, data in actual_level3_combinations.items():
            l1, l2, l3 = data['l1'], data['l2'], data['l3']
            count = data['count']
            
            # 计算各级代码的比重
            l1_count = sum(1 for pq in paper_questions 
                          if parse_question_id(pq.question.id).get("level_codes", [])[:1] == [l1])
            l2_count = sum(1 for pq in paper_questions 
                          if parse_question_id(pq.question.id).get("level_codes", [])[:2] == [l1, l2])
            l3_count = count
            
            l1_proportion = round((l1_count / total_questions) * 100, 1) if total_questions > 0 else 0
            l2_proportion = round((l2_count / total_questions) * 100, 1) if total_questions > 0 else 0
            l3_proportion = round((l3_count / total_questions) * 100, 1) if total_questions > 0 else 0
            
            knowledge_distribution_data.append([
                l1, l1_proportion, l2, l2_proportion, l3, l3_proportion
            ])
        
        # 如果没有数据，添加一行示例数据以保持格式一致
        if not knowledge_distribution_data:
            knowledge_distribution_data.append([
                'A', 0.0, 'A', 0.0, 'A', 0.0
            ])
        
        # 创建Excel文件
        output = BytesIO()
        
        # 创建题型分布DataFrame
        df1 = pd.DataFrame(type_distribution_data)
        df1.columns = ['题库名称\nquestion_bank_name', '题型代码\nquestion_type_code', '题量\nquestion_count', '分值\nscore_value']
        
        # 创建知识点分布DataFrame
        df2 = pd.DataFrame(knowledge_distribution_data)
        df2.columns = ['1级代码\nlevel_1_code', '1级比重(%)\nlevel_1_weight', 
                      '2级代码\nlevel_2_code', '2级比重(%)\nlevel_2_weight', 
                      '3级代码\nlevel_3_code', '3级比重(%)\nlevel_3_weight']
        
        # 写入Excel文件
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df1.to_excel(writer, index=False, sheet_name='题型分布')
            df2.to_excel(writer, index=False, sheet_name='知识点分布')
        
        output.seek(0)
        
        # 生成文件名
        filename = f'{paper.name}_校核报告.xlsx'
        
        return send_file(
            output, 
            as_attachment=True, 
            download_name=filename, 
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        flash(f"生成校核报告失败: {e}", "error")
        return redirect(url_for('paper_detail', paper_id=paper_id))
    finally:
        close_db(db_session)

@app.route('/paper/<paper_id>/export_excel', methods=['GET', 'POST'])
def export_paper_excel(paper_id):
    """导出单套试卷为Excel，结构与题库导入模板一致"""
    import pandas as pd
    db_session = None
    try:
        db_session = get_db()
        paper = db_session.query(Paper).filter(Paper.id == paper_id).first()
        if not paper:
            flash("试卷不存在", "error")
            return redirect(url_for('papers'))
        paper_questions = db_session.query(PaperQuestion).filter(PaperQuestion.paper_id == paper_id).order_by(PaperQuestion.question_order).all()
        # 构造DataFrame
        data = []
        for pq in paper_questions:
            q = pq.question
            data.append({
                '题库名称\nquestion_bank_name': q.question_bank.name if q.question_bank else '',
                '试题ID\nquestion_id': q.id,
                '序号\nserial_number': str(pq.question_order) if pq.question_order else '',
                '认定点代码\nidentification_point_code': '',
                '题型代码\nquestion_type_code': q.question_type_code,
                '题号\nquestion_number': '',
                '试题（题干）\nquestion_stem': q.question_stem,
                '试题（选项 A）\nquestion_option_a': q.option_a,
                '试题（选项 B）\nquestion_option_b': q.option_b,
                '试题（选项 C）\nquestion_option_c': q.option_c,
                '试题（选项 D）\nquestion_option_d': q.option_d,
                '试题（选项 E）\nquestion_option_e': q.option_e,
                '【图】及位置\nimage_and_position': q.image_location,
                '正确答案\ncorrect_answer': q.correct_answer,
                '难度代码\ndifficulty_code': q.difficulty_code,
                '一致性代码\nconsistency_code': q.consistency_code,
                '解析\nexplanation': q.analysis,
                '分值\nscore_value': pq.score
            })
        df = pd.DataFrame(data)
        
        # 创建一个临时文件来存储Excel
        temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_export')
        os.makedirs(temp_dir, exist_ok=True)
        temp_file = os.path.join(temp_dir, 'temp_paper.xlsx')
        
        # 使用统一的样式模板
        from excel_exporter import apply_excel_template_style
        wb = apply_excel_template_style(df, temp_file, sheet_name='题库模板')
        
        # 读取文件内容到内存
        with open(temp_file, 'rb') as f:
            output = BytesIO(f.read())
        
        # 删除临时文件
        os.remove(temp_file)
        
        # 返回文件
        output.seek(0)
        paper_name_str = str(paper.name) if paper.name is not None else ''
        safe_paper_name = secure_filename(paper_name_str if paper_name_str else f"试卷_{paper_id}")
        return send_file(
            output,
            as_attachment=True,
            download_name=f'{safe_paper_name}.xlsx',
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
    except Exception as e:
        flash(f"导出Excel失败: {e}", "error")
        return redirect(url_for('view_paper', paper_id=paper_id))
    finally:
        close_db(db_session)

@app.route('/import-paper', methods=['GET', 'POST'])
def import_paper():
    """从Excel文件导入试卷"""
    if request.method == 'POST':
        if 'file' not in request.files:
            flash('没有文件部分', 'error')
            return redirect(request.url)
        file = request.files['file']
        if not file or not file.filename:
            flash('未选择文件', 'warning')
            return redirect(request.url)
        
        if allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            db_session = get_db()
            try:
                file.save(filepath)
                # 导入试卷
                paper_name = request.form.get('paper_name', '')
                if not paper_name:
                    paper_name = os.path.splitext(filename)[0]  # 使用文件名作为试卷名称
                
                # 创建新试卷
                paper = Paper(
                    name=paper_name,
                    description=request.form.get('description', ''),
                    total_score=float(request.form.get('total_score', 100)),
                    duration=int(request.form.get('duration', 120)),
                    difficulty_level=request.form.get('difficulty_level', '中等')
                )
                db_session.add(paper)
                db_session.flush()  # 获取paper.id
                
                # 读取Excel文件
                import pandas as pd
                try:
                    # 使用header=[0, 1]读取两行表头
                    df = pd.read_excel(filepath, dtype=str, engine='openpyxl', header=[0, 1])
                    df = df.fillna('')
                    
                    # 处理多级表头，合并中英文列名
                    if isinstance(df.columns, pd.MultiIndex):
                        new_columns = []
                        for col in df.columns:
                            if pd.isna(col[1]) or col[1] == '' or 'Unnamed:' in str(col[1]):
                                # 如果第二行为空或是Unnamed，只使用第一行
                                new_columns.append(col[0])
                            else:
                                # 合并两行，格式为"中文\n英文"
                                newline = '\n'
                                new_columns.append(f"{col[0]}{newline}{col[1]}")
                        df.columns = new_columns
                    
                    # 过滤掉第一行数据（可能是列名重复）
                    if not df.empty:
                        first_row = df.iloc[0]
                        # 检查第一行是否包含列名信息（中英文都检查）
                        invalid_values = ['question_bank_name', 'question_id', 'question_type_code', 
                                        '题库名称', '试题ID', '题型代码', 'bank_name', 'question_content', '题目内容']
                        # 检查第一行的所有值
                        first_row_values = [str(val).strip().lower() for val in first_row.values if str(val).strip()]
                        invalid_values_lower = [v.lower() for v in invalid_values]
                        
                        if any(val in invalid_values_lower for val in first_row_values):
                            df = df.iloc[1:].reset_index(drop=True)
                            print(f"检测到并跳过了第一行数据（包含列名信息）: {first_row_values}")
                    
                    # 标准化列名
                    standardized_columns = {}
                    for col in df.columns:
                        for std_col, aliases in EXPECTED_COLUMNS.items():
                            if col in aliases:
                                standardized_columns[col] = std_col
                                break
                    
                    # 重命名列
                    df = df.rename(columns=standardized_columns)
                    
                    # 导入题目
                    question_count = 0
                    for idx, row in df.iterrows():
                        question_id = row.get('试题ID\nQuestion ID', '')
                        if not question_id:
                            continue
                        
                        # 查找题目
                        question = db_session.query(Question).filter(Question.id == question_id).first()
                        if not question:
                            # 如果题目不存在，创建新题目
                            bank_name = row.get('题库名称\nQuestion Bank Name', '导入试卷题库')
                            # 查找或创建题库
                            bank = db_session.query(QuestionBank).filter(QuestionBank.题库名称 == bank_name).first()
                            if not bank:
                                bank = QuestionBank(题库名称=bank_name)
                                db_session.add(bank)
                                db_session.flush()
                            
                            # 创建新题目
                            question = Question(
                                id=question_id,
                                question_bank_id=bank.id,
                                question_type_code=row.get('题型代码\nQuestion Type Code', ''),
                                stem=row.get('试题（题干）\nQuestion Stem', ''),
                                option_a=row.get('试题（选项A）\nOption A', ''),
                                option_b=row.get('试题（选项B）\nOption B', ''),
                                option_c=row.get('试题（选项C）\nOption C', ''),
                                option_d=row.get('试题（选项D）\nOption D', ''),
                                option_e=row.get('试题（选项E）\nOption E', ''),
                                image_info=row.get('【图】及位置\nImage Location', ''),
                                correct_answer=row.get('正确答案\nCorrect Answer', ''),
                                difficulty_code=row.get('难度代码\nDifficulty Code', '3'),
                                consistency_code=row.get('一致性代码\nConsistency Code', ''),
                                analysis=row.get('解析\nAnalysis', '')
                            )
                            db_session.add(question)
                        
                        # 添加到试卷
                        question_order = int(row.get('序号\nSerial Number', question_count + 1))
                        paper_question = PaperQuestion(
                            paper_id=paper.id,
                            question_id=question.id,
                            question_order=question_order,
                            score=5.0,  # 默认分值
                            section_name=''
                        )
                        db_session.add(paper_question)
                        question_count += 1
                    
                    db_session.commit()
                    flash(f'成功导入试卷，包含 {question_count} 道题目！', 'success')
                    return redirect(url_for('view_paper', paper_id=paper.id))
                except Exception as e:
                    db_session.rollback()
                    flash(f'解析Excel文件失败: {e}', 'error')
            except Exception as e:
                flash(f'处理文件时发生错误: {e}', 'error')
            finally:
                close_db(db_session)
            
            return redirect(url_for('papers'))
    
    # GET请求，显示导入表单
    import_paper_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导入试卷</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        input[type="text"],
        input[type="number"],
        textarea,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .btn {
            display: inline-block;
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-secondary {
            background: #95a5a6;
        }
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-info {
            background-color: #d9edf7;
            border-color: #bce8f1;
            color: #31708f;
        }
        .alert-warning {
            background-color: #fcf8e3;
            border-color: #faebcc;
            color: #8a6d3b;
        }
        .nav {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        .nav a {
            margin: 0 10px;
            text-decoration: none;
            color: #3498db;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📥 导入试卷</h1>
        
        <div class="nav">
            <a href="http://localhost:8000/dashboard">🏠 返回主控台</a>
            <a href="/papers">📋 试卷管理</a>
        </div>
        
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <div class="alert alert-info">
            <p>请上传符合格式的Excel文件，文件结构应与题库导入模板相同。</p>
            <p>您可以先<a href="{{ url_for('download_template') }}" target="_blank">下载题库模板</a>查看格式，或者从<a href="{{ url_for('papers') }}">试卷管理</a>页面导出现有试卷作为参考。</p>
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            
            <div class="form-group">
                <label for="file">选择Excel文件 *</label>
                <input type="file" id="file" name="file" accept=".xlsx,.xls" required>
            </div>
            
            <div class="form-group">
                <label for="paper_name">试卷名称</label>
                <input type="text" id="paper_name" name="paper_name" placeholder="留空将使用文件名">
            </div>
            
            <div class="form-group">
                <label for="description">试卷描述</label>
                <textarea id="description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-group">
                <label for="total_score">总分</label>
                <input type="number" id="total_score" name="total_score" value="100" min="0" step="0.5">
            </div>
            
            <div class="form-group">
                <label for="duration">考试时长（分钟）</label>
                <input type="number" id="duration" name="duration" value="120" min="1">
            </div>
            
            <div class="form-group">
                <label for="difficulty_level">难度等级</label>
                <select id="difficulty_level" name="difficulty_level">
                    <option value="简单">简单</option>
                    <option value="中等" selected>中等</option>
                    <option value="困难">困难</option>
                </select>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button type="submit" class="btn">📥 导入试卷</button>
                <a href="http://localhost:5000" class="btn btn-secondary">取消</a>
            </div>
        </form>
    </div>
</body>
</html>
"""
    return render_template_string(import_paper_template)

@app.route('/export_papers_excel', methods=['POST'])
def export_papers_excel():
    """批量导出多套试卷到一个Excel文件（多Sheet）"""
    import pandas as pd
    from excel_exporter import apply_excel_template_style
    db_session = None
    try:
        db_session = get_db()
        paper_ids = request.form.get('paper_ids', '')
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        paper_ids = [pid.strip() for pid in paper_ids.split(',') if pid.strip()]
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        
        # 创建一个临时文件来存储Excel
        temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'temp_export')
        os.makedirs(temp_dir, exist_ok=True)
        temp_file = os.path.join(temp_dir, 'temp_papers.xlsx')
        
        # 创建工作簿
        wb = None
        
        for idx, pid in enumerate(paper_ids):
            paper = db_session.query(Paper).filter(Paper.id == pid).first()
            if not paper:
                continue
            paper_questions = db_session.query(PaperQuestion).filter(PaperQuestion.paper_id == pid).order_by(PaperQuestion.question_order).all()
            data = []
            for pq in paper_questions:
                q = pq.question
                data.append({
                    '题库名称\nquestion_bank_name': q.question_bank.name if q.question_bank else '',
                    '试题ID\nquestion_id': q.id,
                    '序号\nserial_number': str(pq.question_order) if pq.question_order else '',
                    '认定点代码\nidentification_point_code': '',
                    '题型代码\nquestion_type_code': q.question_type_code,
                    '题号\nquestion_number': '',
                    '试题（题干）\nquestion_stem': q.question_stem,
                    '试题（选项 A）\nquestion_option_a': q.option_a,
                    '试题（选项 B）\nquestion_option_b': q.option_b,
                    '试题（选项 C）\nquestion_option_c': q.option_c,
                    '试题（选项 D）\nquestion_option_d': q.option_d,
                    '试题（选项 E）\nquestion_option_e': q.option_e,
                    '【图】及位置\nimage_and_position': q.image_location,
                    '正确答案\ncorrect_answer': q.correct_answer,
                    '难度代码\ndifficulty_code': q.difficulty_code,
                    '一致性代码\nconsistency_code': q.consistency_code,
                    '解析\nexplanation': q.analysis,
                    '分值\nscore_value': pq.score
                })
            if data:
                df = pd.DataFrame(data)
                sheet_name = paper.name if paper.name else f"试卷{idx+1}"
                # Excel sheet名不能超过31字符
                sheet_name = sheet_name[:31]
                # 使用统一的样式模板，复用同一个工作簿
                wb = apply_excel_template_style(df, None, sheet_name=sheet_name, workbook=wb)
        
        if wb:
            # 保存到临时文件
            wb.save(temp_file)
            
            # 读取文件内容到内存
            with open(temp_file, 'rb') as f:
                output = BytesIO(f.read())
            
            # 删除临时文件
            os.remove(temp_file)
            
            # 返回文件
            output.seek(0)
            return send_file(
                output,
                as_attachment=True,
                download_name='批量导出试卷.xlsx',
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            flash('没有可导出的试卷数据', 'error')
            return redirect(url_for('papers'))
            
    except Exception as e:
        flash(f'批量导出Excel失败: {e}', 'error')
        return redirect(url_for('papers'))
    finally:
        close_db(db_session)

@app.route('/banks', methods=['GET', 'POST'])
def manage_banks():
    """题库管理页面，显示题库列表和处理新增题库"""
    db_session = get_db()
    try:
        if request.method == 'POST':
            bank_name = request.form.get('bank_name', '').strip()
            if not bank_name:
                flash('题库名称不能为空', 'error')
            else:
                # 检查题库名称是否已存在
                existing_bank = db_session.query(QuestionBank).filter_by(题库名称=bank_name).first()
                if existing_bank:
                    flash(f'题库名称 "{bank_name}" 已存在', 'error')
                else:
                    # 创建新题库
                    new_bank = QuestionBank(题库名称=bank_name)
                    db_session.add(new_bank)
                    db_session.commit()
                    flash(f'题库 "{bank_name}" 创建成功', 'success')
        
        # 获取题库列表及其统计信息
        banks = []
        for bank in db_session.query(QuestionBank).order_by(QuestionBank.题库名称).all():
            # 获取该题库的题目数量
            question_count = db_session.query(Question).filter(Question.question_bank_id == bank.id).count()
            
            # 构建题库信息字典
            bank_info = {
                'id': bank.id,
                'name': bank.题库名称,
                'question_count': question_count
            }
            banks.append(bank_info)
        
        return render_template_string(banks_template, banks=banks)
    except Exception as e:
        db_session.rollback()
        flash(f'操作失败：{e}', 'error')
        return render_template_string(banks_template, banks=[])
    finally:
        close_db(db_session)

@app.route('/bank/<bank_id>/delete', methods=['POST'])
def delete_bank(bank_id):
    """删除题库及其下所有题目"""
    db_session = get_db()
    try:
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            flash('题库不存在。', 'error')
        else:
            db_session.delete(bank)
            db_session.commit()
            flash(f'题库 "{bank.题库名称}" 及其下所有题目已删除。', 'success')
    except Exception as e:
        db_session.rollback()
        flash(f'删除题库失败：{e}', 'error')
    finally:
        close_db(db_session)
    return redirect(url_for('manage_banks'))

@app.route('/bank/<bank_id>/questions', methods=['GET', 'POST'])
def manage_bank_questions(bank_id):
    """管理题库中的试题，包括查看、添加、编辑和删除试题"""
    db_session = get_db()
    try:
        # 查找题库
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        if not bank:
            flash('题库不存在', 'error')
            return redirect(url_for('manage_banks'))
        
        if request.method == 'POST':
            action = request.form.get('action')
            
            if action == 'add':
                # 添加新试题
                question_type_code = request.form.get('题型代码')
                question_type_name = request.form.get('question_type_name')
                question_content = request.form.get('question_content')
                question_answer = request.form.get('question_answer')
                question_analysis = request.form.get('question_analysis')
                knowledge_point = request.form.get('knowledge_point')
                difficulty = request.form.get('difficulty')
                
                if not all([question_type_code, question_type_name, question_content]):
                    flash('题型代码、题型名称和题目内容不能为空', 'error')
                else:
                    new_question = Question(
                        question_bank_id=bank_id,
                        question_type_code=question_type_code,
                        question_type_name=question_type_name,
                        question_content=question_content,
                        question_answer=question_answer,
                        question_analysis=question_analysis,
                        knowledge_point=knowledge_point,
                        difficulty=difficulty
                    )
                    db_session.add(new_question)
                    db_session.commit()
                    flash('试题添加成功', 'success')
            
            elif action == 'edit':
                # 编辑试题
                question_id = request.form.get('question_id')
                question = db_session.query(Question).filter_by(id=question_id, question_bank_id=bank_id).first()
                
                if not question:
                    flash('试题不存在', 'error')
                else:
                    question.question_type_code = request.form.get('题型代码')
                    question.question_type_name = request.form.get('question_type_name')
                    question.question_content = request.form.get('question_content')
                    question.question_answer = request.form.get('question_answer')
                    question.question_analysis = request.form.get('question_analysis')
                    question.knowledge_point = request.form.get('knowledge_point')
                    question.difficulty = request.form.get('difficulty')
                    
                    db_session.commit()
                    flash('试题更新成功', 'success')
            
            elif action == 'delete':
                # 删除试题
                question_id = request.form.get('question_id')
                question = db_session.query(Question).filter_by(id=question_id, question_bank_id=bank_id).first()
                
                if not question:
                    flash('试题不存在', 'error')
                else:
                    db_session.delete(question)
                    db_session.commit()
                    flash('试题删除成功', 'success')
        
        # 分页参数
        page = request.args.get('page', 1, type=int)
        per_page = 10  # 每页显示10道题目
        
        # 获取该题库下的试题总数
        total_questions = db_session.query(Question).filter_by(question_bank_id=bank_id).count()
        
        # 计算分页信息
        total_pages = (total_questions + per_page - 1) // per_page
        offset = (page - 1) * per_page
        
        # 获取当前页的试题
        questions = db_session.query(Question).filter_by(question_bank_id=bank_id).offset(offset).limit(per_page).all()
        
        # 分页信息
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total_questions,
            'total_pages': total_pages,
            'has_prev': page > 1,
            'has_next': page < total_pages,
            'prev_num': page - 1 if page > 1 else None,
            'next_num': page + 1 if page < total_pages else None
        }
        
        return render_template_string(bank_questions_template, bank=bank, questions=questions, pagination=pagination)
    except Exception as e:
        db_session.rollback()
        flash(f'操作失败：{e}', 'error')
        return redirect(url_for('manage_banks'))
    finally:
        close_db(db_session)

@app.route('/bank/<bank_id>/question/<question_id>', methods=['GET'])
def view_question(bank_id, question_id):
    """查看单个试题详情"""
    db_session = get_db()
    try:
        # 查找题库和试题
        bank = db_session.query(QuestionBank).filter_by(id=bank_id).first()
        question = db_session.query(Question).filter_by(id=question_id, question_bank_id=bank_id).first()
        
        if not bank or not question:
            flash('题库或试题不存在', 'error')
            return redirect(url_for('manage_banks'))
        
        return render_template_string(question_detail_template, bank=bank, question=question)
    except Exception as e:
        flash(f'操作失败：{e}', 'error')
        return redirect(url_for('manage_bank_questions', bank_id=bank_id))
    finally:
        close_db(db_session)

@app.route('/export_papers_word', methods=['POST'])
def export_papers_word():
    """批量导出多套试卷到一个Word文件（合并）"""
    from docx import Document
    db_session = None
    try:
        db_session = get_db()
        paper_ids = request.form.get('paper_ids', '')
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        paper_ids = [pid.strip() for pid in paper_ids.split(',') if pid.strip()]
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        doc = Document()
        for idx, pid in enumerate(paper_ids):
            generator = PaperGenerator(db_session)
            try:
                sub_doc = generator.export_paper_to_docx(pid)
                sub_doc.seek(0)
                sub = Document(sub_doc)
                if idx > 0:
                    doc.add_page_break()
                for element in sub.element.body:
                    doc.element.body.append(element)
            except Exception as e:
                flash(f'导出试卷 {pid} 失败: {e}', 'error')
        output = BytesIO()
        doc.save(output)
        output.seek(0)
        return send_file(
            output,
            as_attachment=True,
            download_name='批量导出试卷.docx',
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
    except Exception as e:
        flash(f'批量导出Word失败: {e}', 'error')
        return redirect(url_for('papers'))
    finally:
        close_db(db_session)

@app.route('/delete_papers', methods=['POST'])
def delete_papers():
    """批量删除试卷"""
    db_session = None
    try:
        db_session = get_db()
        paper_ids = request.form.get('paper_ids', '')
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        paper_ids = [pid.strip() for pid in paper_ids.split(',') if pid.strip()]
        if not paper_ids:
            flash('未选择试卷', 'error')
            return redirect(url_for('papers'))
        deleted = 0
        for pid in paper_ids:
            paper = db_session.query(Paper).filter(Paper.id == pid).first()
            if paper:
                db_session.delete(paper)
                deleted += 1
        db_session.commit()
        flash(f'成功删除 {deleted} 套试卷', 'success')
    except Exception as e:
        flash(f'批量删除失败: {e}', 'error')
    finally:
        close_db(db_session)
    return redirect(url_for('papers'))

@app.route('/export-excel', methods=['GET'])
def handle_export_excel():
    """导出题库为Excel文件"""
    db = get_db()
    try:
        # 生成唯一的文件名
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], f'questions_export_{timestamp}.xlsx')
        
        # 导出题库
        count = export_db_questions_to_excel(db, output_path)
        
        if count > 0:
            # 返回文件下载
            return send_file(
                output_path,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'题库导出_{timestamp}.xlsx'
            )
        else:
            flash("题库中没有题目可导出。", "warning")
            return redirect(url_for('index'))
            
    except Exception as e:
        flash(f"导出题库失败: {e}", "error")
        return redirect(url_for('index'))
    finally:
        close_db(db)

@app.route('/restart_system', methods=['POST'])
def restart_system():
    """系统重启路由"""
    try:
        import os
        import sys
        flash("系统正在重启，请稍候...", "success")
        # 使用os.execv重启当前进程
        os.execv(sys.executable, ['python'] + sys.argv)
    except Exception as e:
        flash(f"重启失败: {e}", "error")
        return redirect(url_for('index'))

# 健康检查接口
@app.route('/api/health', methods=['GET'])
def health_check():
    """
    健康检查接口
    返回服务状态信息
    """
    try:
        # 检查数据库连接
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        
        return jsonify({
            'status': 'healthy',
            'service': '题库管理系统',
            'version': '1.0.0',
            'timestamp': datetime.datetime.now().isoformat(),
            'database': 'connected'
        }), 200
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'service': '题库管理系统',
            'version': '1.0.0',
            'timestamp': datetime.datetime.now().isoformat(),
            'error': str(e)
        }), 500

if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=5002)
