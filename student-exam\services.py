# -*- coding: utf-8 -*-
"""
考生答题模块 - 服务类

功能说明：
- 考生身份验证
- 考试会话管理
- 答题数据处理
- 考试状态跟踪

作者: SOLO Coding
创建时间: 2024
"""

import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from flask import session


class StudentExamService:
    """考生答题服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        # 外部服务URL配置
        self.user_management_url = 'http://localhost:5001'
        self.exam_management_url = 'http://localhost:5003'
        self.question_bank_url = 'http://localhost:5000'
        self.scoring_management_url = 'http://localhost:5006'
    
    def authenticate_student(self, username: str, password: str) -> Optional[Dict]:
        """
        验证考生身份
        
        参数:
            username: 用户名
            password: 密码
            
        返回:
            Dict: 考生信息，验证失败返回None
        """
        try:
            # 调用用户管理模块进行身份验证
            response = requests.post(
                f'{self.user_management_url}/api/v1/auth/login',
                json={'username': username, 'password': password},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    user_data = data.get('data', {})
                    # 检查用户角色是否为学生
                    if user_data.get('role') == 'student':
                        return {
                            'id': user_data.get('id'),
                            'name': user_data.get('name'),
                            'username': user_data.get('username'),
                            'student_number': user_data.get('student_number')
                        }
            
            self.logger.warning(f"学生身份验证失败: {username}")
            return None
            
        except Exception as e:
            self.logger.error(f"身份验证异常: {e}")
            return None
    
    def get_available_exams(self, student_id: int) -> List[Dict]:
        """
        获取学生可参加的考试列表
        
        参数:
            student_id: 学生ID
            
        返回:
            List[Dict]: 可参加的考试列表
        """
        try:
            # 调用考试管理模块获取可用考试
            response = requests.get(
                f'{self.exam_management_url}/api/v1/exams/available',
                params={'student_id': student_id},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    return data.get('data', [])
            
            self.logger.warning(f"获取可用考试失败，学生ID: {student_id}")
            return []
            
        except Exception as e:
            self.logger.error(f"获取可用考试异常: {e}")
            return []
    
    def start_exam(self, student_id: int, exam_id: int) -> Optional[Dict]:
        """
        开始考试
        
        参数:
            student_id: 学生ID
            exam_id: 考试ID
            
        返回:
            Dict: 考试会话信息，失败返回None
        """
        try:
            # 调用考试管理模块开始考试
            response = requests.post(
                f'{self.exam_management_url}/api/v1/exams/{exam_id}/start',
                json={'student_id': student_id},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    session_data = data.get('data', {})
                    # 设置考试会话信息
                    session['exam_session_id'] = session_data.get('session_id')
                    session['exam_id'] = exam_id
                    session['exam_start_time'] = datetime.utcnow().isoformat()
                    
                    return session_data
            
            self.logger.warning(f"开始考试失败，学生ID: {student_id}, 考试ID: {exam_id}")
            return None
            
        except Exception as e:
            self.logger.error(f"开始考试异常: {e}")
            return None
    
    def get_exam_questions(self, exam_id: int, session_id: int) -> List[Dict]:
        """
        获取考试题目
        
        参数:
            exam_id: 考试ID
            session_id: 考试会话ID
            
        返回:
            List[Dict]: 题目列表
        """
        try:
            # 调用题库管理模块获取题目
            response = requests.get(
                f'{self.question_bank_url}/api/v1/exams/{exam_id}/questions',
                params={'session_id': session_id},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    return data.get('data', [])
            
            self.logger.warning(f"获取考试题目失败，考试ID: {exam_id}")
            return []
            
        except Exception as e:
            self.logger.error(f"获取考试题目异常: {e}")
            return []
    
    def save_answer(self, session_id: int, question_id: int, answer: str) -> bool:
        """
        保存答案
        
        参数:
            session_id: 考试会话ID
            question_id: 题目ID
            answer: 答案内容
            
        返回:
            bool: 保存是否成功
        """
        try:
            # 调用考试管理模块保存答案
            response = requests.post(
                f'{self.exam_management_url}/api/v1/sessions/{session_id}/answers',
                json={
                    'question_id': question_id,
                    'answer': answer,
                    'save_time': datetime.utcnow().isoformat()
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('code') == 200
            
            return False
            
        except Exception as e:
            self.logger.error(f"保存答案异常: {e}")
            return False
    
    def submit_exam(self, session_id: int) -> bool:
        """
        提交考试
        
        参数:
            session_id: 考试会话ID
            
        返回:
            bool: 提交是否成功
        """
        try:
            # 调用考试管理模块提交考试
            response = requests.post(
                f'{self.exam_management_url}/api/v1/sessions/{session_id}/submit',
                json={'submit_time': datetime.utcnow().isoformat()},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    # 清除会话信息
                    session.pop('exam_session_id', None)
                    session.pop('exam_id', None)
                    session.pop('exam_start_time', None)
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"提交考试异常: {e}")
            return False
    
    def get_exam_status(self, session_id: int) -> Optional[Dict]:
        """
        获取考试状态
        
        参数:
            session_id: 考试会话ID
            
        返回:
            Dict: 考试状态信息
        """
        try:
            # 调用考试管理模块获取状态
            response = requests.get(
                f'{self.exam_management_url}/api/v1/sessions/{session_id}/status',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    return data.get('data', {})
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取考试状态异常: {e}")
            return None