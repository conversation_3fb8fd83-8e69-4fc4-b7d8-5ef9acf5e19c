@echo off
chcp 65001 >nul
echo ====================================
echo PHRL题库管理系统 安装程序
echo ====================================
echo.

set "TARGET_DIR=D:\61-PHRL_question_bank"
set "SOURCE_DIR=%~dp0dist\PHRL题库管理系统"

echo 正在检查目标目录...
if not exist "%TARGET_DIR%" (
    echo 创建目标目录: %TARGET_DIR%
    mkdir "%TARGET_DIR%"
)

echo 正在复制程序文件...
xcopy "%SOURCE_DIR%" "%TARGET_DIR%" /E /I /Y
if %errorlevel% neq 0 (
    echo 复制失败！请检查权限或路径。
    pause
    exit /b 1
)

echo.
echo 正在创建桌面快捷方式...
set "DESKTOP=%USERPROFILE%\Desktop"
set "SHORTCUT=%DESKTOP%\PHRL题库管理系统.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%TARGET_DIR%\PHRL题库管理系统.exe'; $Shortcut.WorkingDirectory = '%TARGET_DIR%'; $Shortcut.Description = 'PHRL题库管理系统'; $Shortcut.Save()"

echo.
echo ====================================
echo 安装完成！
echo ====================================
echo 程序已安装到: %TARGET_DIR%
echo 桌面快捷方式已创建
echo.
echo 您可以通过以下方式启动程序：
echo 1. 双击桌面快捷方式
echo 2. 运行: %TARGET_DIR%\PHRL题库管理系统.exe
echo.
echo 按任意键退出...
pause >nul