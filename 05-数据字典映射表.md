## 📊 数据字典映射表
### 1. 用户管理模块 (User Management)
中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 
用户ID id Integer - 是 主键，自增 系统自动生成 
用户名 username String 80 是 唯一，索引 登录账号 
邮箱 email String 120 是 唯一，索引 
邮箱格式验证 密码哈希 password_hash String 255 是 bcrypt加密 不存明文 
真实姓名 real_name String 100 是 - - 
手机号 phone String 20 否 - 11位数字 
角色 role Enum - 是 UserRole枚举 admin/expert/student/grader/internal_supervisor 
状态 status Enum - 是 UserStatus枚举 pending/approved/rejected 
部门 department String 100 否 - - 
职位 position String 100 否 - - 
头像链接 avatar_url String 255 否 - - 
最后登录时间 last_login_time DateTime - 否 - - 
最后登录IP last_login_ip String 45 否 - 支持IPv6 
登录次数 login_count Integer - 是 默认0 - 
创建时间 created_time DateTime - 是 默认当前时间 - 
更新时间 updated_time DateTime - 是 自动更新 - 
创建人 created_by Integer - 否 外键关联users.id - 
更新人 updated_by Integer - 否 外键关联users.id - 
申报职业 declared_occupation String 100 否 - 
申报职业(工种) 
申报级别 declared_level String 50 否 - - 
考试类型 exam_type String 50 否 - - 
考核科目 assessment_subject String 100 否 - - 
题库名称 question_bank_name String 100 否 - 关联题库 
试卷ID test_paper_id String 50 否 - 关联试卷

### 2. 题库管理模块 (Question Bank)
中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 
试题ID id String 255 是 主键，格式规范 9段格式：BWGL-3-LL-B-A-B-C-001-002 
序号 seq_num String 50 否 - 对应Excel序号 
认定点代码 assessment_code String 100 否 - 
考核点代码 题型代码 question_type_code String 20 是 枚举值 B/G/C/T/D/U/W/E/F 
题号 question_number String 50 否 - - 
试题题干 question_stem Text - 是 - - 
选项A option_a Text - 否 - - 
选项B option_b Text - 否 - - 
选项C option_c Text - 否 - - 
选项D option_d Text - 否 - - 
选项E option_e Text - 否 - - 
图片位置 image_location String 255 否 - 图片路径或描述 
正确答案 correct_answer Text - 是 - - 
难度代码 difficulty_code String 20 是 枚举值 1-5级难度 
一致性代码 consistency_code String 20 否 枚举值 1-5级一致性 
解析 analysis Text - 否 - - 
创建时间 created_at DateTime - 是 默认当前时间 - 
更新时间 updated_at DateTime - 是 自动更新 - 
题库ID question_bank_id String 36 是 外键，索引 关联题库表

中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 
题库ID id String 36 是 主键，UUID - 
题库名称 题库名称 String 255 是 唯一 中文字段名 
创建时间 created_at DateTime - 是 默认当前时间 -

中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 
试卷ID id String 36 是 主键，UUID - 
试卷名称 name String 255 是 - - 
试卷描述 description Text - 否 - - 
总分 total_score Float - 是 默认100.0 - 
考试时长 duration Integer - 是 默认120分钟 - 
难度等级 difficulty_level String 50 否 - - 
创建时间 created_at DateTime - 是 默认当前时间 - 
更新时间 updated_at DateTime - 是 自动更新 -

### 3. 考试成绩上报模块 (Exam Score Reporting)
中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 
记录ID id Integer - 是 主键，自增 - 
备案号 bgb117 String 50 是 索引 - 
评价计划编号 bgb164 String 50 是 索引 - 
计划名称 plan_name String 200 否 - - 
状态 status String 10 是 默认active - 
创建时间 created_at DateTime - 是 默认当前时间 - 
更新时间 updated_at DateTime - 是 自动更新 -

中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 
记录ID id Integer - 是 主键，自增 - 
备案号 bgb117 String 50 是 索引 - 
评价计划编号 bgb164 String 50 是 索引 - 
姓名 bgc023 String 50 是 - - 
证件号码 bgc118 String 50 是 索引 - 
理论考试类型 bceb43 String 6 是 - - 
理论正考计划编号 bceb44 String 50 否 - - 
理论成绩 bceb40 Numeric 5,2 否 0-100，2位小数 - 
实操考试类型 bceb45 String 6 是 - - 
实操正考计划编号 bceb46 String 50 否 - - 
实操成绩 bceb41 Numeric 5,2 否 0-100，2位小数 - 
综合评审考试类型 bceb47 String 6 是 - - 
综合正考计划编号 bceb48 String 50 否 - - 
综合评审成绩 bceb42 Numeric 5,2 否 0-100，2位小数 - 
评价业务负责人 bgc116 String 50 是 - - 
负责人确认时间 bgb165 DateTime - 是 - - 
状态 status String 10 是 默认submitted - 
创建时间 created_at DateTime - 是 默认当前时间 - 
更新时间 updated_at DateTime - 是 自动更新 -

### 4. 系统日志模块 (System Logs)
中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 
日志ID id Integer - 是 主键，自增 - 
接口路径 request_path String 200 是 索引 - 
HTTP方法 http_method String 10 是 - GET/POST/PUT/DELETE 
请求头 request_headers Text - 否 - JSON格式 
请求参数 request_params Text - 否 - JSON格式 
响应结果 response_result Text - 否 - JSON格式 
响应时间 response_time Integer - 否 - 毫秒 
客户端IP client_ip String 50 否 - - 
应用ID app_id String 50 否 索引 - 
状态码 status_code Integer - 否 - HTTP状态码 
创建时间 created_at DateTime - 是 默认当前时间，索引 -

### 5. 权限管理模块 (Permission Management)
中文字段名 英文字段名 数据类型 长度 是否必填 约束与说明 备注 角色 role String 20 是 - 角色类型 权限 permission String 50 是 - 权限标识，如"exam:create"

## 📝 字段类型说明
### 数据类型定义
- String : 字符串类型
- Integer : 整数类型
- Float : 浮点数类型
- Text : 长文本类型
- DateTime : 日期时间类型
- Numeric(m,n) : 数值类型，m为总位数，n为小数位数
- Enum : 枚举类型
### 约束说明
- 主键 : 表的唯一标识
- 外键 : 关联其他表的字段
- 唯一 : 字段值在表中唯一
- 索引 : 建立索引以提高查询性能
- 默认值 : 字段的默认值
## 🔍 特殊说明
1. 1.
   试题ID格式 : 采用9段式格式，如"BWGL-3-LL-B-A-B-C-001-002"
2. 2.
   密码存储 : 使用bcrypt加密，不存储明文
3. 3.
   时间格式 : 统一使用"YYYY-MM-DD HH:MM:SS"格式
4. 4.
   成绩范围 : 0-100分，支持2位小数
5. 5.
   角色权限 : 采用"模块:资源:操作:范围"格式
## 📋 未在文档中明确说明的字段
以下字段在技术文档中提及但缺少详细定义：

- 监控模块的具体数据表结构
- 审计模块的详细字段定义
- 考试会话管理的数据模型
- 文件上传管理的数据结构
- 系统配置参数的存储模型
建议在后续开发中补充这些模块的详细数据字典定义。

Excel表格中，建议继续使用以下命名规范：
用户相关：user_id, created_by_user_id, updated_by_user_id
题库相关：question_bank_id, question_id
试卷相关：paper_id, test_paper_id
成绩相关：score_record_id, evaluation_plan_id
业务编号：record_number, plan_number, certificate_number