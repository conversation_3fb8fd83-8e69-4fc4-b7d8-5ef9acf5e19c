from flask_restx import Api
from .auth import auth_ns
from .users import users_ns

def create_api(app):
    """创建API实例"""
    api = Api(
        app,
        version='1.0',
        title='User Management API',
        description='用户管理系统API接口文档',
        doc='/docs/',
        prefix='/api/v1'
    )
    
    # 注册命名空间
    api.add_namespace(auth_ns, path='/auth')
    api.add_namespace(users_ns, path='/users')
    
    return api