<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 实操任务管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-header h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }
        
        .login-header p {
            color: #666;
            margin-bottom: 0;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 0.75rem;
            height: auto;
            transition: all 0.3s ease;
        }
        
        .form-floating > .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .form-floating > label {
            color: #666;
            padding: 1rem 0.75rem;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
            color: white;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .forgot-password {
            color: #667eea;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .forgot-password:hover {
            color: #764ba2;
        }
        
        .system-info {
            text-align: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
            color: #666;
            font-size: 0.9rem;
        }
        
        .loading-spinner {
            display: none;
            margin-right: 0.5rem;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-right: none;
            border-radius: 12px 0 0 12px;
        }
        
        .password-toggle {
            background: transparent;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 12px 12px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .password-toggle:hover {
            background-color: #f8f9fa;
        }
        
        @media (max-width: 576px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .login-header h1 {
                font-size: 1.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="bi bi-shield-lock"></i> 系统登录</h1>
            <p>实操任务管理系统</p>
        </div>
        
        <!-- 消息提示 -->
        <div id="message-container"></div>
        
        <form id="loginForm">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" placeholder="用户名" required>
                <label for="username"><i class="bi bi-person"></i> 用户名</label>
            </div>
            
            <div class="form-floating position-relative">
                <input type="password" class="form-control" id="password" name="password" placeholder="密码" required>
                <label for="password"><i class="bi bi-lock"></i> 密码</label>
                <button type="button" class="btn password-toggle position-absolute top-50 end-0 translate-middle-y me-2" id="togglePassword">
                    <i class="bi bi-eye"></i>
                </button>
            </div>
            
            <div class="remember-forgot">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                    <label class="form-check-label" for="rememberMe">
                        记住我
                    </label>
                </div>
                <a href="#" class="forgot-password">忘记密码？</a>
            </div>
            
            <button type="submit" class="btn btn-login" id="loginBtn">
                <span class="loading-spinner spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                <span class="btn-text">登录</span>
            </button>
        </form>
        
        <div class="system-info">
            <p><i class="bi bi-info-circle"></i> 请使用您的系统账户登录</p>
            <p class="mb-0">如有问题，请联系系统管理员</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 登录表单处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            if (!username || !password) {
                showMessage('请输入用户名和密码', 'danger');
                return;
            }
            
            // 显示加载状态
            setLoading(true);
            
            try {
                const response = await fetch('/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    // 保存Token
                    if (rememberMe) {
                        localStorage.setItem('auth_token', result.data.token);
                        localStorage.setItem('user_info', JSON.stringify(result.data.user));
                    } else {
                        sessionStorage.setItem('auth_token', result.data.token);
                        sessionStorage.setItem('user_info', JSON.stringify(result.data.user));
                    }
                    
                    showMessage('登录成功，正在跳转...', 'success');
                    
                    // 延迟跳转
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    showMessage(result.msg || '登录失败', 'danger');
                }
            } catch (error) {
                console.error('登录错误:', error);
                showMessage('网络错误，请稍后重试', 'danger');
            } finally {
                setLoading(false);
            }
        });
        
        // 密码显示/隐藏切换
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
        
        // 设置加载状态
        function setLoading(loading) {
            const btn = document.getElementById('loginBtn');
            const spinner = btn.querySelector('.loading-spinner');
            const text = btn.querySelector('.btn-text');
            
            if (loading) {
                btn.disabled = true;
                spinner.style.display = 'inline-block';
                text.textContent = '登录中...';
            } else {
                btn.disabled = false;
                spinner.style.display = 'none';
                text.textContent = '登录';
            }
        }
        
        // 显示消息
        function showMessage(message, type = 'info') {
            const container = document.getElementById('message-container');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            container.innerHTML = '';
            container.appendChild(alertDiv);
            
            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    alertDiv.remove();
                }, 3000);
            }
        }
        
        // 检查是否已登录
        function checkAuth() {
            const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
            if (token) {
                // 验证Token有效性
                fetch('/api/v1/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.code === 200) {
                        // Token有效，跳转到首页
                        window.location.href = '/';
                    }
                })
                .catch(error => {
                    // Token无效，清除存储
                    localStorage.removeItem('auth_token');
                    localStorage.removeItem('user_info');
                    sessionStorage.removeItem('auth_token');
                    sessionStorage.removeItem('user_info');
                });
            }
        }
        
        // 页面加载时检查认证状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            
            // 焦点设置
            document.getElementById('username').focus();
            
            // 回车键处理
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('loginForm').dispatchEvent(new Event('submit'));
                }
            });
        });
        
        // 处理忘记密码
        document.querySelector('.forgot-password').addEventListener('click', function(e) {
            e.preventDefault();
            showMessage('请联系系统管理员重置密码', 'info');
        });
    </script>
</body>
</html>