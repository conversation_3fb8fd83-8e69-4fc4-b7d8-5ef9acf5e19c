/* 实操任务管理系统样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #0d6efd !important;
}

/* 面包屑样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.stats-card .stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-group .btn {
    transform: none;
}

.btn-group .btn:hover {
    transform: none;
    box-shadow: none;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 10px 10px 0 0;
}

.modal-title {
    font-weight: 600;
}

/* 文件上传区域样式 */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
}

.file-upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

/* 文件列表样式 */
.file-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 0.5rem;
    background-color: white;
    transition: all 0.3s ease;
}

.file-item:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd;
}

.file-icon {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #6c757d;
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-meta {
    font-size: 0.875rem;
    color: #6c757d;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
}

/* 网格视图样式 */
.grid-view .file-item {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
    height: 200px;
    justify-content: center;
}

.grid-view .file-icon {
    font-size: 3rem;
    margin-right: 0;
    margin-bottom: 1rem;
}

.grid-view .file-actions {
    margin-top: 1rem;
    justify-content: center;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 标签样式 */
.tag {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #495057;
    background-color: #e9ecef;
    border-radius: 12px;
    margin-right: 0.25rem;
    margin-bottom: 0.25rem;
}

.tag-input {
    border: none;
    outline: none;
    background: transparent;
    min-width: 100px;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-left: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 5;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h4 {
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 字符计数器样式 */
.char-counter {
    font-size: 0.875rem;
    color: #6c757d;
    text-align: right;
    margin-top: 0.25rem;
}

.char-counter.warning {
    color: #fd7e14;
}

.char-counter.danger {
    color: #dc3545;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    max-width: 300px;
    text-align: left;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .btn-group {
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.25rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .file-item {
        flex-direction: column;
        text-align: center;
    }
    
    .file-actions {
        margin-top: 0.5rem;
        justify-content: center;
    }
}

/* 打印样式 */
@media print {
    .btn, .navbar, .breadcrumb, .modal {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 0.875rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #212529;
        color: #fff;
    }
    
    .card {
        background-color: #343a40;
        color: #fff;
    }
    
    .table {
        color: #fff;
    }
    
    .table th {
        background-color: #495057;
        color: #fff;
    }
    
    .form-control, .form-select {
        background-color: #495057;
        border-color: #6c757d;
        color: #fff;
    }
    
    .modal-content {
        background-color: #343a40;
        color: #fff;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* 特殊效果 */
.highlight {
    background-color: rgba(255, 193, 7, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    animation: highlight 2s ease-out;
}

@keyframes highlight {
    0% { background-color: rgba(255, 193, 7, 0.5); }
    100% { background-color: rgba(255, 193, 7, 0.2); }
}