# -*- coding: utf-8 -*-
"""
职业技能等级考试系统 - 主启动程序
一键启动所有模块，提供统一的登录和模块管理界面
"""

import os
import sys
import subprocess
import threading
import time
from threading import Thread
import requests
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from werkzeug.security import check_password_hash, generate_password_hash
import json
from datetime import datetime
import signal
import socket
import psutil
import webbrowser
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

app = Flask(__name__)
app.secret_key = 'exam-system-secret-key-2024'

# 配置session支持跨域访问
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['SESSION_COOKIE_SECURE'] = False
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['PERMANENT_SESSION_LIFETIME'] = 7200  # 2小时

# 工具函数
def is_port_in_use(port):
    """检查端口是否被占用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def kill_process_on_port(port):
    """杀死占用指定端口的进程"""
    try:
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                try:
                    connections = proc.net_connections(kind='inet')
                except AttributeError:
                    connections = proc.connections(kind='inet')
                
                for conn in connections:
                    if conn.laddr.port == port:
                        safe_print(f"终止进程 {proc.info['name']} (PID: {proc.info['pid']}) 占用端口 {port}")
                        proc.terminate()
                        proc.wait(timeout=3)
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
    except Exception as e:
        safe_print(f"终止端口 {port} 上的进程时出错: {e}")
    return False

# 系统配置
SYSTEM_CONFIG = {
    'name': '职业技能等级考试系统',
    'version': 'v1.0.0',
    'admin_user': 'admin',
    'admin_password': 'admin123',
    'main_port': 8000
}

# 预定义用户账户
USERS = {
    'admin': {'password': 'admin123', 'role': 'admin'},
    'expert': {'password': 'expert123', 'role': 'expert'},
    'student': {'password': 'student123', 'role': 'student'},
    'grader': {'password': 'grader123', 'role': 'grader'},
    'internal_supervisor': {'password': 'supervisor123', 'role': 'internal_supervisor'}
}

# 模块配置
MODULES_CONFIG = {
    'user_management': {
        'name': '用户管理', 'description': '用户账户管理和权限配置', 'icon': 'fas fa-users', 'port': 5001,
        'path': 'user-management', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['admin']
    },
    'question_bank': {
        'name': '题库管理', 'description': '题目库管理和题目编辑', 'icon': 'fas fa-book', 'port': 5002,
        'path': 'question_bank', 'start_command': 'python app.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['admin', 'expert', 'internal_supervisor']
    },

    'student_exam': {
        'name': '考生答题模块', 'description': '考生参加考试、答题、提交试卷', 'icon': 'fas fa-edit', 'port': 5003,
        'path': 'student-exam', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['student', 'admin']
    },
    'scoring_management': {
        'name': '评分管理模块', 'description': '自动评分和人工阅卷管理', 'icon': 'fas fa-clipboard-check', 'port': 5004,
        'path': 'scoring-management', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['admin', 'grader', 'internal_supervisor']
    },
    'grade_query': {
        'name': '成绩查询模块', 'description': '成绩查询、统计和导出功能', 'icon': 'fas fa-search', 'port': 5007,
        'path': 'grade-query', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['admin', 'grader', 'internal_supervisor', 'student']
    },
    'exam_score_reporting_interface': {
        'name': '成绩上报接口', 'description': '政府要求的唯一互联网连接模块 - 成绩上报和查询接口', 'icon': 'fas fa-upload', 'port': 5008,
        'path': 'exam_score_reporting_interface', 'start_command': 'python app.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['admin', 'teacher', 'grader'], 'special_type': 'internet_connection', 'health_endpoint': '/health'
    },
    'monitoring': {
        'name': '监控审计模块', 'description': '系统状态和性能监控', 'icon': 'fas fa-desktop', 'port': 5009,
        'path': 'monitoring', 'start_command': 'python app.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin', 'grader', 'internal_supervisor']
    },
    'auditing': {
        'name': '审计日志', 'description': '操作日志和安全审计', 'icon': 'fas fa-shield-alt', 'port': 5010,
        'path': 'auditing', 'start_command': 'python app.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin']
    },
    'api_gateway': {
        'name': 'API网关', 'description': 'API路由和统一认证', 'icon': 'fas fa-network-wired', 'port': 8080,
        'path': 'api-gateway', 'start_command': 'python app.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['admin']
    },
    'backup_restore': {
        'name': '备份恢复', 'description': '数据备份和恢复管理', 'icon': 'fas fa-database', 'port': 5016,
        'path': 'backup-restore', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin']
    },
    'notification': {
        'name': '通知中心', 'description': '消息通知和公告管理', 'icon': 'fas fa-bell', 'port': 5017,
        'path': 'notification', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin', 'teacher']
    },
    'report_center': {
        'name': '成绩查询模块', 'description': '统计报表和数据分析', 'icon': 'fas fa-chart-bar', 'port': 5011,
        'path': 'report-center', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin', 'grader', 'internal_supervisor']
    },
    'system_config': {
        'name': '系统配置模块', 'description': '系统参数和环境配置', 'icon': 'fas fa-cogs', 'port': 5012,
        'path': 'system-config', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin']
    },
    'practical_task_management': {
        'name': '实操任务管理模块', 'description': '非集成型试题管理', 'icon': 'fas fa-tasks', 'port': 5013,
        'path': 'practical-task-management', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin', 'grader', 'internal_supervisor']
    },
    'file_management': {
        'name': '成果文件管理模块', 'description': '实操成果文件存储管理', 'icon': 'fas fa-file-archive', 'port': 5014,
        'path': 'file-management', 'start_command': 'python run.py', 'status': 'stopped', 'development_status': 'in_development',
        'permissions': ['admin', 'grader', 'internal_supervisor']
    },
    'file_project_management': {
        'name': '文件与项目管理工具', 'description': '文件管理、项目管理和任务管理工具', 'icon': 'fas fa-folder-open', 'port': 5015,
        'path': 'file_project_management', 'start_command': 'python app.py', 'status': 'stopped', 'development_status': 'completed',
        'permissions': ['admin', 'grader', 'internal_supervisor', 'expert']
    }
}

# 用户权限配置
USER_ROLES = {
    'admin': {'name': '管理员', 'permissions': ['admin'], 'modules': list(MODULES_CONFIG.keys())},
    'expert': {'name': '专家', 'permissions': ['expert'], 'modules': ['question_bank']},
    'student': {'name': '考生', 'permissions': ['student'], 'modules': ['student_portal']},
    'grader': {'name': '考评员', 'permissions': ['grader'], 'modules': ['practical_task_management', 'exam_management', 'student_portal', 'file_management', 'file_project_management', 'grade_management', 'report_center', 'monitoring']},
    'internal_supervisor': {'name': '内部督导员', 'permissions': ['internal_supervisor'], 'modules': ['question_bank', 'practical_task_management', 'exam_management', 'file_management', 'file_project_management', 'grade_management', 'report_center', 'monitoring']}
}

# 全局变量存储模块进程
module_processes = {}

# 模块状态缓存
module_status_cache = {}
module_status_cache_time = {}
CACHE_TIMEOUT = 3  # 减少缓存超时时间到3秒，提高状态更新频率

def print_banner():
    """打印启动横幅"""
    safe_print("=" * 80)
    safe_print(f"[*] {SYSTEM_CONFIG['name']} - 主启动程序")
    safe_print(f"[*] Professional Skills Level Examination System")
    safe_print(f"[*] 版本: {SYSTEM_CONFIG['version']}")
    safe_print("=" * 80)
    safe_print("")

def safe_print(text, encoding='utf-8'):
    """安全打印函数，处理Windows控制台编码问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        safe_text = text.encode('ascii', errors='ignore').decode('ascii')
        print(safe_text)

def check_environment():
    """检查运行环境"""
    import platform
    os_name = platform.system()
    safe_print(f"[+] 操作系统: {os_name} {platform.release()}")
    safe_print(f"[+] Python路径: {sys.executable}")
    work_dir = os.getcwd()
    safe_print(f"[+] 工作目录: {work_dir}")
    project_root = os.path.dirname(os.path.abspath(__file__))
    safe_print(f"[+] 项目根目录: {project_root}")
    required_dirs = ['templates', 'static']
    for dir_name in required_dirs:
        dir_path = os.path.join(project_root, dir_name)
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
    main_port = SYSTEM_CONFIG['main_port']
    if is_port_in_use(main_port):
        safe_print(f"[!] 端口 {main_port} 已被占用，尝试释放...")
        if not kill_process_on_port(main_port):
            safe_print(f"[!] 无法释放端口 {main_port}，请手动处理。")
            return False
    return True

def check_module_status(module_id, use_cache=True):
    """检查模块运行状态 - 使用健康检查端点"""
    if use_cache and module_id in module_status_cache:
        cache_time = module_status_cache_time.get(module_id, 0)
        if time.time() - cache_time < CACHE_TIMEOUT:
            return module_status_cache[module_id]
    
    module = MODULES_CONFIG.get(module_id)
    if not module:
        return 'unknown'
    
    status = 'stopped'
    try:
        # 首先检查端口是否可连接
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(0.3)
            result = s.connect_ex(('localhost', module['port']))
            if result != 0:
                status = 'stopped'
            else:
                # 端口可连接，进一步检查健康检查端点
                try:
                    import requests
                    # 根据模块配置选择健康检查端点
                    health_endpoint = module.get('health_endpoint', '/api/health')
                    health_url = f"http://localhost:{module['port']}{health_endpoint}"
                    response = requests.get(health_url, timeout=2)
                    if response.status_code == 200:
                        # 检查响应内容是否包含健康状态
                        try:
                            health_data = response.json()
                            # 接受 'ok' 或 'healthy' 状态
                            if health_data.get('status') in ['ok', 'healthy']:
                                status = 'running'
                            else:
                                status = 'unhealthy'
                        except:
                            # 如果不是JSON响应，检查是否包含"healthy"或"ok"字符串
                            response_text = response.text.lower()
                            if 'healthy' in response_text or 'ok' in response_text:
                                status = 'running'
                            else:
                                status = 'unhealthy'
                    else:
                        status = 'unhealthy'
                except requests.exceptions.RequestException:
                    # 健康检查失败，但端口可连接，可能是模块启动中
                    status = 'starting'
                except Exception:
                    status = 'unhealthy'
    except Exception:
        status = 'stopped'
    
    # 总是更新缓存，即使use_cache为False
    module_status_cache[module_id] = status
    module_status_cache_time[module_id] = time.time()
    
    return status

def start_module(module_id):
    """启动模块"""
    module = MODULES_CONFIG.get(module_id)
    if not module:
        return False, "模块不存在"
    
    module_path = os.path.join(os.path.dirname(__file__), module['path'])
    if not os.path.exists(module_path):
        return False, "模块目录不存在"
    
    if check_module_status(module_id, use_cache=False) == 'running':
        return True, "模块已在运行"
    
    if is_port_in_use(module['port']):
        kill_process_on_port(module['port'])
        time.sleep(1)
    
    try:
        # 创建临时文件来捕获错误输出
        import tempfile
        temp_stdout = tempfile.NamedTemporaryFile(mode='w+', delete=False, encoding='utf-8')
        temp_stderr = tempfile.NamedTemporaryFile(mode='w+', delete=False, encoding='utf-8')
        
        # 使用虚拟环境中的Python解释器
        python_executable = sys.executable
        start_command_parts = module['start_command'].split()
        if start_command_parts[0] == 'python':
            start_command_parts[0] = python_executable
        
        safe_print(f"[*] 启动模块 {module_id}，命令: {' '.join(start_command_parts)}，路径: {module_path}")
        
        # 在Windows下隐藏控制台窗口，但捕获输出用于调试
        if os.name == 'nt':  # Windows系统
            process = subprocess.Popen(
                start_command_parts,
                cwd=module_path,
                stdout=temp_stdout,
                stderr=temp_stderr,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:  # 其他系统
            process = subprocess.Popen(
                start_command_parts,
                cwd=module_path,
                stdout=temp_stdout,
                stderr=temp_stderr
            )
        
        temp_stdout.close()
        temp_stderr.close()
        module_processes[module_id] = process
        
        max_wait_time = 60
        poll_interval = 0.5
        start_time = time.time()
        last_status_check = 0
        
        while time.time() - start_time < max_wait_time:
            current_time = time.time() - start_time
            
            if process.poll() is not None:
                # 进程退出，读取错误信息
                try:
                    with open(temp_stdout.name, 'r', encoding='utf-8') as f:
                        stdout_content = f.read().strip()
                    with open(temp_stderr.name, 'r', encoding='utf-8') as f:
                        stderr_content = f.read().strip()
                    
                    error_msg = f"进程启动后立即退出 (退出码: {process.returncode})"
                    if stderr_content:
                        error_msg += f"\n错误信息: {stderr_content}"
                    if stdout_content:
                        error_msg += f"\n输出信息: {stdout_content}"
                    
                    safe_print(f"[!] 模块 {module_id} 启动失败: {error_msg}")
                    
                    # 清理临时文件
                    os.unlink(temp_stdout.name)
                    os.unlink(temp_stderr.name)
                    
                    return False, error_msg
                except Exception as read_error:
                    safe_print(f"[!] 读取错误信息失败: {str(read_error)}")
                    return False, f"进程启动后立即退出 (退出码: {process.returncode})"
            
            # 每5秒输出一次状态检查信息
            if current_time - last_status_check >= 5:
                safe_print(f"[*] 模块 {module_id} 启动中... ({current_time:.0f}s/{max_wait_time}s)")
                last_status_check = current_time
            
            if check_module_status(module_id, use_cache=False) == 'running':
                # 清理临时文件
                try:
                    os.unlink(temp_stdout.name)
                    os.unlink(temp_stderr.name)
                except:
                    pass
                safe_print(f"[+] 模块 {module_id} 启动成功 (耗时: {current_time:.1f}s)")
                return True, "模块启动成功"
            time.sleep(poll_interval)

        # 超时处理
        process.terminate()
        process.wait(timeout=2)
        
        # 清理临时文件
        try:
            os.unlink(temp_stdout.name)
            os.unlink(temp_stderr.name)
        except:
            pass
            
        return False, f"模块启动超时（超过 {max_wait_time} 秒）"
            
    except Exception as e:
        safe_print(f"[!] 启动模块 {module_id} 异常: {str(e)}")
        return False, f"启动失败: {str(e)}"

def stop_module(module_id):
    """停止模块"""
    try:
        if module_id in module_processes:
            process = module_processes[module_id]
            if process.poll() is None:
                process.terminate()
                process.wait(timeout=5)
            del module_processes[module_id]
        
        if module_id in MODULES_CONFIG:
            kill_process_on_port(MODULES_CONFIG[module_id]['port'])
        
        safe_print(f"[+] 模块 {module_id} 已停止")
        return True, "模块已停止"
    except Exception as e:
        safe_print(f"[!] 停止模块 {module_id} 失败: {str(e)}")
        return False, f"停止失败: {str(e)}"

# --- Routes ---

@app.route('/')
def index():
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        if username in USERS and USERS[username]['password'] == password:
            user_role = USERS[username]['role']
            session.permanent = True
            session['user'] = {'username': username, 'role': user_role, 'login_time': datetime.now().isoformat()}
            flash(f'欢迎 {USER_ROLES[user_role]["name"]} {username}！', 'success')
            return redirect(url_for('dashboard', auto_refresh='true'))
        else:
            flash('用户名或密码错误！', 'error')
    return render_template('login.html', system_config=SYSTEM_CONFIG, users=USERS)

@app.route('/logout')
def logout():
    session.pop('user', None)
    flash('已退出登录', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    if 'user' not in session:
        return redirect(url_for('login'))
    user = session['user']
    user_role = user.get('role', 'student')
    available_modules = {}
    user_permissions = USER_ROLES.get(user_role, {}).get('modules', [])
    for module_id in user_permissions:
        if module_id in MODULES_CONFIG:
            module = MODULES_CONFIG[module_id].copy()
            # 调用check_module_status函数获取真实的模块状态
            module['status'] = check_module_status(module_id, use_cache=True)
            available_modules[module_id] = module
    
    # 检查是否从模块返回，用于前端处理
    from_module = request.args.get('from_module')
    return_info = {'from_module': from_module} if from_module else {}
    
    return render_template('dashboard.html', user=user, modules=available_modules, system_config=SYSTEM_CONFIG, return_info=return_info)

@app.route('/module/<module_id>')
def module_page(module_id):
    if 'user' not in session:
        return redirect(url_for('login'))
    user = session['user']
    user_role = user.get('role', 'student')
    user_modules = USER_ROLES.get(user_role, {}).get('modules', [])
    if module_id not in user_modules:
        flash('您没有访问此模块的权限！', 'error')
        return redirect(url_for('dashboard'))
    module = MODULES_CONFIG.get(module_id)
    if not module:
        flash('模块不存在！', 'error')
        return redirect(url_for('dashboard'))
    module_url = f"http://localhost:{module['port']}"
    return redirect(module_url)

def start_module_in_background(module_id):
    """Wrapper to run start_module in a thread to avoid blocking the main app."""
    safe_print(f"Starting module {module_id} in a background thread.")
    success, message = start_module(module_id)
    safe_print(f"Background start for {module_id} finished. Success: {success}, Message: {message}")

@app.route('/api/module/<module_id>/start', methods=['POST'])
def api_start_module(module_id):
    """启动模块API（异步）"""
    if 'user' not in session:
        return jsonify({'success': False, 'message': '未登录'}), 401
    thread = Thread(target=start_module_in_background, args=(module_id,))
    thread.daemon = True
    thread.start()
    return jsonify({'success': True, 'message': f'正在后台启动模块 {module_id}...'}), 202

@app.route('/api/module/<module_id>/stop', methods=['POST'])
def api_stop_module(module_id):
    if 'user' not in session:
        return jsonify({'success': False, 'message': '未登录'}), 401
    success, message = stop_module(module_id)
    return jsonify({'success': success, 'message': message})

@app.route('/api/modules/status')
def api_modules_status():
    if 'user' not in session:
        return jsonify({'error': 'Unauthorized'}), 401
    user = session['user']
    user_role = user.get('role', 'student')
    user_permissions = USER_ROLES.get(user_role, {}).get('modules', [])
    requested_modules = request.args.get('modules', '').split(',') if request.args.get('modules') else user_permissions
    valid_module_ids = [mid.strip() for mid in requested_modules if mid.strip() in MODULES_CONFIG and mid.strip() in user_permissions]
    
    # 检查是否强制刷新缓存
    force_refresh = request.args.get('force_refresh', 'false').lower() == 'true'
    
    modules = {}
    for module_id in valid_module_ids:
        module = MODULES_CONFIG[module_id].copy()
        module['id'] = module_id # Ensure id is present
        module['status'] = check_module_status(module_id, use_cache=not force_refresh)
        # Ensure development_status is included in the response
        module['development_status'] = module.get('development_status', 'completed')
        modules[module_id] = module
    return jsonify({'success': True, 'modules': modules})

@app.route('/api/modules/clear-cache', methods=['POST'])
def api_clear_module_cache():
    if 'user' not in session:
        return jsonify({'error': 'Unauthorized'}), 401
    
    # 允许所有登录用户清除缓存，提高响应速度
    module_status_cache.clear()
    module_status_cache_time.clear()
    return jsonify({'success': True, 'message': '模块状态缓存已清除'})

@app.route('/api/modules/refresh-status', methods=['POST'])
def api_refresh_module_status():
    """强制刷新模块状态，清除缓存并重新检查"""
    if 'user' not in session:
        return jsonify({'error': 'Unauthorized'}), 401
    
    # 清除缓存
    module_status_cache.clear()
    module_status_cache_time.clear()
    
    # 返回刷新成功消息
    return jsonify({'success': True, 'message': '模块状态已强制刷新'})

@app.route('/api/modules/start-all', methods=['POST'])
def api_start_all_modules():
    """启动所有已完成的模块API（异步）"""
    if 'user' not in session:
        return jsonify({'success': False, 'message': '未登录'}), 401
    for module_id, module in MODULES_CONFIG.items():
        if module['development_status'] == 'completed':
            thread = threading.Thread(target=start_module_in_background, args=(module_id,))
            thread.daemon = True
            thread.start()
    return jsonify({'success': True, 'message': '正在后台启动所有已完成的模块...'}), 202

@app.route('/api/modules/stop-all', methods=['POST'])
def api_stop_all_modules():
    if 'user' not in session:
        return jsonify({'success': False, 'message': '未登录'}), 401
    results = {}
    for module_id in MODULES_CONFIG:
        success, message = stop_module(module_id)
        results[module_id] = {'success': success, 'message': message}
    return jsonify({'success': True, 'results': results})

@app.route('/api/verify_session', methods=['GET'])
def api_verify_session():
    """验证session状态，供API网关调用"""
    if 'user' not in session:
        return jsonify({'error': 'Unauthorized', 'authenticated': False}), 401
    
    user = session['user']
    return jsonify({
        'authenticated': True,
        'user': {
            'username': user.get('username'),
            'role': user.get('role'),
            'permissions': USER_ROLES.get(user.get('role', 'student'), {}).get('modules', [])
        }
    }), 200

def cleanup_processes():
    safe_print("\n[*] 正在清理进程...")
    for module_id in list(module_processes.keys()):
        stop_module(module_id)
    safe_print("[+] 清理完成")

def auto_start_api_gateway():
    """使用非阻塞方式启动API网关"""
    safe_print("[*] 正在启动API网关...")
    
    try:
        # 直接导入并启动API网关，避免通过start_module的阻塞调用
        import sys
        import os
        api_gateway_path = os.path.join(os.path.dirname(__file__), 'api-gateway')
        sys.path.insert(0, api_gateway_path)
        
        from app import APIGateway
        gateway = APIGateway()
        
        # 使用非阻塞启动
        success = gateway.start_non_blocking()
        
        if success:
            safe_print("[+] API网关非阻塞启动成功")
            # 额外等待确保完全启动
            time.sleep(3)
        else:
            safe_print("[!] API网关启动失败")
            # 如果非阻塞启动失败，尝试传统方式
            safe_print("[*] 尝试传统启动方式...")
            success, message = start_module('api_gateway')
            if success:
                safe_print("[+] API网关传统方式启动成功")
            else:
                safe_print(f"[!] API网关启动失败: {message}")
                
    except Exception as e:
        safe_print(f"[!] API网关启动异常: {e}")
        # 回退到传统启动方式
        safe_print("[*] 回退到传统启动方式...")
        success, message = start_module('api_gateway')
        if success:
            safe_print("[+] API网关传统方式启动成功")
        else:
            safe_print(f"[!] API网关启动失败: {message}")

def auto_start_all_completed_services():
    """按依赖关系和优先级启动所有已完成的模块"""
    safe_print("[*] 正在按依赖关系启动所有其他已完成的服务...")
    
    # 定义启动顺序（基于依赖关系和优先级）
    startup_order = [
        'user_management',                    # 优先级2，基础服务
        'question_bank',                      # 优先级3，依赖用户管理
        'exam_score_reporting_interface',     # 优先级4，考试相关
        'student_exam',                       # 考生答题模块
        'scoring_management',                 # 评分管理模块
        'grade_query',                        # 成绩查询模块
        'file_project_management'             # 文件管理
    ]
    
    for module_id in startup_order:
        if module_id in MODULES_CONFIG and MODULES_CONFIG[module_id]['development_status'] == 'completed':
            safe_print(f"[*] 启动模块: {MODULES_CONFIG[module_id]['name']}")
            thread = threading.Thread(target=start_module_in_background, args=(module_id,))
            thread.daemon = True
            thread.start()
            # 模块间启动间隔，避免资源竞争
            time.sleep(2)
    
    safe_print("[+] 所有服务的后台启动指令已发送。")

def open_browser():
    time.sleep(2)
    url = f"http://localhost:{SYSTEM_CONFIG['main_port']}/login"
    print(f"[*] 正在打开浏览器: {url}")
    webbrowser.open(url)

def signal_handler(sig, frame):
    print("\n正在关闭系统...")
    cleanup_processes()
    sys.exit(0)

def main():
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    print_banner()
    if not check_environment():
        safe_print("[!] 环境检查失败，请检查系统配置")
        return
    safe_print("[+] 环境检查完成")
    safe_print("")
    auto_start_api_gateway()
    auto_start_all_completed_services()
    safe_print("")
    safe_print("[*] 启动主控制台...")
    safe_print(f"[*] 访问地址: http://localhost:{SYSTEM_CONFIG['main_port']}")
    safe_print(f"[*] 默认管理员: {SYSTEM_CONFIG['admin_user']} / {SYSTEM_CONFIG['admin_password']}")
    safe_print("[*] 按 Ctrl+C 停止服务")
    safe_print("-" * 80)
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    try:
        app.run(debug=False, host='0.0.0.0', port=SYSTEM_CONFIG['main_port'], threaded=True, use_reloader=False)
    except KeyboardInterrupt:
        safe_print("\n[*] 正在停止所有服务...")
    finally:
        cleanup_processes()
        print("[+] 所有服务已停止")

if __name__ == '__main__':
    main()