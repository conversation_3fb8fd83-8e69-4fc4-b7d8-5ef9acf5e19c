#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志记录模块
提供操作审计、错误日志、性能监控等功能
"""

import logging
import logging.handlers
import os
import time
import json
import traceback
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from functools import wraps
import sqlite3
import threading
from contextlib import contextmanager

class LogLevel:
    """日志级别常量"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL

class LogType:
    """日志类型常量"""
    OPERATION = "operation"  # 操作日志
    ERROR = "error"         # 错误日志
    PERFORMANCE = "performance"  # 性能日志
    SECURITY = "security"   # 安全日志
    AUDIT = "audit"         # 审计日志
    SYSTEM = "system"       # 系统日志

class CustomFormatter(logging.Formatter):
    """自定义日志格式化器"""
    
    def __init__(self):
        super().__init__()
        self.formatters = {
            logging.DEBUG: logging.Formatter(
                '[%(asctime)s] [DEBUG] [%(name)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ),
            logging.INFO: logging.Formatter(
                '[%(asctime)s] [INFO] [%(name)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ),
            logging.WARNING: logging.Formatter(
                '[%(asctime)s] [WARNING] [%(name)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            ),
            logging.ERROR: logging.Formatter(
                '[%(asctime)s] [ERROR] [%(name)s] %(message)s\n%(pathname)s:%(lineno)d',
                datefmt='%Y-%m-%d %H:%M:%S'
            ),
            logging.CRITICAL: logging.Formatter(
                '[%(asctime)s] [CRITICAL] [%(name)s] %(message)s\n%(pathname)s:%(lineno)d',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
        }
    
    def format(self, record):
        formatter = self.formatters.get(record.levelno)
        if formatter:
            return formatter.format(record)
        return super().format(record)

class DatabaseLogHandler(logging.Handler):
    """数据库日志处理器"""
    
    def __init__(self, db_path: str = "logs.db"):
        super().__init__()
        self.db_path = db_path
        self._init_db()
        self._lock = threading.Lock()
    
    def _init_db(self):
        """初始化日志数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    level TEXT NOT NULL,
                    logger_name TEXT NOT NULL,
                    message TEXT NOT NULL,
                    module TEXT,
                    function TEXT,
                    line_number INTEGER,
                    thread_id INTEGER,
                    process_id INTEGER,
                    log_type TEXT,
                    user_id TEXT,
                    session_id TEXT,
                    extra_data TEXT
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON logs(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_level ON logs(level)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_log_type ON logs(log_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_user_id ON logs(user_id)")
    
    def emit(self, record):
        """发送日志记录到数据库"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    # 获取额外数据
                    extra_data = {}
                    if hasattr(record, 'log_type'):
                        extra_data['log_type'] = record.log_type
                    if hasattr(record, 'user_id'):
                        extra_data['user_id'] = record.user_id
                    if hasattr(record, 'session_id'):
                        extra_data['session_id'] = record.session_id
                    if hasattr(record, 'extra'):
                        extra_data.update(record.extra)
                    
                    conn.execute("""
                        INSERT INTO logs 
                        (timestamp, level, logger_name, message, module, function, 
                         line_number, thread_id, process_id, log_type, user_id, 
                         session_id, extra_data)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        datetime.fromtimestamp(record.created).isoformat(),
                        record.levelname,
                        record.name,
                        record.getMessage(),
                        record.module,
                        record.funcName,
                        record.lineno,
                        record.thread,
                        record.process,
                        getattr(record, 'log_type', None),
                        getattr(record, 'user_id', None),
                        getattr(record, 'session_id', None),
                        json.dumps(extra_data) if extra_data else None
                    ))
        except Exception:
            self.handleError(record)

class ExamLogger:
    """考试系统日志管理器"""
    
    def __init__(self, name: str = "exam_management", log_dir: str = "logs"):
        """
        初始化日志管理器
        
        Args:
            name: 日志器名称
            log_dir: 日志目录
        """
        self.name = name
        self.log_dir = log_dir
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 设置处理器
        self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(CustomFormatter())
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 所有日志
        all_log_file = os.path.join(self.log_dir, "exam_management.log")
        file_handler = logging.handlers.RotatingFileHandler(
            all_log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(CustomFormatter())
        self.logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = os.path.join(self.log_dir, "error.log")
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file, maxBytes=5*1024*1024, backupCount=3, encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(CustomFormatter())
        self.logger.addHandler(error_handler)
        
        # 数据库处理器
        db_log_file = os.path.join(self.log_dir, "logs.db")
        db_handler = DatabaseLogHandler(db_log_file)
        db_handler.setLevel(logging.INFO)
        self.logger.addHandler(db_handler)
    
    def log(self, level: int, message: str, log_type: str = LogType.SYSTEM,
            user_id: str = None, session_id: str = None, extra: Dict = None):
        """
        记录日志
        
        Args:
            level: 日志级别
            message: 日志消息
            log_type: 日志类型
            user_id: 用户ID
            session_id: 会话ID
            extra: 额外数据
        """
        # 创建日志记录
        record = self.logger.makeRecord(
            self.logger.name, level, "", 0, message, (), None
        )
        
        # 添加额外属性
        record.log_type = log_type
        record.user_id = user_id
        record.session_id = session_id
        record.extra = extra or {}
        
        # 发送日志
        self.logger.handle(record)
    
    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        self.log(LogLevel.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """记录信息日志"""
        self.log(LogLevel.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        self.log(LogLevel.WARNING, message, **kwargs)
    
    def error(self, message: str, exception: Exception = None, **kwargs):
        """记录错误日志"""
        if exception:
            message += f"\n异常信息: {str(exception)}\n{traceback.format_exc()}"
        self.log(LogLevel.ERROR, message, log_type=LogType.ERROR, **kwargs)
    
    def critical(self, message: str, exception: Exception = None, **kwargs):
        """记录严重错误日志"""
        if exception:
            message += f"\n异常信息: {str(exception)}\n{traceback.format_exc()}"
        self.log(LogLevel.CRITICAL, message, log_type=LogType.ERROR, **kwargs)
    
    def operation(self, message: str, user_id: str = None, **kwargs):
        """记录操作日志"""
        self.log(LogLevel.INFO, message, log_type=LogType.OPERATION, 
                user_id=user_id, **kwargs)
    
    def security(self, message: str, user_id: str = None, **kwargs):
        """记录安全日志"""
        self.log(LogLevel.WARNING, message, log_type=LogType.SECURITY, 
                user_id=user_id, **kwargs)
    
    def audit(self, message: str, user_id: str = None, **kwargs):
        """记录审计日志"""
        self.log(LogLevel.INFO, message, log_type=LogType.AUDIT, 
                user_id=user_id, **kwargs)
    
    def performance(self, message: str, duration: float = None, **kwargs):
        """记录性能日志"""
        extra = kwargs.get('extra', {})
        if duration is not None:
            extra['duration'] = duration
        kwargs['extra'] = extra
        self.log(LogLevel.INFO, message, log_type=LogType.PERFORMANCE, **kwargs)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, logger: ExamLogger):
        self.logger = logger
    
    @contextmanager
    def monitor(self, operation_name: str, user_id: str = None):
        """
        性能监控上下文管理器
        
        Args:
            operation_name: 操作名称
            user_id: 用户ID
        """
        start_time = time.time()
        try:
            yield
        finally:
            duration = time.time() - start_time
            self.logger.performance(
                f"操作 '{operation_name}' 执行完成",
                duration=duration,
                user_id=user_id,
                extra={'operation': operation_name}
            )
            
            # 如果执行时间过长，记录警告
            if duration > 5.0:  # 5秒
                self.logger.warning(
                    f"操作 '{operation_name}' 执行时间过长: {duration:.2f}秒",
                    log_type=LogType.PERFORMANCE,
                    user_id=user_id
                )

# 全局日志实例
exam_logger = ExamLogger()
performance_monitor = PerformanceMonitor(exam_logger)

# 装饰器函数
def log_operation(operation_name: str = None, log_args: bool = False):
    """
    操作日志装饰器
    
    Args:
        operation_name: 操作名称
        log_args: 是否记录参数
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            user_id = kwargs.get('user_id')
            
            # 记录操作开始
            extra_info = {}
            if log_args:
                extra_info['args'] = str(args)[:200]  # 限制长度
                extra_info['kwargs'] = {k: str(v)[:100] for k, v in kwargs.items()}
            
            exam_logger.operation(
                f"开始执行操作: {op_name}",
                user_id=user_id,
                extra=extra_info
            )
            
            try:
                with performance_monitor.monitor(op_name, user_id):
                    result = func(*args, **kwargs)
                
                exam_logger.operation(
                    f"操作执行成功: {op_name}",
                    user_id=user_id
                )
                return result
            
            except Exception as e:
                exam_logger.error(
                    f"操作执行失败: {op_name}",
                    exception=e,
                    user_id=user_id
                )
                raise
        
        return wrapper
    return decorator

def log_errors(func):
    """
    错误日志装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰器函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            exam_logger.error(
                f"函数 {func.__name__} 执行出错",
                exception=e,
                user_id=kwargs.get('user_id')
            )
            raise
    return wrapper

class LogQuery:
    """日志查询工具"""
    
    def __init__(self, db_path: str = "logs/logs.db"):
        self.db_path = db_path
    
    def get_logs(self, log_type: str = None, user_id: str = None,
                 level: str = None, start_time: datetime = None,
                 end_time: datetime = None, limit: int = 100) -> List[Dict]:
        """
        查询日志
        
        Args:
            log_type: 日志类型
            user_id: 用户ID
            level: 日志级别
            start_time: 开始时间
            end_time: 结束时间
            limit: 返回数量限制
            
        Returns:
            日志列表
        """
        query = "SELECT * FROM logs WHERE 1=1"
        params = []
        
        if log_type:
            query += " AND log_type = ?"
            params.append(log_type)
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if level:
            query += " AND level = ?"
            params.append(level)
        
        if start_time:
            query += " AND timestamp >= ?"
            params.append(start_time.isoformat())
        
        if end_time:
            query += " AND timestamp <= ?"
            params.append(end_time.isoformat())
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            exam_logger.error("查询日志失败", exception=e)
            return []
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, int]:
        """
        获取错误统计
        
        Args:
            hours: 统计时间范围（小时）
            
        Returns:
            错误统计字典
        """
        start_time = datetime.now() - timedelta(hours=hours)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT level, COUNT(*) as count
                    FROM logs 
                    WHERE timestamp >= ? AND level IN ('ERROR', 'CRITICAL')
                    GROUP BY level
                """, (start_time.isoformat(),))
                
                return {row[0]: row[1] for row in cursor.fetchall()}
        except Exception as e:
            exam_logger.error("获取错误统计失败", exception=e)
            return {}

# 使用示例
if __name__ == "__main__":
    # 测试日志功能
    exam_logger.info("系统启动", log_type=LogType.SYSTEM)
    exam_logger.operation("用户登录", user_id="user123")
    exam_logger.security("检测到可疑操作", user_id="user456")
    
    # 测试性能监控
    with performance_monitor.monitor("数据库查询", "user123"):
        time.sleep(0.1)  # 模拟操作
    
    # 测试装饰器
    @log_operation("测试操作")
    def test_function(user_id=None):
        return "success"
    
    test_function(user_id="user123")
    
    # 查询日志
    log_query = LogQuery()
    recent_logs = log_query.get_logs(limit=5)
    print(f"最近的日志: {len(recent_logs)} 条")
    
    error_summary = log_query.get_error_summary()
    print(f"错误统计: {error_summary}")