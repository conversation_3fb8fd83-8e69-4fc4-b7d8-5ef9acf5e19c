{% extends "base.html" %}

{% block title %}用户管理 - 实操任务管理系统{% endblock %}

{% block extra_css %}
<style>
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #6610f2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 16px;
    }
    
    .role-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .user-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .user-card {
        transition: transform 0.2s;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .user-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="bi bi-people"></i> 用户管理</h2>
            <p class="text-muted mb-0">管理系统用户账户和权限</p>
        </div>
        <div>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createUserModal">
                <i class="bi bi-person-plus"></i> 创建用户
            </button>
        </div>
    </div>
    
    <!-- 用户统计 -->
    <div class="user-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="total-users">0</h3>
                    <p class="mb-0">总用户数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="active-users">0</h3>
                    <p class="mb-0">活跃用户</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="admin-users">0</h3>
                    <p class="mb-0">管理员</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="teacher-users">0</h3>
                    <p class="mb-0">教师</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search-keyword" placeholder="搜索用户名、姓名或邮箱...">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filter-role">
                        <option value="">所有角色</option>
                        <option value="admin">管理员</option>
                        <option value="teacher">教师</option>
                        <option value="student">学生</option>
                        <option value="examiner">考官</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filter-status">
                        <option value="">所有状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">禁用</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="sort-by">
                        <option value="created_at_desc">创建时间(新到旧)</option>
                        <option value="created_at_asc">创建时间(旧到新)</option>
                        <option value="username_asc">用户名(A-Z)</option>
                        <option value="username_desc">用户名(Z-A)</option>
                        <option value="last_login_desc">最后登录</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-primary w-100" onclick="searchUsers()">
                        <i class="bi bi-search"></i> 搜索
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 用户列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list"></i> 用户列表</h5>
            <div>
                <button class="btn btn-sm btn-outline-danger" id="batch-delete-btn" style="display: none;" onclick="batchDeleteUsers()">
                    <i class="bi bi-trash"></i> 批量删除
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="exportUsers()">
                    <i class="bi bi-download"></i> 导出
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 加载指示器 -->
            <div id="loading-indicator" class="text-center py-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载用户数据...</p>
            </div>
            
            <!-- 用户表格 -->
            <div class="table-responsive" id="users-table-container">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                            </th>
                            <th>用户</th>
                            <th>角色</th>
                            <th>状态</th>
                            <th>最后登录</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="users-table-body">
                        <!-- 用户数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 空状态 -->
            <div id="empty-state" class="text-center py-5" style="display: none;">
                <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-3">暂无用户数据</h5>
                <p class="text-muted">点击上方按钮创建第一个用户</p>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="用户分页" id="pagination-container" style="display: none;">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 创建用户模态框 -->
<div class="modal fade" id="createUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="create-username" class="form-label">用户名 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="create-username" required>
                        <div class="form-text">用户名只能包含字母、数字和下划线</div>
                    </div>
                    <div class="mb-3">
                        <label for="create-password" class="form-label">密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="create-password" required minlength="6">
                        <div class="form-text">密码长度至少6位</div>
                    </div>
                    <div class="mb-3">
                        <label for="create-full-name" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="create-full-name">
                    </div>
                    <div class="mb-3">
                        <label for="create-email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="create-email">
                    </div>
                    <div class="mb-3">
                        <label for="create-role" class="form-label">角色 <span class="text-danger">*</span></label>
                        <select class="form-select" id="create-role" required>
                            <option value="">请选择角色</option>
                            <option value="admin">管理员</option>
                            <option value="teacher">教师</option>
                            <option value="student">学生</option>
                            <option value="examiner">考官</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="create-is-active" checked>
                            <label class="form-check-label" for="create-is-active">
                                启用用户
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建用户</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" id="edit-user-id">
                    <div class="mb-3">
                        <label for="edit-username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="edit-username" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit-full-name" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="edit-full-name">
                    </div>
                    <div class="mb-3">
                        <label for="edit-email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="edit-email">
                    </div>
                    <div class="mb-3">
                        <label for="edit-role" class="form-label">角色 <span class="text-danger">*</span></label>
                        <select class="form-select" id="edit-role" required>
                            <option value="admin">管理员</option>
                            <option value="teacher">教师</option>
                            <option value="student">学生</option>
                            <option value="examiner">考官</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit-is-active">
                            <label class="form-check-label" for="edit-is-active">
                                启用用户
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 重置密码模态框 -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重置密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="resetPasswordForm">
                <div class="modal-body">
                    <input type="hidden" id="reset-user-id">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        确定要重置用户 <strong id="reset-username"></strong> 的密码吗？
                    </div>
                    <div class="mb-3">
                        <label for="reset-new-password" class="form-label">新密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="reset-new-password" required minlength="6">
                        <div class="form-text">密码长度至少6位</div>
                    </div>
                    <div class="mb-3">
                        <label for="reset-confirm-password" class="form-label">确认密码 <span class="text-danger">*</span></label>
                        <input type="password" class="form-control" id="reset-confirm-password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">重置密码</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let currentPage = 1;
    let totalPages = 1;
    let selectedUsers = new Set();
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadUsers();
        loadUserStats();
        
        // 绑定搜索事件
        document.getElementById('search-keyword').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchUsers();
            }
        });
        
        // 绑定表单提交事件
        document.getElementById('createUserForm').addEventListener('submit', handleCreateUser);
        document.getElementById('editUserForm').addEventListener('submit', handleEditUser);
        document.getElementById('resetPasswordForm').addEventListener('submit', handleResetPassword);
    });
    
    // 加载用户统计
    async function loadUserStats() {
        try {
            const response = await apiRequest('/api/v1/auth/users/stats');
            if (response.code === 200) {
                const stats = response.data;
                document.getElementById('total-users').textContent = stats.total || 0;
                document.getElementById('active-users').textContent = stats.active || 0;
                document.getElementById('admin-users').textContent = stats.admin || 0;
                document.getElementById('teacher-users').textContent = stats.teacher || 0;
            }
        } catch (error) {
            console.error('加载用户统计失败:', error);
        }
    }
    
    // 加载用户列表
    async function loadUsers(page = 1) {
        showLoading(true);
        
        try {
            const params = new URLSearchParams({
                page: page,
                per_page: 20,
                keyword: document.getElementById('search-keyword').value,
                role: document.getElementById('filter-role').value,
                status: document.getElementById('filter-status').value,
                sort_by: document.getElementById('sort-by').value
            });
            
            const response = await apiRequest(`/api/v1/auth/users?${params}`);
            
            if (response.code === 200) {
                const data = response.data;
                renderUsers(data.users);
                renderPagination(data.pagination);
                currentPage = page;
                totalPages = data.pagination.total_pages;
            } else {
                showMessage(response.msg || '加载用户列表失败', 'error');
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
            showMessage('加载用户列表失败', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    // 渲染用户列表
    function renderUsers(users) {
        const tbody = document.getElementById('users-table-body');
        const emptyState = document.getElementById('empty-state');
        const tableContainer = document.getElementById('users-table-container');
        
        if (!users || users.length === 0) {
            tbody.innerHTML = '';
            tableContainer.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }
        
        tableContainer.style.display = 'block';
        emptyState.style.display = 'none';
        
        tbody.innerHTML = users.map(user => `
            <tr>
                <td>
                    <input type="checkbox" class="user-checkbox" value="${user.id}" onchange="updateSelection()">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="user-avatar me-3">
                            ${(user.full_name || user.username).charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <div class="fw-bold">${user.full_name || user.username}</div>
                            <small class="text-muted">@${user.username}</small>
                            ${user.email ? `<br><small class="text-muted">${user.email}</small>` : ''}
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge role-badge ${getRoleBadgeClass(user.role)}">
                        ${getRoleName(user.role)}
                    </span>
                </td>
                <td>
                    <span class="badge status-badge ${user.is_active ? 'bg-success' : 'bg-secondary'}">
                        ${user.is_active ? '活跃' : '禁用'}
                    </span>
                </td>
                <td>
                    <small class="text-muted">
                        ${user.last_login ? formatDateTime(user.last_login) : '从未登录'}
                    </small>
                </td>
                <td>
                    <small class="text-muted">
                        ${formatDateTime(user.created_at)}
                    </small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editUser(${user.id})" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="resetPassword(${user.id}, '${user.username}')" title="重置密码">
                            <i class="bi bi-key"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteUser(${user.id}, '${user.username}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    // 获取角色徽章样式
    function getRoleBadgeClass(role) {
        const classes = {
            'admin': 'bg-danger',
            'teacher': 'bg-primary',
            'student': 'bg-info',
            'examiner': 'bg-warning text-dark'
        };
        return classes[role] || 'bg-secondary';
    }
    
    // 获取角色名称
    function getRoleName(role) {
        const names = {
            'admin': '管理员',
            'teacher': '教师',
            'student': '学生',
            'examiner': '考官'
        };
        return names[role] || role;
    }
    
    // 渲染分页
    function renderPagination(pagination) {
        const container = document.getElementById('pagination-container');
        const paginationEl = document.getElementById('pagination');
        
        if (pagination.total_pages <= 1) {
            container.style.display = 'none';
            return;
        }
        
        container.style.display = 'block';
        
        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${pagination.current_page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadUsers(${pagination.current_page - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        for (let i = 1; i <= pagination.total_pages; i++) {
            if (i === 1 || i === pagination.total_pages || 
                (i >= pagination.current_page - 2 && i <= pagination.current_page + 2)) {
                html += `
                    <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
                    </li>
                `;
            } else if (i === pagination.current_page - 3 || i === pagination.current_page + 3) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // 下一页
        html += `
            <li class="page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadUsers(${pagination.current_page + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;
        
        paginationEl.innerHTML = html;
    }
    
    // 搜索用户
    function searchUsers() {
        currentPage = 1;
        loadUsers(1);
    }
    
    // 显示加载状态
    function showLoading(show) {
        const loading = document.getElementById('loading-indicator');
        const table = document.getElementById('users-table-container');
        const empty = document.getElementById('empty-state');
        
        if (show) {
            loading.style.display = 'block';
            table.style.display = 'none';
            empty.style.display = 'none';
        } else {
            loading.style.display = 'none';
        }
    }
    
    // 全选/取消全选
    function toggleSelectAll() {
        const selectAll = document.getElementById('select-all');
        const checkboxes = document.querySelectorAll('.user-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        
        updateSelection();
    }
    
    // 更新选择状态
    function updateSelection() {
        const checkboxes = document.querySelectorAll('.user-checkbox:checked');
        const batchDeleteBtn = document.getElementById('batch-delete-btn');
        
        selectedUsers.clear();
        checkboxes.forEach(checkbox => {
            selectedUsers.add(parseInt(checkbox.value));
        });
        
        if (selectedUsers.size > 0) {
            batchDeleteBtn.style.display = 'inline-block';
        } else {
            batchDeleteBtn.style.display = 'none';
        }
        
        // 更新全选状态
        const allCheckboxes = document.querySelectorAll('.user-checkbox');
        const selectAll = document.getElementById('select-all');
        selectAll.checked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;
    }
    
    // 创建用户
    async function handleCreateUser(e) {
        e.preventDefault();
        
        const formData = {
            username: document.getElementById('create-username').value,
            password: document.getElementById('create-password').value,
            full_name: document.getElementById('create-full-name').value,
            email: document.getElementById('create-email').value,
            role: document.getElementById('create-role').value,
            is_active: document.getElementById('create-is-active').checked
        };
        
        try {
            const response = await apiRequest('/api/v1/auth/users', {
                method: 'POST',
                body: JSON.stringify(formData)
            });
            
            if (response.code === 200) {
                showMessage('用户创建成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createUserModal')).hide();
                document.getElementById('createUserForm').reset();
                loadUsers(currentPage);
                loadUserStats();
            } else {
                showMessage(response.msg || '创建用户失败', 'error');
            }
        } catch (error) {
            console.error('创建用户失败:', error);
            showMessage('创建用户失败', 'error');
        }
    }
    
    // 编辑用户
    async function editUser(userId) {
        try {
            const response = await apiRequest(`/api/v1/auth/users/${userId}`);
            
            if (response.code === 200) {
                const user = response.data.user;
                
                document.getElementById('edit-user-id').value = user.id;
                document.getElementById('edit-username').value = user.username;
                document.getElementById('edit-full-name').value = user.full_name || '';
                document.getElementById('edit-email').value = user.email || '';
                document.getElementById('edit-role').value = user.role;
                document.getElementById('edit-is-active').checked = user.is_active;
                
                new bootstrap.Modal(document.getElementById('editUserModal')).show();
            } else {
                showMessage(response.msg || '获取用户信息失败', 'error');
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
            showMessage('获取用户信息失败', 'error');
        }
    }
    
    // 处理编辑用户
    async function handleEditUser(e) {
        e.preventDefault();
        
        const userId = document.getElementById('edit-user-id').value;
        const formData = {
            full_name: document.getElementById('edit-full-name').value,
            email: document.getElementById('edit-email').value,
            role: document.getElementById('edit-role').value,
            is_active: document.getElementById('edit-is-active').checked
        };
        
        try {
            const response = await apiRequest(`/api/v1/auth/users/${userId}`, {
                method: 'PUT',
                body: JSON.stringify(formData)
            });
            
            if (response.code === 200) {
                showMessage('用户更新成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
                loadUsers(currentPage);
                loadUserStats();
            } else {
                showMessage(response.msg || '更新用户失败', 'error');
            }
        } catch (error) {
            console.error('更新用户失败:', error);
            showMessage('更新用户失败', 'error');
        }
    }
    
    // 重置密码
    function resetPassword(userId, username) {
        document.getElementById('reset-user-id').value = userId;
        document.getElementById('reset-username').textContent = username;
        document.getElementById('reset-new-password').value = '';
        document.getElementById('reset-confirm-password').value = '';
        
        new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
    }
    
    // 处理重置密码
    async function handleResetPassword(e) {
        e.preventDefault();
        
        const userId = document.getElementById('reset-user-id').value;
        const newPassword = document.getElementById('reset-new-password').value;
        const confirmPassword = document.getElementById('reset-confirm-password').value;
        
        if (newPassword !== confirmPassword) {
            showMessage('两次输入的密码不一致', 'error');
            return;
        }
        
        try {
            const response = await apiRequest(`/api/v1/auth/users/${userId}/reset-password`, {
                method: 'POST',
                body: JSON.stringify({ new_password: newPassword })
            });
            
            if (response.code === 200) {
                showMessage('密码重置成功', 'success');
                bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
            } else {
                showMessage(response.msg || '重置密码失败', 'error');
            }
        } catch (error) {
            console.error('重置密码失败:', error);
            showMessage('重置密码失败', 'error');
        }
    }
    
    // 删除用户
    async function deleteUser(userId, username) {
        if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可撤销。`)) {
            return;
        }
        
        try {
            const response = await apiRequest(`/api/v1/auth/users/${userId}`, {
                method: 'DELETE'
            });
            
            if (response.code === 200) {
                showMessage('用户删除成功', 'success');
                loadUsers(currentPage);
                loadUserStats();
            } else {
                showMessage(response.msg || '删除用户失败', 'error');
            }
        } catch (error) {
            console.error('删除用户失败:', error);
            showMessage('删除用户失败', 'error');
        }
    }
    
    // 批量删除用户
    async function batchDeleteUsers() {
        if (selectedUsers.size === 0) {
            showMessage('请选择要删除的用户', 'warning');
            return;
        }
        
        if (!confirm(`确定要删除选中的 ${selectedUsers.size} 个用户吗？此操作不可撤销。`)) {
            return;
        }
        
        try {
            const response = await apiRequest('/api/v1/auth/users/batch-delete', {
                method: 'POST',
                body: JSON.stringify({ user_ids: Array.from(selectedUsers) })
            });
            
            if (response.code === 200) {
                showMessage(`成功删除 ${selectedUsers.size} 个用户`, 'success');
                selectedUsers.clear();
                document.getElementById('batch-delete-btn').style.display = 'none';
                document.getElementById('select-all').checked = false;
                loadUsers(currentPage);
                loadUserStats();
            } else {
                showMessage(response.msg || '批量删除失败', 'error');
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            showMessage('批量删除失败', 'error');
        }
    }
    
    // 导出用户
    async function exportUsers() {
        try {
            const params = new URLSearchParams({
                keyword: document.getElementById('search-keyword').value,
                role: document.getElementById('filter-role').value,
                status: document.getElementById('filter-status').value,
                sort_by: document.getElementById('sort-by').value
            });
            
            const response = await fetch(`/api/v1/auth/users/export?${params}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')}`
                }
            });
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `users_${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                showMessage('用户数据导出成功', 'success');
            } else {
                showMessage('导出失败', 'error');
            }
        } catch (error) {
            console.error('导出失败:', error);
            showMessage('导出失败', 'error');
        }
    }
</script>
{% endblock %}