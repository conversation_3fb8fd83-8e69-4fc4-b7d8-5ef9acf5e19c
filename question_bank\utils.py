import uuid

def parse_question_id(id_str):
    """
    解析题目ID字符串，返回结构化数据
    标准9段格式：第1段-第2段-第3段-第4段-第5段-第6段-第7段-第8段-第9段
    例如：BWGL-3-LL-B-A-A-A-001-001
    其中：
    - 第1段：题库代码（BWGL）
    - 第2段：等级代码（3）
    - 第3段：类别代码（LL）
    - 第4段：题型代码缩写（B-单选题）
    - 第5-7段：三级代码（A-A-A）
    - 第8段：知识点代码（001）
    - 第9段：顺序号（001）
    """
    parts = id_str.strip().split('-')
    if len(parts) != 9:
        raise ValueError(f"ID格式错误: '{id_str}'. 应为9段格式'题库代码-等级代码-类别代码-题型代码缩写-一级代码-二级代码-三级代码-知识点代码-顺序号'")
    
    return {
        "full_id": id_str,
        "bank_code": parts[0],
        "level_code": parts[1],
        "category_code": parts[2],
        "question_type_code": parts[3],
        "level_codes": [parts[4], parts[5], parts[6]], # 三级代码列表
        "knowledge_point_code": parts[7],
        "sequence_number": parts[8]
    }

def generate_question_id(level_codes, knowledge_point_code, sequence_number):
    """
    Generates a composite question ID.
    level_codes: A list or tuple of strings for the hierarchical codes (e.g., ['A', 'B', 'C'])
    knowledge_point_code: String for the knowledge point code (e.g., 'KP001')
    sequence_number: Integer or string for the sequence (e.g., 1 or '001'). Will be zero-padded to 3 digits.
    Returns the formatted ID string.
    """
    if not isinstance(level_codes, (list, tuple)) or not all(isinstance(lc, str) for lc in level_codes):
        raise ValueError("level_codes must be a list or tuple of strings.")
    if not isinstance(knowledge_point_code, str) or not knowledge_point_code.strip():
        raise ValueError("knowledge_point_code must be a non-empty string.")
    
    try:
        seq_int = int(sequence_number)
        if seq_int < 0:
            raise ValueError("Sequence number cannot be negative.")
        # Zero-pad the sequence number, e.g., to 3 digits
        formatted_sequence = f"{seq_int:03d}"
    except ValueError:
        raise ValueError(f"Invalid sequence_number: '{sequence_number}'. Must be convertible to an integer.")

    id_parts = list(level_codes) + [knowledge_point_code.strip(), formatted_sequence]
    return "-".join(id_parts)

# Example Usage:
if __name__ == '__main__':
    try:
        parsed = parse_question_id("SYS-DEV-PY-OOP-001")
        print(f"Parsed ID: {parsed}")
    except ValueError as e:
        print(f"Error parsing: {e}")

    try:
        parsed_invalid = parse_question_id("A-B-1") # Too short
        print(f"Parsed Invalid ID: {parsed_invalid}")
    except ValueError as e:
        print(f"Error parsing invalid ID: {e}")

    new_id = generate_question_id(['SYS', 'WEB', 'API'], 'AUTH', 5)
    print(f"Generated ID: {new_id}") # Expected: SYS-WEB-API-AUTH-005

    new_id_str_seq = generate_question_id(['DATA', 'SQL'], 'QUERY', '042')
    print(f"Generated ID with str sequence: {new_id_str_seq}") # Expected: DATA-SQL-QUERY-042

    try:
        generate_question_id(['MOD1'], 'KP1', 'abc') # Invalid sequence
    except ValueError as e:
        print(f"Error generating ID: {e}")