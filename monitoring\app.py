# -*- coding: utf-8 -*-
"""
监控审计模块 - 核心服务
"""
import psutil
import socket
from flask import Flask, jsonify
from flask_cors import CORS

# A list of core services and their ports to monitor
# In a real system, this would come from a config service or service discovery
SERVICES_TO_MONITOR = {
    'user_management': 5002,
    'question_bank': 5001,
    'exam_management': 5003,
    'score_management': 5004,
    'api_gateway': 5008,
}

def check_service_health(port):
    """Checks if a service is running by connecting to its port."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.settimeout(1)  # Set a timeout to avoid long waits
        try:
            s.connect(('localhost', port))
            return 'running'
        except (socket.timeout, ConnectionRefusedError):
            return 'stopped'

def create_app():
    """Application factory to create and configure the Flask app."""
    app = Flask(__name__)
    CORS(app)

    @app.route('/', methods=['GET'])
    def index():
        return jsonify({'service': 'Monitoring API', 'status': 'running'})

    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({'status': 'healthy', 'service': 'monitoring'})

    @app.route('/api/v1/status/system', methods=['GET'])
    def get_system_status():
        """Returns the current status of the host system and core services."""

        # Get host system metrics
        cpu_usage = psutil.cpu_percent(interval=0.1)
        memory_info = psutil.virtual_memory()

        # Check status of other core services
        service_status = {
            name: check_service_health(port)
            for name, port in SERVICES_TO_MONITOR.items()
        }

        return jsonify({
            'host_metrics': {
                'cpu_percent': cpu_usage,
                'memory_percent': memory_info.percent,
                'memory_total_gb': round(memory_info.total / (1024**3), 2),
                'memory_used_gb': round(memory_info.used / (1024**3), 2),
            },
            'service_status': service_status
        })

    return app

if __name__ == '__main__':
    app = create_app()
    # The port is configured to 5009 to match main_launcher.py configuration
    port = 5009
    print("--- Monitoring Service ---")
    print(f"Starting server at: http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=True)
