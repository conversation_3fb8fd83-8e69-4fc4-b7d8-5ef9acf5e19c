# -*- coding: utf-8 -*-
"""
考试编排模块 - 工具函数
提供数据验证、时间处理、安全控制等辅助功能
"""

from datetime import datetime, timedelta
from typing import Tuple, List
import re
import hashlib
import secrets
import sqlite3
from functools import wraps
from flask import request, jsonify, g
import logging
from typing import Dict, Any, List, Optional, Tuple
import json

def safe_print(text: str, encoding: str = 'utf-8'):
    """
    安全打印函数，处理Windows控制台编码问题
    
    Args:
        text: 要打印的文本
        encoding: 编码格式
    """
    try:
        print(text)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(f"[*] {safe_text}")

def validate_exam_data(exam_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    验证考试数据
    
    Args:
        exam_data: 考试数据字典
        
    Returns:
        <PERSON><PERSON>[bool, List[str]]: (是否有效, 错误信息列表)
    """
    errors = []
    
    # 必填字段验证
    required_fields = ['name']
    for field in required_fields:
        if not exam_data.get(field):
            errors.append(f"字段 '{field}' 是必填的")
    
    # 考试名称验证
    if exam_data.get('name'):
        if len(exam_data['name']) < 2:
            errors.append("考试名称至少需要2个字符")
        if len(exam_data['name']) > 100:
            errors.append("考试名称不能超过100个字符")
    
    # 时间验证
    start_time = exam_data.get('start_time')
    end_time = exam_data.get('end_time')
    
    if start_time and end_time:
        try:
            start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            
            if start_dt >= end_dt:
                errors.append("开始时间必须早于结束时间")
            
            # 检查考试时长是否合理（最少15分钟，最多8小时）
            duration = end_dt - start_dt
            if duration.total_seconds() < 900:  # 15分钟
                errors.append("考试时长不能少于15分钟")
            if duration.total_seconds() > 28800:  # 8小时
                errors.append("考试时长不能超过8小时")
                
        except ValueError as e:
            errors.append(f"时间格式错误: {str(e)}")
    
    # 分数验证
    total_score = exam_data.get('total_score')
    passing_score = exam_data.get('passing_score')
    
    if total_score is not None:
        if not isinstance(total_score, (int, float)) or total_score <= 0:
            errors.append("总分必须是大于0的数字")
        if total_score > 1000:
            errors.append("总分不能超过1000")
    
    if passing_score is not None:
        if not isinstance(passing_score, (int, float)) or passing_score < 0:
            errors.append("及格分数必须是非负数字")
        if total_score and passing_score > total_score:
            errors.append("及格分数不能超过总分")
    
    # 时长验证
    duration_minutes = exam_data.get('duration_minutes')
    if duration_minutes is not None:
        if not isinstance(duration_minutes, int) or duration_minutes <= 0:
            errors.append("考试时长必须是正整数（分钟）")
        if duration_minutes > 480:  # 8小时
            errors.append("考试时长不能超过480分钟（8小时）")
    
    # 题库ID验证
    question_bank_ids = exam_data.get('question_bank_ids', [])
    if question_bank_ids and not isinstance(question_bank_ids, list):
        errors.append("题库ID必须是数组格式")
    
    # 实操任务ID验证
    practical_task_ids = exam_data.get('practical_task_ids', [])
    if practical_task_ids and not isinstance(practical_task_ids, list):
        errors.append("实操任务ID必须是数组格式")
    
    return len(errors) == 0, errors

def validate_participant_data(participant_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    验证参与者数据
    
    Args:
        participant_data: 参与者数据字典
        
    Returns:
        Tuple[bool, List[str]]: (是否有效, 错误信息列表)
    """
    errors = []
    
    # 必填字段验证
    required_fields = ['exam_id', 'user_id']
    for field in required_fields:
        if not participant_data.get(field):
            errors.append(f"字段 '{field}' 是必填的")
    
    # ID验证
    exam_id = participant_data.get('exam_id')
    if exam_id is not None and (not isinstance(exam_id, int) or exam_id <= 0):
        errors.append("考试ID必须是正整数")
    
    user_id = participant_data.get('user_id')
    if user_id is not None and (not isinstance(user_id, int) or user_id <= 0):
        errors.append("用户ID必须是正整数")
    
    # 角色验证
    role = participant_data.get('role')
    valid_roles = ['student', 'teacher', 'proctor', 'admin']
    if role and role not in valid_roles:
        errors.append(f"角色必须是以下之一: {', '.join(valid_roles)}")
    
    # 座位号验证
    seat_number = participant_data.get('seat_number')
    if seat_number and not re.match(r'^[A-Z0-9-]+$', seat_number):
        errors.append("座位号只能包含大写字母、数字和连字符")
    
    return len(errors) == 0, errors

def format_datetime(dt_str: str, format_type: str = 'display') -> str:
    """
    格式化日期时间字符串
    
    Args:
        dt_str: ISO格式的日期时间字符串
        format_type: 格式类型 ('display', 'date', 'time')
        
    Returns:
        str: 格式化后的日期时间字符串
    """
    try:
        dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        
        if format_type == 'display':
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        elif format_type == 'date':
            return dt.strftime('%Y-%m-%d')
        elif format_type == 'time':
            return dt.strftime('%H:%M:%S')
        else:
            return dt_str
    except (ValueError, AttributeError):
        return dt_str

def get_exam_status_display(status: str) -> str:
    """
    获取考试状态的显示文本
    
    Args:
        status: 考试状态
        
    Returns:
        str: 状态显示文本
    """
    status_map = {
        'draft': '草稿',
        'published': '已发布',
        'in_progress': '进行中',
        'completed': '已结束',
        'cancelled': '已取消'
    }
    return status_map.get(status, status)

def get_exam_type_display(exam_type: str) -> str:
    """
    获取考试类型的显示文本
    
    Args:
        exam_type: 考试类型
        
    Returns:
        str: 类型显示文本
    """
    type_map = {
        'theory': '理论考试',
        'practical': '实操考试',
        'mixed': '混合考试'
    }
    return type_map.get(exam_type, exam_type)

def get_role_display(role: str) -> str:
    """
    获取角色显示名称
    
    Args:
        role: 角色代码
        
    Returns:
        str: 角色显示名称
    """
    role_map = {
        'student': '学生',
        'teacher': '教师',
        'proctor': '监考员',
        'admin': '管理员'
    }
    return role_map.get(role, role)

def get_participant_role_display(role):
    """获取参与者角色的显示文本
    
    Args:
        role: 角色代码
        
    Returns:
        str: 显示文本
    """
    return get_role_display(role)

def calculate_exam_duration(start_time: str, end_time: str) -> int:
    """
    计算考试时长（分钟）
    
    Args:
        start_time: 开始时间
        end_time: 结束时间
        
    Returns:
        int: 时长（分钟）
    """
    try:
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        duration = end_dt - start_dt
        return int(duration.total_seconds() / 60)
    except (ValueError, AttributeError):
        return 0

def is_valid_exam_time(start_time, end_time):
    """
    检查考试时间是否有效
    
    Args:
        start_time: 开始时间字符串
        end_time: 结束时间字符串
        
    Returns:
        bool: 时间是否有效
    """
    try:
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        # 开始时间必须早于结束时间
        if start_dt >= end_dt:
            return False
        
        # 开始时间必须晚于当前时间（至少提前30分钟）
        now = datetime.now()
        if start_dt <= now + timedelta(minutes=30):
            return False
        
        # 考试时长不能超过24小时
        duration = end_dt - start_dt
        if duration.total_seconds() > 24 * 3600:
            return False
        
        # 考试时长不能少于10分钟
        if duration.total_seconds() < 10 * 60:
            return False
        
        # 检查是否在合理的工作时间内（6:00-22:00）
        if start_dt.hour < 6 or start_dt.hour > 22 or end_dt.hour > 23:
            return False
        
        return True
        
    except (ValueError, TypeError):
        return False

def is_exam_time_valid(start_time: str, end_time: str) -> bool:
    """
    检查考试时间是否有效的别名函数
    
    Args:
        start_time: 开始时间
        end_time: 结束时间
        
    Returns:
        bool: 时间是否有效
    """
    return is_valid_exam_time(start_time, end_time)

def validate_exam_time_detailed(start_time: str, end_time: str) -> Tuple[bool, List[str]]:
    """
    详细验证考试时间并返回错误信息
    
    Args:
        start_time: 开始时间字符串
        end_time: 结束时间字符串
        
    Returns:
        Tuple[bool, List[str]]: (是否有效, 错误信息列表)
    """
    errors = []
    
    try:
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
    except (ValueError, TypeError):
        errors.append("时间格式无效")
        return False, errors
    
    # 开始时间必须早于结束时间
    if start_dt >= end_dt:
        errors.append("开始时间必须早于结束时间")
    
    # 开始时间必须晚于当前时间（至少提前30分钟）
    now = datetime.now()
    if start_dt <= now + timedelta(minutes=30):
        errors.append("考试开始时间至少需要提前30分钟安排")
    
    # 计算考试时长
    duration = end_dt - start_dt
    
    # 考试时长不能超过24小时
    if duration.total_seconds() > 24 * 3600:
        errors.append("考试时长不能超过24小时")
    
    # 考试时长不能少于10分钟
    if duration.total_seconds() < 10 * 60:
        errors.append("考试时长不能少于10分钟")
    
    # 检查是否在合理的工作时间内（6:00-22:00）
    if start_dt.hour < 6 or start_dt.hour > 22:
        errors.append("考试开始时间应在6:00-22:00之间")
    
    if end_dt.hour > 23:
        errors.append("考试结束时间不能超过23:00")
    
    # 检查是否跨天但时间合理
    if start_dt.date() != end_dt.date():
        if (end_dt - start_dt).days > 1:
            errors.append("考试不能跨越多天")
    
    return len(errors) == 0, errors

def is_valid_status_transition(current_status, new_status):
    """
    检查状态转换是否有效
    
    Args:
        current_status: 当前状态
        new_status: 新状态
        
    Returns:
        bool: 转换是否有效
    """
    # 定义有效的状态转换规则
    valid_transitions = {
        'draft': ['published', 'cancelled'],
        'published': ['in_progress', 'cancelled'],
        'in_progress': ['completed', 'cancelled'],
        'completed': [],  # 已完成的考试不能转换到其他状态
        'cancelled': ['draft']  # 已取消的考试可以重新编辑
    }
    
    return new_status in valid_transitions.get(current_status, [])

def generate_seat_number(exam_id: int, sequence: int) -> str:
    """
    生成座位号
    
    Args:
        exam_id: 考试ID
        sequence: 序号
        
    Returns:
        str: 座位号
    """
    return f"E{exam_id:04d}-{sequence:03d}"

def sanitize_input(data, max_length: int = 1000):
    """
    清理输入数据，防止XSS攻击
    
    Args:
        data: 输入数据（字符串或字典）
        max_length: 最大长度
        
    Returns:
        清理后的数据
    """
    if isinstance(data, str):
        if not data:
            return ''
        
        # 移除HTML标签
        clean_str = re.sub(r'<[^>]+>', '', data)
        
        # 限制长度
        if len(clean_str) > max_length:
            clean_str = clean_str[:max_length]
        
        return clean_str.strip()
    
    elif isinstance(data, dict):
        cleaned = {}
        for key, value in data.items():
            if isinstance(value, str):
                # 移除潜在的恶意脚本
                value = value.strip()
                value = value.replace('<script>', '').replace('</script>', '')
                value = value.replace('<', '&lt;').replace('>', '&gt;')
            elif isinstance(value, dict):
                value = sanitize_input(value)
            elif isinstance(value, list):
                value = [sanitize_input(item) if isinstance(item, dict) else item for item in value]
            
            cleaned[key] = value
        
        return cleaned
    
    else:
        return data

def clean_input_data(data):
    """
    清理输入数据的别名函数
    
    Args:
        data: 输入数据字典
        
    Returns:
        dict: 清理后的数据
    """
    return sanitize_input(data)

def generate_token(length: int = 32) -> str:
    """
    生成安全令牌
    
    Args:
        length: 令牌长度
        
    Returns:
        str: 安全令牌
    """
    return secrets.token_urlsafe(length)

def hash_password(password: str, salt: str = None) -> Tuple[str, str]:
    """
    哈希密码
    
    Args:
        password: 原始密码
        salt: 盐值（可选）
        
    Returns:
        Tuple[str, str]: (哈希值, 盐值)
    """
    if salt is None:
        salt = secrets.token_hex(16)
    
    hash_obj = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return hash_obj.hex(), salt

def verify_password(password: str, hash_value: str, salt: str) -> bool:
    """
    验证密码
    
    Args:
        password: 原始密码
        hash_value: 哈希值
        salt: 盐值
        
    Returns:
        bool: 密码是否正确
    """
    computed_hash, _ = hash_password(password, salt)
    return computed_hash == hash_value

def require_auth(f):
    """
    认证装饰器
    
    Args:
        f: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 这里应该实现实际的认证逻辑
        # 暂时返回原函数
        return f(*args, **kwargs)
    return decorated_function

def require_permission(permission: str):
    """
    权限检查装饰器
    
    Args:
        permission: 所需权限，如 'exam:create', 'exam:edit', 'exam:delete'
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 从请求头获取token
                auth_header = request.headers.get('Authorization')
                if not auth_header or not auth_header.startswith('Bearer '):
                    return jsonify(create_api_response(401, "未提供认证令牌")), 401
                
                token = auth_header.split(' ')[1]
                
                # 验证token并获取用户信息
                user_info = verify_token(token)
                if not user_info:
                    return jsonify(create_api_response(401, "无效的认证令牌")), 401
                
                # 检查用户权限
                if not check_user_permission(user_info.get('user_id'), permission):
                    return jsonify(create_api_response(403, "权限不足")), 403
                
                # 将用户信息添加到请求上下文
                request.current_user = user_info
                
                return f(*args, **kwargs)
            except Exception as e:
                return jsonify(create_api_response(500, f"权限验证失败: {str(e)}")), 500
        return decorated_function
    return decorator

def verify_token(token):
    """
    验证JWT令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        dict: 用户信息，如果验证失败返回None
    """
    try:
        # 这里应该实现JWT令牌验证逻辑
        # 暂时返回模拟的用户信息
        # 实际应该解析JWT并验证签名
        import jwt
        import os
        
        secret_key = os.getenv('JWT_SECRET_KEY', 'default_secret_key')
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        
        return {
            'user_id': payload.get('user_id'),
            'username': payload.get('username'),
            'role': payload.get('role'),
            'permissions': payload.get('permissions', [])
        }
    except Exception:
        return None

def check_user_permission(user_id, permission):
    """
    检查用户权限
    
    Args:
        user_id: 用户ID
        permission: 权限字符串
        
    Returns:
        bool: 是否有权限
    """
    try:
        # 这里应该从数据库查询用户权限
        # 暂时实现简单的权限检查逻辑
        
        # 管理员拥有所有权限
        if hasattr(request, 'current_user') and request.current_user.get('role') == 'admin':
            return True
        
        # 检查用户是否有特定权限
        user_permissions = get_user_permissions(user_id)
        return permission in user_permissions
    except Exception:
        return False

def get_user_permissions(user_id):
    """
    获取用户权限列表
    
    Args:
        user_id: 用户ID
        
    Returns:
        list: 权限列表
    """
    try:
        # 这里应该从数据库查询用户权限
        # 暂时返回默认权限
        default_permissions = [
            'exam:view',
            'exam:create',
            'exam:edit',
            'participant:view',
            'participant:add',
            'participant:remove'
        ]
        
        # 根据用户角色返回不同权限
        if hasattr(request, 'current_user'):
            role = request.current_user.get('role')
            if role == 'admin':
                return [
                    'exam:view', 'exam:create', 'exam:edit', 'exam:delete',
                    'participant:view', 'participant:add', 'participant:remove',
                    'status:change', 'statistics:view'
                ]
            elif role == 'teacher':
                return [
                    'exam:view', 'exam:create', 'exam:edit',
                    'participant:view', 'participant:add', 'participant:remove',
                    'statistics:view'
                ]
            elif role == 'student':
                return ['exam:view', 'participant:view']
        
        return default_permissions
    except Exception:
        return []

def call_question_bank_service(bank_id: int) -> dict:
    """
    调用题库服务获取题库信息
    
    Args:
        bank_id: 题库ID
        
    Returns:
        dict: 题库信息
    """
    import requests
    try:
        # 通过API网关调用题库服务
        response = requests.get(
            f'http://localhost:8080/api/v1/question-banks/{bank_id}',
            timeout=5
        )
        if response.status_code == 200:
            return response.json().get('data', {})
        else:
            return {
                'id': bank_id,
                'name': f'题库{bank_id}',
                'status': 'unknown',
                'error': f'HTTP {response.status_code}'
            }
    except Exception as e:
        return {
            'id': bank_id,
            'name': f'题库{bank_id}',
            'status': 'error',
            'error': str(e)
        }

def call_practical_task_service(task_id: int) -> dict:
    """
    调用实操任务服务获取任务信息
    
    Args:
        task_id: 实操任务ID
        
    Returns:
        dict: 实操任务信息
    """
    import requests
    try:
        # 通过API网关调用实操任务服务
        response = requests.get(
            f'http://localhost:8080/api/v1/practical-tasks/{task_id}',
            timeout=5
        )
        if response.status_code == 200:
            return response.json().get('data', {})
        else:
            return {
                'id': task_id,
                'name': f'实操任务{task_id}',
                'status': 'unknown',
                'error': f'HTTP {response.status_code}'
            }
    except Exception as e:
        return {
            'id': task_id,
            'name': f'实操任务{task_id}',
            'status': 'error',
            'error': str(e)
        }

def validate_question_bank_ids(bank_ids: List[int]) -> Tuple[bool, str]:
    """
    验证题库ID列表的有效性
    
    Args:
        bank_ids: 题库ID列表
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    if not isinstance(bank_ids, list):
        return False, "题库ID列表格式错误"
    
    for bank_id in bank_ids:
        if not isinstance(bank_id, int) or bank_id <= 0:
            return False, f"无效的题库ID: {bank_id}"
        
        # 验证题库是否存在
        bank_info = call_question_bank_service(bank_id)
        if 'error' in bank_info:
            return False, f"题库{bank_id}不存在或无法访问: {bank_info['error']}"
    
    return True, ""

def validate_practical_task_ids(task_ids):
    """
    验证实操任务ID列表的有效性
    
    Args:
        task_ids (list): 实操任务ID列表
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not isinstance(task_ids, list):
        return False, "实操任务ID必须是列表格式"
    
    if not task_ids:
        return True, None  # 空列表是有效的
    
    # 检查每个ID是否为正整数
    for task_id in task_ids:
        if not isinstance(task_id, int) or task_id <= 0:
            return False, f"实操任务ID {task_id} 必须是正整数"
    
    # 调用实操任务服务验证ID存在性
    for task_id in task_ids:
        task_info = call_practical_task_service(task_id)
        if not task_info or task_info.get('error'):
            return False, f"实操任务ID {task_id} 不存在或无法访问"
    
    return True, None


def get_exam_status_display(status):
    """
    获取考试状态的显示名称
    
    Args:
        status (str): 考试状态代码
        
    Returns:
        str: 状态显示名称
    """
    status_map = {
        'draft': '草稿',
        'published': '已发布',
        'in_progress': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
    }
    return status_map.get(status, '未知状态')


def get_exam_type_display(exam_type):
    """
    获取考试类型的显示名称
    
    Args:
        exam_type (str): 考试类型代码
        
    Returns:
        str: 类型显示名称
    """
    type_map = {
        'theory': '理论考试',
        'practical': '实操考试',
        'comprehensive': '综合考试',
        'simulation': '模拟考试'
    }
    return type_map.get(exam_type, '未知类型')


def get_participant_role_display(role):
    """
    获取参与者角色的显示名称
    
    Args:
        role (str): 参与者角色代码
        
    Returns:
        str: 角色显示名称
    """
    role_mapping = {
        'student': '考生',
        'teacher': '监考老师',
        'admin': '管理员',
        'observer': '观察员'
    }
    return role_mapping.get(role, role)

def calculate_exam_duration(start_time, end_time):
    """
    计算考试持续时间
    
    Args:
        start_time (str): 开始时间 (ISO格式)
        end_time (str): 结束时间 (ISO格式)
        
    Returns:
        dict: 包含持续时间信息的字典
    """
    try:
        from datetime import datetime
        
        # 解析时间字符串
        start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
        
        # 计算时间差
        duration = end_dt - start_dt
        total_seconds = int(duration.total_seconds())
        
        # 转换为小时、分钟、秒
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        seconds = total_seconds % 60
        
        return {
            'total_seconds': total_seconds,
            'total_minutes': total_seconds // 60,
            'hours': hours,
            'minutes': minutes,
            'seconds': seconds,
            'display': f"{hours}小时{minutes}分钟" if hours > 0 else f"{minutes}分钟",
            'is_valid': total_seconds > 0
        }
    except Exception as e:
        return {
            'total_seconds': 0,
            'total_minutes': 0,
            'hours': 0,
            'minutes': 0,
            'seconds': 0,
            'display': '未知',
            'is_valid': False,
            'error': str(e)
        }

def format_datetime(datetime_str):
    """
    格式化日期时间字符串为可读格式
    
    Args:
        datetime_str (str): ISO格式的日期时间字符串
        
    Returns:
        str: 格式化后的日期时间字符串
    """
    try:
        from datetime import datetime
        
        if not datetime_str:
            return None
            
        # 解析ISO格式时间
        dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
        
        # 格式化为中文格式
        return dt.strftime('%Y年%m月%d日 %H:%M:%S')
    except Exception as e:
        return datetime_str  # 如果解析失败，返回原字符串

def log_action(action: str, details: str = None):
    """
    操作日志装饰器
    
    Args:
        action: 操作动作
        details: 详细信息
        
    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                result = f(*args, **kwargs)
                # 这里应该记录成功的操作日志
                safe_print(f"[+] 操作成功: {action} - {details or ''}")
                return result
            except Exception as e:
                # 这里应该记录失败的操作日志
                safe_print(f"[!] 操作失败: {action} - {str(e)}")
                raise
        return decorated_function
    return decorator

def paginate_results(results: List[Any], page: int = 1, per_page: int = 20) -> Dict[str, Any]:
    """
    分页处理结果
    
    Args:
        results: 结果列表
        page: 页码
        per_page: 每页数量
        
    Returns:
        Dict[str, Any]: 分页结果
    """
    total = len(results)
    start = (page - 1) * per_page
    end = start + per_page
    
    return {
        'data': results[start:end],
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page
        }
    }

def create_api_response(code: int = 200, msg: str = 'success', data: Any = None) -> Dict[str, Any]:
    """
    创建标准API响应
    
    Args:
        code: 状态码
        msg: 消息
        data: 数据
        
    Returns:
        Dict[str, Any]: API响应
    """
    return {
        'code': code,
        'msg': msg,
        'data': data or {}
    }

def handle_exception(e: Exception) -> Dict[str, Any]:
    """
    处理异常并返回标准错误响应
    
    Args:
        e: 异常对象
        
    Returns:
        Dict[str, Any]: 错误响应
    """
    error_msg = str(e)
    safe_print(f"[!] 异常: {error_msg}")
    
    # 根据异常类型返回不同的错误码
    if isinstance(e, ValueError):
        return create_api_response(400, f"参数错误: {error_msg}")
    elif isinstance(e, PermissionError):
        return create_api_response(403, "权限不足")
    elif isinstance(e, FileNotFoundError):
        return create_api_response(404, "资源不存在")
    else:
        return create_api_response(500, "服务器内部错误")

def log_exam_operation(operation: str, exam_id: int, user_id: str, details: str = None) -> None:
    """
    记录考试操作日志
    
    Args:
        operation: 操作类型
        exam_id: 考试ID
        user_id: 用户ID
        details: 操作详情
    """
    try:
        from models import ExamLogModel
        log_model = ExamLogModel()
        
        log_data = {
            'exam_id': exam_id,
            'user_id': user_id,
            'operation': operation,
            'details': details or '',
            'timestamp': datetime.now().isoformat()
        }
        
        log_model.create_log(log_data)
        
    except Exception as e:
        print(f"记录日志失败: {e}")

def log_operation(operation: str, exam_id: int, user_id: str, details: str = None) -> None:
    """记录操作日志的别名函数
    
    Args:
        operation: 操作类型
        exam_id: 考试ID
        user_id: 用户ID
        details: 操作详情
    """
    log_exam_operation(operation, exam_id, user_id, details)