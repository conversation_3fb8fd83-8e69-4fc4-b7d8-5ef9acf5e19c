# 文件项目管理工具 API 接口说明文档

## 概述

本文档详细说明了文件项目管理工具的所有API接口，包括请求方法、参数、响应格式等信息。

## 基础信息

- **基础URL**: `http://localhost:5000`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {}
}
```

### 错误响应
```json
{
    "code": 400,
    "msg": "错误信息",
    "data": {}
}
```

## 系统接口

### 1. 健康检查

**接口地址**: `GET /api/health`

**功能描述**: 检查系统运行状态

**请求参数**: 无

**响应示例**:
```json
{
    "code": 200,
    "msg": "系统运行正常",
    "data": {
        "status": "healthy",
        "timestamp": "2025-08-06T05:17:00Z"
    }
}
```

### 2. 系统信息

**接口地址**: `GET /api/info`

**功能描述**: 获取系统基本信息

**请求参数**: 无

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "name": "文件项目管理工具",
        "version": "1.0.0",
        "description": "提供文件管理、项目管理和任务管理功能"
    }
}
```

## 文件管理接口

### 1. 文件上传

**接口地址**: `POST /api/v1/files/upload`

**功能描述**: 上传文件到系统

**请求参数**:
- `file`: 文件对象（必需）
- `description`: 文件描述（可选）

**响应示例**:
```json
{
    "code": 201,
    "msg": "文件上传成功",
    "data": {
        "file_id": 1,
        "filename": "example.pdf",
        "file_size": 1024000,
        "upload_time": "2025-08-06T05:17:00Z"
    }
}
```

### 2. 文件列表

**接口地址**: `GET /api/v1/files/list`

**功能描述**: 获取文件列表

**请求参数**:
- `page`: 页码（默认1）
- `per_page`: 每页数量（默认10）
- `search`: 搜索关键词（可选）

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "files": [
            {
                "file_id": 1,
                "filename": "example.pdf",
                "file_size": 1024000,
                "upload_time": "2025-08-06T05:17:00Z",
                "description": "示例文件"
            }
        ],
        "pagination": {
            "page": 1,
            "per_page": 10,
            "total": 1,
            "pages": 1
        }
    }
}
```

### 3. 文件下载

**接口地址**: `GET /api/v1/files/download/<file_id>`

**功能描述**: 下载指定文件

**请求参数**:
- `file_id`: 文件ID（路径参数）

**响应**: 文件流

### 4. 文件删除

**接口地址**: `DELETE /api/v1/files/<file_id>`

**功能描述**: 删除指定文件

**请求参数**:
- `file_id`: 文件ID（路径参数）

**响应示例**:
```json
{
    "code": 200,
    "msg": "文件删除成功",
    "data": {}
}
```

### 5. 文件分享

**接口地址**: `POST /api/v1/files/<file_id>/share`

**功能描述**: 生成文件分享链接

**请求参数**:
- `file_id`: 文件ID（路径参数）
- `expires_in`: 过期时间（小时，可选，默认24）

**响应示例**:
```json
{
    "code": 200,
    "msg": "分享链接生成成功",
    "data": {
        "share_token": "abc123def456",
        "share_url": "http://localhost:5000/api/v1/files/share/abc123def456",
        "expires_at": "2025-08-07T05:17:00Z"
    }
}
```

## 项目管理接口

### 1. 创建项目

**接口地址**: `POST /api/v1/projects`

**功能描述**: 创建新项目

**请求参数**:
```json
{
    "project_name": "项目名称",
    "description": "项目描述",
    "start_date": "2025-08-06",
    "end_date": "2025-12-31",
    "status": "active"
}
```

**响应示例**:
```json
{
    "code": 201,
    "msg": "项目创建成功",
    "data": {
        "project_id": 1,
        "project_name": "项目名称",
        "description": "项目描述",
        "start_date": "2025-08-06",
        "end_date": "2025-12-31",
        "status": "active",
        "created_at": "2025-08-06T05:17:00Z"
    }
}
```

### 2. 项目列表

**接口地址**: `GET /api/v1/projects`

**功能描述**: 获取项目列表

**请求参数**:
- `page`: 页码（默认1）
- `per_page`: 每页数量（默认10）
- `status`: 项目状态筛选（可选）
- `search`: 搜索关键词（可选）

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "projects": [
            {
                "project_id": 1,
                "project_name": "项目名称",
                "description": "项目描述",
                "status": "active",
                "start_date": "2025-08-06",
                "end_date": "2025-12-31",
                "created_at": "2025-08-06T05:17:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "per_page": 10,
            "total": 1,
            "pages": 1
        }
    }
}
```

### 3. 项目详情

**接口地址**: `GET /api/v1/projects/<project_id>`

**功能描述**: 获取项目详细信息

**请求参数**:
- `project_id`: 项目ID（路径参数）

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "project_id": 1,
        "project_name": "项目名称",
        "description": "项目描述",
        "status": "active",
        "start_date": "2025-08-06",
        "end_date": "2025-12-31",
        "created_at": "2025-08-06T05:17:00Z",
        "updated_at": "2025-08-06T05:17:00Z"
    }
}
```

### 4. 更新项目

**接口地址**: `PUT /api/v1/projects/<project_id>`

**功能描述**: 更新项目信息

**请求参数**:
- `project_id`: 项目ID（路径参数）
- 请求体同创建项目

**响应示例**:
```json
{
    "code": 200,
    "msg": "项目更新成功",
    "data": {
        "project_id": 1,
        "project_name": "更新后的项目名称",
        "updated_at": "2025-08-06T05:17:00Z"
    }
}
```

### 5. 删除项目

**接口地址**: `DELETE /api/v1/projects/<project_id>`

**功能描述**: 删除指定项目

**请求参数**:
- `project_id`: 项目ID（路径参数）

**响应示例**:
```json
{
    "code": 200,
    "msg": "项目删除成功",
    "data": {}
}
```

## 任务管理接口

### 1. 创建任务

**接口地址**: `POST /api/v1/tasks`

**功能描述**: 创建新任务

**请求参数**:
```json
{
    "task_name": "任务名称",
    "project_id": 1,
    "description": "任务描述",
    "priority": "high",
    "due_date": "2025-08-15",
    "assigned_to": "用户ID"
}
```

**响应示例**:
```json
{
    "code": 201,
    "msg": "任务创建成功",
    "data": {
        "task_id": 1,
        "task_name": "任务名称",
        "project_id": 1,
        "description": "任务描述",
        "priority": "high",
        "status": "pending",
        "due_date": "2025-08-15",
        "assigned_to": "用户ID",
        "created_at": "2025-08-06T05:17:00Z"
    }
}
```

### 2. 任务列表

**接口地址**: `GET /api/v1/tasks`

**功能描述**: 获取任务列表

**请求参数**:
- `page`: 页码（默认1）
- `per_page`: 每页数量（默认10）
- `project_id`: 项目ID筛选（可选）
- `status`: 任务状态筛选（可选）
- `priority`: 优先级筛选（可选）
- `search`: 搜索关键词（可选）

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "tasks": [
            {
                "task_id": 1,
                "task_name": "任务名称",
                "project_id": 1,
                "project_name": "项目名称",
                "description": "任务描述",
                "priority": "high",
                "status": "pending",
                "due_date": "2025-08-15",
                "assigned_to": "用户ID",
                "created_at": "2025-08-06T05:17:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "per_page": 10,
            "total": 1,
            "pages": 1
        }
    }
}
```

### 3. 任务详情

**接口地址**: `GET /api/v1/tasks/<task_id>`

**功能描述**: 获取任务详细信息

**请求参数**:
- `task_id`: 任务ID（路径参数）

**响应示例**:
```json
{
    "code": 200,
    "msg": "获取成功",
    "data": {
        "task_id": 1,
        "task_name": "任务名称",
        "project_id": 1,
        "project_name": "项目名称",
        "description": "任务描述",
        "priority": "high",
        "status": "pending",
        "due_date": "2025-08-15",
        "assigned_to": "用户ID",
        "created_at": "2025-08-06T05:17:00Z",
        "updated_at": "2025-08-06T05:17:00Z"
    }
}
```

### 4. 更新任务

**接口地址**: `PUT /api/v1/tasks/<task_id>`

**功能描述**: 更新任务信息

**请求参数**:
- `task_id`: 任务ID（路径参数）
- 请求体同创建任务

**响应示例**:
```json
{
    "code": 200,
    "msg": "任务更新成功",
    "data": {
        "task_id": 1,
        "task_name": "更新后的任务名称",
        "updated_at": "2025-08-06T05:17:00Z"
    }
}
```

### 5. 更新任务状态

**接口地址**: `PATCH /api/v1/tasks/<task_id>/status`

**功能描述**: 更新任务状态

**请求参数**:
- `task_id`: 任务ID（路径参数）
```json
{
    "status": "completed"
}
```

**响应示例**:
```json
{
    "code": 200,
    "msg": "任务状态更新成功",
    "data": {
        "task_id": 1,
        "status": "completed",
        "updated_at": "2025-08-06T05:17:00Z"
    }
}
```

### 6. 删除任务

**接口地址**: `DELETE /api/v1/tasks/<task_id>`

**功能描述**: 删除指定任务

**请求参数**:
- `task_id`: 任务ID（路径参数）

**响应示例**:
```json
{
    "code": 200,
    "msg": "任务删除成功",
    "data": {}
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 413 | 文件过大 |
| 500 | 服务器内部错误 |

## 数据字典

### 项目状态
- `active`: 进行中
- `completed`: 已完成
- `paused`: 已暂停
- `cancelled`: 已取消

### 任务状态
- `pending`: 待处理
- `in_progress`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

### 任务优先级
- `low`: 低优先级
- `medium`: 中优先级
- `high`: 高优先级
- `urgent`: 紧急

## 部署说明

### 环境要求
- Python 3.8+
- Flask 2.0+
- SQLite 3

### 安装步骤
1. 解压项目文件
2. 安装依赖：`pip install -r requirements.txt`
3. 初始化数据库：`python init_db.py`
4. 启动应用：`python app.py`

### 配置说明
- 数据库配置：`config.py`
- 文件上传目录：`uploads/`
- 日志目录：`logs/`

## 注意事项

1. 所有接口均支持CORS跨域访问
2. 文件上传大小限制为16MB
3. 数据库文件位于`data/dev.db`
4. 静态文件服务路径为`/static/`
5. 建议在生产环境中使用专业的Web服务器（如Nginx）进行反向代理

---

**文档版本**: 1.0.0  
**最后更新**: 2025-08-06  
**联系方式**: 开发团队