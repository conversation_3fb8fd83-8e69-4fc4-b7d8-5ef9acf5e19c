# 文件项目管理工具 - 部署说明

## 概述

本文档提供文件项目管理工具的完整部署指南，包括环境准备、安装步骤、配置说明和使用指南。

## 系统要求

### 硬件要求
- CPU: 1核心以上
- 内存: 512MB以上
- 存储: 1GB可用空间

### 软件要求
- 操作系统: Windows 10/11, Linux, macOS
- Python: 3.8或更高版本
- 数据库: SQLite（内置）

## 安装步骤

### 1. 解压项目文件

```bash
# 解压压缩包到目标目录
unzip file-project-manager-complete.zip
cd file-project-manager
```

### 2. 创建虚拟环境（推荐）

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/macOS
source venv/bin/activate
```

### 3. 安装依赖包

```bash
# 安装项目依赖
pip install -r requirements.txt
```

### 4. 初始化数据库

```bash
# 运行数据库初始化脚本
python init_db.py
```

### 5. 启动应用

```bash
# 启动Flask应用
python app.py
```

应用启动后，访问 `http://localhost:5000` 即可使用系统。

## 目录结构

```
file-project-manager/
├── app/                    # 应用核心模块
│   ├── __init__.py
│   ├── api/               # API接口模块
│   │   ├── files.py       # 文件管理接口
│   │   ├── projects.py    # 项目管理接口
│   │   └── tasks.py       # 任务管理接口
│   ├── extensions.py      # Flask扩展配置
│   ├── models/           # 数据模型
│   │   ├── file.py       # 文件模型
│   │   ├── project.py    # 项目模型
│   │   └── task.py       # 任务模型
│   ├── services/         # 业务逻辑服务
│   └── utils.py          # 工具函数
├── config/               # 配置文件目录
│   └── config.py         # 应用配置
├── static/               # 静态文件
│   ├── css/             # 样式文件
│   ├── js/              # JavaScript文件
│   ├── images/          # 图片资源
│   └── index.html       # 前端页面
├── templates/            # 模板文件
├── data/                # 数据目录
│   └── dev.db           # SQLite数据库文件
├── uploads/             # 文件上传目录
│   ├── files/           # 上传文件存储
│   ├── temp/            # 临时文件
│   └── thumbnails/      # 缩略图
├── logs/                # 日志目录
├── app.py               # 应用入口文件
├── config.py            # 配置文件
├── init_db.py           # 数据库初始化脚本
├── requirements.txt     # 依赖包列表
└── README.md            # 项目说明
```

## 配置说明

### 环境配置

系统支持多环境配置，通过环境变量 `FLASK_ENV` 控制：

- `development`: 开发环境（默认）
- `testing`: 测试环境
- `production`: 生产环境

### 主要配置项

在 `config.py` 文件中可以修改以下配置：

```python
# 数据库配置
DATABASE_URL = 'sqlite:///data/dev.db'

# 文件上传配置
UPLOAD_FOLDER = 'uploads/files'
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

# 安全配置
SECRET_KEY = 'your-secret-key-here'

# API配置
API_VERSION = 'v1'
API_PREFIX = '/api'
```

### 生产环境配置

生产环境部署时，建议修改以下配置：

1. **安全密钥**: 更改 `SECRET_KEY` 为随机字符串
2. **数据库**: 使用 PostgreSQL 或 MySQL
3. **文件存储**: 配置云存储服务
4. **日志级别**: 设置为 `WARNING` 或 `ERROR`
5. **CORS设置**: 限制允许的域名

## 功能模块

### 1. 文件管理
- 文件上传、下载、删除
- 文件列表查看和搜索
- 文件分享功能
- 支持多种文件格式

### 2. 项目管理
- 项目创建、编辑、删除
- 项目状态管理
- 项目列表和详情查看
- 项目搜索和筛选

### 3. 任务管理
- 任务创建、分配、更新
- 任务状态跟踪
- 优先级设置
- 任务列表和详情管理

### 4. 系统管理
- 健康检查接口
- 系统信息查看
- 错误处理和日志记录

## API接口

详细的API接口说明请参考 `API接口说明文档.md`。

### 主要接口

- **系统接口**: `/api/health`, `/api/info`
- **文件接口**: `/api/v1/files/*`
- **项目接口**: `/api/v1/projects/*`
- **任务接口**: `/api/v1/tasks/*`

### 接口测试

可以使用以下工具测试API接口：
- Postman
- curl命令
- 内置的Web界面（`http://localhost:5000`）

## 使用指南

### Web界面使用

1. **访问系统**: 打开浏览器访问 `http://localhost:5000`
2. **文件管理**: 点击"文件管理"标签页进行文件操作
3. **项目管理**: 在"项目管理"中创建和管理项目
4. **任务管理**: 在"任务管理"中创建和跟踪任务

### API调用示例

```bash
# 获取系统信息
curl http://localhost:5000/api/info

# 上传文件
curl -X POST -F "file=@example.txt" http://localhost:5000/api/v1/files/upload

# 创建项目
curl -X POST -H "Content-Type: application/json" \
  -d '{"project_name":"测试项目","description":"项目描述"}' \
  http://localhost:5000/api/v1/projects
```

## 故障排除

### 常见问题

1. **端口占用**
   - 错误: `Address already in use`
   - 解决: 更改端口或停止占用进程

2. **依赖包安装失败**
   - 错误: `pip install` 失败
   - 解决: 升级pip版本，使用国内镜像源

3. **数据库初始化失败**
   - 错误: 数据库文件创建失败
   - 解决: 检查目录权限，确保data目录存在

4. **文件上传失败**
   - 错误: 文件上传返回错误
   - 解决: 检查uploads目录权限和磁盘空间

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```

### 性能优化

1. **数据库优化**
   - 定期清理临时文件
   - 添加数据库索引
   - 使用连接池

2. **文件存储优化**
   - 配置文件压缩
   - 使用CDN加速
   - 定期清理过期文件

3. **缓存配置**
   - 启用Redis缓存
   - 配置静态文件缓存
   - 使用浏览器缓存

## 安全建议

1. **访问控制**
   - 配置防火墙规则
   - 使用HTTPS协议
   - 实施IP白名单

2. **数据安全**
   - 定期备份数据库
   - 加密敏感数据
   - 设置文件访问权限

3. **系统安全**
   - 定期更新依赖包
   - 监控系统日志
   - 配置入侵检测

## 备份与恢复

### 数据备份

```bash
# 备份数据库
cp data/dev.db backup/dev_$(date +%Y%m%d).db

# 备份上传文件
tar -czf backup/uploads_$(date +%Y%m%d).tar.gz uploads/
```

### 数据恢复

```bash
# 恢复数据库
cp backup/dev_20250806.db data/dev.db

# 恢复上传文件
tar -xzf backup/uploads_20250806.tar.gz
```

## 升级指南

1. **备份当前数据**
2. **停止应用服务**
3. **更新代码文件**
4. **升级依赖包**
5. **运行数据库迁移**
6. **重启应用服务**
7. **验证功能正常**

## 技术支持

如遇到问题，请按以下步骤处理：

1. 查看本文档的故障排除部分
2. 检查系统日志文件
3. 参考API接口说明文档
4. 联系技术支持团队

---

**文档版本**: 1.0.0  
**最后更新**: 2025-08-06  
**适用版本**: 文件项目管理工具 v1.0.0