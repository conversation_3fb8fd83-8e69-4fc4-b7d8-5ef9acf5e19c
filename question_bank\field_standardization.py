#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段名称标准化工具
将旧模板的驼峰式英文字段名更新为新模板的下划线格式
"""

import pandas as pd
import json
import os
from pathlib import Path

# 字段映射：旧字段名 -> 新字段名
FIELD_MAPPING = {
    # 题库模板字段映射
    'Bank Name': 'question_bank_name',
    'Question ID': 'question_id', 
    'Serial Number': 'serial_number',
    'Certification Point Code': 'identification_point_code',
    'Question Type Code': 'question_type_code',
    'Question Number': 'question_number',
    'Question Stem': 'question_stem',
    'Option A': 'option_a',
    'Option B': 'option_b', 
    'Option C': 'option_c',
    'Option D': 'option_d',
    'Option E': 'option_e',
    'Image Location': 'image_and_position',
    'Correct Answer': 'correct_answer',
    'Difficulty Code': 'difficulty_code',
    'Consistency Code': 'consistency_code',
    'Analysis': 'explanation',
    
    # 组卷模板字段映射
    'Question Bank Name': 'question_bank_name',
    'Question Count': 'number_of_questions',
    'Score Value': 'score_value',
    
    # 其他可能的字段
    'Image and Position': 'image_and_position'
}

# 完整字段映射（中英文对照）
FULL_FIELD_MAPPING = {
    # 题库模板
    '题库名称\nBank Name': '题库名称\nquestion_bank_name',
    '试题ID\nQuestion ID': '试题ID\nquestion_id',
    '序号\nSerial Number': '序号\nserial_number', 
    '认定点代码\nCertification Point Code': '认定点代码\nidentification_point_code',
    '题型代码\nQuestion Type Code': '题型代码\nquestion_type_code',
    '题号\nQuestion Number': '题号\nquestion_number',
    '试题（题干）\nQuestion Stem': '试题（题干）\nquestion_stem',
    '试题（选项A）\nOption A': '试题（选项 A）\noption_a',
    '试题（选项B）\nOption B': '试题（选项 B）\noption_b',
    '试题（选项C）\nOption C': '试题（选项 C）\noption_c', 
    '试题（选项D）\nOption D': '试题（选项 D）\noption_d',
    '试题（选项E）\nOption E': '试题（选项 E）\noption_e',
    '【图】及位置\nImage and Position': '【图】及位置\nimage_and_position',
    '正确答案\nCorrect Answer': '正确答案\ncorrect_answer',
    '难度代码\nDifficulty Code': '难度代码\ndifficulty_code',
    '一致性代码\nConsistency Code': '一致性代码\nconsistency_code',
    '解析\nAnalysis': '解析\nexplanation',
    
    # 组卷模板
    '题库名称\nQuestion Bank Name': '题库名称\nquestion_bank_name',
    '题量\nQuestion Count': '题量\nnumber_of_questions',
    '分值\nScore Value': '分值\nscore_value'
}

def update_excel_template(old_file_path, new_file_path):
    """
    更新Excel模板文件的字段名称
    """
    try:
        # 读取旧模板（双行表头）
        df = pd.read_excel(old_file_path, header=[0, 1])
        
        print(f"原始列名: {list(df.columns)}")
        
        # 创建新的列名映射
        new_columns = []
        for col in df.columns:
            chinese_name = col[0]
            english_name = col[1]
            
            # 查找新的英文字段名
            new_english_name = FIELD_MAPPING.get(english_name, english_name)
            
            # 构建新的列名
            new_col = (chinese_name, new_english_name)
            new_columns.append(new_col)
            
            print(f"字段映射: {col} -> {new_col}")
        
        # 更新列名
        df.columns = pd.MultiIndex.from_tuples(new_columns)
        
        # 保存新模板 - 处理MultiIndex列名
        # 先将MultiIndex转换为单层列名
        df_copy = df.copy()
        newline = '\n'
    df_copy.columns = [f"{col[0]}{newline}{col[1]}" for col in df.columns]
        
        with pd.ExcelWriter(new_file_path, engine='openpyxl') as writer:
            df_copy.to_excel(writer, index=False, sheet_name='Sheet1')
            
            # 手动设置双行表头
            worksheet = writer.sheets['Sheet1']
            
            # 清除现有内容并重新写入双行表头
            worksheet.delete_rows(1, worksheet.max_row)
            
            # 写入中文表头（第一行）
            chinese_headers = [col[0] for col in new_columns]
            for col_idx, header in enumerate(chinese_headers, 1):
                worksheet.cell(row=1, column=col_idx, value=header)
            
            # 写入英文表头（第二行）
            english_headers = [col[1] for col in new_columns]
            for col_idx, header in enumerate(english_headers, 1):
                worksheet.cell(row=2, column=col_idx, value=header)
            
            # 写入数据（从第三行开始）
            for row_idx, (_, row) in enumerate(df.iterrows(), 3):
                for col_idx, value in enumerate(row, 1):
                    worksheet.cell(row=row_idx, column=col_idx, value=value)
        
        print(f"成功更新模板: {new_file_path}")
        return True
        
    except Exception as e:
        print(f"更新模板失败 {old_file_path}: {e}")
        return False

def update_standard_field_config():
    """
    更新标准字段配置文件
    """
    config_file = 'd:/61-PHRL_question_bank/standard_field_config.json'
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新标准字段映射
        new_standard_fields = {}
        for old_field, db_field in config['standard_fields'].items():
            # 查找新的字段名
            new_field = FULL_FIELD_MAPPING.get(old_field, old_field)
            new_standard_fields[new_field] = db_field
        
        config['standard_fields'] = new_standard_fields
        
        # 备份原配置
        backup_file = config_file + '.backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        # 保存新配置
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"成功更新配置文件: {config_file}")
        print(f"备份文件: {backup_file}")
        return True
        
    except Exception as e:
        print(f"更新配置文件失败: {e}")
        return False

def update_code_files():
    """
    更新代码文件中的字段映射
    """
    files_to_update = [
        'd:/61-PHRL_question_bank/excel_importer.py',
        'd:/61-PHRL_question_bank/excel_exporter.py',
        'd:/61-PHRL_question_bank/app.py'
    ]
    
    # 新的字段映射代码
    new_mapping_code = '''
# 实际Excel文件的列名映射到标准字段名（更新为新的下划线格式）
ACTUAL_TO_STANDARD_MAPPING = {
    '题库名称': '题库名称\\nquestion_bank_name',
    '试题ID': '试题ID\\nquestion_id',
    '题型代码': '题型代码\\nquestion_type_code',
    '题号': '题号\\nquestion_number',
    '试题（题干）': '试题（题干）\\nquestion_stem',
    '试题（选项A）': '试题（选项 A）\\nquestion_option_a',
    '试题（选项B）': '试题（选项 B）\\nquestion_option_b',
    '试题（选项C）': '试题（选项 C）\\nquestion_option_c',
    '试题（选项D）': '试题（选项 D）\\nquestion_option_d',
    '试题（选项E）': '试题（选项 E）\\nquestion_option_e',
    '【图】及位置': '【图】及位置\\nimage_and_position',
    '正确答案': '正确答案\\ncorrect_answer',
    '难度代码': '难度代码\\ndifficulty_code',
    '一致性代码': '一致性代码\\nconsistency_code',
    '解析': '解析\\nexplanation'
}
'''
    
    print("代码文件更新需要手动进行，请参考以下新的映射代码:")
    print(new_mapping_code)
    
    return True

def main():
    """
    主函数：执行字段标准化
    """
    print("=== 开始字段名称标准化 ===")
    
    # 1. 更新旧模板文件
    templates_to_update = [
        {
            'old': 'd:/61-PHRL_question_bank/uploads/Question-Bank-Template_SPCT-4-LL.xlsx',
            'new': 'd:/61-PHRL_question_bank/uploads/Question-Bank-Template_SPCT-4-LL_UPDATED.xlsx'
        },
        {
            'old': 'd:/61-PHRL_question_bank/uploads/Paper-Compilation-Template_SPCT-4-LL.xlsx', 
            'new': 'd:/61-PHRL_question_bank/uploads/Paper-Compilation-Template_SPCT-4-LL_UPDATED.xlsx'
        }
    ]
    
    print("\n1. 更新模板文件...")
    for template in templates_to_update:
        if os.path.exists(template['old']):
            success = update_excel_template(template['old'], template['new'])
            if success:
                print(f"✅ 成功更新: {template['new']}")
            else:
                print(f"❌ 更新失败: {template['old']}")
        else:
            print(f"⚠️  文件不存在: {template['old']}")
    
    # 2. 更新配置文件
    print("\n2. 更新配置文件...")
    if update_standard_field_config():
        print("✅ 配置文件更新成功")
    else:
        print("❌ 配置文件更新失败")
    
    # 3. 提示代码文件更新
    print("\n3. 代码文件更新...")
    update_code_files()
    
    print("\n=== 字段标准化完成 ===")
    print("\n📋 后续步骤:")
    print("1. 检查生成的新模板文件")
    print("2. 手动更新代码文件中的字段映射")
    print("3. 测试导入功能")
    print("4. 替换旧模板文件（可选）")

if __name__ == '__main__':
    main()