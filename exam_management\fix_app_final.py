#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复app.py文件的脚本
"""

import re
import os
import sys

def read_file_with_fallback(filename):
    """使用多种编码尝试读取文件"""
    encodings = ['utf-8', 'gbk', 'latin1', 'cp1252']
    
    for encoding in encodings:
        try:
            with open(filename, 'r', encoding=encoding) as f:
                content = f.read()
            print(f"成功使用 {encoding} 编码读取文件")
            return content
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"使用 {encoding} 编码读取失败: {e}")
            continue
    
    raise Exception("无法使用任何编码读取文件")

def fix_syntax_errors():
    """修复语法错误"""
    try:
        # 读取文件
        content = read_file_with_fallback('app.py')
        print("文件读取成功")
        
        fixes_count = 0
        
        # 1. 修复docstring问题
        docstring_patterns = [
            (r'"""健康检查接\?"""', '"""健康检查接口"""'),
            (r'"""根路径接\?"""', '"""根路径接口"""'),
            (r'"""参与者管理页\?"""', '"""参与者管理页面"""'),
        ]
        
        for pattern, replacement in docstring_patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                fixes_count += 1
                print(f"修复docstring: {pattern}")
        
        # 2. 修复HTML中的截断文本
        html_fixes = [
            ('加载参与者列表失?', '加载参与者列表失败'),
            ('显示参与者列?', '显示参与者列表'),
            ('暂无参与者数?', '暂无参与者数据'),
            ('座位?', '座位号'),
            ('状?', '状态'),
            ('上一?', '上一页'),
            ('下一?', '下一页'),
            ('应用筛?', '应用筛选'),
            ('添加参与?', '添加参与者'),
            ('编辑参与?', '编辑参与者'),
            ('查看参与?', '查看参与者'),
            ('移除参与?', '移除参与者'),
            ('更新参与?', '更新参与者'),
            ('管理参与?', '管理参与者'),
            ('状态分?', '状态分布'),
        ]
        
        for old, new in html_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes_count += 1
                print(f"修复HTML文本: {old} -> {new}")
        
        # 3. 修复HTML标签问题
        tag_fixes = [
            ('座位号/th>', '座位号</th>'),
            ('状?/th>', '状态</th>'),
        ]
        
        for old, new in tag_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes_count += 1
                print(f"修复HTML标签: {old} -> {new}")
        
        # 4. 修复JavaScript注释中的问号
        js_comment_pattern = r'// ([^\n]*?)\?([^\n]*?)\n'
        js_matches = re.findall(js_comment_pattern, content)
        if js_matches:
            for match in js_matches:
                old_comment = f"// {match[0]}?{match[1]}"
                new_comment = f"// {match[0]}{match[1]}"
                content = content.replace(old_comment, new_comment)
                fixes_count += 1
                print(f"修复JS注释: {old_comment} -> {new_comment}")
        
        # 5. 修复特定的缩进问题
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 检查是否有缩进问题的行
            if '# 测试数据库连接' in line and 'with db_manager.get_connection()' in line:
                # 分割这一行
                indent = len(line) - len(line.lstrip())
                comment_part = '# 测试数据库连接'
                code_part = 'with db_manager.get_connection() as conn:'
                
                fixed_lines.append(' ' * indent + comment_part)
                fixed_lines.append(' ' * indent + code_part)
                fixes_count += 1
                print(f"修复第{i+1}行的缩进问题")
            else:
                fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # 6. 确保所有三引号字符串正确配对
        triple_quotes = content.count('"""')
        if triple_quotes % 2 != 0:
            print(f"警告: 三引号数量不匹配 ({triple_quotes})")
            # 在文件末尾添加缺失的三引号
            content += '\n"""\n'
            fixes_count += 1
            print("在文件末尾添加了缺失的三引号")
        
        # 写入修复后的文件
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"\n修复完成！总共修复了 {fixes_count} 个问题")
        return True
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        return False

def test_syntax():
    """测试Python语法"""
    try:
        import py_compile
        py_compile.compile('app.py', doraise=True)
        print("\n✓ Python语法检查通过！")
        return True
        
    except py_compile.PyCompileError as e:
        print(f"\n✗ 编译错误: {e}")
        return False
    except SyntaxError as e:
        print(f"\n✗ 语法错误: {e}")
        print(f"行号: {e.lineno}")
        print(f"位置: {e.offset}")
        return False
    except Exception as e:
        print(f"\n✗ 检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    print("开始最终修复app.py文件...")
    
    if fix_syntax_errors():
        print("\n测试语法...")
        if test_syntax():
            print("\n🎉 所有问题已修复！")
        else:
            print("\n⚠️ 仍有语法问题需要手动检查")
    else:
        print("修复失败")