#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全控制模块
提供权限验证、操作日志、防SQL注入等安全功能
"""

import hashlib
import hmac
import secrets
import time
import jwt
import logging
from typing import Dict, List, Any, Optional, Callable, Tuple
from functools import wraps
from datetime import datetime, timedelta
import sqlite3
import re

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SecurityError(Exception):
    """安全错误异常类"""
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code
        super().__init__(self.message)

class PermissionDeniedError(SecurityError):
    """权限拒绝异常类"""
    def __init__(self, message: str = "权限不足"):
        super().__init__(message, "PERMISSION_DENIED")

class AuthenticationError(SecurityError):
    """认证失败异常类"""
    def __init__(self, message: str = "认证失败"):
        super().__init__(message, "AUTHENTICATION_FAILED")

class SQLInjectionError(SecurityError):
    """SQL注入攻击异常类"""
    def __init__(self, message: str = "检测到SQL注入攻击"):
        super().__init__(message, "SQL_INJECTION_DETECTED")

class SecurityManager:
    """安全管理器"""
    
    def __init__(self, secret_key: str = None):
        """
        初始化安全管理器
        
        Args:
            secret_key: JWT密钥
        """
        self.secret_key = secret_key or secrets.token_urlsafe(32)
        self.token_expiry = 3600  # 1小时
        
        # SQL注入检测模式
        self.sql_injection_patterns = [
            r"('|(\-\-)|(;)|(\||\|)|(\*|\*))",
            r"(union|select|insert|delete|update|drop|create|alter|exec|execute)",
            r"(script|javascript|vbscript|onload|onerror|onclick)",
            r"(<|>|&lt;|&gt;|&amp;)"
        ]
    
    def generate_token(self, user_id: str, permissions: List[str] = None) -> str:
        """
        生成JWT令牌
        
        Args:
            user_id: 用户ID
            permissions: 用户权限列表
            
        Returns:
            JWT令牌字符串
        """
        payload = {
            'user_id': user_id,
            'permissions': permissions or [],
            'exp': datetime.utcnow() + timedelta(seconds=self.token_expiry),
            'iat': datetime.utcnow(),
            'jti': secrets.token_urlsafe(16)  # JWT ID
        }
        
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """
        验证JWT令牌
        
        Args:
            token: JWT令牌字符串
            
        Returns:
            解码后的payload
            
        Raises:
            AuthenticationError: 当令牌无效时
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("令牌已过期")
        except jwt.InvalidTokenError:
            raise AuthenticationError("无效的令牌")
    
    def hash_password(self, password: str, salt: str = None) -> Tuple[str, str]:
        """
        哈希密码
        
        Args:
            password: 明文密码
            salt: 盐值
            
        Returns:
            (哈希值, 盐值)元组
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # 使用PBKDF2进行密码哈希
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        
        return password_hash.hex(), salt
    
    def verify_password(self, password: str, password_hash: str, salt: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            password_hash: 存储的密码哈希
            salt: 盐值
            
        Returns:
            验证结果
        """
        computed_hash, _ = self.hash_password(password, salt)
        return hmac.compare_digest(computed_hash, password_hash)
    
    def check_sql_injection(self, input_string: str) -> bool:
        """
        检查SQL注入攻击
        
        Args:
            input_string: 输入字符串
            
        Returns:
            True表示安全，False表示可能存在SQL注入
            
        Raises:
            SQLInjectionError: 当检测到SQL注入时
        """
        if not isinstance(input_string, str):
            return True
        
        # 转换为小写进行检查
        lower_input = input_string.lower()
        
        for pattern in self.sql_injection_patterns:
            if re.search(pattern, lower_input, re.IGNORECASE):
                logger.warning(f"检测到可能的SQL注入攻击: {input_string}")
                raise SQLInjectionError(f"输入包含不安全的字符或关键字")
        
        return True
    
    def sanitize_input(self, input_string: str) -> str:
        """
        清理输入字符串
        
        Args:
            input_string: 输入字符串
            
        Returns:
            清理后的字符串
        """
        if not isinstance(input_string, str):
            return str(input_string)
        
        # 移除危险字符
        sanitized = re.sub(r'[<>"\'\\/]', '', input_string)
        
        # 限制长度
        if len(sanitized) > 1000:
            sanitized = sanitized[:1000]
        
        return sanitized.strip()

class PermissionManager:
    """权限管理器"""
    
    # 定义权限常量
    PERMISSIONS = {
        'exam_create': '创建考试',
        'exam_edit': '编辑考试',
        'exam_delete': '删除考试',
        'exam_view': '查看考试',
        'exam_publish': '发布考试',
        'participant_add': '添加参与者',
        'participant_remove': '移除参与者',
        'participant_view': '查看参与者',
        'result_view': '查看结果',
        'result_export': '导出结果',
        'system_admin': '系统管理'
    }
    
    # 角色权限映射
    ROLE_PERMISSIONS = {
        'admin': list(PERMISSIONS.keys()),
        'teacher': [
            'exam_create', 'exam_edit', 'exam_view', 'exam_publish',
            'participant_add', 'participant_remove', 'participant_view',
            'result_view', 'result_export'
        ],
        'assistant': [
            'exam_view', 'participant_add', 'participant_view', 'result_view'
        ],
        'student': ['exam_view']
    }
    
    @classmethod
    def get_role_permissions(cls, role: str) -> List[str]:
        """
        获取角色权限
        
        Args:
            role: 角色名称
            
        Returns:
            权限列表
        """
        return cls.ROLE_PERMISSIONS.get(role, [])
    
    @classmethod
    def check_permission(cls, user_permissions: List[str], required_permission: str) -> bool:
        """
        检查用户权限
        
        Args:
            user_permissions: 用户权限列表
            required_permission: 所需权限
            
        Returns:
            是否有权限
        """
        return required_permission in user_permissions or 'system_admin' in user_permissions

class AuditLogger:
    """审计日志记录器"""
    
    def __init__(self, db_path: str = "audit.db"):
        """
        初始化审计日志记录器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """初始化审计日志数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    user_id TEXT,
                    action TEXT NOT NULL,
                    resource_type TEXT,
                    resource_id TEXT,
                    details TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    success BOOLEAN NOT NULL,
                    error_message TEXT
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON audit_logs(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_user_id ON audit_logs(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_action ON audit_logs(action)")
    
    def log_action(self, user_id: str, action: str, resource_type: str = None,
                   resource_id: str = None, details: str = None,
                   ip_address: str = None, user_agent: str = None,
                   success: bool = True, error_message: str = None):
        """
        记录操作日志
        
        Args:
            user_id: 用户ID
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            details: 详细信息
            ip_address: IP地址
            user_agent: 用户代理
            success: 是否成功
            error_message: 错误信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO audit_logs 
                    (timestamp, user_id, action, resource_type, resource_id, 
                     details, ip_address, user_agent, success, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    datetime.now().isoformat(),
                    user_id,
                    action,
                    resource_type,
                    resource_id,
                    details,
                    ip_address,
                    user_agent,
                    success,
                    error_message
                ))
        except Exception as e:
            logger.error(f"记录审计日志失败: {str(e)}")
    
    def get_logs(self, user_id: str = None, action: str = None,
                 start_time: datetime = None, end_time: datetime = None,
                 limit: int = 100) -> List[Dict]:
        """
        获取审计日志
        
        Args:
            user_id: 用户ID过滤
            action: 操作类型过滤
            start_time: 开始时间
            end_time: 结束时间
            limit: 返回数量限制
            
        Returns:
            日志列表
        """
        query = "SELECT * FROM audit_logs WHERE 1=1"
        params = []
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if action:
            query += " AND action = ?"
            params.append(action)
        
        if start_time:
            query += " AND timestamp >= ?"
            params.append(start_time.isoformat())
        
        if end_time:
            query += " AND timestamp <= ?"
            params.append(end_time.isoformat())
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute(query, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取审计日志失败: {str(e)}")
            return []

# 装饰器函数
def require_permission(permission: str):
    """
    权限验证装饰器
    
    Args:
        permission: 所需权限
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 从请求中获取用户信息（这里需要根据实际框架调整）
            user_permissions = kwargs.get('user_permissions', [])
            
            if not PermissionManager.check_permission(user_permissions, permission):
                raise PermissionDeniedError(f"需要权限: {permission}")
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def audit_log(action: str, resource_type: str = None):
    """
    审计日志装饰器
    
    Args:
        action: 操作类型
        resource_type: 资源类型
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            audit_logger = AuditLogger()
            user_id = kwargs.get('user_id', 'unknown')
            
            try:
                result = func(*args, **kwargs)
                audit_logger.log_action(
                    user_id=user_id,
                    action=action,
                    resource_type=resource_type,
                    success=True
                )
                return result
            except Exception as e:
                audit_logger.log_action(
                    user_id=user_id,
                    action=action,
                    resource_type=resource_type,
                    success=False,
                    error_message=str(e)
                )
                raise
        return wrapper
    return decorator

def sql_injection_check(func):
    """
    SQL注入检查装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰器函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        security_manager = SecurityManager()
        
        # 检查所有字符串参数
        for arg in args:
            if isinstance(arg, str):
                security_manager.check_sql_injection(arg)
        
        for key, value in kwargs.items():
            if isinstance(value, str):
                security_manager.check_sql_injection(value)
        
        return func(*args, **kwargs)
    return wrapper

# 使用示例
if __name__ == "__main__":
    # 测试安全管理器
    security_manager = SecurityManager()
    
    # 测试密码哈希
    password = "test123"
    hash_value, salt = security_manager.hash_password(password)
    print(f"密码哈希: {hash_value}")
    print(f"验证结果: {security_manager.verify_password(password, hash_value, salt)}")
    
    # 测试JWT令牌
    token = security_manager.generate_token("user123", ["exam_view", "exam_create"])
    print(f"生成的令牌: {token}")
    
    try:
        payload = security_manager.verify_token(token)
        print(f"令牌验证成功: {payload}")
    except AuthenticationError as e:
        print(f"令牌验证失败: {e.message}")
    
    # 测试SQL注入检查
    try:
        security_manager.check_sql_injection("SELECT * FROM users")
    except SQLInjectionError as e:
        print(f"检测到SQL注入: {e.message}")
    
    # 测试审计日志
    audit_logger = AuditLogger()
    audit_logger.log_action(
        user_id="user123",
        action="create_exam",
        resource_type="exam",
        resource_id="exam456",
        success=True
    )
    
    logs = audit_logger.get_logs(limit=5)
    print(f"审计日志: {logs}")