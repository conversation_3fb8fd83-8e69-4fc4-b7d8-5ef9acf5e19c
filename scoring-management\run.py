# -*- coding: utf-8 -*-
"""
评分管理模块启动文件

功能说明:
- 启动评分管理模块Flask应用
- 配置日志系统
- 初始化数据库
- 提供服务信息
"""

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from app import create_app, db
from config import get_config

def setup_logging(app):
    """配置日志系统"""
    if not app.debug:
        # 创建日志目录
        log_dir = os.path.dirname(app.config['LOG_FILE'])
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 配置文件日志处理器
        file_handler = RotatingFileHandler(
            app.config['LOG_FILE'],
            maxBytes=app.config['LOG_MAX_SIZE'],
            backupCount=app.config['LOG_BACKUP_COUNT'],
            encoding='utf-8'
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        
        # 配置控制台日志处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        console_handler.setLevel(logging.INFO)
        
        # 添加处理器到应用日志器
        app.logger.addHandler(file_handler)
        app.logger.addHandler(console_handler)
        app.logger.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        
        app.logger.info('评分管理模块日志系统已启动')

def init_database(app):
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            app.logger.info('数据库表创建完成')
        except Exception as e:
            app.logger.error(f'数据库初始化失败: {str(e)}')
            raise

def safe_print(text):
    """安全打印函数，处理Windows控制台编码问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 如果遇到编码错误，使用ASCII编码并忽略错误字符
        print(text.encode('ascii', 'ignore').decode('ascii'))
    except Exception as e:
        print(f"Print error: {e}")

def main():
    """主函数"""
    try:
        # 创建应用实例
        config_class = get_config()
        app = create_app(config_class)
        
        # 设置日志
        setup_logging(app)
        
        # 初始化数据库
        init_database(app)
        
        # 打印启动信息
        safe_print("\n" + "="*60)
        safe_print("评分管理模块启动中...")
        safe_print("="*60)
        safe_print(f"服务地址: http://0.0.0.0:5004")
        safe_print(f"API文档: http://0.0.0.0:5004/api/v1")
        safe_print(f"健康检查: http://0.0.0.0:5004/api/v1/health")
        safe_print(f"登录页面: http://0.0.0.0:5004/")
        safe_print(f"评分页面: http://0.0.0.0:5004/grading")
        safe_print(f"调试模式: {app.config['DEBUG']}")
        safe_print(f"数据库: {app.config['SQLALCHEMY_DATABASE_URI']}")
        safe_print("="*60)
        safe_print("按 Ctrl+C 停止服务")
        safe_print("="*60 + "\n")
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=5004,
            debug=app.config['DEBUG'],
            threaded=True
        )
        
    except KeyboardInterrupt:
        safe_print("\n评分管理模块已停止")
    except Exception as e:
        safe_print(f"启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()