# -*- coding: utf-8 -*-
"""
评分管理模块 - 核心服务
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
from datetime import datetime

# --- In-Memory Database ---
db = {
    "scores": {
        # Key is a tuple: (exam_id, student_id)
        (1, 1): {"exam_id": 1, "student_id": 1, "score": 95, "grader_id": 101, "graded_at": "2024-09-01T12:00:00"},
        (1, 2): {"exam_id": 1, "student_id": 2, "score": 88, "grader_id": 101, "graded_at": "2024-09-01T12:05:00"},
    },
    "next_score_id": 3
}

# --- Flask App Initialization ---
app = Flask(__name__)
CORS(app)

# --- API Endpoints ---

@app.route('/', methods=['GET'])
def index():
    """Root endpoint to confirm the service is running."""
    return jsonify({
        'service': 'Score Management API',
        'version': '1.0.0',
        'status': 'running'
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint for monitoring."""
    return jsonify({
        'status': 'healthy',
        'service': 'score_management',
        'timestamp': datetime.utcnow().isoformat() + 'Z'
    }), 200

@app.route('/api/v1/exams/<int:exam_id>/scores', methods=['GET'])
def get_scores_for_exam(exam_id):
    """Get all scores for a specific exam."""
    exam_scores = [score for key, score in db['scores'].items() if key[0] == exam_id]
    return jsonify(exam_scores)

@app.route('/api/v1/exams/<int:exam_id>/students/<int:student_id>/scores', methods=['POST'])
def submit_score(exam_id, student_id):
    """Submit or update a score for a student in an exam."""
    if not request.json or 'score' not in request.json or 'grader_id' not in request.json:
        return jsonify({'error': 'Missing required fields: score, grader_id'}), 400

    score_data = request.json
    score_key = (exam_id, student_id)

    new_score = {
        "exam_id": exam_id,
        "student_id": student_id,
        "score": score_data['score'],
        "grader_id": score_data['grader_id'],
        "graded_at": datetime.utcnow().isoformat() + 'Z'
    }

    db['scores'][score_key] = new_score

    return jsonify(new_score), 201

# --- Main Execution ---
if __name__ == '__main__':
    # The port is configured to 5004 as specified in main_launcher.py
    port = 5004
    print("Score Management Service Starting...")
    print(f"Service running at: http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=True)
