#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化工具
职业技能等级考试系统 - 系统性能优化

功能特性：
- 内存使用优化和监控
- 进程池管理和调度
- 智能缓存机制
- 资源自动清理
- 性能分析和诊断
- 系统资源监控

作者：系统架构师
版本：2.0.0
日期：2024-01-15
"""

import os
import gc
import sys
import time
import psutil
import threading
import multiprocessing
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from functools import wraps, lru_cache
from collections import defaultdict, OrderedDict
import logging
import weakref
import json


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: datetime
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_mb: float = 0.0
    disk_io_read: float = 0.0
    disk_io_write: float = 0.0
    network_sent: float = 0.0
    network_recv: float = 0.0
    process_count: int = 0
    thread_count: int = 0
    open_files: int = 0
    

@dataclass
class CacheStats:
    """缓存统计数据类"""
    hits: int = 0
    misses: int = 0
    size: int = 0
    max_size: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return (self.hits / total * 100) if total > 0 else 0.0


class MemoryOptimizer:
    """
    内存优化器
    
    负责内存使用监控和优化：
    - 内存泄漏检测
    - 垃圾回收优化
    - 内存使用分析
    - 自动内存清理
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """初始化内存优化器"""
        self.logger = logger or self._init_logger()
        self.memory_threshold = 80.0  # 内存使用率阈值
        self.gc_threshold = 70.0      # 垃圾回收阈值
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.memory_history: List[float] = []
        self.max_history_size = 100
        
    def _init_logger(self) -> logging.Logger:
        """初始化日志系统"""
        logger = logging.getLogger("memory_optimizer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        system_memory = psutil.virtual_memory()
        
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,
            "vms_mb": memory_info.vms / 1024 / 1024,
            "percent": process.memory_percent(),
            "system_total_gb": system_memory.total / 1024 / 1024 / 1024,
            "system_available_gb": system_memory.available / 1024 / 1024 / 1024,
            "system_percent": system_memory.percent
        }
    
    def optimize_memory(self, force: bool = False) -> Dict[str, Any]:
        """优化内存使用"""
        before_memory = self.get_memory_usage()
        
        # 检查是否需要优化
        if not force and before_memory["system_percent"] < self.gc_threshold:
            return {
                "optimized": False,
                "reason": "内存使用率未达到优化阈值",
                "memory_usage": before_memory
            }
        
        self.logger.info("开始内存优化...")
        
        # 执行垃圾回收
        collected_objects = []
        for generation in range(3):
            collected = gc.collect(generation)
            collected_objects.append(collected)
        
        # 清理弱引用
        weakref_count = len(weakref.getweakrefs(object))
        
        # 获取优化后的内存使用情况
        after_memory = self.get_memory_usage()
        
        # 计算优化效果
        memory_saved = before_memory["rss_mb"] - after_memory["rss_mb"]
        
        result = {
            "optimized": True,
            "memory_before_mb": before_memory["rss_mb"],
            "memory_after_mb": after_memory["rss_mb"],
            "memory_saved_mb": memory_saved,
            "collected_objects": collected_objects,
            "weakref_count": weakref_count,
            "system_memory_percent": after_memory["system_percent"]
        }
        
        self.logger.info(f"内存优化完成，释放 {memory_saved:.2f}MB 内存")
        return result
    
    def start_monitoring(self, interval: int = 60):
        """开始内存监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info("内存监控已启动")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("内存监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                memory_usage = self.get_memory_usage()
                self.memory_history.append(memory_usage["system_percent"])
                
                # 保持历史记录大小
                if len(self.memory_history) > self.max_history_size:
                    self.memory_history.pop(0)
                
                # 检查是否需要自动优化
                if memory_usage["system_percent"] > self.memory_threshold:
                    self.logger.warning(f"内存使用率过高: {memory_usage['system_percent']:.1f}%")
                    self.optimize_memory()
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"内存监控出错: {e}")
                time.sleep(5)


class ProcessPoolManager:
    """
    进程池管理器
    
    负责管理和优化进程池：
    - 动态进程池大小调整
    - 任务负载均衡
    - 进程健康监控
    - 资源回收管理
    """
    
    def __init__(self, max_workers: Optional[int] = None, logger: Optional[logging.Logger] = None):
        """初始化进程池管理器"""
        self.logger = logger or self._init_logger()
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.thread_pool: Optional[ThreadPoolExecutor] = None
        self.process_pool: Optional[ProcessPoolExecutor] = None
        self.task_stats = defaultdict(int)
        self.active_tasks = set()
        self.completed_tasks = 0
        self.failed_tasks = 0
        
    def _init_logger(self) -> logging.Logger:
        """初始化日志系统"""
        logger = logging.getLogger("process_pool_manager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def get_thread_pool(self, max_workers: Optional[int] = None) -> ThreadPoolExecutor:
        """获取线程池"""
        if self.thread_pool is None or self.thread_pool._shutdown:
            workers = max_workers or self.max_workers
            self.thread_pool = ThreadPoolExecutor(max_workers=workers)
            self.logger.info(f"创建线程池，最大工作线程数: {workers}")
        return self.thread_pool
    
    def get_process_pool(self, max_workers: Optional[int] = None) -> ProcessPoolExecutor:
        """获取进程池"""
        if self.process_pool is None or self.process_pool._shutdown:
            workers = max_workers or min(self.max_workers, os.cpu_count() or 1)
            self.process_pool = ProcessPoolExecutor(max_workers=workers)
            self.logger.info(f"创建进程池，最大工作进程数: {workers}")
        return self.process_pool
    
    def submit_task(self, func: Callable, *args, use_process: bool = False, **kwargs):
        """提交任务"""
        pool = self.get_process_pool() if use_process else self.get_thread_pool()
        
        future = pool.submit(func, *args, **kwargs)
        task_id = id(future)
        self.active_tasks.add(task_id)
        self.task_stats["submitted"] += 1
        
        # 添加完成回调
        def task_done(fut):
            self.active_tasks.discard(task_id)
            if fut.exception():
                self.failed_tasks += 1
                self.task_stats["failed"] += 1
                self.logger.error(f"任务执行失败: {fut.exception()}")
            else:
                self.completed_tasks += 1
                self.task_stats["completed"] += 1
        
        future.add_done_callback(task_done)
        return future
    
    def submit_batch(self, func: Callable, args_list: List[tuple], use_process: bool = False) -> List:
        """批量提交任务"""
        futures = []
        for args in args_list:
            future = self.submit_task(func, *args, use_process=use_process)
            futures.append(future)
        
        self.logger.info(f"批量提交 {len(futures)} 个任务")
        return futures
    
    def wait_for_completion(self, futures: List, timeout: Optional[float] = None) -> Dict[str, Any]:
        """等待任务完成"""
        start_time = time.time()
        completed = []
        failed = []
        
        try:
            for future in as_completed(futures, timeout=timeout):
                try:
                    result = future.result()
                    completed.append(result)
                except Exception as e:
                    failed.append(str(e))
        except TimeoutError:
            self.logger.warning(f"等待任务完成超时: {timeout}秒")
        
        elapsed_time = time.time() - start_time
        
        return {
            "completed_count": len(completed),
            "failed_count": len(failed),
            "total_count": len(futures),
            "elapsed_time": elapsed_time,
            "success_rate": len(completed) / len(futures) * 100 if futures else 0,
            "results": completed,
            "errors": failed
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        thread_pool_info = {}
        if self.thread_pool:
            thread_pool_info = {
                "max_workers": self.thread_pool._max_workers,
                "active_threads": len(self.thread_pool._threads),
                "shutdown": self.thread_pool._shutdown
            }
        
        process_pool_info = {}
        if self.process_pool:
            process_pool_info = {
                "max_workers": self.process_pool._max_workers,
                "shutdown": self.process_pool._shutdown
            }
        
        return {
            "active_tasks": len(self.active_tasks),
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "task_stats": dict(self.task_stats),
            "thread_pool": thread_pool_info,
            "process_pool": process_pool_info
        }
    
    def shutdown(self, wait: bool = True):
        """关闭进程池"""
        if self.thread_pool:
            self.thread_pool.shutdown(wait=wait)
            self.logger.info("线程池已关闭")
        
        if self.process_pool:
            self.process_pool.shutdown(wait=wait)
            self.logger.info("进程池已关闭")


class SmartCache:
    """
    智能缓存系统
    
    提供高效的缓存机制：
    - LRU缓存策略
    - 自动过期清理
    - 缓存统计分析
    - 内存使用控制
    """
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600, logger: Optional[logging.Logger] = None):
        """初始化智能缓存"""
        self.max_size = max_size
        self.ttl = ttl  # 生存时间（秒）
        self.logger = logger or self._init_logger()
        
        self.cache: OrderedDict = OrderedDict()
        self.timestamps: Dict[str, datetime] = {}
        self.stats = CacheStats(max_size=max_size)
        self.lock = threading.RLock()
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def _init_logger(self) -> logging.Logger:
        """初始化日志系统"""
        logger = logging.getLogger("smart_cache")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            # 检查是否存在
            if key not in self.cache:
                self.stats.misses += 1
                return None
            
            # 检查是否过期
            if self._is_expired(key):
                self._remove_key(key)
                self.stats.misses += 1
                return None
            
            # 移动到末尾（LRU策略）
            value = self.cache.pop(key)
            self.cache[key] = value
            self.stats.hits += 1
            
            return value
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self.lock:
            current_time = datetime.now()
            
            # 如果键已存在，更新值和时间戳
            if key in self.cache:
                self.cache.pop(key)
            
            # 检查缓存大小限制
            while len(self.cache) >= self.max_size:
                oldest_key = next(iter(self.cache))
                self._remove_key(oldest_key)
            
            # 添加新值
            self.cache[key] = value
            self.timestamps[key] = current_time
            self.stats.size = len(self.cache)
    
    def delete(self, key: str) -> bool:
        """删除缓存值"""
        with self.lock:
            if key in self.cache:
                self._remove_key(key)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
            self.stats.size = 0
            self.logger.info("缓存已清空")
    
    def _is_expired(self, key: str) -> bool:
        """检查键是否过期"""
        if key not in self.timestamps:
            return True
        
        age = (datetime.now() - self.timestamps[key]).total_seconds()
        return age > self.ttl
    
    def _remove_key(self, key: str) -> None:
        """移除键"""
        self.cache.pop(key, None)
        self.timestamps.pop(key, None)
        self.stats.size = len(self.cache)
    
    def _cleanup_loop(self):
        """清理循环"""
        while True:
            try:
                time.sleep(60)  # 每分钟清理一次
                self._cleanup_expired()
            except Exception as e:
                self.logger.error(f"缓存清理出错: {e}")
    
    def _cleanup_expired(self):
        """清理过期项"""
        with self.lock:
            expired_keys = []
            for key in list(self.timestamps.keys()):
                if self._is_expired(key):
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_key(key)
            
            if expired_keys:
                self.logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            "size": self.stats.size,
            "max_size": self.stats.max_size,
            "hits": self.stats.hits,
            "misses": self.stats.misses,
            "hit_rate": self.stats.hit_rate,
            "usage_percent": (self.stats.size / self.stats.max_size * 100) if self.stats.max_size > 0 else 0
        }


class PerformanceAnalyzer:
    """
    性能分析器
    
    提供系统性能分析和诊断：
    - 系统资源监控
    - 性能瓶颈识别
    - 性能趋势分析
    - 优化建议生成
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """初始化性能分析器"""
        self.logger = logger or self._init_logger()
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history_size = 1000
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
    
    def _init_logger(self) -> logging.Logger:
        """初始化日志系统"""
        logger = logging.getLogger("performance_analyzer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # 系统CPU和内存
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            disk_io_read = disk_io.read_bytes / 1024 / 1024 if disk_io else 0  # MB
            disk_io_write = disk_io.write_bytes / 1024 / 1024 if disk_io else 0  # MB
            
            # 网络IO
            network_io = psutil.net_io_counters()
            network_sent = network_io.bytes_sent / 1024 / 1024 if network_io else 0  # MB
            network_recv = network_io.bytes_recv / 1024 / 1024 if network_io else 0  # MB
            
            # 进程和线程数
            process_count = len(psutil.pids())
            
            # 当前进程的线程数和打开文件数
            current_process = psutil.Process()
            thread_count = current_process.num_threads()
            try:
                open_files = len(current_process.open_files())
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = 0
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_mb=memory.used / 1024 / 1024,
                disk_io_read=disk_io_read,
                disk_io_write=disk_io_write,
                network_sent=network_sent,
                network_recv=network_recv,
                process_count=process_count,
                thread_count=thread_count,
                open_files=open_files
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集性能指标失败: {e}")
            return PerformanceMetrics(timestamp=datetime.now())
    
    def start_monitoring(self, interval: int = 30):
        """开始性能监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("性能监控已停止")
    
    def _monitor_loop(self, interval: int):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self.collect_metrics()
                self.metrics_history.append(metrics)
                
                # 保持历史记录大小
                if len(self.metrics_history) > self.max_history_size:
                    self.metrics_history.pop(0)
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"性能监控出错: {e}")
                time.sleep(5)
    
    def analyze_performance(self, hours: int = 1) -> Dict[str, Any]:
        """分析性能数据"""
        if not self.metrics_history:
            return {"error": "没有性能数据可供分析"}
        
        # 获取指定时间范围内的数据
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [
            m for m in self.metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"error": "指定时间范围内没有性能数据"}
        
        # 计算统计数据
        cpu_values = [m.cpu_percent for m in recent_metrics]
        memory_values = [m.memory_percent for m in recent_metrics]
        
        analysis = {
            "time_range_hours": hours,
            "data_points": len(recent_metrics),
            "cpu": {
                "avg": sum(cpu_values) / len(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values)
            },
            "memory": {
                "avg": sum(memory_values) / len(memory_values),
                "max": max(memory_values),
                "min": min(memory_values)
            },
            "latest_metrics": {
                "cpu_percent": recent_metrics[-1].cpu_percent,
                "memory_percent": recent_metrics[-1].memory_percent,
                "memory_mb": recent_metrics[-1].memory_mb,
                "process_count": recent_metrics[-1].process_count,
                "thread_count": recent_metrics[-1].thread_count,
                "open_files": recent_metrics[-1].open_files
            }
        }
        
        # 生成优化建议
        recommendations = self._generate_recommendations(analysis)
        analysis["recommendations"] = recommendations
        
        return analysis
    
    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        cpu_avg = analysis["cpu"]["avg"]
        cpu_max = analysis["cpu"]["max"]
        memory_avg = analysis["memory"]["avg"]
        memory_max = analysis["memory"]["max"]
        
        # CPU相关建议
        if cpu_avg > 80:
            recommendations.append("CPU平均使用率过高，建议优化计算密集型任务或增加CPU资源")
        elif cpu_max > 95:
            recommendations.append("CPU峰值使用率过高，建议检查是否有CPU密集型任务导致系统卡顿")
        
        # 内存相关建议
        if memory_avg > 80:
            recommendations.append("内存平均使用率过高，建议优化内存使用或增加内存容量")
        elif memory_max > 95:
            recommendations.append("内存峰值使用率过高，建议检查是否有内存泄漏或大内存占用")
        
        # 进程和线程建议
        latest = analysis["latest_metrics"]
        if latest["thread_count"] > 100:
            recommendations.append("线程数量较多，建议检查是否有线程泄漏或优化线程使用")
        
        if latest["open_files"] > 1000:
            recommendations.append("打开文件数量较多，建议检查文件句柄是否正确关闭")
        
        if not recommendations:
            recommendations.append("系统性能良好，无需特别优化")
        
        return recommendations
    
    def get_performance_report(self) -> str:
        """生成性能报告"""
        analysis = self.analyze_performance()
        
        if "error" in analysis:
            return f"性能报告生成失败: {analysis['error']}"
        
        report = []
        report.append("=" * 60)
        report.append("系统性能分析报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"分析时间范围: {analysis['time_range_hours']} 小时")
        report.append(f"数据点数量: {analysis['data_points']}")
        report.append("")
        
        # CPU分析
        cpu = analysis["cpu"]
        report.append("CPU使用率分析:")
        report.append(f"  平均值: {cpu['avg']:.1f}%")
        report.append(f"  最大值: {cpu['max']:.1f}%")
        report.append(f"  最小值: {cpu['min']:.1f}%")
        report.append("")
        
        # 内存分析
        memory = analysis["memory"]
        report.append("内存使用率分析:")
        report.append(f"  平均值: {memory['avg']:.1f}%")
        report.append(f"  最大值: {memory['max']:.1f}%")
        report.append(f"  最小值: {memory['min']:.1f}%")
        report.append("")
        
        # 当前状态
        latest = analysis["latest_metrics"]
        report.append("当前系统状态:")
        report.append(f"  CPU使用率: {latest['cpu_percent']:.1f}%")
        report.append(f"  内存使用率: {latest['memory_percent']:.1f}%")
        report.append(f"  内存使用量: {latest['memory_mb']:.1f}MB")
        report.append(f"  进程数量: {latest['process_count']}")
        report.append(f"  线程数量: {latest['thread_count']}")
        report.append(f"  打开文件数: {latest['open_files']}")
        report.append("")
        
        # 优化建议
        report.append("优化建议:")
        for i, recommendation in enumerate(analysis["recommendations"], 1):
            report.append(f"  {i}. {recommendation}")
        
        report.append("=" * 60)
        
        return "\n".join(report)


# 性能装饰器
def performance_monitor(func: Callable) -> Callable:
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            result = func(*args, **kwargs)
            success = True
        except Exception as e:
            result = e
            success = False
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss
        
        execution_time = end_time - start_time
        memory_delta = (end_memory - start_memory) / 1024 / 1024  # MB
        
        # 记录性能数据
        logger = logging.getLogger("performance_monitor")
        logger.info(
            f"函数 {func.__name__} 执行完成 - "
            f"耗时: {execution_time:.3f}s, "
            f"内存变化: {memory_delta:+.2f}MB, "
            f"状态: {'成功' if success else '失败'}"
        )
        
        if not success:
            raise result
        
        return result
    
    return wrapper


# 缓存装饰器
def cached(ttl: int = 3600, max_size: int = 128):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        cache = SmartCache(max_size=max_size, ttl=ttl)
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{func.__name__}:{hash((args, tuple(sorted(kwargs.items()))))}"
            
            # 尝试从缓存获取
            result = cache.get(cache_key)
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result)
            
            return result
        
        # 添加缓存管理方法
        wrapper.cache = cache
        wrapper.clear_cache = cache.clear
        wrapper.cache_stats = cache.get_stats
        
        return wrapper
    
    return decorator