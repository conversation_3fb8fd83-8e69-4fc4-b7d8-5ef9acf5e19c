# -*- coding: utf-8 -*-
"""
考生答题模块配置文件

功能说明:
- 数据库连接配置
- 应用基础配置
- 外部服务配置
- 安全配置

作者: 系统生成
创建时间: 2024
"""

import os
from datetime import timedelta

class Config:
    """
    基础配置类
    
    包含应用的基础配置信息，如数据库连接、密钥、外部服务地址等
    """
    
    # 应用基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'student-exam-secret-key-2024'
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///student_exam.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=3)  # 考试会话3小时有效
    SESSION_COOKIE_SECURE = False  # 局域网环境，设置为False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-2024'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=3)
    
    # 外部服务配置
    USER_MANAGEMENT_URL = os.environ.get('USER_MANAGEMENT_URL') or 'http://localhost:5002'
    EXAM_MANAGEMENT_URL = os.environ.get('EXAM_MANAGEMENT_URL') or 'http://localhost:5003'
    QUESTION_BANK_URL = os.environ.get('QUESTION_BANK_URL') or 'http://localhost:5004'
    API_GATEWAY_URL = os.environ.get('API_GATEWAY_URL') or 'http://localhost:8080'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    
    # 考试配置
    EXAM_AUTO_SAVE_INTERVAL = 30  # 自动保存间隔（秒）
    EXAM_WARNING_TIME = 10  # 考试结束前警告时间（分钟）
    MAX_EXAM_DURATION = 180  # 最大考试时长（分钟）
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'student_exam.log')
    
    # CORS配置
    CORS_ORIGINS = [
        'http://localhost:8000',  # 主控台
        'http://localhost:8080',  # API网关
        'http://127.0.0.1:8000',
        'http://127.0.0.1:8080'
    ]
    
    # 安全配置
    WTF_CSRF_ENABLED = False  # API模式，禁用CSRF
    WTF_CSRF_TIME_LIMIT = None
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    @staticmethod
    def init_app(app):
        """
        初始化应用配置
        
        参数:
            app: Flask应用实例
        """
        # 创建必要的目录
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(os.path.dirname(Config.LOG_FILE), exist_ok=True)

class DevelopmentConfig(Config):
    """
    开发环境配置
    """
    DEBUG = True
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """
    生产环境配置
    """
    DEBUG = False
    LOG_LEVEL = 'WARNING'
    SESSION_COOKIE_SECURE = True  # 生产环境启用HTTPS

class TestingConfig(Config):
    """
    测试环境配置
    """
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# 获取当前配置
def get_config():
    """
    获取当前环境配置
    
    返回值:
        Config: 配置类实例
    """
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])