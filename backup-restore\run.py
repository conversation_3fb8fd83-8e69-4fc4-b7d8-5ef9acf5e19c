# -*- coding: utf-8 -*-
"""
备份恢复模块 - 启动脚本
"""

from app import create_app

# Create the Flask app instance using the factory
app = create_app()

if __name__ == '__main__':
    # The port is configured to 5016 to avoid conflicts
    port = 5016

    print("--- Backup & Restore Service ---")
    print(f"Starting server at: http://0.0.0.0:{port}")

    # Using debug=True for development provides helpful logs and auto-reloading.
    app.run(host='0.0.0.0', port=port, debug=True)
