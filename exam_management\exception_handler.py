#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常处理和错误恢复机制模块
提供统一的异常处理、错误恢复和重试机制
"""

import time
import traceback
import functools
import threading
from typing import Dict, Any, Optional, Callable, Type, List, Tuple
from datetime import datetime, timedelta
import sqlite3
import json
from enum import Enum

class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    LOW = "low"           # 低级错误，不影响主要功能
    MEDIUM = "medium"     # 中级错误，影响部分功能
    HIGH = "high"         # 高级错误，影响主要功能
    CRITICAL = "critical" # 严重错误，系统无法正常运行

class ErrorCategory(Enum):
    """错误分类枚举"""
    DATABASE = "database"         # 数据库错误
    NETWORK = "network"           # 网络错误
    VALIDATION = "validation"     # 验证错误
    PERMISSION = "permission"     # 权限错误
    BUSINESS = "business"         # 业务逻辑错误
    SYSTEM = "system"             # 系统错误
    EXTERNAL = "external"         # 外部服务错误

class ExamException(Exception):
    """考试系统基础异常类"""
    
    def __init__(self, message: str, code: str = None, 
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 category: ErrorCategory = ErrorCategory.SYSTEM,
                 details: Dict[str, Any] = None,
                 recoverable: bool = True):
        """
        初始化异常
        
        Args:
            message: 错误消息
            code: 错误代码
            severity: 错误严重程度
            category: 错误分类
            details: 错误详细信息
            recoverable: 是否可恢复
        """
        super().__init__(message)
        self.message = message
        self.code = code or self.__class__.__name__
        self.severity = severity
        self.category = category
        self.details = details or {}
        self.recoverable = recoverable
        self.timestamp = datetime.now()
        self.traceback = traceback.format_exc()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'message': self.message,
            'code': self.code,
            'severity': self.severity.value,
            'category': self.category.value,
            'details': self.details,
            'recoverable': self.recoverable,
            'timestamp': self.timestamp.isoformat(),
            'traceback': self.traceback
        }

class DatabaseException(ExamException):
    """数据库异常"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.DATABASE, **kwargs)

class ValidationException(ExamException):
    """验证异常"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.VALIDATION, 
                        severity=ErrorSeverity.LOW, **kwargs)

class PermissionException(ExamException):
    """权限异常"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.PERMISSION,
                        severity=ErrorSeverity.HIGH, recoverable=False, **kwargs)

class BusinessException(ExamException):
    """业务逻辑异常"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.BUSINESS, **kwargs)

class NetworkException(ExamException):
    """网络异常"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.NETWORK, **kwargs)

class ExternalServiceException(ExamException):
    """外部服务异常"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, category=ErrorCategory.EXTERNAL, **kwargs)

class RetryConfig:
    """重试配置"""
    
    def __init__(self, max_attempts: int = 3, delay: float = 1.0,
                 backoff_factor: float = 2.0, max_delay: float = 60.0,
                 retry_exceptions: Tuple[Type[Exception], ...] = None):
        """
        初始化重试配置
        
        Args:
            max_attempts: 最大重试次数
            delay: 初始延迟时间（秒）
            backoff_factor: 退避因子
            max_delay: 最大延迟时间（秒）
            retry_exceptions: 需要重试的异常类型
        """
        self.max_attempts = max_attempts
        self.delay = delay
        self.backoff_factor = backoff_factor
        self.max_delay = max_delay
        self.retry_exceptions = retry_exceptions or (NetworkException, ExternalServiceException)

class ErrorRecoveryManager:
    """错误恢复管理器"""
    
    def __init__(self, db_path: str = "error_recovery.db"):
        """
        初始化错误恢复管理器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.recovery_strategies = {}
        self.error_history = []
        self._init_db()
        self._lock = threading.Lock()
    
    def _init_db(self):
        """初始化错误恢复数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS error_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    error_code TEXT NOT NULL,
                    error_message TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    category TEXT NOT NULL,
                    details TEXT,
                    recoverable BOOLEAN NOT NULL,
                    recovery_attempted BOOLEAN DEFAULT FALSE,
                    recovery_successful BOOLEAN DEFAULT FALSE,
                    recovery_strategy TEXT,
                    retry_count INTEGER DEFAULT 0,
                    resolved BOOLEAN DEFAULT FALSE
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON error_records(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_error_code ON error_records(error_code)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_severity ON error_records(severity)")
    
    def register_recovery_strategy(self, error_code: str, strategy: Callable):
        """
        注册错误恢复策略
        
        Args:
            error_code: 错误代码
            strategy: 恢复策略函数
        """
        self.recovery_strategies[error_code] = strategy
    
    def record_error(self, exception: ExamException) -> int:
        """
        记录错误
        
        Args:
            exception: 异常对象
            
        Returns:
            错误记录ID
        """
        with self._lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    INSERT INTO error_records 
                    (timestamp, error_code, error_message, severity, category, 
                     details, recoverable)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    exception.timestamp.isoformat(),
                    exception.code,
                    exception.message,
                    exception.severity.value,
                    exception.category.value,
                    json.dumps(exception.details),
                    exception.recoverable
                ))
                return cursor.lastrowid
    
    def attempt_recovery(self, error_id: int, exception: ExamException) -> bool:
        """
        尝试错误恢复
        
        Args:
            error_id: 错误记录ID
            exception: 异常对象
            
        Returns:
            恢复是否成功
        """
        if not exception.recoverable:
            return False
        
        strategy = self.recovery_strategies.get(exception.code)
        if not strategy:
            # 尝试通用恢复策略
            strategy = self.recovery_strategies.get('default')
        
        if not strategy:
            return False
        
        try:
            # 更新恢复尝试状态
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE error_records 
                    SET recovery_attempted = TRUE, recovery_strategy = ?
                    WHERE id = ?
                """, (strategy.__name__, error_id))
            
            # 执行恢复策略
            result = strategy(exception)
            
            # 更新恢复结果
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE error_records 
                    SET recovery_successful = ?, resolved = ?
                    WHERE id = ?
                """, (result, result, error_id))
            
            return result
        
        except Exception as e:
            # 恢复策略本身失败
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE error_records 
                    SET recovery_successful = FALSE
                    WHERE id = ?
                """, (error_id,))
            return False
    
    def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """
        获取错误统计信息
        
        Args:
            hours: 统计时间范围（小时）
            
        Returns:
            错误统计字典
        """
        start_time = datetime.now() - timedelta(hours=hours)
        
        with sqlite3.connect(self.db_path) as conn:
            # 按严重程度统计
            cursor = conn.execute("""
                SELECT severity, COUNT(*) as count
                FROM error_records 
                WHERE timestamp >= ?
                GROUP BY severity
            """, (start_time.isoformat(),))
            severity_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 按分类统计
            cursor = conn.execute("""
                SELECT category, COUNT(*) as count
                FROM error_records 
                WHERE timestamp >= ?
                GROUP BY category
            """, (start_time.isoformat(),))
            category_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 恢复成功率
            cursor = conn.execute("""
                SELECT 
                    COUNT(*) as total_recoverable,
                    SUM(CASE WHEN recovery_successful = 1 THEN 1 ELSE 0 END) as successful_recoveries
                FROM error_records 
                WHERE timestamp >= ? AND recoverable = 1
            """, (start_time.isoformat(),))
            recovery_data = cursor.fetchone()
            
            recovery_rate = 0
            if recovery_data[0] > 0:
                recovery_rate = recovery_data[1] / recovery_data[0]
            
            return {
                'severity_distribution': severity_stats,
                'category_distribution': category_stats,
                'total_errors': sum(severity_stats.values()),
                'recovery_rate': recovery_rate,
                'time_range_hours': hours
            }

def retry_on_exception(config: RetryConfig = None):
    """
    重试装饰器
    
    Args:
        config: 重试配置
        
    Returns:
        装饰器函数
    """
    if config is None:
        config = RetryConfig()
    
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(config.max_attempts):
                try:
                    return func(*args, **kwargs)
                except config.retry_exceptions as e:
                    last_exception = e
                    
                    if attempt < config.max_attempts - 1:
                        # 计算延迟时间
                        delay = min(
                            config.delay * (config.backoff_factor ** attempt),
                            config.max_delay
                        )
                        time.sleep(delay)
                    
                except Exception as e:
                    # 不在重试范围内的异常直接抛出
                    raise
            
            # 所有重试都失败，抛出最后一个异常
            raise last_exception
        
        return wrapper
    return decorator

def handle_exceptions(recovery_manager: ErrorRecoveryManager = None,
                     auto_recover: bool = True):
    """
    异常处理装饰器
    
    Args:
        recovery_manager: 错误恢复管理器
        auto_recover: 是否自动尝试恢复
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ExamException as e:
                # 记录错误
                if recovery_manager:
                    error_id = recovery_manager.record_error(e)
                    
                    # 尝试自动恢复
                    if auto_recover and e.recoverable:
                        if recovery_manager.attempt_recovery(error_id, e):
                            # 恢复成功，重新执行函数
                            return func(*args, **kwargs)
                
                # 重新抛出异常
                raise
            except Exception as e:
                # 将普通异常转换为ExamException
                exam_exception = ExamException(
                    message=str(e),
                    code=e.__class__.__name__,
                    severity=ErrorSeverity.MEDIUM,
                    category=ErrorCategory.SYSTEM
                )
                
                if recovery_manager:
                    recovery_manager.record_error(exam_exception)
                
                raise exam_exception
        
        return wrapper
    return decorator

# 默认恢复策略
def default_database_recovery(exception: DatabaseException) -> bool:
    """
    默认数据库恢复策略
    
    Args:
        exception: 数据库异常
        
    Returns:
        恢复是否成功
    """
    try:
        # 尝试重新连接数据库
        time.sleep(1)
        # 这里应该实现具体的数据库重连逻辑
        return True
    except Exception:
        return False

def default_network_recovery(exception: NetworkException) -> bool:
    """
    默认网络恢复策略
    
    Args:
        exception: 网络异常
        
    Returns:
        恢复是否成功
    """
    try:
        # 等待网络恢复
        time.sleep(2)
        # 这里应该实现具体的网络检查逻辑
        return True
    except Exception:
        return False

# 全局错误恢复管理器
global_recovery_manager = ErrorRecoveryManager()

# 注册默认恢复策略
global_recovery_manager.register_recovery_strategy('DatabaseException', default_database_recovery)
global_recovery_manager.register_recovery_strategy('NetworkException', default_network_recovery)

# 使用示例
if __name__ == "__main__":
    # 测试异常处理
    @handle_exceptions(global_recovery_manager)
    @retry_on_exception(RetryConfig(max_attempts=3))
    def test_function():
        # 模拟可能失败的操作
        import random
        if random.random() < 0.7:
            raise NetworkException("网络连接失败")
        return "操作成功"
    
    try:
        result = test_function()
        print(f"结果: {result}")
    except ExamException as e:
        print(f"操作失败: {e.message}")
        print(f"错误详情: {e.to_dict()}")
    
    # 查看错误统计
    stats = global_recovery_manager.get_error_statistics()
    print(f"错误统计: {stats}")