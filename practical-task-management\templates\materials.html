{% extends "base.html" %}

{% block title %}素材管理 - 实操任务管理系统{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{{ url_for('web.tasks') }}">任务管理</a></li>
<li class="breadcrumb-item active">素材管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-file-earmark-text"></i> 素材管理
                {% if task %}
                <small class="text-muted">- {{ task.title }}</small>
                {% endif %}
            </h1>
            <div>
                {% if task %}
                <a href="{{ url_for('web.task_detail', task_id=task.id) }}" class="btn btn-outline-secondary me-2">
                    <i class="bi bi-arrow-left"></i> 返回任务
                </a>
                {% endif %}
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    <i class="bi bi-cloud-upload"></i> 上传素材
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 任务信息卡片 -->
{% if task %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h5 class="card-title">{{ task.title }}</h5>
                        <p class="card-text text-muted">{{ task.description[:100] }}{% if task.description|length > 100 %}...{% endif %}</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-primary">{{ materials|length }}</div>
                                    <small class="text-muted">素材文件</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 mb-0 text-info">{{ total_size_mb }}MB</div>
                                    <small class="text-muted">总大小</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    {% if task %}
                    <input type="hidden" name="task_id" value="{{ task.id }}">
                    {% endif %}
                    <div class="col-md-4">
                        <label for="search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.args.get('search', '') }}" placeholder="文件名或描述">
                    </div>
                    <div class="col-md-3">
                        <label for="file_type" class="form-label">文件类型</label>
                        <select class="form-select" id="file_type" name="file_type">
                            <option value="">全部类型</option>
                            <option value="document" {{ 'selected' if request.args.get('file_type') == 'document' }}>文档</option>
                            <option value="image" {{ 'selected' if request.args.get('file_type') == 'image' }}>图片</option>
                            <option value="video" {{ 'selected' if request.args.get('file_type') == 'video' }}>视频</option>
                            <option value="audio" {{ 'selected' if request.args.get('file_type') == 'audio' }}>音频</option>
                            <option value="archive" {{ 'selected' if request.args.get('file_type') == 'archive' }}>压缩包</option>
                            <option value="other" {{ 'selected' if request.args.get('file_type') == 'other' }}>其他</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sort_by" class="form-label">排序方式</label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="created_at" {{ 'selected' if request.args.get('sort_by') == 'created_at' }}>创建时间</option>
                            <option value="file_name" {{ 'selected' if request.args.get('sort_by') == 'file_name' }}>文件名</option>
                            <option value="file_size" {{ 'selected' if request.args.get('sort_by') == 'file_size' }}>文件大小</option>
                            <option value="version" {{ 'selected' if request.args.get('sort_by') == 'version' }}>版本号</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                            </button>
                            <a href="{{ url_for('web.materials', task_id=task.id if task else '') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 素材列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">素材列表 (共 {{ materials|length }} 个)</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="toggleView('grid')" id="gridViewBtn">
                        <i class="bi bi-grid-3x3-gap"></i> 网格
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="toggleView('list')" id="listViewBtn">
                        <i class="bi bi-list"></i> 列表
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if materials %}
                <!-- 列表视图 -->
                <div id="listView" class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50">#</th>
                                <th>文件信息</th>
                                <th>类型</th>
                                <th>大小</th>
                                <th>版本</th>
                                <th>上传时间</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for material in materials %}
                            <tr>
                                <td>{{ material.id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="bi {{ get_file_icon(material.file_type) }} fs-4 text-{{ get_file_color(material.file_type) }}"></i>
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ material.file_name }}</div>
                                            {% if material.description %}
                                            <small class="text-muted">{{ material.description[:50] }}{% if material.description|length > 50 %}...{% endif %}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ get_file_color(material.file_type) }}">{{ material.file_type }}</span>
                                </td>
                                <td>{{ format_file_size(material.file_size) }}</td>
                                <td>
                                    <span class="badge bg-info">v{{ material.version }}</span>
                                </td>
                                <td>{{ material.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="downloadMaterial({{ material.id }})" title="下载">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info" 
                                                onclick="viewMaterial({{ material.id }})" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-warning" 
                                                onclick="editMaterial({{ material.id }})" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteMaterial({{ material.id }}, '{{ material.file_name }}')" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 网格视图 -->
                <div id="gridView" class="d-none p-3">
                    <div class="row">
                        {% for material in materials %}
                        <div class="col-md-4 col-lg-3 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="bi {{ get_file_icon(material.file_type) }} display-4 text-{{ get_file_color(material.file_type) }}"></i>
                                    <h6 class="card-title mt-2">{{ material.file_name[:20] }}{% if material.file_name|length > 20 %}...{% endif %}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            {{ format_file_size(material.file_size) }} | v{{ material.version }}
                                        </small>
                                    </p>
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="btn-group w-100" role="group">
                                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                                onclick="downloadMaterial({{ material.id }})">
                                            <i class="bi bi-download"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-sm" 
                                                onclick="viewMaterial({{ material.id }})">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                onclick="deleteMaterial({{ material.id }}, '{{ material.file_name }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-file-earmark-text display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无素材文件</h4>
                    <p class="text-muted">点击上方按钮上传第一个素材文件</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 上传素材模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-cloud-upload"></i> 上传素材文件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    {% if task %}
                    <input type="hidden" name="task_id" value="{{ task.id }}">
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="materialFile" class="form-label">选择文件 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="materialFile" name="file" required>
                        <div class="form-text">支持的文件类型：文档、图片、视频、音频、压缩包等，最大50MB</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="materialDescription" class="form-label">文件描述</label>
                        <textarea class="form-control" id="materialDescription" name="description" rows="3" maxlength="500"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="materialTags" class="form-label">标签</label>
                        <input type="text" class="form-control" id="materialTags" name="tags" maxlength="200">
                        <div class="form-text">多个标签用逗号分隔</div>
                    </div>
                    
                    <div class="progress d-none" id="uploadProgress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="uploadMaterial()">
                    <i class="bi bi-cloud-upload"></i> 上传
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 素材详情模态框 -->
<div class="modal fade" id="materialDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-file-earmark-text"></i> 素材详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="materialDetailContent">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 关闭
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 切换视图
function toggleView(viewType) {
    const listView = document.getElementById('listView');
    const gridView = document.getElementById('gridView');
    const listBtn = document.getElementById('listViewBtn');
    const gridBtn = document.getElementById('gridViewBtn');
    
    if (viewType === 'grid') {
        listView.classList.add('d-none');
        gridView.classList.remove('d-none');
        listBtn.classList.remove('active');
        gridBtn.classList.add('active');
    } else {
        gridView.classList.add('d-none');
        listView.classList.remove('d-none');
        gridBtn.classList.remove('active');
        listBtn.classList.add('active');
    }
}

// 上传素材
function uploadMaterial() {
    const form = document.getElementById('uploadForm');
    const formData = new FormData(form);
    const progressBar = document.getElementById('uploadProgress');
    
    progressBar.classList.remove('d-none');
    
    fetch('/api/v1/materials', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            location.reload();
        } else {
            alert('上传失败: ' + data.msg);
        }
    })
    .catch(error => {
        alert('上传失败: ' + error.message);
    })
    .finally(() => {
        progressBar.classList.add('d-none');
    });
}

// 下载素材
function downloadMaterial(materialId) {
    window.open(`/api/v1/materials/${materialId}/download`, '_blank');
}

// 查看素材详情
function viewMaterial(materialId) {
    fetch(`/api/v1/materials/${materialId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                const material = data.data;
                const content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <table class="table table-sm">
                                <tr><td>文件名</td><td>${material.file_name}</td></tr>
                                <tr><td>文件类型</td><td>${material.file_type}</td></tr>
                                <tr><td>文件大小</td><td>${formatFileSize(material.file_size)}</td></tr>
                                <tr><td>版本号</td><td>v${material.version}</td></tr>
                                <tr><td>上传时间</td><td>${new Date(material.created_at).toLocaleString()}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>描述信息</h6>
                            <p>${material.description || '暂无描述'}</p>
                            <h6>标签</h6>
                            <p>${material.tags || '暂无标签'}</p>
                        </div>
                    </div>
                `;
                document.getElementById('materialDetailContent').innerHTML = content;
                
                const modal = new bootstrap.Modal(document.getElementById('materialDetailModal'));
                modal.show();
            } else {
                alert('获取素材详情失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('获取素材详情失败: ' + error.message);
        });
}

// 编辑素材
function editMaterial(materialId) {
    // 这里可以实现编辑功能
    alert('编辑功能待实现');
}

// 删除素材
function deleteMaterial(materialId, fileName) {
    if (confirm(`确定要删除素材文件 "${fileName}" 吗？此操作不可撤销。`)) {
        fetch(`/api/v1/materials/${materialId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                location.reload();
            } else {
                alert('删除失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error.message);
        });
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 重置上传表单
document.getElementById('uploadModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('uploadForm').reset();
    document.getElementById('uploadProgress').classList.add('d-none');
});
</script>
{% endblock %}