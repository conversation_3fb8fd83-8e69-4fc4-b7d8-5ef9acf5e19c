## 一、模块概述

### 1. 模块定位

用户管理模块是系统的基础核心模块，负责所有用户的账号管理、身份认证、权限控制及信息维护，为其他模块（如题库管理、考试编排）提供统一的用户身份与权限支持。

### 2. 核心目标

- 实现用户全生命周期管理（创建、查询、更新、删除）
- 提供安全的身份认证机制
- 基于角色的权限控制（不同角色操作不同功能）
- 支持批量导入 / 导出用户数据（适配 Excel 格式）

## 二、功能范围

### 1. 核心功能清单

|功能类别|具体功能点|说明|
|---|---|---|
|**用户基础管理**|单个用户创建|手动输入用户信息创建账号|
||批量用户导入（Excel）|通过 Excel 表格批量创建用户（支持模板下载）|
||用户信息查询|按用户名、部门、角色等条件筛选|
||用户信息编辑|修改用户姓名、手机号、部门等非敏感信息|
||用户状态管理|启用 / 禁用账号（禁用后无法登录系统）|
||用户删除|移除用户账号（支持批量删除）|
|**身份认证**|用户登录|用户名 + 密码验证，返回认证令牌|
||密码重置|管理员重置用户密码（生成临时密码）|
||密码修改|用户自主修改密码（需验证原密码）|
||会话管理|记录登录状态，超时自动登出|
|**角色与权限**|角色分配|为用户指定角色（考生 / 管理员 / 考评员 / 专家  / 内部督导员）|
||权限关联|基于角色绑定可操作的功能权限（如管理员可创建考试）|

## 三、数据设计

### 1. 核心数据实体

#### （1）用户表（user）
| 字段名                               | 数据类型   | 说明（约束）                                                          |
| --------------------------------- | ------ | ----------------------------------------------------------------- |
| serial_number                     | number | 序号（自增，非唯一）                                                        |
| user_name                         | string | 用户名（主键，登录账号，唯一，支持字母 / 数字 / 下划线，长度 1-50） （必填）       |
| real_name                         | string | 姓名（长度 1-50） （必填）                                           |
| id_card                           | string | 身份证号码（18 位，可选，加密存储） （必填）                                   |
| role                              | string | 角色（枚举值：admin = 管理员，expert = 专家，student = 考生，grader = 考评员，internal_supervisor = 内部督导员） （必填） |
| gender                            | string | 性别（枚举值：male = 男，female = 女，unknown = 未知）                          |
| date_of_birth                     | string | 出生日期（格式：yyyy-mm-dd）                                               |
| household_registration_address    | string | 户籍所在地（长度 1-200）                                                   |
| educational_level                 | string | 文化程度（枚举值：博士研究生，硕士研究生，大学本科，大学专科，中等专科，职业高中，技工学校，普通中学，初级中学，小学，高技，其他） |
| major                             | string | 所学专业（长度 1-100，初中、高中、其他无需填）                                 |
| declared_occupation               | string | 申报职业（工种）（长度 1-100）                                                |
| declared_level                    | string | 申报级别（枚举值：五级，四级，三级，二级，一级）                                |
| exam_type                         | string | 考试类型（枚举值：新考，重考，补考）                                           |
| assessment_subject                | string | 考核科目（枚举值：理论，实操，答辩）                                           |
| seat_serial_number                | number | 座次序号（整数）                                                        |
| exam_date                         | string | 考试日期（格式：yyyy-MM-dd）                                               |
| exam_session                      | string | 考试期数（长度 1-20，如 “2025 第 1 期”）                                      |
| seat_number                       | string | 座位号（长度 1-10，如 “A01”）                                              |
| email                             | string | 邮箱（符合邮箱格式，可选）                                                     |
| password                          | string | 密码（加密存储，禁止明文，长度≥6） （必填）                                      |
| phone                             | string | 手机号码（11 位数字，可选）    （必填）                                   |
| company                           | string | 工作单位（长度 1-200，可选）                                                 |
| department                        | string | 部门（长度 1-100，可选）                                                   |
| status                            | string | 状态（枚举值：待审核，审核通过，审核未通过）                                      |
| avatar_photo_link                 | string | 头像照片链接（局域网路径，可选）                                                  |
| id_card_photo_link                | string | 身份证照片链接（局域网路径，可选）                                              |
| graduation_certificate_photo_link | string | 毕业证照片链接（局域网路径，可选）                                              |
| skill_certificate_photo_link      | string | 技能证书照片链接（局域网路径，可选）                                            |
| question_bank_name                | string | 关联题库名称（长度 1-100，可选）                                               |
| test_paper_id                     | string | 关联试卷 ID（长度 1-32，可选）                                               |

#### （2）角色权限关联表（role_permission）

|字段名|数据类型|长度|是否必填|说明|
|---|---|---|---|---|
|role|字符串|20|是|角色类型（同用户表的 role 字段）|
|permission|字符串|50|是|权限标识（如 “exam:create”= 创建考试权限）|

## 四、接口设计

### 1. 接口规范（遵循全局《接口规范手册》）

- 基础 URL：`/api/v1/user`
- 数据格式：JSON
- 认证方式：除登录接口外，均需在请求头携带令牌 `Authorization: Bearer {token}`
- 通过API网关统一路由和管理
- 模块端口：5001，网关路由前缀：/api/v1/user
- 健康检查接口：`GET /health`

### 2. 核心接口清单

|接口功能|请求方法|接口路径|输入参数（示例）|输出参数（示例）|
|---|---|---|---|---|
|用户登录|POST|`/login`|`{"user_name": "zhangsan", "password": "123456"}`|`{"code": 200, "msg": "登录成功", "data": {"token": "xxx", "user_id": "u123", "role": "student"}}`|
|批量导入用户|POST|`/batch-import`|`{"file_url": "/upload/user_template.xlsx"}`（Excel 文件存储路径）|`{"code": 200, "msg": "导入成功", "data": {"success_count": 50, "fail_count": 2, "fail_list": ["lisi"]}}`|
|查询用户列表|GET|`/list`|Query 参数：`role=student&department=计算机系&page=1&size=20`|`{"code": 200, "msg": "查询成功", "data": {"total": 120, "list": [{"user_id": "u123", "user_name": "zhangsan", "real_name": "张三", ...}]}}`|
|修改用户状态|PUT|`/status`|`{"user_ids": ["u123", "u124"], "status": 0}`（0 = 禁用，1 = 启用）|`{"code": 200, "msg": "状态更新成功"}`|
|密码重置|PUT|`/reset-password`|`{"user_id": "u123"}`|`{"code": 200, "msg": "密码重置成功", "data": {"temp_password": "Abc123"}}`|

## 五、业务流程

### 1. 批量用户导入流程

### 2. 用户登录流程

### 3. 健康检查接口

|接口功能|请求方法|接口路径|输入参数|输出参数（示例）|
|---|---|---|---|---|
|健康检查|GET|`/health`|无|`{"status": "healthy", "timestamp": "2025-01-01T10:00:00Z", "module": "user-management", "version": "1.0.0"}`|

## 六、非功能需求

### 1. 安全性

- 密码存储：采用不可逆加密算法（如 SHA-256 加盐），禁止明文存储
- 权限控制：严格校验操作权限（如考生无法访问 “用户管理” 功能）
- 日志审计：记录所有关键操作（登录、批量导入、密码重置），包含操作人、时间、IP 地址

### 2. 性能

- 响应时间：单用户操作（如登录、查询）≤1 秒；批量导入（1000 条数据）≤10 秒
- 并发支持：支持 50 人同时在线操作（查询、编辑用户）

### 3. 易用性

- 提供 Excel 导入模板（含字段说明），支持错误数据下载（标注失败原因）
- 用户列表支持导出 Excel（含用户基本信息，不含密码等敏感数据）

## 七、依赖与约束

### 1. 依赖模块

- 无前置依赖模块，可独立开发（为系统第一个开发的核心模块）

### 2. 外部约束

- 浏览器兼容性：支持 Chrome 90+、Edge 90+、Firefox 88+
- 数据库：开发环境使用SQLite（轻量级，无需额外配置）；生产环境可升级至MySQL 8.0+
- 数据导入：仅支持.xlsx 格式 Excel 文件（单个文件≤10MB）
- 权限关联：需与后续开发的 "权限管理模块" 对接（当前暂存角色信息）
- API网关集成：需要配置网关路由规则和认证策略

### 3. 技术依赖

#### 开发框架
- Flask 2.0+
- SQLAlchemy 1.4+
- Flask-JWT-Extended
- Flask-CORS
- Flask-RESTful

#### 数据库
- SQLite（开发环境）/ MySQL 8.0+（生产环境）
- Redis（会话缓存，可选）

#### 其他依赖
- bcrypt（密码加密）
- pandas（Excel处理）
- openpyxl（Excel读写）
- requests（API调用）

#### 集成依赖
- API网关：统一路由和认证
- 配置管理：YAML配置文件
- 日志系统：结构化日志输出

## 八、开发计划

### 第一阶段（3天）：基础架构
- 项目结构搭建
- 数据库设计和创建
- 基础配置和工具类
- API网关集成配置

### 第二阶段（4天）：核心功能
- 用户CRUD操作
- 登录认证功能
- 权限控制机制
- 健康检查接口

### 第三阶段（3天）：高级功能
- 批量导入导出
- 密码重置功能
- 会话管理
- 日志审计功能

### 第四阶段（3天）：集成测试
- 单元测试
- 接口测试
- API网关集成测试
- 性能优化

**总计：13天**

## 九、交付物清单

1. 模块部署包（含可执行程序）
2. 用户操作手册（含管理员操作步骤、Excel 模板使用说明）
3. 接口文档（含所有接口的输入输出、参数说明）
4. 测试报告（含功能测试、性能测试结果）
5. API网关配置文件和集成文档