#!/bin/bash

# ================================================================
# 职业技能等级考试系统 - Linux/macOS启动脚本
# Professional Skills Assessment System - Linux/macOS Startup Script
# 
# 版本: 2.0.0
# 作者: 系统架构师
# 日期: 2024-01-15
# ================================================================

# 设置脚本选项
set -e  # 遇到错误立即退出
set -u  # 使用未定义变量时报错
set -o pipefail  # 管道中任何命令失败都会导致整个管道失败

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 图标定义
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"
ROCKET="🚀"
GEAR="🔧"
SHIELD="🛡️"
LIGHTNING="⚡"
STOP="🛑"
RESTART="🔄"
STATUS="📊"
SEARCH="🔍"
BOOK="📖"
LIGHT="💡"
NOTE="📝"

# 全局变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
PYTHON_CMD="python3"
VENV_PATH=""
DEV_MODE=false
BACKGROUND=false
STATUS_ONLY=false
STOP_ONLY=false
RESTART=false
MODULES=""
SHOW_HELP=false

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local icon=$2
    local message=$3
    echo -e "${color}${icon} ${message}${NC}"
}

# 函数：打印成功消息
print_success() {
    print_message "$GREEN" "$CHECK" "$1"
}

# 函数：打印错误消息
print_error() {
    print_message "$RED" "$CROSS" "$1"
}

# 函数：打印警告消息
print_warning() {
    print_message "$YELLOW" "$WARNING" "$1"
}

# 函数：打印信息消息
print_info() {
    print_message "$BLUE" "$INFO" "$1"
}

# 函数：显示系统横幅
show_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
╔══════════════════════════════════════════════════════════════╗
║                职业技能等级考试系统 v2.0                      ║
║                Professional Skills Assessment System          ║
╠══════════════════════════════════════════════════════════════╣
║  🚀 新一代架构 | 🔧 模块化设计 | ⚡ 高性能启动 | 🛡️ 安全可靠  ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 函数：显示帮助信息
show_help() {
    echo
    print_info "${BOOK} 使用说明:"
    echo
    echo "  ./start.sh                    启动所有模块"
    echo "  ./start.sh --dev              开发模式启动"
    echo "  ./start.sh --modules MODULE   启动指定模块"
    echo "  ./start.sh --status           查看系统状态"
    echo "  ./start.sh --stop             停止所有服务"
    echo "  ./start.sh --restart          重启系统"
    echo "  ./start.sh --background       后台模式启动"
    echo "  ./start.sh --help             显示此帮助信息"
    echo
    print_info "📋 可用模块:"
    echo "  user_management              用户管理模块"
    echo "  question_bank                题库管理模块"
    echo "  exam_management              考试管理模块"
    echo "  score_management             成绩管理模块"
    echo "  monitoring                   监控模块"
    echo "  auditing                     审计模块"
    echo "  api_gateway                  API网关"
    echo
    print_info "${LIGHT} 示例:"
    echo "  ./start.sh --modules user_management,question_bank"
    echo "  ./start.sh --dev --modules api_gateway"
    echo "  chmod +x start.sh && ./start.sh  # 首次使用需要添加执行权限"
    echo
}

# 函数：检查Python环境
check_python() {
    print_info "${SEARCH} 检查Python环境..."
    
    # 尝试不同的Python命令
    for cmd in python3 python python3.8 python3.9 python3.10 python3.11 python3.12; do
        if command -v "$cmd" >/dev/null 2>&1; then
            PYTHON_CMD="$cmd"
            break
        fi
    done
    
    if ! command -v "$PYTHON_CMD" >/dev/null 2>&1; then
        print_error "未找到Python环境"
        print_error "请确保已安装Python 3.8或更高版本"
        print_info "Ubuntu/Debian: sudo apt-get install python3 python3-pip"
        print_info "CentOS/RHEL: sudo yum install python3 python3-pip"
        print_info "macOS: brew install python3"
        exit 1
    fi
    
    # 检查Python版本
    local python_version
    python_version=$("$PYTHON_CMD" --version 2>&1 | cut -d' ' -f2)
    print_success "Python版本: $python_version"
    
    # 检查版本是否满足要求 (>= 3.8)
    local major minor
    major=$(echo "$python_version" | cut -d'.' -f1)
    minor=$(echo "$python_version" | cut -d'.' -f2)
    
    if [ "$major" -lt 3 ] || ([ "$major" -eq 3 ] && [ "$minor" -lt 8 ]); then
        print_error "Python版本过低，需要3.8或更高版本"
        exit 1
    fi
}

# 函数：检查项目目录
check_project_directory() {
    if [ ! -f "$PROJECT_ROOT/start.py" ]; then
        print_error "请在项目根目录下运行此脚本"
        print_error "当前目录: $(pwd)"
        exit 1
    fi
}

# 函数：检查和激活虚拟环境
check_virtual_environment() {
    print_info "${SEARCH} 检查虚拟环境..."
    
    # 检查常见的虚拟环境路径
    local venv_paths=("venv" ".venv" "env" ".env")
    
    for venv_path in "${venv_paths[@]}"; do
        if [ -f "$PROJECT_ROOT/$venv_path/bin/activate" ]; then
            VENV_PATH="$PROJECT_ROOT/$venv_path"
            print_success "发现虚拟环境: $venv_path"
            
            # 激活虚拟环境
            # shellcheck source=/dev/null
            source "$VENV_PATH/bin/activate"
            print_success "虚拟环境已激活"
            
            # 更新Python命令路径
            PYTHON_CMD="$VENV_PATH/bin/python"
            return 0
        fi
    done
    
    print_warning "未发现虚拟环境，使用系统Python环境"
    print_warning "建议创建虚拟环境以避免依赖冲突:"
    print_info "  python3 -m venv venv"
    print_info "  source venv/bin/activate"
}

# 函数：检查依赖
check_dependencies() {
    print_info "${SEARCH} 检查系统依赖..."
    
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        print_success "发现依赖文件: requirements.txt"
        
        # 检查是否需要安装依赖
        if [ ! -f "$PROJECT_ROOT/.dependencies_installed" ]; then
            print_info "正在检查依赖安装状态..."
            
            # 可以在这里添加依赖检查逻辑
            # 例如：pip check 或检查关键包
        fi
    else
        print_warning "未发现requirements.txt文件"
    fi
}

# 函数：解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help|-h)
                SHOW_HELP=true
                shift
                ;;
            --dev|-d)
                DEV_MODE=true
                shift
                ;;
            --background|-b)
                BACKGROUND=true
                shift
                ;;
            --status|-s)
                STATUS_ONLY=true
                shift
                ;;
            --stop)
                STOP_ONLY=true
                shift
                ;;
            --restart|-r)
                RESTART=true
                shift
                ;;
            --modules|-m)
                if [[ -n "${2:-}" ]]; then
                    MODULES="$2"
                    shift 2
                else
                    print_error "--modules 参数需要指定模块名称"
                    exit 1
                fi
                ;;
            *)
                print_error "未知参数: $1"
                print_info "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# 函数：构建Python命令
build_python_command() {
    local cmd="$PYTHON_CMD start.py"
    
    if [ "$DEV_MODE" = true ]; then
        cmd="$cmd --dev"
    fi
    
    if [ "$BACKGROUND" = true ]; then
        cmd="$cmd --background"
    fi
    
    if [ "$STATUS_ONLY" = true ]; then
        cmd="$cmd --status"
    fi
    
    if [ "$STOP_ONLY" = true ]; then
        cmd="$cmd --stop"
    fi
    
    if [ "$RESTART" = true ]; then
        cmd="$cmd --restart"
    fi
    
    if [ -n "$MODULES" ]; then
        cmd="$cmd --modules $MODULES"
    fi
    
    echo "$cmd"
}

# 函数：设置环境变量
setup_environment() {
    export PYTHONPATH="$PROJECT_ROOT"
    export PYTHONIOENCODING="utf-8"
    
    # 设置语言环境
    export LANG="en_US.UTF-8"
    export LC_ALL="en_US.UTF-8"
}

# 函数：执行系统启动
execute_startup() {
    local python_cmd
    python_cmd=$(build_python_command)
    
    echo
    print_info "${ROCKET} 正在启动系统..."
    print_info "${NOTE} 执行命令: $python_cmd"
    echo
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 执行Python启动脚本
    if eval "$python_cmd"; then
        local exit_code=$?
        
        if [ "$STATUS_ONLY" = true ]; then
            echo
            print_success "状态查询完成"
        elif [ "$STOP_ONLY" = true ]; then
            echo
            print_success "系统已停止"
        elif [ "$RESTART" = true ]; then
            echo
            print_success "系统重启完成"
        elif [ "$BACKGROUND" = true ]; then
            echo
            print_success "系统已在后台启动"
            print_info "${LIGHT} 使用 './start.sh --status' 查看状态"
            print_info "${LIGHT} 使用 './start.sh --stop' 停止系统"
        else
            echo
            print_success "系统启动完成"
        fi
        
        return $exit_code
    else
        local exit_code=$?
        echo
        print_error "系统启动失败 (错误代码: $exit_code)"
        print_info "${LIGHT} 请检查错误信息并重试"
        print_info "${LIGHT} 如需帮助，请运行: ./start.sh --help"
        return $exit_code
    fi
}

# 函数：信号处理
handle_signal() {
    echo
    print_info "${STOP} 接收到停止信号，正在优雅关闭..."
    
    # 如果有子进程，发送TERM信号
    if [ -n "${CHILD_PID:-}" ]; then
        kill -TERM "$CHILD_PID" 2>/dev/null || true
        wait "$CHILD_PID" 2>/dev/null || true
    fi
    
    print_success "脚本已退出"
    exit 0
}

# 函数：检查脚本权限
check_script_permissions() {
    if [ ! -x "$0" ]; then
        print_warning "脚本没有执行权限"
        print_info "正在添加执行权限..."
        chmod +x "$0"
        print_success "执行权限已添加"
    fi
}

# 主函数
main() {
    # 注册信号处理器
    trap handle_signal SIGINT SIGTERM
    
    # 检查脚本权限
    check_script_permissions
    
    # 解析命令行参数
    parse_arguments "$@"
    
    # 显示帮助信息
    if [ "$SHOW_HELP" = true ]; then
        show_banner
        show_help
        exit 0
    fi
    
    # 显示横幅
    show_banner
    
    # 检查环境
    check_python
    check_project_directory
    check_virtual_environment
    check_dependencies
    
    # 设置环境变量
    setup_environment
    
    # 执行启动
    execute_startup
    local exit_code=$?
    
    # 如果不是后台模式且不是状态查询，等待用户输入
    if [ "$BACKGROUND" = false ] && [ "$STATUS_ONLY" = false ] && [ "$STOP_ONLY" = false ]; then
        echo
        read -p "按回车键退出..." -r
    fi
    
    exit $exit_code
}

# 执行主函数
main "$@"