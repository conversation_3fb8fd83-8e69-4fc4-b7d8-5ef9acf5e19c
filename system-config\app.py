# -*- coding: utf-8 -*-
"""
系统配置模块 - 核心服务
"""
from flask import Flask, jsonify, request
from flask_cors import CORS

# --- In-Memory Database for Configuration ---
db = {
    "config": {
        "PASSING_GRADE": 60,
        "SESSION_TIMEOUT_SECONDS": 7200,
        "MAX_LOGIN_ATTEMPTS": 5,
        "ALLOW_FILE_UPLOAD": True
    }
}

def create_app():
    """Application factory to create and configure the Flask app."""
    app = Flask(__name__)
    CORS(app)

    @app.route('/', methods=['GET'])
    def index():
        return jsonify({'service': 'System Config API', 'status': 'running'})

    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({'status': 'healthy', 'service': 'system-config'})

    @app.route('/api/v1/config', methods=['GET'])
    def get_all_config():
        """Returns the entire system configuration."""
        return jsonify(db['config'])

    @app.route('/api/v1/config/<string:key>', methods=['GET'])
    def get_config_value(key):
        """Returns the value of a specific configuration key."""
        if key not in db['config']:
            return jsonify({'error': f"Configuration key '{key}' not found"}), 404

        return jsonify({'key': key, 'value': db['config'][key]})

    @app.route('/api/v1/config/<string:key>', methods=['PUT'])
    def update_config_value(key):
        """Creates or updates a configuration value."""
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        if 'value' not in data:
            return jsonify({"error": "Missing 'value' in request body"}), 400

        value = data['value']
        db['config'][key] = value

        return jsonify({'key': key, 'value': value}), 200

    return app
