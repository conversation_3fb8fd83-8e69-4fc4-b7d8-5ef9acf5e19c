<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动刷新测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h2>自动刷新功能测试</h2>
                <div class="alert alert-info">
                    <strong>说明：</strong>此页面用于测试登录后自动刷新功能。请打开浏览器开发者工具查看控制台日志。
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5>测试控制</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary me-2" onclick="testAutoRefresh()">模拟登录后跳转</button>
                        <button class="btn btn-success me-2" onclick="manualRefresh()">手动刷新状态</button>
                        <button class="btn btn-warning" onclick="clearLogs()">清除日志</button>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>模拟模块状态</h5>
                    </div>
                    <div class="card-body">
                        <div class="row" id="modules-container">
                            <div class="col-md-4 mb-3" data-module-id="user_management">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>用户管理模块</h6>
                                        <div id="module-status-user_management">
                                            <span class="badge bg-warning">状态未知</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3" data-module-id="question_bank">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>题库管理模块</h6>
                                        <div id="module-status-question_bank">
                                            <span class="badge bg-warning">状态未知</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3" data-module-id="exam_management">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h6>考试管理模块</h6>
                                        <div id="module-status-exam_management">
                                            <span class="badge bg-warning">状态未知</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>控制台日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="console-logs" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                            <!-- 日志将显示在这里 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模块状态缓存
        let moduleStatusCache = {};
        let cacheTimeout = 30000; // 30秒缓存
        
        // 自动检查标志
        let autoCheckExecuted = false;
        
        // 日志记录函数
        function logToPage(message, type = 'info') {
            const logsContainer = document.getElementById('console-logs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }
        
        // 重写console.log以显示在页面上
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToPage(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToPage(args.join(' '), 'warning');
        };
        
        // 执行自动状态检查的函数
        function executeAutoCheck() {
            if (autoCheckExecuted) {
                console.log('自动检查已执行，跳过重复执行');
                return;
            }
            
            console.log('=== 开始执行自动状态检查 ===');
            console.log('当前时间:', new Date().toLocaleString());
            
            try {
                refreshAllModuleStatus(true);
                autoCheckExecuted = true;
                console.log('自动状态检查执行成功');
            } catch (error) {
                console.error('自动状态检查执行失败:', error);
            }
        }
        
        // 检查URL参数，如果是登录后跳转则立即执行自动刷新
        function checkAutoRefreshParam() {
            const urlParams = new URLSearchParams(window.location.search);
            const autoRefresh = urlParams.get('auto_refresh');
            
            if (autoRefresh === 'true') {
                console.log('=== 检测到登录后跳转，立即执行自动刷新 ===');
                // 清除URL参数，避免刷新页面时重复触发
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
                
                // 重置自动检查标志，确保登录后能够执行
                autoCheckExecuted = false;
                
                // 立即执行自动检查
                console.log('强制执行登录后自动检查');
                setTimeout(function() {
                    refreshAllModuleStatus(true);
                    autoCheckExecuted = true;
                }, 100);
            }
        }
        
        // 生成模块状态HTML
        function generateModuleStatusHTML(moduleId, module) {
            console.log(`generateModuleStatusHTML 被调用，模块ID: ${moduleId}, 模块数据:`, module);
            console.log(`模块状态值: '${module.status}', 类型: ${typeof module.status}`);
            
            if (module.status === 'running') {
                console.log(`模块 ${moduleId} 状态匹配 'running'`);
                return `<span class="badge bg-success">运行中</span>`;
            } else if (module.status === 'stopped') {
                console.log(`模块 ${moduleId} 状态匹配 'stopped'`);
                return `<span class="badge bg-secondary">已停止</span>`;
            } else {
                console.log(`模块 ${moduleId} 状态未匹配，进入默认分支，状态值: '${module.status}'`);
                return `<span class="badge bg-warning">状态未知</span>`;
            }
        }
        
        // 刷新所有模块状态
        function refreshAllModuleStatus(isAutoCheck = false) {
            console.log('=== refreshAllModuleStatus 函数被调用 ===');
            console.log('isAutoCheck:', isAutoCheck);
            
            // 清除缓存
            moduleStatusCache = {};
            console.log('模块状态缓存已清除');
            
            // 批量检查所有模块状态
            const moduleElements = document.querySelectorAll('[data-module-id]');
            console.log('找到的模块元素数量:', moduleElements.length);
            
            const moduleIds = Array.from(moduleElements).map(el => el.getAttribute('data-module-id'));
            console.log('模块ID列表:', moduleIds);
            
            if (moduleIds.length === 0) {
                console.warn('没有找到任何模块元素');
                return;
            }
            
            if (isAutoCheck) {
                console.log('执行自动检查模式');
            } else {
                console.log('执行手动检查模式');
            }
            
            // 显示加载状态
            moduleIds.forEach(moduleId => {
                const statusElement = document.getElementById(`module-status-${moduleId}`);
                if (statusElement) {
                    statusElement.innerHTML = `<span class="badge bg-info">检查中...</span>`;
                }
            });
            
            // 使用批量API接口获取所有模块状态
            fetch('/api/modules/status')
            .then(response => {
                console.log('API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('API响应数据:', data);
                if (data.success && data.modules) {
                    console.log('开始更新模块状态，模块数量:', Object.keys(data.modules).length);
                    // 更新所有模块状态
                    Object.keys(data.modules).forEach(moduleId => {
                        const module = data.modules[moduleId];
                        console.log(`处理模块 ${moduleId}:`, module);
                        console.log(`模块 ${moduleId} 状态:`, module.status);
                        
                        const statusElement = document.getElementById(`module-status-${moduleId}`);
                        console.log(`模块 ${moduleId} 状态元素:`, statusElement);
                        
                        if (statusElement) {
                            const newHTML = generateModuleStatusHTML(moduleId, module);
                            console.log(`模块 ${moduleId} 新HTML:`, newHTML);
                            statusElement.innerHTML = newHTML;
                            console.log(`模块 ${moduleId} 状态已更新`);
                        } else {
                            console.warn(`未找到模块 ${moduleId} 的状态元素`);
                        }
                        
                        // 更新缓存
                        moduleStatusCache[moduleId] = {
                            status: module.status,
                            port: module.port || 'unknown',
                            pid: module.pid || null,
                            timestamp: Date.now()
                        };
                    });
                    
                    console.log('模块状态更新完成');
                } else {
                    console.error('API返回错误:', data.message || '未知错误');
                }
            })
            .catch(error => {
                console.error('获取模块状态失败:', error);
                
                // 显示错误状态
                moduleIds.forEach(moduleId => {
                    const statusElement = document.getElementById(`module-status-${moduleId}`);
                    if (statusElement) {
                        statusElement.innerHTML = `<span class="badge bg-danger">检查失败</span>`;
                    }
                });
            });
        }
        
        // 测试函数
        function testAutoRefresh() {
            console.log('=== 模拟登录后跳转测试 ===');
            // 重置标志
            autoCheckExecuted = false;
            // 添加URL参数
            const newUrl = window.location.pathname + '?auto_refresh=true';
            window.history.pushState({}, document.title, newUrl);
            // 触发检查
            checkAutoRefreshParam();
        }
        
        function manualRefresh() {
            console.log('=== 手动刷新测试 ===');
            refreshAllModuleStatus(false);
        }
        
        function clearLogs() {
            document.getElementById('console-logs').innerHTML = '';
        }
        
        // 页面加载时的事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 页面DOMContentLoaded事件触发 ===');
            checkAutoRefreshParam();
            executeAutoCheck();
        });
        
        window.addEventListener('load', function() {
            console.log('=== 页面load事件触发 ===');
            executeAutoCheck();
        });
        
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('=== 页面可见性变化，页面变为可见 ===');
                executeAutoCheck();
            }
        });
        
        // 2秒后的强制定时检查
        setTimeout(function() {
            if (!autoCheckExecuted) {
                console.log('=== 强制定时检查触发 ===');
                executeAutoCheck();
            }
        }, 2000);
        
        console.log('=== 测试页面脚本加载完成 ===');
    </script>
</body>
</html>