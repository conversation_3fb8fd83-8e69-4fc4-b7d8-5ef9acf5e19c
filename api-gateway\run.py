#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API网关启动脚本

提供统一的API网关服务，包括：
- 请求路由和代理
- 健康检查和服务发现
- 负载均衡
- 监控和指标收集
- 安全认证和授权
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import APIGateway


def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('logs/gateway.log', encoding='utf-8')
        ]
    )


def ensure_directories():
    """确保必要的目录存在"""
    directories = ['logs', 'config', 'certs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)


def main():
    """主函数"""
    try:
        # 确保目录存在
        ensure_directories()
        
        # 设置日志
        setup_logging()
        logger = logging.getLogger(__name__)
        
        logger.info("正在启动API网关...")
        
        # 创建API网关实例
        gateway = APIGateway('config/gateway.yaml')
        
        # 从配置文件读取端口
        gateway_config = gateway.config.get('gateway', {})
        port = gateway_config.get('port', 5008)
        
        logger.info(f"API网关启动成功")
        logger.info(f"监听地址: http://0.0.0.0:{port}")
        logger.info(f"健康检查: http://localhost:{port}/api/health")
        logger.info(f"模块状态: http://localhost:{port}/api/health/modules")
        logger.info(f"监控指标: http://localhost:{port}/metrics")
        
        # 启动网关
        gateway.start()
        
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭API网关...")
    except Exception as e:
        logger.error(f"API网关启动失败: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()