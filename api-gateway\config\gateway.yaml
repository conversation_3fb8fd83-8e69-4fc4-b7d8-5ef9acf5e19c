# API网关配置文件
# 统一健康检查端点配置，确保设计与实现一致性

# 网关基础配置
gateway:
  host: "0.0.0.0"
  port: 8080
  debug: false
  
# 健康检查配置
health_check:
  interval: 30  # 检查间隔（秒）
  timeout: 10   # 超时时间（秒）
  retries: 3    # 重试次数
  
# 模块配置 - 统一使用 /api/health 端点
modules:
  user_management:
    host: "localhost"
    port: 5001
    prefix: "/api/v1/user"
    health_check: "/api/health"  # 统一健康检查端点
    timeout: 30
    retry: 3
    
  question_bank:
    host: "localhost"
    port: 5002
    prefix: "/api/v1/question"
    health_check: "/api/health"  # 统一健康检查端点
    timeout: 30
    retry: 3
    
  # exam_management:
  #   host: "localhost"
  #   port: 5003
  #   prefix: "/api/v1/exam"
  #   health_check: "/api/health"  # 统一健康检查端点
  #   timeout: 30
  #   retry: 3
  #   # 注释：此模块正在开发中，暂时不启用
    
  student_exam:
    host: "localhost"
    port: 5003
    prefix: "/api/v1/student-exam"
    health_check: "/api/health"  # 统一健康检查端点
    timeout: 30
    retry: 3
    
  scoring_management:
    host: "localhost"
    port: 5004
    prefix: "/api/v1/scoring"
    health_check: "/api/health"  # 统一健康检查端点
    timeout: 30
    retry: 3
    
  grade_query:
    host: "localhost"
    port: 5007
    prefix: "/api/v1/grades"
    health_check: "/api/health"  # 统一健康检查端点
    timeout: 30
    retry: 3
    
  exam_score_reporting_interface:
    host: localhost
    port: 5008
    prefix: "/api/v1/score-reporting"
    health_check: "/health"  # 修正健康检查端点路径
    timeout: 30
    retry: 3
    special_type: "internet_connection"  # 标记为唯一的互联网连接模块
    security_level: "high"  # 高安全级别
    
  # monitoring:
  #   host: "localhost"
  #   port: 5009
  #   prefix: "/api/v1/monitor"
  #   health_check: "/api/health"  # 统一健康检查端点
  #   timeout: 30
  #   retry: 3
  #   # 注释：此模块正在开发中，暂时不启用
    
  # auditing:
  #   host: "localhost"
  #   port: 5010
  #   prefix: "/api/v1/audit"
  #   health_check: "/api/health"  # 统一健康检查端点
  #   timeout: 30
  #   retry: 3
  #   # 注释：此模块正在开发中，暂时不启用
    
  file_project_management:
    host: "localhost"
    port: 5015
    prefix: "/api/v1/filemanager"
    health_check: "/api/health"  # 统一健康检查端点
    timeout: 30
    retry: 3

# 路由规则配置
routing:
  rules:
    - pattern: "/api/v1/user/**"
      target: "user_management"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    - pattern: "/api/v1/question/**"
      target: "question_bank"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    # 考试管理路由（模块开发中，暂时禁用）
    # - pattern: "/api/v1/exam/**"
    #   target: "exam_management"
    #   methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
    #   auth_required: true
      
    # 考生答题模块路由
    - pattern: "/api/v1/student-exam/**"
      target: "student_exam"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    # 评分管理模块路由
    - pattern: "/api/v1/scoring/**"
      target: "scoring_management"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    # 成绩查询模块路由
    - pattern: "/api/v1/grades/**"
      target: "grade_query"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    # 公开端点 - 必须放在通用规则之前
    - pattern: "/api/v1/user/login"
      target: "user_management"
      methods: ["POST"]
      auth_required: false
      
    # 成绩上报模块健康检查端点 - 公开访问
    - pattern: "/api/v1/score-reporting/health"
      target: "exam_score_reporting_interface"
      methods: ["GET"]
      auth_required: false
      
    # 考试成绩报告模块路由
    - pattern: "/api/v1/exam-score/**"
      target: "exam_score_reporting_interface"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      special_security: true  # 特殊安全策略
      
    # 监控管理路由（模块开发中，暂时禁用）
    # - pattern: "/api/v1/monitor/**"
    #   target: "monitoring"
    #   methods: ["GET", "POST"]
    #   auth_required: true
      
    # 审计日志路由（模块开发中，暂时禁用）
    # - pattern: "/api/v1/audit/**"
    #   target: "auditing"
    #   methods: ["GET", "POST"]
    #   auth_required: true
      
    # 成绩上报接口 - 政府要求的唯一互联网连接模块
    - pattern: "/api/v1/score-reporting/**"
      target: "exam_score_reporting_interface"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      special_security: true  # 特殊安全策略
      
    # 成绩上报模块页面路由
    - pattern: "/exam-score-reporting/**"
      target: "exam_score_reporting_interface"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      special_security: true
      
    # 文件项目管理模块公开端点 - 必须放在通用规则之前
    - pattern: "/api/v1/filemanager/health"
      target: "file_project_management"
      methods: ["GET"]
      auth_required: false
      
    - pattern: "/api/v1/filemanager/info"
      target: "file_project_management"
      methods: ["GET"]
      auth_required: false
      
    # 文件项目管理模块通用规则 - 集成认证模式，需要认证
    - pattern: "/api/v1/filemanager/**"
      target: "file_project_management"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    - pattern: "/api/v1/files/**"
      target: "file_project_management"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    - pattern: "/api/v1/projects/**"
      target: "file_project_management"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    - pattern: "/api/v1/tasks/**"
      target: "file_project_management"
      methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
      auth_required: true
      
    # 模块页面路由 - 通过路径匹配到对应模块
    - pattern: "/user-management/**"
      target: "user_management"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      
    - pattern: "/question-bank/**"
      target: "question_bank"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      
    - pattern: "/exam-management/**"
      target: "exam_management"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      
    # 考生答题模块页面路由
    - pattern: "/student-exam/**"
      target: "student_exam"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      
    # 评分管理模块页面路由
    - pattern: "/scoring-management/**"
      target: "scoring_management"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      
    # 成绩查询模块页面路由
    - pattern: "/grade-query/**"
      target: "grade_query"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      
    # 监控管理页面路由（模块开发中，暂时禁用）
    # - pattern: "/monitoring/**"
    #   target: "monitoring"
    #   methods: ["GET", "POST", "PUT", "DELETE"]
    #   auth_required: true
      
    # 审计日志页面路由（模块开发中，暂时禁用）
    # - pattern: "/audit/**"
    #   target: "auditing"
    #   methods: ["GET", "POST", "PUT", "DELETE"]
    #   auth_required: true
      
    # 文件项目管理模块页面路由
    - pattern: "/file-project-management/**"
      target: "file_project_management"
      methods: ["GET", "POST", "PUT", "DELETE"]
      auth_required: true
      
    - pattern: "/api/health"
      target: "gateway"
      methods: ["GET"]
      auth_required: false
      
    - pattern: "/metrics"
      target: "gateway"
      methods: ["GET"]
      auth_required: false

# 限流配置
rate_limiting:
  global:
    requests_per_minute: 1000
    requests_per_hour: 10000
    
  api_specific:
    "/api/v1/user/login":
      requests_per_minute: 10
      requests_per_hour: 100
      
    "/api/v1/question/import":
      requests_per_minute: 5
      requests_per_hour: 50
      
    # 成绩上报接口特殊限流 - 政府要求的唯一互联网连接模块
    "/api/v1/score-reporting/**":
      requests_per_minute: 20
      requests_per_hour: 200
      burst_limit: 5  # 突发限制
      
    "/exam-score-reporting/**":
      requests_per_minute: 30
      requests_per_hour: 300

# 安全配置
security:
  cors:
    enabled: true
    origins: ["*"]
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
    headers: ["Content-Type", "Authorization", "X-Request-ID"]
    
  headers:
    X-Frame-Options: "DENY"
    X-Content-Type-Options: "nosniff"
    X-XSS-Protection: "1; mode=block"
    Strict-Transport-Security: "max-age=31536000; includeSubDomains"

# 监控配置
monitoring:
  metrics:
    enabled: true
    endpoint: "/metrics"
    
  logging:
    level: "INFO"
    format: "json"
    file: "logs/gateway.log"
    max_size: "100MB"
    backup_count: 5
    
  alerts:
    enabled: true
    thresholds:
      error_rate: 0.05  # 5%
      response_time_p95: 2000  # 2秒
      module_down_duration: 300  # 5分钟

# 服务发现配置（为未来扩展预留）
service_discovery:
  type: "static"  # static, consul, etcd, kubernetes
  
  # 静态配置（当前使用）
  static:
    refresh_interval: 60
    
  # Consul配置（未来扩展）
  consul:
    host: "localhost"
    port: 8500
    datacenter: "dc1"
    
  # Kubernetes配置（未来扩展）
  kubernetes:
    namespace: "default"
    label_selector: "app=exam-system"

# 缓存配置（未来扩展）
caching:
  enabled: false
  type: "redis"  # redis, memory
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: ""
    
# 断路器配置（未来扩展）
circuit_breaker:
  enabled: false
  failure_threshold: 5
  recovery_timeout: 60
  half_open_max_calls: 3