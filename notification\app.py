# -*- coding: utf-8 -*-
"""
通知中心模块 - 核心服务
"""
from flask import Flask, jsonify, request
from flask_cors import CORS
from datetime import datetime, timezone

# --- In-Memory Database for Notifications ---
db = {
    "notifications": [
        {"id": 1, "user_id": "student", "message": "Your upcoming exam 'Final Exam' has been scheduled.", "status": "unread", "created_at": "2024-08-14T11:00:00Z"},
        {"id": 2, "user_id": "grader", "message": "You have new submissions to grade for 'Mid-term Exam'.", "status": "unread", "created_at": "2024-08-14T11:05:00Z"}
    ],
    "next_notification_id": 3
}

def create_app():
    """Application factory to create and configure the Flask app."""
    app = Flask(__name__)
    CORS(app)

    def get_utc_now_iso():
        return datetime.now(timezone.utc).isoformat()

    @app.route('/', methods=['GET'])
    def index():
        return jsonify({'service': 'Notification API', 'status': 'running'})

    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({'status': 'healthy', 'service': 'notification'})

    @app.route('/api/v1/notifications', methods=['POST'])
    def create_notification():
        """Creates a new notification for a user."""
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        required_fields = ['user_id', 'message']
        if not all(field in data for field in required_fields):
            return jsonify({"error": f"Missing required fields: {required_fields}"}), 400

        new_id = db['next_notification_id']

        notification = {
            'id': new_id,
            'user_id': data['user_id'],
            'message': data['message'],
            'status': 'unread',
            'created_at': get_utc_now_iso()
        }

        db['notifications'].append(notification)
        db['next_notification_id'] += 1

        return jsonify(notification), 201

    @app.route('/api/v1/users/<user_id>/notifications', methods=['GET'])
    def get_user_notifications(user_id):
        """Retrieves all notifications for a specific user."""
        user_notifications = [n for n in db['notifications'] if n['user_id'] == user_id]
        # Return most recent notifications first
        return jsonify(sorted(user_notifications, key=lambda x: x['id'], reverse=True))

    @app.route('/api/v1/notifications/<int:notification_id>/read', methods=['PATCH'])
    def mark_as_read(notification_id):
        """Marks a specific notification as read."""
        notification = next((n for n in db['notifications'] if n['id'] == notification_id), None)

        if not notification:
            return jsonify({"error": "Notification not found"}), 404

        notification['status'] = 'read'

        return jsonify(notification), 200

    return app
