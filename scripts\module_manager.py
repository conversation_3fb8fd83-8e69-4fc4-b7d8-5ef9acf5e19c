#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块管理器 - 实现并行启动、智能缓存和性能优化

功能:
1. 并行启动所有模块
2. 智能依赖管理
3. 性能监控和优化
4. 模块生命周期管理
5. 自动重启和故障恢复

作者: SOLO Coding
创建时间: 2025-08-15
"""

import os
import sys
import time
import yaml
import json
import logging
import asyncio
import subprocess
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, asdict
import psutil
import requests
from queue import Queue, Empty

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(project_root / 'logs' / 'module_manager.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ModuleStatus:
    """模块状态信息"""
    name: str
    status: str  # starting, running, stopped, error
    pid: Optional[int] = None
    port: Optional[int] = None
    start_time: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    error_message: Optional[str] = None
    restart_count: int = 0
    
class ModuleManager:
    """模块管理器 - 负责模块的并行启动、监控和管理"""
    
    def __init__(self, config_path: str = None):
        """初始化模块管理器"""
        self.project_root = project_root
        self.config_path = config_path or self.project_root / 'config' / 'modules.yaml'
        self.modules_config = self._load_modules_config()
        self.module_processes: Dict[str, subprocess.Popen] = {}
        self.module_status: Dict[str, ModuleStatus] = {}
        self.performance_cache = {}
        self.monitoring_active = False
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # 创建日志目录
        (self.project_root / 'logs').mkdir(exist_ok=True)
        
        logger.info("模块管理器初始化完成")
    
    def _load_modules_config(self) -> Dict:
        """加载模块配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"成功加载模块配置: {self.config_path}")
                return config
        except Exception as e:
            logger.error(f"加载模块配置失败: {e}")
            return {'modules': {}}
    
    def _get_module_startup_command(self, module_name: str, module_config: Dict) -> List[str]:
        """获取模块启动命令"""
        module_path = self.project_root / module_name
        
        # 检查是否有启动脚本
        startup_scripts = ['app.py', 'main.py', 'run.py', '__main__.py']
        
        for script in startup_scripts:
            script_path = module_path / script
            if script_path.exists():
                return [sys.executable, str(script_path)]
        
        # 如果没有找到启动脚本，尝试作为包运行
        if (module_path / '__init__.py').exists():
            return [sys.executable, '-m', module_name]
        
        raise FileNotFoundError(f"模块 {module_name} 没有找到启动脚本")
    
    def _start_single_module(self, module_name: str, module_config: Dict) -> bool:
        """启动单个模块"""
        try:
            if not module_config.get('enabled', True):
                logger.info(f"模块 {module_name} 已禁用，跳过启动")
                return False
            
            logger.info(f"正在启动模块: {module_name}")
            
            # 获取启动命令
            cmd = self._get_module_startup_command(module_name, module_config)
            
            # 设置工作目录
            cwd = self.project_root / module_name
            
            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONPATH'] = str(self.project_root)
            env['MODULE_NAME'] = module_name
            
            # 启动进程
            # 设置进程创建标志，避免创建新的终端窗口
            creation_flags = 0
            if os.name == 'nt':  # Windows系统
                creation_flags = subprocess.CREATE_NO_WINDOW
            
            process = subprocess.Popen(
                cmd,
                cwd=str(cwd),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                creationflags=creation_flags
            )
            
            # 记录进程信息
            self.module_processes[module_name] = process
            self.module_status[module_name] = ModuleStatus(
                name=module_name,
                status='starting',
                pid=process.pid,
                port=module_config.get('port'),
                start_time=datetime.now()
            )
            
            logger.info(f"模块 {module_name} 启动成功，PID: {process.pid}")
            return True
            
        except Exception as e:
            error_msg = f"启动模块 {module_name} 失败: {e}"
            logger.error(error_msg)
            self.module_status[module_name] = ModuleStatus(
                name=module_name,
                status='error',
                error_message=str(e)
            )
            return False
    
    def start_all_modules(self, parallel: bool = True) -> Dict[str, bool]:
        """启动所有模块"""
        logger.info("开始启动所有模块...")
        
        modules = self.modules_config.get('modules', {})
        results = {}
        
        if parallel:
            # 并行启动
            futures = {}
            for module_name, module_config in modules.items():
                future = self.executor.submit(self._start_single_module, module_name, module_config)
                futures[future] = module_name
            
            # 等待所有模块启动完成
            for future in as_completed(futures):
                module_name = futures[future]
                try:
                    result = future.result(timeout=30)  # 30秒超时
                    results[module_name] = result
                except Exception as e:
                    logger.error(f"模块 {module_name} 启动超时或异常: {e}")
                    results[module_name] = False
        else:
            # 顺序启动
            for module_name, module_config in modules.items():
                results[module_name] = self._start_single_module(module_name, module_config)
                time.sleep(2)  # 给每个模块一些启动时间
        
        # 等待模块完全启动
        logger.info("等待模块完全启动...")
        time.sleep(5)
        
        # 检查模块健康状态
        self._check_all_modules_health()
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"模块启动完成: {success_count}/{total_count} 个模块成功启动")
        return results
    
    def _check_module_health(self, module_name: str, module_config: Dict) -> bool:
        """检查单个模块健康状态"""
        try:
            port = module_config.get('port')
            if not port:
                return False
            
            # 尝试健康检查端点
            health_endpoint = module_config.get('health_check', '/health')
            url = f"http://127.0.0.1:{port}{health_endpoint}"
            
            response = requests.get(url, timeout=5)
            is_healthy = response.status_code == 200
            
            # 更新模块状态
            if module_name in self.module_status:
                self.module_status[module_name].status = 'running' if is_healthy else 'error'
                self.module_status[module_name].last_health_check = datetime.now()
                
                # 更新性能指标
                if is_healthy and self.module_status[module_name].pid:
                    self._update_performance_metrics(module_name)
            
            return is_healthy
            
        except Exception as e:
            logger.warning(f"模块 {module_name} 健康检查失败: {e}")
            if module_name in self.module_status:
                self.module_status[module_name].status = 'error'
                self.module_status[module_name].error_message = str(e)
            return False
    
    def _check_all_modules_health(self):
        """检查所有模块健康状态"""
        modules = self.modules_config.get('modules', {})
        healthy_count = 0
        
        for module_name, module_config in modules.items():
            if module_config.get('enabled', True):
                is_healthy = self._check_module_health(module_name, module_config)
                if is_healthy:
                    healthy_count += 1
        
        total_enabled = sum(1 for config in modules.values() if config.get('enabled', True))
        logger.info(f"健康检查完成: {healthy_count}/{total_enabled} 个模块健康")
    
    def _update_performance_metrics(self, module_name: str):
        """更新模块性能指标"""
        try:
            status = self.module_status.get(module_name)
            if not status or not status.pid:
                return
            
            process = psutil.Process(status.pid)
            status.cpu_usage = process.cpu_percent()
            status.memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            # 进程可能已经结束
            if module_name in self.module_status:
                self.module_status[module_name].status = 'stopped'
    
    def start_monitoring(self):
        """启动性能监控"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        def monitor_loop():
            while self.monitoring_active:
                try:
                    self._check_all_modules_health()
                    time.sleep(30)  # 每30秒检查一次
                except Exception as e:
                    logger.error(f"监控循环异常: {e}")
                    time.sleep(10)
        
        monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        monitor_thread.start()
        logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止性能监控"""
        self.monitoring_active = False
        logger.info("性能监控已停止")
    
    def get_status_report(self) -> Dict:
        """获取状态报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'modules': {},
            'summary': {
                'total': len(self.module_status),
                'running': 0,
                'stopped': 0,
                'error': 0
            }
        }
        
        for module_name, status in self.module_status.items():
            report['modules'][module_name] = asdict(status)
            
            # 更新汇总信息
            if status.status == 'running':
                report['summary']['running'] += 1
            elif status.status in ['stopped', 'starting']:
                report['summary']['stopped'] += 1
            else:
                report['summary']['error'] += 1
        
        return report
    
    def stop_all_modules(self):
        """停止所有模块"""
        logger.info("正在停止所有模块...")
        
        for module_name, process in self.module_processes.items():
            try:
                if process.poll() is None:  # 进程仍在运行
                    logger.info(f"正在停止模块: {module_name}")
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"模块 {module_name} 未能正常结束，强制终止")
                        process.kill()
                    
                    # 更新状态
                    if module_name in self.module_status:
                        self.module_status[module_name].status = 'stopped'
                        
            except Exception as e:
                logger.error(f"停止模块 {module_name} 时发生错误: {e}")
        
        self.stop_monitoring()
        self.executor.shutdown(wait=True)
        logger.info("所有模块已停止")
    
    def restart_module(self, module_name: str) -> bool:
        """重启指定模块"""
        logger.info(f"正在重启模块: {module_name}")
        
        # 停止模块
        if module_name in self.module_processes:
            process = self.module_processes[module_name]
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        # 重新启动
        module_config = self.modules_config.get('modules', {}).get(module_name, {})
        if module_config:
            success = self._start_single_module(module_name, module_config)
            if success and module_name in self.module_status:
                self.module_status[module_name].restart_count += 1
            return success
        
        return False

def main():
    """主函数 - 演示模块管理器功能"""
    print("="*60)
    print("模块管理器 - 并行启动和性能优化")
    print("="*60)
    
    manager = ModuleManager()
    
    try:
        # 启动所有模块
        print("\n1. 启动所有模块...")
        results = manager.start_all_modules(parallel=True)
        
        # 显示启动结果
        print("\n启动结果:")
        for module_name, success in results.items():
            status = "✓" if success else "✗"
            print(f"  {status} {module_name}")
        
        # 启动监控
        print("\n2. 启动性能监控...")
        manager.start_monitoring()
        
        # 等待一段时间让模块完全启动
        print("\n3. 等待模块完全启动...")
        time.sleep(10)
        
        # 显示状态报告
        print("\n4. 状态报告:")
        report = manager.get_status_report()
        print(f"总模块数: {report['summary']['total']}")
        print(f"运行中: {report['summary']['running']}")
        print(f"已停止: {report['summary']['stopped']}")
        print(f"错误: {report['summary']['error']}")
        
        print("\n详细状态:")
        for module_name, status in report['modules'].items():
            print(f"  {module_name}: {status['status']} (PID: {status['pid']})")
        
        # 保持运行一段时间进行监控
        print("\n5. 监控运行中... (按 Ctrl+C 停止)")
        try:
            while True:
                time.sleep(30)
                report = manager.get_status_report()
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 运行中: {report['summary']['running']}/{report['summary']['total']}")
        except KeyboardInterrupt:
            print("\n收到停止信号...")
        
    except Exception as e:
        logger.error(f"运行过程中发生错误: {e}")
    
    finally:
        # 清理资源
        print("\n6. 清理资源...")
        manager.stop_all_modules()
        print("模块管理器已退出")

if __name__ == '__main__':
    main()