<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API网关管理控制台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin: 20px auto;
            padding: 30px;
            max-width: 1200px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .header h1 {
            color: #2c3e50;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .header p {
            color: #6c757d;
            font-size: 1.1em;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            font-weight: 600;
        }
        .status-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 600;
        }
        .status-running {
            background-color: #d4edda;
            color: #155724;
        }
        .status-stopped {
            background-color: #f8d7da;
            color: #721c24;
        }
        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
            transition: all 0.3s ease;
        }
        .btn-outline-primary:hover {
            background-color: #667eea;
            border-color: #667eea;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
        }
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        .metric-number {
            font-size: 2.5em;
            font-weight: 700;
            color: #667eea;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .route-item {
            padding: 10px;
            border-left: 4px solid #667eea;
            margin-bottom: 10px;
            background-color: #f8f9fa;
        }
        .module-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="back-button">
        <button class="btn btn-outline-primary" onclick="window.location.href='/dashboard'">
            <i class="fas fa-arrow-left"></i> 返回主控台
        </button>
    </div>

    <div class="container-fluid">
        <div class="main-container">
            <div class="header">
                <h1><i class="fas fa-network-wired"></i> API网关管理控制台</h1>
                <p>统一路由管理 | 模块状态监控 | 系统性能分析</p>
            </div>

            <!-- 系统概览 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="metric-number" id="total-routes">0</div>
                        <div class="metric-label">总路由数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="metric-number" id="active-modules">0</div>
                        <div class="metric-label">活跃模块</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="metric-number" id="total-requests">0</div>
                        <div class="metric-label">总请求数</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="metric-number" id="avg-response-time">0ms</div>
                        <div class="metric-label">平均响应时间</div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- 模块状态 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-server"></i> 模块状态监控
                        </div>
                        <div class="card-body">
                            <div id="modules-status">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载模块状态...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 路由信息 -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-route"></i> 路由配置
                        </div>
                        <div class="card-body">
                            <div id="routes-info">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <p class="mt-2">正在加载路由信息...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统健康检查 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <i class="fas fa-heartbeat"></i> 系统健康检查
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <button class="btn btn-primary w-100" onclick="refreshData()">
                                        <i class="fas fa-sync-alt"></i> 刷新数据
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-info w-100" onclick="viewLogs()">
                                        <i class="fas fa-file-alt"></i> 查看日志
                                    </button>
                                </div>
                                <div class="col-md-4">
                                    <button class="btn btn-success w-100" onclick="exportConfig()">
                                        <i class="fas fa-download"></i> 导出配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化数据
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            // 每30秒自动刷新数据
            setInterval(loadDashboardData, 30000);
        });

        async function loadDashboardData() {
            try {
                // 加载模块状态
                await loadModulesStatus();
                // 加载路由信息
                await loadRoutesInfo();
                // 加载统计数据
                await loadStatistics();
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        async function loadModulesStatus() {
            try {
                const response = await fetch('/api/modules/status');
                const data = await response.json();
                
                const container = document.getElementById('modules-status');
                if (data.modules) {
                    let html = '';
                    let activeCount = 0;
                    
                    for (const [moduleId, moduleInfo] of Object.entries(data.modules)) {
                        const isActive = moduleInfo.status === 'running';
                        if (isActive) activeCount++;
                        
                        html += `
                            <div class="module-item">
                                <div>
                                    <strong>${moduleInfo.name || moduleId}</strong>
                                    <br>
                                    <small class="text-muted">${moduleInfo.description || '无描述'}</small>
                                </div>
                                <span class="status-badge ${isActive ? 'status-running' : 'status-stopped'}">
                                    ${isActive ? '运行中' : '已停止'}
                                </span>
                            </div>
                        `;
                    }
                    
                    container.innerHTML = html;
                    document.getElementById('active-modules').textContent = activeCount;
                } else {
                    container.innerHTML = '<p class="text-muted">暂无模块数据</p>';
                }
            } catch (error) {
                document.getElementById('modules-status').innerHTML = '<p class="text-danger">加载模块状态失败</p>';
            }
        }

        async function loadRoutesInfo() {
            try {
                const response = await fetch('/api/routes');
                const data = await response.json();
                
                const container = document.getElementById('routes-info');
                if (data.routes) {
                    let html = '';
                    let routeCount = 0;
                    
                    for (const route of data.routes.slice(0, 10)) { // 只显示前10个路由
                        routeCount++;
                        html += `
                            <div class="route-item">
                                <strong>${route.pattern || route.path}</strong>
                                <br>
                                <small class="text-muted">
                                    目标: ${route.target || '未知'} | 
                                    方法: ${(route.methods || ['GET']).join(', ')}
                                </small>
                            </div>
                        `;
                    }
                    
                    if (data.routes.length > 10) {
                        html += `<p class="text-muted mt-2">还有 ${data.routes.length - 10} 个路由...</p>`;
                    }
                    
                    container.innerHTML = html;
                    document.getElementById('total-routes').textContent = data.routes.length;
                } else {
                    container.innerHTML = '<p class="text-muted">暂无路由数据</p>';
                }
            } catch (error) {
                document.getElementById('routes-info').innerHTML = '<p class="text-danger">加载路由信息失败</p>';
            }
        }

        async function loadStatistics() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.total_requests !== undefined) {
                    document.getElementById('total-requests').textContent = data.total_requests;
                }
                if (data.avg_response_time !== undefined) {
                    document.getElementById('avg-response-time').textContent = data.avg_response_time + 'ms';
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
            }
        }

        function refreshData() {
            loadDashboardData();
            // 显示刷新提示
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }

        function viewLogs() {
            window.open('/api/logs', '_blank');
        }

        function exportConfig() {
            window.open('/api/config/export', '_blank');
        }
    </script>
</body>
</html>