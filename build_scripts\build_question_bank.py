# -*- coding: utf-8 -*-
"""
题库管理模块 PyInstaller 构建脚本

功能说明：
- 使用 PyInstaller 将题库管理模块打包为独立的可执行文件
- 包含所有必要的依赖包、配置文件、模板文件和静态资源
- 支持 Windows 环境下的独立运行
- 包含 Excel 处理、JSON 导入导出等功能

使用方法：
1. 确保已安装 PyInstaller: pip install pyinstaller
2. 在项目根目录运行: python build_scripts/build_question_bank.py
3. 生成的可执行文件位于 dist/question_bank/ 目录

作者: 系统构建脚本
日期: 2025-01-17
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
QUESTION_BANK_DIR = PROJECT_ROOT / "question_bank"
BUILD_DIR = PROJECT_ROOT / "build"
DIST_DIR = PROJECT_ROOT / "dist"

def safe_print(message):
    """
    安全打印函数，处理 Windows 控制台编码问题
    
    参数:
        message (str): 要打印的消息
    """
    try:
        print(message)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_message = message.encode('ascii', 'ignore').decode('ascii')
        print(safe_message)

def check_dependencies():
    """
    检查构建依赖是否满足
    
    返回:
        bool: 依赖检查是否通过
    """
    safe_print("[*] 检查构建依赖...")
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        safe_print(f"[+] PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        safe_print("[!] 错误: 未安装 PyInstaller")
        safe_print("[!] 请运行: pip install pyinstaller")
        return False
    
    # 检查题库管理模块目录
    if not QUESTION_BANK_DIR.exists():
        safe_print(f"[!] 错误: 题库管理模块目录不存在: {QUESTION_BANK_DIR}")
        return False
    
    # 检查主入口文件
    main_file = QUESTION_BANK_DIR / "run.py"
    if not main_file.exists():
        safe_print(f"[!] 错误: 主入口文件不存在: {main_file}")
        return False
    
    safe_print("[+] 依赖检查通过")
    return True

def clean_build_dirs():
    """
    清理之前的构建目录
    """
    safe_print("[*] 清理构建目录...")
    
    # 清理 build 目录
    if BUILD_DIR.exists():
        shutil.rmtree(BUILD_DIR)
        safe_print("[+] 已清理 build 目录")
    
    # 清理 dist/question_bank 目录
    question_bank_dist = DIST_DIR / "question_bank"
    if question_bank_dist.exists():
        shutil.rmtree(question_bank_dist)
        safe_print("[+] 已清理 dist/question_bank 目录")

def build_executable():
    """
    使用 PyInstaller 构建可执行文件
    
    返回:
        bool: 构建是否成功
    """
    safe_print("[*] 开始构建题库管理模块可执行文件...")
    
    # 构建命令参数
    cmd = [
        "pyinstaller",
        "--name=question_bank",  # 可执行文件名称
        "--onedir",  # 生成目录形式的分发包
        "--windowed",  # Windows下不显示控制台窗口
        "--noconfirm",  # 覆盖输出目录而不询问
        "--clean",  # 清理临时文件
        
        # 添加数据文件
        f"--add-data={QUESTION_BANK_DIR / 'templates'};templates",
        f"--add-data={QUESTION_BANK_DIR / 'requirements.txt'};.",
        f"--add-data={QUESTION_BANK_DIR / 'standard_field_config.json'};.",
        f"--add-data={QUESTION_BANK_DIR / 'test_3row_header.xlsx'};.",
        f"--add-data={QUESTION_BANK_DIR / 'test_structure.xlsx'};.",
        
        # 添加隐藏导入
        "--hidden-import=flask",
        "--hidden-import=flask_sqlalchemy",
        "--hidden-import=flask_wtf",
        "--hidden-import=wtforms",
        "--hidden-import=werkzeug",
        "--hidden-import=sqlalchemy",
        "--hidden-import=jinja2",
        "--hidden-import=markupsafe",
        "--hidden-import=itsdangerous",
        "--hidden-import=click",
        "--hidden-import=blinker",
        "--hidden-import=openpyxl",
        "--hidden-import=xlrd",
        "--hidden-import=xlwt",
        "--hidden-import=pandas",
        "--hidden-import=numpy",
        "--hidden-import=json",
        "--hidden-import=csv",
        "--hidden-import=datetime",
        "--hidden-import=uuid",
        "--hidden-import=hashlib",
        "--hidden-import=base64",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=scipy",
        "--exclude-module=IPython",
        "--exclude-module=jupyter",
        
        # 主入口文件
        str(QUESTION_BANK_DIR / "run.py")
    ]
    
    try:
        # 执行构建命令
        safe_print(f"[*] 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=True, text=True)
        
        if result.returncode == 0:
            safe_print("[+] PyInstaller 构建成功")
            return True
        else:
            safe_print("[!] PyInstaller 构建失败")
            safe_print(f"[!] 错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        safe_print(f"[!] 构建过程中发生异常: {e}")
        return False

def copy_additional_files():
    """
    复制额外的配置文件和资源
    """
    safe_print("[*] 复制额外文件...")
    
    dist_dir = DIST_DIR / "question_bank"
    if not dist_dir.exists():
        safe_print("[!] 错误: 分发目录不存在")
        return False
    
    try:
        # 复制配置文件和文档
        config_files = [
            "README.md",
            "README_CN.md",
            "PROJECT_REQUIREMENTS.md",
            "使用说明.txt",
            "install.bat"
        ]
        
        for config_file in config_files:
            src_file = QUESTION_BANK_DIR / config_file
            if src_file.exists():
                dst_file = dist_dir / config_file
                shutil.copy2(src_file, dst_file)
                safe_print(f"[+] 已复制: {config_file}")
        
        # 复制Python工具脚本
        tool_scripts = [
            "excel_exporter.py",
            "excel_importer.py",
            "json_importer.py",
            "paper_generator.py",
            "field_standardization.py",
            "bank_name_mapping.py",
            "create_template.py",
            "utils.py",
            "models.py"
        ]
        
        tools_dir = dist_dir / "tools"
        tools_dir.mkdir(exist_ok=True)
        
        for tool_script in tool_scripts:
            src_file = QUESTION_BANK_DIR / tool_script
            if src_file.exists():
                dst_file = tools_dir / tool_script
                shutil.copy2(src_file, dst_file)
                safe_print(f"[+] 已复制工具脚本: {tool_script}")
        
        # 创建启动脚本
        start_script = dist_dir / "start_question_bank.bat"
        with open(start_script, 'w', encoding='utf-8') as f:
            f.write("@echo off\n")
            f.write("echo 启动题库管理模块...\n")
            f.write("question_bank.exe\n")
            f.write("pause\n")
        
        safe_print("[+] 已创建启动脚本: start_question_bank.bat")
        
        # 创建工具使用说明
        tools_readme = tools_dir / "README.txt"
        with open(tools_readme, 'w', encoding='utf-8') as f:
            f.write("题库管理工具脚本说明\n")
            f.write("===================\n\n")
            f.write("excel_exporter.py - Excel导出工具\n")
            f.write("excel_importer.py - Excel导入工具\n")
            f.write("json_importer.py - JSON导入工具\n")
            f.write("paper_generator.py - 试卷生成工具\n")
            f.write("field_standardization.py - 字段标准化工具\n")
            f.write("bank_name_mapping.py - 题库名称映射工具\n")
            f.write("create_template.py - 模板创建工具\n")
            f.write("utils.py - 通用工具函数\n")
            f.write("models.py - 数据模型定义\n\n")
            f.write("注意: 这些脚本需要Python环境才能运行\n")
        
        return True
        
    except Exception as e:
        safe_print(f"[!] 复制文件时发生错误: {e}")
        return False

def verify_build():
    """
    验证构建结果
    
    返回:
        bool: 验证是否通过
    """
    safe_print("[*] 验证构建结果...")
    
    dist_dir = DIST_DIR / "question_bank"
    exe_file = dist_dir / "question_bank.exe"
    
    if not exe_file.exists():
        safe_print("[!] 错误: 可执行文件不存在")
        return False
    
    # 检查文件大小
    file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
    safe_print(f"[+] 可执行文件大小: {file_size:.2f} MB")
    
    # 检查必要的目录和文件
    required_items = [
        "templates",
        "tools",
        "_internal",
        "standard_field_config.json"
    ]
    
    for item in required_items:
        item_path = dist_dir / item
        if item_path.exists():
            safe_print(f"[+] 已包含: {item}")
        else:
            safe_print(f"[!] 缺失: {item}")
            return False
    
    safe_print("[+] 构建验证通过")
    return True

def main():
    """
    主函数：执行完整的构建流程
    
    返回:
        int: 退出代码（0表示成功，1表示失败）
    """
    safe_print("="*60)
    safe_print("题库管理模块 PyInstaller 构建脚本")
    safe_print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建可执行文件
    if not build_executable():
        return 1
    
    # 复制额外文件
    if not copy_additional_files():
        return 1
    
    # 验证构建结果
    if not verify_build():
        return 1
    
    safe_print("="*60)
    safe_print("[+] 题库管理模块构建完成!")
    safe_print(f"[+] 输出目录: {DIST_DIR / 'question_bank'}")
    safe_print(f"[+] 可执行文件: {DIST_DIR / 'question_bank' / 'question_bank.exe'}")
    safe_print("[+] 使用 start_question_bank.bat 启动应用")
    safe_print("[+] 工具脚本位于 tools/ 目录")
    safe_print("="*60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())