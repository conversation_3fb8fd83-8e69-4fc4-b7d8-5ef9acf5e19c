# -*- coding: utf-8 -*-
"""
实操任务管理模块 - 启动脚本
"""

from app import create_app

# Create the Flask app instance using the factory
app = create_app()

if __name__ == '__main__':
    # The port is configured to 5013 as specified in main_launcher.py
    port = 5013

    print("--- Practical Task Management Service ---")
    print(f"Starting server at: http://0.0.0.0:{port}")

    # Using debug=True for development provides helpful logs and auto-reloading.
    # In a production environment, this should be False and a proper WSGI server
    # like Gunicorn or uWSGI should be used.
    app.run(host='0.0.0.0', port=port, debug=True)
