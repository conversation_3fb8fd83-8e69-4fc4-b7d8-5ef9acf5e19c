# -*- coding: utf-8 -*-
"""
实操任务管理模块 - 数据访问层 (DAO)
提供所有数据表的CRUD操作接口
"""

from typing import List, Dict, Optional, Tuple
from datetime import datetime
import json
import logging
from models import db_manager, safe_print

logger = logging.getLogger(__name__)

class TaskCategoryDAO:
    """
    任务分类数据访问对象
    """
    
    @staticmethod
    def create(name: str, description: str, software_type: str, difficulty_level: int) -> int:
        """
        创建任务分类
        
        Args:
            name: 分类名称
            description: 分类描述
            software_type: 软件类型
            difficulty_level: 难度等级(1-5)
            
        Returns:
            int: 新创建分类的ID
        """
        query = """
            INSERT INTO task_categories (name, description, software_type, difficulty_level)
            VALUES (?, ?, ?, ?)
        """
        db_manager.execute_update(query, (name, description, software_type, difficulty_level))
        return db_manager.get_last_insert_id()
    
    @staticmethod
    def get_all() -> List[Dict]:
        """
        获取所有任务分类
        
        Returns:
            List[Dict]: 分类列表
        """
        query = "SELECT * FROM task_categories ORDER BY software_type, difficulty_level"
        return db_manager.execute_query(query)
    
    @staticmethod
    def get_by_id(category_id: int) -> Optional[Dict]:
        """
        根据ID获取分类
        
        Args:
            category_id: 分类ID
            
        Returns:
            Optional[Dict]: 分类信息
        """
        query = "SELECT * FROM task_categories WHERE id = ?"
        results = db_manager.execute_query(query, (category_id,))
        return results[0] if results else None
    
    @staticmethod
    def get_by_software_type(software_type: str) -> List[Dict]:
        """
        根据软件类型获取分类
        
        Args:
            software_type: 软件类型
            
        Returns:
            List[Dict]: 分类列表
        """
        query = "SELECT * FROM task_categories WHERE software_type = ? ORDER BY difficulty_level"
        return db_manager.execute_query(query, (software_type,))
    
    @staticmethod
    def update(category_id: int, **kwargs) -> bool:
        """
        更新分类信息
        
        Args:
            category_id: 分类ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        if not kwargs:
            return False
        
        set_clause = ", ".join([f"{key} = ?" for key in kwargs.keys()])
        query = f"UPDATE task_categories SET {set_clause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        
        params = list(kwargs.values()) + [category_id]
        rows_affected = db_manager.execute_update(query, tuple(params))
        return rows_affected > 0
    
    @staticmethod
    def delete(category_id: int) -> bool:
        """
        删除分类
        
        Args:
            category_id: 分类ID
            
        Returns:
            bool: 删除是否成功
        """
        query = "DELETE FROM task_categories WHERE id = ?"
        rows_affected = db_manager.execute_update(query, (category_id,))
        return rows_affected > 0

class PracticalTaskDAO:
    """
    实操任务数据访问对象
    """
    
    @staticmethod
    def create(name: str, description: str, category_id: int, software_version: str,
               difficulty_level: int, estimated_duration: int, max_score: int,
               requirements: Dict, evaluation_criteria: Dict, created_by: int = None) -> int:
        """
        创建实操任务
        
        Args:
            name: 任务名称
            description: 任务描述
            category_id: 分类ID
            software_version: 软件版本
            difficulty_level: 难度等级
            estimated_duration: 预估时长(分钟)
            max_score: 最高分数
            requirements: 任务要求(字典)
            evaluation_criteria: 评分标准(字典)
            created_by: 创建者ID
            
        Returns:
            int: 新创建任务的ID
        """
        query = """
            INSERT INTO practical_tasks 
            (name, description, category_id, software_version, difficulty_level, 
             estimated_duration, max_score, requirements, evaluation_criteria, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (name, description, category_id, software_version, difficulty_level,
                 estimated_duration, max_score, json.dumps(requirements, ensure_ascii=False),
                 json.dumps(evaluation_criteria, ensure_ascii=False), created_by)
        
        db_manager.execute_update(query, params)
        return db_manager.get_last_insert_id()
    
    @staticmethod
    def get_all(status: str = None) -> List[Dict]:
        """
        获取所有任务
        
        Args:
            status: 任务状态过滤
            
        Returns:
            List[Dict]: 任务列表
        """
        if status:
            query = """
                SELECT t.*, c.name as category_name, c.software_type
                FROM practical_tasks t
                LEFT JOIN task_categories c ON t.category_id = c.id
                WHERE t.status = ?
                ORDER BY t.created_at DESC
            """
            results = db_manager.execute_query(query, (status,))
        else:
            query = """
                SELECT t.*, c.name as category_name, c.software_type
                FROM practical_tasks t
                LEFT JOIN task_categories c ON t.category_id = c.id
                ORDER BY t.created_at DESC
            """
            results = db_manager.execute_query(query)
        
        # 解析JSON字段
        for task in results:
            if task['requirements']:
                task['requirements'] = json.loads(task['requirements'])
            if task['evaluation_criteria']:
                task['evaluation_criteria'] = json.loads(task['evaluation_criteria'])
        
        return results
    
    @staticmethod
    def get_by_id(task_id: int) -> Optional[Dict]:
        """
        根据ID获取任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[Dict]: 任务信息
        """
        query = """
            SELECT t.*, c.name as category_name, c.software_type
            FROM practical_tasks t
            LEFT JOIN task_categories c ON t.category_id = c.id
            WHERE t.id = ?
        """
        results = db_manager.execute_query(query, (task_id,))
        
        if results:
            task = results[0]
            # 解析JSON字段
            if task['requirements']:
                task['requirements'] = json.loads(task['requirements'])
            if task['evaluation_criteria']:
                task['evaluation_criteria'] = json.loads(task['evaluation_criteria'])
            return task
        
        return None
    
    @staticmethod
    def get_by_category(category_id: int) -> List[Dict]:
        """
        根据分类获取任务
        
        Args:
            category_id: 分类ID
            
        Returns:
            List[Dict]: 任务列表
        """
        query = """
            SELECT t.*, c.name as category_name, c.software_type
            FROM practical_tasks t
            LEFT JOIN task_categories c ON t.category_id = c.id
            WHERE t.category_id = ? AND t.status = 'active'
            ORDER BY t.difficulty_level, t.created_at DESC
        """
        results = db_manager.execute_query(query, (category_id,))
        
        # 解析JSON字段
        for task in results:
            if task['requirements']:
                task['requirements'] = json.loads(task['requirements'])
            if task['evaluation_criteria']:
                task['evaluation_criteria'] = json.loads(task['evaluation_criteria'])
        
        return results
    
    @staticmethod
    def update(task_id: int, **kwargs) -> bool:
        """
        更新任务信息
        
        Args:
            task_id: 任务ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        if not kwargs:
            return False
        
        # 处理JSON字段
        if 'requirements' in kwargs and isinstance(kwargs['requirements'], dict):
            kwargs['requirements'] = json.dumps(kwargs['requirements'], ensure_ascii=False)
        if 'evaluation_criteria' in kwargs and isinstance(kwargs['evaluation_criteria'], dict):
            kwargs['evaluation_criteria'] = json.dumps(kwargs['evaluation_criteria'], ensure_ascii=False)
        
        set_clause = ", ".join([f"{key} = ?" for key in kwargs.keys()])
        query = f"UPDATE practical_tasks SET {set_clause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        
        params = list(kwargs.values()) + [task_id]
        rows_affected = db_manager.execute_update(query, tuple(params))
        return rows_affected > 0
    
    @staticmethod
    def delete(task_id: int) -> bool:
        """
        删除任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 删除是否成功
        """
        query = "DELETE FROM practical_tasks WHERE id = ?"
        rows_affected = db_manager.execute_update(query, (task_id,))
        return rows_affected > 0
    
    @staticmethod
    def get_statistics() -> Dict:
        """
        获取任务统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = {}
        
        # 总任务数
        query = "SELECT COUNT(*) as total FROM practical_tasks"
        result = db_manager.execute_query(query)
        stats['total_tasks'] = result[0]['total'] if result else 0
        
        # 按状态统计
        query = "SELECT status, COUNT(*) as count FROM practical_tasks GROUP BY status"
        status_stats = db_manager.execute_query(query)
        stats['by_status'] = {item['status']: item['count'] for item in status_stats}
        
        # 按分类统计
        query = """
            SELECT c.name, COUNT(t.id) as count
            FROM task_categories c
            LEFT JOIN practical_tasks t ON c.id = t.category_id
            GROUP BY c.id, c.name
        """
        category_stats = db_manager.execute_query(query)
        stats['by_category'] = {item['name']: item['count'] for item in category_stats}
        
        # 按难度统计
        query = "SELECT difficulty_level, COUNT(*) as count FROM practical_tasks GROUP BY difficulty_level"
        difficulty_stats = db_manager.execute_query(query)
        stats['by_difficulty'] = {item['difficulty_level']: item['count'] for item in difficulty_stats}
        
        return stats

class TaskMaterialDAO:
    """
    任务素材数据访问对象
    """
    
    @staticmethod
    def create(task_id: int, file_name: str, file_path: str, file_size: int,
               file_type: str, version: str = "1.0", description: str = "",
               is_required: bool = True, uploaded_by: int = None) -> int:
        """
        创建任务素材记录
        
        Args:
            task_id: 任务ID
            file_name: 文件名
            file_path: 文件路径
            file_size: 文件大小
            file_type: 文件类型
            version: 版本号
            description: 描述
            is_required: 是否必需
            uploaded_by: 上传者ID
            
        Returns:
            int: 新创建素材的ID
        """
        query = """
            INSERT INTO task_materials 
            (task_id, file_name, file_path, file_size, file_type, version, 
             description, is_required, uploaded_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (task_id, file_name, file_path, file_size, file_type,
                 version, description, is_required, uploaded_by)
        
        db_manager.execute_update(query, params)
        return db_manager.get_last_insert_id()
    
    @staticmethod
    def get_by_task(task_id: int) -> List[Dict]:
        """
        获取任务的所有素材
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[Dict]: 素材列表
        """
        query = "SELECT * FROM task_materials WHERE task_id = ? ORDER BY is_required DESC, upload_time DESC"
        return db_manager.execute_query(query, (task_id,))
    
    @staticmethod
    def get_by_id(material_id: int) -> Optional[Dict]:
        """
        根据ID获取素材
        
        Args:
            material_id: 素材ID
            
        Returns:
            Optional[Dict]: 素材信息
        """
        query = "SELECT * FROM task_materials WHERE id = ?"
        results = db_manager.execute_query(query, (material_id,))
        return results[0] if results else None
    
    @staticmethod
    def update(material_id: int, **kwargs) -> bool:
        """
        更新素材信息
        
        Args:
            material_id: 素材ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        if not kwargs:
            return False
        
        set_clause = ", ".join([f"{key} = ?" for key in kwargs.keys()])
        query = f"UPDATE task_materials SET {set_clause} WHERE id = ?"
        
        params = list(kwargs.values()) + [material_id]
        rows_affected = db_manager.execute_update(query, tuple(params))
        return rows_affected > 0
    
    @staticmethod
    def delete(material_id: int) -> bool:
        """
        删除素材
        
        Args:
            material_id: 素材ID
            
        Returns:
            bool: 删除是否成功
        """
        query = "DELETE FROM task_materials WHERE id = ?"
        rows_affected = db_manager.execute_update(query, (material_id,))
        return rows_affected > 0

class TaskExecutionDAO:
    """
    任务执行记录数据访问对象
    """
    
    @staticmethod
    def create(task_id: int, student_id: int, student_name: str, exam_id: int = None) -> int:
        """
        创建任务执行记录
        
        Args:
            task_id: 任务ID
            student_id: 学生ID
            student_name: 学生姓名
            exam_id: 考试ID
            
        Returns:
            int: 新创建记录的ID
        """
        # 获取任务的最高分数
        task = PracticalTaskDAO.get_by_id(task_id)
        max_score = task['max_score'] if task else 100
        
        query = """
            INSERT INTO task_executions (task_id, student_id, student_name, exam_id, max_score)
            VALUES (?, ?, ?, ?, ?)
        """
        params = (task_id, student_id, student_name, exam_id, max_score)
        
        db_manager.execute_update(query, params)
        return db_manager.get_last_insert_id()
    
    @staticmethod
    def start_execution(execution_id: int) -> bool:
        """
        开始任务执行
        
        Args:
            execution_id: 执行记录ID
            
        Returns:
            bool: 操作是否成功
        """
        query = """
            UPDATE task_executions 
            SET status = 'in_progress', start_time = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """
        rows_affected = db_manager.execute_update(query, (execution_id,))
        return rows_affected > 0
    
    @staticmethod
    def complete_execution(execution_id: int, submission_files: List[str] = None) -> bool:
        """
        完成任务执行
        
        Args:
            execution_id: 执行记录ID
            submission_files: 提交的文件列表
            
        Returns:
            bool: 操作是否成功
        """
        submission_json = json.dumps(submission_files or [], ensure_ascii=False)
        
        query = """
            UPDATE task_executions 
            SET status = 'completed', end_time = CURRENT_TIMESTAMP, 
                duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 24 * 60 AS INTEGER),
                submission_files = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """
        rows_affected = db_manager.execute_update(query, (submission_json, execution_id))
        return rows_affected > 0
    
    @staticmethod
    def evaluate_execution(execution_id: int, score: int, evaluation_details: Dict,
                          evaluator_id: int, remarks: str = "") -> bool:
        """
        评价任务执行
        
        Args:
            execution_id: 执行记录ID
            score: 得分
            evaluation_details: 评价详情
            evaluator_id: 评价者ID
            remarks: 备注
            
        Returns:
            bool: 操作是否成功
        """
        evaluation_json = json.dumps(evaluation_details, ensure_ascii=False)
        
        query = """
            UPDATE task_executions 
            SET score = ?, evaluation_details = ?, evaluator_id = ?, 
                evaluation_time = CURRENT_TIMESTAMP, remarks = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """
        params = (score, evaluation_json, evaluator_id, remarks, execution_id)
        rows_affected = db_manager.execute_update(query, params)
        return rows_affected > 0
    
    @staticmethod
    def get_by_student(student_id: int) -> List[Dict]:
        """
        获取学生的所有执行记录
        
        Args:
            student_id: 学生ID
            
        Returns:
            List[Dict]: 执行记录列表
        """
        query = """
            SELECT e.*, t.name as task_name, t.software_version, c.name as category_name
            FROM task_executions e
            LEFT JOIN practical_tasks t ON e.task_id = t.id
            LEFT JOIN task_categories c ON t.category_id = c.id
            WHERE e.student_id = ?
            ORDER BY e.created_at DESC
        """
        results = db_manager.execute_query(query, (student_id,))
        
        # 解析JSON字段
        for record in results:
            if record['evaluation_details']:
                record['evaluation_details'] = json.loads(record['evaluation_details'])
            if record['submission_files']:
                record['submission_files'] = json.loads(record['submission_files'])
        
        return results
    
    @staticmethod
    def get_by_task(task_id: int) -> List[Dict]:
        """
        获取任务的所有执行记录
        
        Args:
            task_id: 任务ID
            
        Returns:
            List[Dict]: 执行记录列表
        """
        query = """
            SELECT e.*, t.name as task_name
            FROM task_executions e
            LEFT JOIN practical_tasks t ON e.task_id = t.id
            WHERE e.task_id = ?
            ORDER BY e.created_at DESC
        """
        results = db_manager.execute_query(query, (task_id,))
        
        # 解析JSON字段
        for record in results:
            if record['evaluation_details']:
                record['evaluation_details'] = json.loads(record['evaluation_details'])
            if record['submission_files']:
                record['submission_files'] = json.loads(record['submission_files'])
        
        return results
    
    @staticmethod
    def get_statistics(task_id: int = None, exam_id: int = None) -> Dict:
        """
        获取执行统计信息
        
        Args:
            task_id: 任务ID（可选）
            exam_id: 考试ID（可选）
            
        Returns:
            Dict: 统计信息
        """
        stats = {}
        where_clause = "WHERE 1=1"
        params = []
        
        if task_id:
            where_clause += " AND task_id = ?"
            params.append(task_id)
        if exam_id:
            where_clause += " AND exam_id = ?"
            params.append(exam_id)
        
        # 总执行数
        query = f"SELECT COUNT(*) as total FROM task_executions {where_clause}"
        result = db_manager.execute_query(query, tuple(params))
        stats['total_executions'] = result[0]['total'] if result else 0
        
        # 按状态统计
        query = f"SELECT status, COUNT(*) as count FROM task_executions {where_clause} GROUP BY status"
        status_stats = db_manager.execute_query(query, tuple(params))
        stats['by_status'] = {item['status']: item['count'] for item in status_stats}
        
        # 平均分数
        query = f"SELECT AVG(score) as avg_score FROM task_executions {where_clause} AND score IS NOT NULL"
        result = db_manager.execute_query(query, tuple(params))
        stats['average_score'] = round(result[0]['avg_score'], 2) if result and result[0]['avg_score'] else 0
        
        # 平均用时
        query = f"SELECT AVG(duration) as avg_duration FROM task_executions {where_clause} AND duration IS NOT NULL"
        result = db_manager.execute_query(query, tuple(params))
        stats['average_duration'] = round(result[0]['avg_duration'], 2) if result and result[0]['avg_duration'] else 0
        
        return stats

class OperationLogDAO:
    """
    操作日志数据访问对象
    """
    
    @staticmethod
    def log_operation(user_id: int, user_name: str, operation: str, target_type: str,
                     target_id: int = None, details: Dict = None, ip_address: str = None,
                     user_agent: str = None) -> int:
        """
        记录操作日志
        
        Args:
            user_id: 用户ID
            user_name: 用户名
            operation: 操作类型
            target_type: 目标类型
            target_id: 目标ID
            details: 操作详情
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            int: 日志记录ID
        """
        details_json = json.dumps(details or {}, ensure_ascii=False)
        
        query = """
            INSERT INTO operation_logs 
            (user_id, user_name, operation, target_type, target_id, details, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (user_id, user_name, operation, target_type, target_id,
                 details_json, ip_address, user_agent)
        
        db_manager.execute_update(query, params)
        return db_manager.get_last_insert_id()
    
    @staticmethod
    def get_logs(user_id: int = None, target_type: str = None, limit: int = 100) -> List[Dict]:
        """
        获取操作日志
        
        Args:
            user_id: 用户ID过滤
            target_type: 目标类型过滤
            limit: 返回记录数限制
            
        Returns:
            List[Dict]: 日志记录列表
        """
        where_clause = "WHERE 1=1"
        params = []
        
        if user_id:
            where_clause += " AND user_id = ?"
            params.append(user_id)
        if target_type:
            where_clause += " AND target_type = ?"
            params.append(target_type)
        
        query = f"""
            SELECT * FROM operation_logs {where_clause}
            ORDER BY created_at DESC LIMIT ?
        """
        params.append(limit)
        
        results = db_manager.execute_query(query, tuple(params))
        
        # 解析JSON字段
        for log in results:
            if log['details']:
                log['details'] = json.loads(log['details'])
        
        return results