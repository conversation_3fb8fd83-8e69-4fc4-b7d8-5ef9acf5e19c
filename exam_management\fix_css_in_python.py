#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Python文件中CSS代码导致的语法错误
"""

import re
import ast

def fix_css_syntax_errors(file_path):
    """修复文件中CSS代码导致的Python语法错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_count = 0
        
        # 查找所有HTML模板字符串的开始和结束位置
        html_template_pattern = r'html_template\s*=\s*"""([\s\S]*?)"""'
        matches = list(re.finditer(html_template_pattern, content))
        
        print(f"找到 {len(matches)} 个HTML模板")
        
        # 如果没有找到完整的HTML模板，说明有未闭合的字符串
        if len(matches) == 0:
            print("没有找到完整的HTML模板，查找未闭合的字符串...")
            
            # 查找html_template = """的开始位置
            start_pattern = r'html_template\s*=\s*"""'
            start_matches = list(re.finditer(start_pattern, content))
            
            if start_matches:
                print(f"找到 {len(start_matches)} 个HTML模板开始标记")
                
                for i, start_match in enumerate(start_matches):
                    start_pos = start_match.end()
                    
                    # 查找这个开始标记后面的结束标记
                    remaining_content = content[start_pos:]
                    end_match = re.search(r'"""', remaining_content)
                    
                    if not end_match:
                        print(f"HTML模板 {i+1} 没有找到结束标记，在文件末尾添加")
                        
                        # 在文件末尾添加结束标记
                        content = content + '\n        """\n'
                        fixes_count += 1
                    else:
                        print(f"HTML模板 {i+1} 有结束标记")
        
        # 修复CSS中的百分号问题 - 在字符串内部
        # 查找所有可能导致问题的CSS百分号
        css_percent_pattern = r'(\d+)%'
        
        # 只在HTML模板字符串内部进行替换
        def replace_css_percent(match):
            return f'{match.group(1)}%'
        
        # 重新查找HTML模板
        html_template_pattern = r'(html_template\s*=\s*"""[\s\S]*?"""?)'
        
        def fix_css_in_template(match):
            template_content = match.group(1)
            # 在模板内容中修复CSS问题
            fixed_content = template_content
            return fixed_content
        
        content = re.sub(html_template_pattern, fix_css_in_template, content)
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\nCSS语法错误修复完成！")
            print(f"已修复 {fixes_count} 个问题")
        else:
            print("没有发现需要修复的CSS语法问题")
            
        return fixes_count
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        return 0

def check_python_syntax(file_path):
    """检查Python文件语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试编译
        compile(content, file_path, 'exec')
        print("Python语法检查通过！")
        return True
        
    except SyntaxError as e:
        print(f"语法错误: {e}")
        print(f"行号: {e.lineno}")
        print(f"位置: {e.offset}")
        return False
    except Exception as e:
        print(f"检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    file_path = "app.py"
    
    print("开始修复CSS语法错误...")
    fix_css_syntax_errors(file_path)
    
    print("\n检查Python语法...")
    check_python_syntax(file_path)