{% extends "base.html" %}

{% block title %}{{ '编辑任务' if task else '创建任务' }} - 实操任务管理系统{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item"><a href="{{ url_for('web.tasks') }}">任务管理</a></li>
<li class="breadcrumb-item active">{{ '编辑任务' if task else '创建任务' }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-{{ 'pencil' if task else 'plus-circle' }}"></i> 
                {{ '编辑任务' if task else '创建任务' }}
            </h1>
            <a href="{{ url_for('web.tasks') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> 基本信息
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="taskForm">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="title" class="form-label">任务标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title" 
                                       value="{{ task.title if task else '' }}" required maxlength="200">
                                <div class="form-text">请输入简洁明确的任务标题</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="status" class="form-label">状态</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active" {{ 'selected' if not task or task.status == 'active' }}>启用</option>
                                    <option value="inactive" {{ 'selected' if task and task.status == 'inactive' }}>禁用</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">任务描述</label>
                        <textarea class="form-control" id="description" name="description" rows="4" maxlength="1000">{{ task.description if task else '' }}</textarea>
                        <div class="form-text">详细描述任务的目标和要求</div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category_id" class="form-label">任务分类</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">请选择分类</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" 
                                            {{ 'selected' if task and task.category_id == category.id }}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <a href="{{ url_for('web.category_create') }}" target="_blank">创建新分类</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="software_type" class="form-label">软件类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="software_type" name="software_type" required>
                                    <option value="">请选择软件类型</option>
                                    <option value="Word" {{ 'selected' if task and task.software_type == 'Word' }}>Word</option>
                                    <option value="Excel" {{ 'selected' if task and task.software_type == 'Excel' }}>Excel</option>
                                    <option value="PowerPoint" {{ 'selected' if task and task.software_type == 'PowerPoint' }}>PowerPoint</option>
                                    <option value="Photoshop" {{ 'selected' if task and task.software_type == 'Photoshop' }}>Photoshop</option>
                                    <option value="AutoCAD" {{ 'selected' if task and task.software_type == 'AutoCAD' }}>AutoCAD</option>
                                    <option value="其他" {{ 'selected' if task and task.software_type == '其他' }}>其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="difficulty_level" class="form-label">难度等级 <span class="text-danger">*</span></label>
                                <select class="form-select" id="difficulty_level" name="difficulty_level" required>
                                    <option value="">请选择难度等级</option>
                                    <option value="初级" {{ 'selected' if task and task.difficulty_level == '初级' }}>初级</option>
                                    <option value="中级" {{ 'selected' if task and task.difficulty_level == '中级' }}>中级</option>
                                    <option value="高级" {{ 'selected' if task and task.difficulty_level == '高级' }}>高级</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="duration_minutes" class="form-label">预计时长(分钟) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="duration_minutes" name="duration_minutes" 
                                       value="{{ task.duration_minutes if task else '' }}" required min="1" max="480">
                                <div class="form-text">建议范围: 15-480分钟</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_score" class="form-label">满分分值 <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="max_score" name="max_score" 
                                       value="{{ task.max_score if task else '100' }}" required min="1" max="1000" step="0.1">
                                <div class="form-text">默认100分</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="requirements" class="form-label">任务要求</label>
                        <textarea class="form-control" id="requirements" name="requirements" rows="6" maxlength="2000">{{ task.requirements if task else '' }}</textarea>
                        <div class="form-text">详细列出任务的具体要求和操作步骤</div>
                    </div>

                    <div class="mb-3">
                        <label for="scoring_criteria" class="form-label">评分标准</label>
                        <textarea class="form-control" id="scoring_criteria" name="scoring_criteria" rows="6" maxlength="2000">{{ task.scoring_criteria if task else '' }}</textarea>
                        <div class="form-text">详细说明评分标准和扣分规则</div>
                    </div>

                    <div class="mb-3">
                        <label for="tags" class="form-label">标签</label>
                        <input type="text" class="form-control" id="tags" name="tags" 
                               value="{{ task.tags if task else '' }}" maxlength="500">
                        <div class="form-text">多个标签用逗号分隔，如: 基础操作,格式设置,图表制作</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> {{ '更新任务' if task else '创建任务' }}
                            </button>
                            <a href="{{ url_for('web.tasks') }}" class="btn btn-outline-secondary ms-2">
                                <i class="bi bi-x-circle"></i> 取消
                            </a>
                        </div>
                        {% if task %}
                        <div>
                            <a href="{{ url_for('web.task_materials', task_id=task.id) }}" class="btn btn-outline-success">
                                <i class="bi bi-file-earmark"></i> 管理素材
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- 帮助信息 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-question-circle"></i> 填写说明
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="bi bi-info-circle text-primary"></i> 任务标题</h6>
                    <p class="small text-muted">简洁明确地描述任务内容，建议不超过50字。</p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="bi bi-info-circle text-primary"></i> 软件类型</h6>
                    <p class="small text-muted">选择任务所使用的主要软件，影响考试环境配置。</p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="bi bi-info-circle text-primary"></i> 难度等级</h6>
                    <ul class="small text-muted">
                        <li><strong>初级:</strong> 基础操作，适合新手</li>
                        <li><strong>中级:</strong> 综合应用，需要一定经验</li>
                        <li><strong>高级:</strong> 复杂操作，需要熟练掌握</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6><i class="bi bi-info-circle text-primary"></i> 预计时长</h6>
                    <p class="small text-muted">根据任务复杂度合理设置，建议留有适当余量。</p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="bi bi-info-circle text-primary"></i> 评分标准</h6>
                    <p class="small text-muted">明确各项操作的分值分配，便于统一评分。</p>
                </div>
            </div>
        </div>
        
        {% if task %}
        <!-- 任务统计 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i> 任务统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ task.execution_count or 0 }}</h4>
                        <small class="text-muted">执行次数</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ task.material_count or 0 }}</h4>
                        <small class="text-muted">素材文件</small>
                    </div>
                </div>
                <hr>
                <div class="small text-muted">
                    <p><strong>创建时间:</strong> {{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                    {% if task.updated_at %}
                    <p><strong>更新时间:</strong> {{ task.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 表单验证
document.getElementById('taskForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const softwareType = document.getElementById('software_type').value;
    const difficultyLevel = document.getElementById('difficulty_level').value;
    const duration = document.getElementById('duration_minutes').value;
    const maxScore = document.getElementById('max_score').value;
    
    if (!title) {
        alert('请输入任务标题');
        e.preventDefault();
        return;
    }
    
    if (!softwareType) {
        alert('请选择软件类型');
        e.preventDefault();
        return;
    }
    
    if (!difficultyLevel) {
        alert('请选择难度等级');
        e.preventDefault();
        return;
    }
    
    if (!duration || duration < 1 || duration > 480) {
        alert('请输入有效的预计时长(1-480分钟)');
        e.preventDefault();
        return;
    }
    
    if (!maxScore || maxScore < 1 || maxScore > 1000) {
        alert('请输入有效的满分分值(1-1000分)');
        e.preventDefault();
        return;
    }
});

// 字符计数
function setupCharacterCount(textareaId, maxLength) {
    const textarea = document.getElementById(textareaId);
    const helpText = textarea.nextElementSibling;
    
    function updateCount() {
        const remaining = maxLength - textarea.value.length;
        helpText.textContent = helpText.textContent.split('(')[0] + ` (还可输入${remaining}字)`;
        
        if (remaining < 50) {
            helpText.classList.add('text-warning');
        } else {
            helpText.classList.remove('text-warning');
        }
    }
    
    textarea.addEventListener('input', updateCount);
    updateCount();
}

// 初始化字符计数
setupCharacterCount('description', 1000);
setupCharacterCount('requirements', 2000);
setupCharacterCount('scoring_criteria', 2000);
setupCharacterCount('tags', 500);
</script>
{% endblock %}