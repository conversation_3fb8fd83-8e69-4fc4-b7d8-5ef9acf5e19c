#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证和输入校验模块
提供考试编排系统的各种数据验证功能
"""

import re
import datetime
from typing import Dict, List, Any, Optional, Tuple
from functools import wraps
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """验证错误异常类"""
    def __init__(self, message: str, field: str = None, code: str = None):
        self.message = message
        self.field = field
        self.code = code
        super().__init__(self.message)

class DataValidator:
    """数据验证器类"""
    
    @staticmethod
    def validate_required(value: Any, field_name: str) -> Any:
        """
        验证必填字段
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            
        Returns:
            验证后的值
            
        Raises:
            ValidationError: 当字段为空时
        """
        if value is None or (isinstance(value, str) and value.strip() == ''):
            raise ValidationError(f"{field_name}不能为空", field_name, "REQUIRED")
        return value
    
    @staticmethod
    def validate_string_length(value: str, field_name: str, min_length: int = 0, max_length: int = 255) -> str:
        """
        验证字符串长度
        
        Args:
            value: 字符串值
            field_name: 字段名称
            min_length: 最小长度
            max_length: 最大长度
            
        Returns:
            验证后的字符串
            
        Raises:
            ValidationError: 当长度不符合要求时
        """
        if not isinstance(value, str):
            raise ValidationError(f"{field_name}必须是字符串", field_name, "TYPE_ERROR")
        
        length = len(value.strip())
        if length < min_length:
            raise ValidationError(f"{field_name}长度不能少于{min_length}个字符", field_name, "MIN_LENGTH")
        if length > max_length:
            raise ValidationError(f"{field_name}长度不能超过{max_length}个字符", field_name, "MAX_LENGTH")
        
        return value.strip()
    
    @staticmethod
    def validate_integer(value: Any, field_name: str, min_value: int = None, max_value: int = None) -> int:
        """
        验证整数
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            min_value: 最小值
            max_value: 最大值
            
        Returns:
            验证后的整数
            
        Raises:
            ValidationError: 当值不是有效整数或超出范围时
        """
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(f"{field_name}必须是有效的整数", field_name, "TYPE_ERROR")
        
        if min_value is not None and int_value < min_value:
            raise ValidationError(f"{field_name}不能小于{min_value}", field_name, "MIN_VALUE")
        if max_value is not None and int_value > max_value:
            raise ValidationError(f"{field_name}不能大于{max_value}", field_name, "MAX_VALUE")
        
        return int_value
    
    @staticmethod
    def validate_datetime(value: Any, field_name: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime.datetime:
        """
        验证日期时间格式
        
        Args:
            value: 要验证的值
            field_name: 字段名称
            format_str: 日期时间格式字符串
            
        Returns:
            验证后的datetime对象
            
        Raises:
            ValidationError: 当日期时间格式无效时
        """
        if isinstance(value, datetime.datetime):
            return value
        
        if not isinstance(value, str):
            raise ValidationError(f"{field_name}必须是字符串或datetime对象", field_name, "TYPE_ERROR")
        
        try:
            return datetime.datetime.strptime(value, format_str)
        except ValueError:
            raise ValidationError(f"{field_name}格式无效，应为{format_str}", field_name, "FORMAT_ERROR")
    
    @staticmethod
    def validate_email(value: str, field_name: str) -> str:
        """
        验证邮箱格式
        
        Args:
            value: 邮箱字符串
            field_name: 字段名称
            
        Returns:
            验证后的邮箱字符串
            
        Raises:
            ValidationError: 当邮箱格式无效时
        """
        if not isinstance(value, str):
            raise ValidationError(f"{field_name}必须是字符串", field_name, "TYPE_ERROR")
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, value):
            raise ValidationError(f"{field_name}格式无效", field_name, "FORMAT_ERROR")
        
        return value.lower().strip()
    
    @staticmethod
    def validate_phone(value: str, field_name: str) -> str:
        """
        验证手机号格式
        
        Args:
            value: 手机号字符串
            field_name: 字段名称
            
        Returns:
            验证后的手机号字符串
            
        Raises:
            ValidationError: 当手机号格式无效时
        """
        if not isinstance(value, str):
            raise ValidationError(f"{field_name}必须是字符串", field_name, "TYPE_ERROR")
        
        # 中国手机号格式验证
        phone_pattern = r'^1[3-9]\d{9}$'
        clean_phone = re.sub(r'[\s-]', '', value)
        
        if not re.match(phone_pattern, clean_phone):
            raise ValidationError(f"{field_name}格式无效", field_name, "FORMAT_ERROR")
        
        return clean_phone
    
    @staticmethod
    def validate_id_card(value: str, field_name: str) -> str:
        """
        验证身份证号格式
        
        Args:
            value: 身份证号字符串
            field_name: 字段名称
            
        Returns:
            验证后的身份证号字符串
            
        Raises:
            ValidationError: 当身份证号格式无效时
        """
        if not isinstance(value, str):
            raise ValidationError(f"{field_name}必须是字符串", field_name, "TYPE_ERROR")
        
        # 18位身份证号验证
        id_pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
        
        if not re.match(id_pattern, value):
            raise ValidationError(f"{field_name}格式无效", field_name, "FORMAT_ERROR")
        
        return value.upper()

class ExamValidator:
    """考试相关验证器"""
    
    @staticmethod
    def validate_exam_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证考试数据
        
        Args:
            data: 考试数据字典
            
        Returns:
            验证后的数据字典
            
        Raises:
            ValidationError: 当数据验证失败时
        """
        validator = DataValidator()
        validated_data = {}
        
        # 验证考试名称
        validated_data['exam_name'] = validator.validate_string_length(
            validator.validate_required(data.get('exam_name'), '考试名称'),
            '考试名称', 2, 100
        )
        
        # 验证考试描述
        if data.get('description'):
            validated_data['description'] = validator.validate_string_length(
                data['description'], '考试描述', 0, 500
            )
        
        # 验证开始时间
        validated_data['start_time'] = validator.validate_datetime(
            validator.validate_required(data.get('start_time'), '开始时间'),
            '开始时间'
        )
        
        # 验证结束时间
        validated_data['end_time'] = validator.validate_datetime(
            validator.validate_required(data.get('end_time'), '结束时间'),
            '结束时间'
        )
        
        # 验证时间逻辑
        if validated_data['end_time'] <= validated_data['start_time']:
            raise ValidationError("结束时间必须晚于开始时间", "end_time", "TIME_LOGIC")
        
        # 验证考试时长（分钟）
        if data.get('duration'):
            validated_data['duration'] = validator.validate_integer(
                data['duration'], '考试时长', 1, 600
            )
        
        # 验证最大参与者数量
        if data.get('max_participants'):
            validated_data['max_participants'] = validator.validate_integer(
                data['max_participants'], '最大参与者数量', 1, 1000
            )
        
        # 验证考试类型
        valid_types = ['理论考试', '实操考试', '综合考试']
        exam_type = data.get('exam_type')
        if exam_type and exam_type not in valid_types:
            raise ValidationError(f"考试类型必须是{valid_types}中的一种", "exam_type", "INVALID_CHOICE")
        validated_data['exam_type'] = exam_type
        
        # 验证考试状态
        valid_statuses = ['草稿', '已发布', '进行中', '已结束']
        status = data.get('status', '草稿')
        if status not in valid_statuses:
            raise ValidationError(f"考试状态必须是{valid_statuses}中的一种", "status", "INVALID_CHOICE")
        validated_data['status'] = status
        
        return validated_data
    
    @staticmethod
    def validate_participant_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证参与者数据
        
        Args:
            data: 参与者数据字典
            
        Returns:
            验证后的数据字典
            
        Raises:
            ValidationError: 当数据验证失败时
        """
        validator = DataValidator()
        validated_data = {}
        
        # 验证姓名
        validated_data['name'] = validator.validate_string_length(
            validator.validate_required(data.get('name'), '姓名'),
            '姓名', 2, 50
        )
        
        # 验证身份证号
        if data.get('id_card'):
            validated_data['id_card'] = validator.validate_id_card(
                data['id_card'], '身份证号'
            )
        
        # 验证手机号
        if data.get('phone'):
            validated_data['phone'] = validator.validate_phone(
                data['phone'], '手机号'
            )
        
        # 验证邮箱
        if data.get('email'):
            validated_data['email'] = validator.validate_email(
                data['email'], '邮箱'
            )
        
        # 验证座位号
        if data.get('seat_number'):
            validated_data['seat_number'] = validator.validate_string_length(
                data['seat_number'], '座位号', 1, 20
            )
        
        # 验证参与者状态
        valid_statuses = ['已报名', '已确认', '已参加', '缺考', '取消']
        status = data.get('status', '已报名')
        if status not in valid_statuses:
            raise ValidationError(f"参与者状态必须是{valid_statuses}中的一种", "status", "INVALID_CHOICE")
        validated_data['status'] = status
        
        return validated_data
    
    @staticmethod
    def validate_time_conflict(start_time: datetime.datetime, end_time: datetime.datetime, 
                             existing_exams: List[Dict]) -> bool:
        """
        验证考试时间冲突
        
        Args:
            start_time: 新考试开始时间
            end_time: 新考试结束时间
            existing_exams: 现有考试列表
            
        Returns:
            True表示无冲突，False表示有冲突
            
        Raises:
            ValidationError: 当存在时间冲突时
        """
        for exam in existing_exams:
            exam_start = exam.get('start_time')
            exam_end = exam.get('end_time')
            
            if isinstance(exam_start, str):
                exam_start = datetime.datetime.strptime(exam_start, "%Y-%m-%d %H:%M:%S")
            if isinstance(exam_end, str):
                exam_end = datetime.datetime.strptime(exam_end, "%Y-%m-%d %H:%M:%S")
            
            # 检查时间重叠
            if (start_time < exam_end and end_time > exam_start):
                raise ValidationError(
                    f"考试时间与现有考试'{exam.get('exam_name', '未知考试')}'冲突",
                    "time_conflict",
                    "TIME_CONFLICT"
                )
        
        return True

def validation_decorator(validator_func):
    """
    验证装饰器
    
    Args:
        validator_func: 验证函数
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 假设第一个参数是数据
                if args:
                    validated_data = validator_func(args[0])
                    args = (validated_data,) + args[1:]
                return func(*args, **kwargs)
            except ValidationError as e:
                logger.error(f"验证失败: {e.message}")
                raise
            except Exception as e:
                logger.error(f"验证过程中发生错误: {str(e)}")
                raise ValidationError("数据验证失败", code="VALIDATION_ERROR")
        return wrapper
    return decorator

# 使用示例
if __name__ == "__main__":
    # 测试考试数据验证
    exam_data = {
        'exam_name': '期末考试',
        'description': '本学期期末考试',
        'start_time': '2024-01-15 09:00:00',
        'end_time': '2024-01-15 11:00:00',
        'duration': 120,
        'max_participants': 50,
        'exam_type': '理论考试',
        'status': '草稿'
    }
    
    try:
        validated_exam = ExamValidator.validate_exam_data(exam_data)
        print("考试数据验证通过:", validated_exam)
    except ValidationError as e:
        print(f"验证失败: {e.message}")
    
    # 测试参与者数据验证
    participant_data = {
        'name': '张三',
        'id_card': '110101199001011234',
        'phone': '13800138000',
        'email': '<EMAIL>',
        'seat_number': 'A01',
        'status': '已报名'
    }
    
    try:
        validated_participant = ExamValidator.validate_participant_data(participant_data)
        print("参与者数据验证通过:", validated_participant)
    except ValidationError as e:
        print(f"验证失败: {e.message}")