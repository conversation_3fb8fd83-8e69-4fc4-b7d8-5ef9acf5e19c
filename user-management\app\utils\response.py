from flask import jsonify
from typing import Any, Dict, Optional, List

def success_response(data: Any = None, message: str = "操作成功", code: int = 200) -> Dict:
    """成功响应格式"""
    response = {
        "success": True,
        "code": code,
        "message": message,
        "timestamp": int(__import__('time').time() * 1000)
    }
    
    if data is not None:
        response["data"] = data
    
    return response

def error_response(message: str = "操作失败", code: int = 400, error_code: str = None) -> Dict:
    """错误响应格式"""
    response = {
        "success": False,
        "code": code,
        "message": message,
        "timestamp": int(__import__('time').time() * 1000)
    }
    
    if error_code:
        response["error_code"] = error_code
    
    return response

def paginated_response(data: List, page: int, page_size: int, total: int, message: str = "查询成功") -> Dict:
    """分页响应格式"""
    total_pages = (total + page_size - 1) // page_size
    
    return {
        "success": True,
        "code": 200,
        "message": message,
        "data": {
            "items": data,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        },
        "timestamp": int(__import__('time').time() * 1000)
    }

def validation_error_response(errors: Dict) -> Dict:
    """参数验证错误响应"""
    response = error_response(
        message="参数验证失败",
        code=400,
        error_code="VALIDATION_ERROR"
    )
    response["errors"] = errors
    return response