{% extends "base.html" %}

{% block title %}任务管理 - 实操任务管理系统{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">任务管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-list-task"></i> 任务管理
            </h1>
            <a href="{{ url_for('web.task_create') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建新任务
            </a>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.args.get('search', '') }}" placeholder="任务标题或描述">
                    </div>
                    <div class="col-md-2">
                        <label for="category_id" class="form-label">任务分类</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">全部分类</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}" 
                                    {{ 'selected' if request.args.get('category_id') == category.id|string }}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="software_type" class="form-label">软件类型</label>
                        <select class="form-select" id="software_type" name="software_type">
                            <option value="">全部类型</option>
                            <option value="Word" {{ 'selected' if request.args.get('software_type') == 'Word' }}>Word</option>
                            <option value="Excel" {{ 'selected' if request.args.get('software_type') == 'Excel' }}>Excel</option>
                            <option value="PowerPoint" {{ 'selected' if request.args.get('software_type') == 'PowerPoint' }}>PowerPoint</option>
                            <option value="Photoshop" {{ 'selected' if request.args.get('software_type') == 'Photoshop' }}>Photoshop</option>
                            <option value="AutoCAD" {{ 'selected' if request.args.get('software_type') == 'AutoCAD' }}>AutoCAD</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="difficulty_level" class="form-label">难度等级</label>
                        <select class="form-select" id="difficulty_level" name="difficulty_level">
                            <option value="">全部难度</option>
                            <option value="初级" {{ 'selected' if request.args.get('difficulty_level') == '初级' }}>初级</option>
                            <option value="中级" {{ 'selected' if request.args.get('difficulty_level') == '中级' }}>中级</option>
                            <option value="高级" {{ 'selected' if request.args.get('difficulty_level') == '高级' }}>高级</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="active" {{ 'selected' if request.args.get('status') == 'active' }}>启用</option>
                            <option value="inactive" {{ 'selected' if request.args.get('status') == 'inactive' }}>禁用</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                            </button>
                            <a href="{{ url_for('web.tasks') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">任务列表 (共 {{ pagination.total }} 个)</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="selectAll()">
                        <i class="bi bi-check-all"></i> 全选
                    </button>
                    <button type="button" class="btn btn-outline-danger btn-sm" onclick="batchDelete()">
                        <i class="bi bi-trash"></i> 批量删除
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if tasks %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                </th>
                                <th>任务标题</th>
                                <th>分类</th>
                                <th>软件类型</th>
                                <th>难度等级</th>
                                <th>时长(分钟)</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="task-checkbox" value="{{ task.id }}">
                                </td>
                                <td>
                                    <strong>{{ task.title }}</strong>
                                    {% if task.description %}
                                    <br><small class="text-muted">{{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.category_name %}
                                    <span class="badge bg-secondary">{{ task.category_name }}</span>
                                    {% else %}
                                    <span class="text-muted">未分类</span>
                                    {% endif %}
                                </td>
                                <td>{{ task.software_type }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if task.difficulty_level == '初级' else 'warning' if task.difficulty_level == '中级' else 'danger' }}">
                                        {{ task.difficulty_level }}
                                    </span>
                                </td>
                                <td>{{ task.duration_minutes }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if task.status == 'active' else 'secondary' }}">
                                        {{ '启用' if task.status == 'active' else '禁用' }}
                                    </span>
                                </td>
                                <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('web.task_detail', task_id=task.id) }}" 
                                           class="btn btn-outline-info" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ url_for('web.task_edit', task_id=task.id) }}" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="{{ url_for('web.task_materials', task_id=task.id) }}" 
                                           class="btn btn-outline-success" title="素材管理">
                                            <i class="bi bi-file-earmark"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteTask({{ task.id }}, '{{ task.title }}')" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if pagination.pages > 1 %}
                <div class="d-flex justify-content-center mt-3">
                    <nav>
                        <ul class="pagination">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('web.tasks', page=pagination.prev_num, **request.args) }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('web.tasks', page=page_num, **request.args) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('web.tasks', page=pagination.next_num, **request.args) }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无任务</h4>
                    <p class="text-muted">点击上方按钮创建第一个任务</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    
    taskCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
}

// 选择全部
function selectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    
    selectAllCheckbox.checked = true;
    taskCheckboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

// 批量删除
function batchDelete() {
    const selectedTasks = document.querySelectorAll('.task-checkbox:checked');
    
    if (selectedTasks.length === 0) {
        alert('请选择要删除的任务');
        return;
    }
    
    if (confirm(`确定要删除选中的 ${selectedTasks.length} 个任务吗？此操作不可撤销。`)) {
        const taskIds = Array.from(selectedTasks).map(checkbox => checkbox.value);
        
        // 发送批量删除请求
        fetch('/api/v1/tasks/batch', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ task_ids: taskIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                location.reload();
            } else {
                alert('删除失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error.message);
        });
    }
}

// 删除单个任务
function deleteTask(taskId, taskTitle) {
    if (confirm(`确定要删除任务 "${taskTitle}" 吗？此操作不可撤销。`)) {
        fetch(`/api/v1/tasks/${taskId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                location.reload();
            } else {
                alert('删除失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error.message);
        });
    }
}
</script>
{% endblock %}