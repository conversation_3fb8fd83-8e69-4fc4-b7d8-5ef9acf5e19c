#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评分管理模块启动脚本

提供评分管理服务，包括：
- 考试成绩录入和管理
- 评分数据查询
- 成绩统计分析
- 评分权限控制
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app import app

def main():
    """
    主函数 - 启动评分管理服务
    """
    try:
        port = 5006  # 使用module_status.json中配置的端口
        
        print("============================================================")
        print("评分管理模块 - 局域网在线考试系统")
        print("============================================================")
        print(f"[*] 服务地址: http://0.0.0.0:{port}")
        print(f"[*] API文档: http://0.0.0.0:{port}/api/")
        print(f"[*] 健康检查: http://0.0.0.0:{port}/api/health")
        print(f"[*] 成绩查询: http://0.0.0.0:{port}/api/v1/exams/<exam_id>/scores")
        print(f"[*] 成绩录入: http://0.0.0.0:{port}/api/v1/exams/<exam_id>/students/<student_id>/scores")
        print(f"[*] 调试模式: True")
        print("============================================================")
        print("[*] 按 Ctrl+C 停止服务")
        print("============================================================")
        
        # 启动Flask应用
        app.run(host='0.0.0.0', port=port, debug=True)
        
    except KeyboardInterrupt:
        print("\n[*] 收到停止信号，正在关闭评分管理服务...")
    except Exception as e:
        print(f"[!] 评分管理服务启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()