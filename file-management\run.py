# -*- coding: utf-8 -*-
"""
成果文件管理模块 - 启动脚本
"""

from app import create_app

# Create the Flask app instance using the factory
app = create_app()

if __name__ == '__main__':
    # The port is configured to 5014 as specified in main_launcher.py
    port = 5014

    print("--- File Management Service ---")
    print(f"Starting server at: http://0.0.0.0:{port}")

    # Using debug=True for development provides helpful logs and auto-reloading.
    app.run(host='0.0.0.0', port=port, debug=True)
