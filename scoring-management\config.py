# -*- coding: utf-8 -*-
"""
评分管理模块配置文件

功能说明:
- 定义评分管理模块的基础配置
- 配置数据库连接信息
- 设置外部服务API地址
- 配置评分相关参数
"""

import os
from datetime import timedelta

class Config:
    """基础配置类"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'scoring-management-secret-key-2024'
    DEBUG = False
    TESTING = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///scoring_management.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'scoring-jwt-secret-2024'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=8)
    JWT_ALGORITHM = 'HS256'
    
    # 外部服务地址
    USER_MANAGEMENT_URL = os.environ.get('USER_MANAGEMENT_URL') or 'http://localhost:5002'
    EXAM_MANAGEMENT_URL = os.environ.get('EXAM_MANAGEMENT_URL') or 'http://localhost:5003'
    QUESTION_BANK_URL = os.environ.get('QUESTION_BANK_URL') or 'http://localhost:5004'
    STUDENT_EXAM_URL = os.environ.get('STUDENT_EXAM_URL') or 'http://localhost:5005'
    API_GATEWAY_URL = os.environ.get('API_GATEWAY_URL') or 'http://localhost:8080'
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
    
    # 评分相关配置
    GRADING_CONFIG = {
        'min_graders_per_question': 3,  # 每道主观题最少评分人数
        'score_difference_threshold': 5.0,  # 评分差异阈值（分）
        'auto_grading_enabled': True,  # 是否启用自动评分
        'grading_timeout': 3600,  # 评分超时时间（秒）
        'max_grading_tasks_per_user': 50,  # 每个用户最大评分任务数
    }
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/scoring_management.log'
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # CORS配置
    CORS_ORIGINS = ['http://localhost:8000', 'http://localhost:3000', 'http://127.0.0.1:8000']
    CORS_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    CORS_ALLOW_HEADERS = ['Content-Type', 'Authorization', 'X-Requested-With']
    
    # 安全配置
    SECURITY_CONFIG = {
        'password_hash_method': 'pbkdf2:sha256',
        'password_salt_length': 16,
        'session_protection': 'strong',
        'login_attempts_limit': 5,
        'login_attempts_timeout': 300,  # 5分钟
    }
    
    # API配置
    API_CONFIG = {
        'version': 'v1',
        'prefix': '/api/v1',
        'timeout': 30,
        'retry_times': 3,
        'rate_limit': '100/hour',
    }
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        os.makedirs(os.path.dirname(app.config['LOG_FILE']), exist_ok=True)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    
    # 开发环境数据库
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.abspath(__file__)), 'scoring_dev.db')
    
    # 开发环境CORS设置（更宽松）
    CORS_ORIGINS = ['*']

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    LOG_LEVEL = 'WARNING'
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    SECURITY_CONFIG = {
        **Config.SECURITY_CONFIG,
        'session_protection': 'strong',
        'login_attempts_limit': 3,
        'login_attempts_timeout': 600,  # 10分钟
    }
    
    # 生产环境数据库（建议使用PostgreSQL或MySQL）
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:password@localhost/scoring_management'
    
    @staticmethod
    def init_app(app):
        Config.init_app(app)
        
        # 生产环境日志配置
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            file_handler = RotatingFileHandler(
                app.config['LOG_FILE'],
                maxBytes=app.config['LOG_MAX_SIZE'],
                backupCount=app.config['LOG_BACKUP_COUNT']
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('评分管理模块启动')

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    LOG_LEVEL = 'DEBUG'
    
    # 测试数据库（内存数据库）
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # 测试环境禁用CSRF保护
    WTF_CSRF_ENABLED = False
    
    # 测试环境JWT配置（较短的过期时间）
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=15)

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# 获取当前配置
def get_config():
    """获取当前环境配置"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])