#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后的系统启动器
职业技能等级考试系统 - 高性能启动器

功能特性：
- 并行启动机制，大幅提升启动速度
- 智能依赖检查和自动安装
- 模块健康检查和状态监控
- 启动进度显示和错误处理
- 优雅关闭和重启机制
- 资源优化和内存管理

作者：系统架构师
版本：2.0.0
日期：2024-01-15
"""

import os
import sys
import time
import json
import yaml
import signal
import psutil
import asyncio
import logging
import threading
import subprocess
from pathlib import Path
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Tuple, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入自定义模块
try:
    from launcher.startup_manager import StartupManager
    from launcher.health_monitor import HealthMonitor
    from utils.performance import PerformanceOptimizer
    from utils.logger import SystemLogger
except ImportError as e:
    print(f"警告：无法导入某些模块，将使用基础功能: {e}")
    StartupManager = None
    HealthMonitor = None
    PerformanceOptimizer = None
    SystemLogger = None


class OptimizedLauncher:
    """
    优化后的系统启动器
    
    主要功能：
    1. 并行启动所有模块
    2. 智能依赖管理
    3. 实时健康监控
    4. 性能优化
    5. 错误处理和恢复
    """
    
    def __init__(self):
        """初始化启动器"""
        self.project_root = project_root
        self.config_dir = self.project_root / "config"
        self.launcher_dir = self.project_root / "launcher"
        
        # 初始化日志系统
        self.logger = self._init_logger()
        
        # 加载配置
        self.global_config = self._load_global_config()
        self.modules_config = self._load_modules_config()
        
        # 初始化组件
        self.startup_manager = None
        self.health_monitor = None
        self.performance_optimizer = None
        
        # 运行状态
        self.running_processes = {}
        self.startup_start_time = None
        self.is_shutting_down = False
        
        # 注册信号处理器
        self._register_signal_handlers()
        
        self.logger.info("优化启动器初始化完成")
    
    def _init_logger(self) -> logging.Logger:
        """初始化日志系统"""
        if SystemLogger:
            return SystemLogger().get_logger("optimized_launcher")
        else:
            # 基础日志配置
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler('launcher.log', encoding='utf-8')
                ]
            )
            return logging.getLogger("optimized_launcher")
    
    def _load_global_config(self) -> Dict[str, Any]:
        """加载全局配置"""
        config_file = self.config_dir / "global.yaml"
        try:
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.logger.info("全局配置加载成功")
                return config
        except Exception as e:
            self.logger.error(f"加载全局配置失败: {e}")
        
        # 返回默认配置
        return {
            "system": {
                "name": "职业技能等级考试系统",
                "version": "2.0.0",
                "environment": "production",
                "debug": False
            },
            "startup": {
                "parallel_startup": True,
                "max_workers": 8,
                "startup_timeout": 120,
                "health_check_interval": 30
            }
        }
    
    def _load_modules_config(self) -> Dict[str, Any]:
        """加载模块配置"""
        config_file = self.config_dir / "modules.yaml"
        try:
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self.logger.info("模块配置加载成功")
                return config
        except Exception as e:
            self.logger.error(f"加载模块配置失败: {e}")
        
        # 返回默认模块配置
        return self._get_default_modules_config()
    
    def _get_default_modules_config(self) -> Dict[str, Any]:
        """获取默认模块配置"""
        return {
            "modules": {
                "api_gateway": {
                    "name": "API网关",
                    "path": "api-gateway",
                    "port": 8080,
                    "startup_command": "python app.py",
                    "priority": 1,
                    "required": True,
                    "health_check": "/api/health"
                },
                "user_management": {
                    "name": "用户管理",
                    "path": "user-management",
                    "port": 5002,
                    "startup_command": "python run.py",
                    "priority": 2,
                    "required": True,
                    "health_check": "/api/health"
                },
                "question_bank": {
                    "name": "题库管理",
                    "path": "question_bank",
                    "port": 5001,
                    "startup_command": "python app.py",
                    "priority": 2,
                    "required": True,
                    "health_check": "/api/health"
                },
                "exam_management": {
                    "name": "考试管理",
                    "path": "exam_management",
                    "port": 5003,
                    "startup_command": "python app.py",
                    "priority": 3,
                    "required": True,
                    "health_check": "/api/health"
                },
                "file_project_management": {
                    "name": "文件项目管理",
                    "path": "file_project_management",
                    "port": 5011,
                    "startup_command": "python app.py",
                    "priority": 2,
                    "required": True,
                    "health_check": "/api/health"
                }
            }
        }
    
    def _register_signal_handlers(self):
        """注册信号处理器"""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        if hasattr(signal, 'SIGBREAK'):  # Windows
            signal.signal(signal.SIGBREAK, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
        self.shutdown()
    
    def print_banner(self):
        """打印系统横幅"""
        banner = f"""
╔══════════════════════════════════════════════════════════════╗
║                    职业技能等级考试系统                        ║
║                   Optimized Launcher v2.0                   ║
║                                                              ║
║  🚀 高性能并行启动  📊 实时健康监控  ⚡ 智能资源优化          ║
║                                                              ║
║  启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}                           ║
║  系统版本: {self.global_config.get('system', {}).get('version', '2.0.0')}                                        ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        self.logger.info("系统启动横幅显示完成")
    
    def check_system_requirements(self) -> bool:
        """检查系统要求"""
        self.logger.info("开始检查系统要求...")
        
        try:
            # 检查Python版本
            python_version = sys.version_info
            if python_version < (3, 7):
                self.logger.error(f"Python版本过低: {python_version}, 需要3.7+")
                return False
            
            # 检查可用内存
            memory = psutil.virtual_memory()
            if memory.available < 1024 * 1024 * 1024:  # 1GB
                self.logger.warning(f"可用内存较低: {memory.available / 1024 / 1024 / 1024:.1f}GB")
            
            # 检查磁盘空间
            disk = psutil.disk_usage(str(self.project_root))
            if disk.free < 1024 * 1024 * 1024:  # 1GB
                self.logger.warning(f"磁盘空间不足: {disk.free / 1024 / 1024 / 1024:.1f}GB")
            
            self.logger.info("系统要求检查通过")
            return True
            
        except Exception as e:
            self.logger.error(f"系统要求检查失败: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """安装依赖"""
        self.logger.info("开始检查和安装依赖...")
        
        try:
            requirements_file = self.project_root / "requirements.txt"
            if not requirements_file.exists():
                self.logger.warning("requirements.txt文件不存在")
                return True
            
            # 检查pip版本
            result = subprocess.run(
                [sys.executable, "-m", "pip", "--version"],
                capture_output=True, text=True, timeout=30
            )
            
            if result.returncode != 0:
                self.logger.error("pip不可用")
                return False
            
            # 安装依赖
            self.logger.info("正在安装依赖包...")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)],
                capture_output=True, text=True, timeout=300
            )
            
            if result.returncode == 0:
                self.logger.info("依赖安装成功")
                return True
            else:
                self.logger.error(f"依赖安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"依赖安装过程出错: {e}")
            return False
    
    def start_module(self, module_name: str, module_config: Dict[str, Any]) -> Tuple[bool, Optional[subprocess.Popen]]:
        """启动单个模块"""
        try:
            module_path = self.project_root / module_config["path"]
            if not module_path.exists():
                self.logger.error(f"模块路径不存在: {module_path}")
                return False, None
            
            # 构建启动命令
            command = module_config["startup_command"].split()
            
            self.logger.info(f"启动模块: {module_name} ({module_config['name']})")
            
            # 启动进程
            # 设置进程创建标志，避免创建新的终端窗口
            creation_flags = 0
            if os.name == 'nt':  # Windows系统
                creation_flags = subprocess.CREATE_NO_WINDOW
            
            process = subprocess.Popen(
                command,
                cwd=str(module_path),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                creationflags=creation_flags
            )
            
            # 等待一小段时间检查进程是否正常启动
            time.sleep(2)
            
            if process.poll() is None:
                self.logger.info(f"模块 {module_name} 启动成功 (PID: {process.pid})")
                return True, process
            else:
                stdout, stderr = process.communicate()
                self.logger.error(f"模块 {module_name} 启动失败: {stderr}")
                return False, None
                
        except Exception as e:
            self.logger.error(f"启动模块 {module_name} 时出错: {e}")
            return False, None
    
    def parallel_startup(self) -> bool:
        """并行启动所有模块"""
        self.logger.info("开始并行启动模块...")
        self.startup_start_time = time.time()
        
        modules = self.modules_config.get("modules", {})
        if not modules:
            self.logger.error("没有找到模块配置")
            return False
        
        # 按优先级分组
        priority_groups = {}
        for module_name, module_config in modules.items():
            priority = module_config.get("priority", 999)
            if priority not in priority_groups:
                priority_groups[priority] = []
            priority_groups[priority].append((module_name, module_config))
        
        # 按优先级顺序启动
        total_modules = len(modules)
        started_modules = 0
        
        for priority in sorted(priority_groups.keys()):
            group_modules = priority_groups[priority]
            self.logger.info(f"启动优先级 {priority} 的模块组 ({len(group_modules)} 个模块)")
            
            # 并行启动同优先级的模块
            max_workers = min(len(group_modules), self.global_config.get("startup", {}).get("max_workers", 4))
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_module = {
                    executor.submit(self.start_module, module_name, module_config): module_name
                    for module_name, module_config in group_modules
                }
                
                for future in as_completed(future_to_module):
                    module_name = future_to_module[future]
                    try:
                        success, process = future.result()
                        if success and process:
                            self.running_processes[module_name] = process
                            started_modules += 1
                        
                        # 显示进度
                        progress = (started_modules / total_modules) * 100
                        print(f"\r启动进度: {progress:.1f}% ({started_modules}/{total_modules})", end="", flush=True)
                        
                    except Exception as e:
                        self.logger.error(f"启动模块 {module_name} 时出现异常: {e}")
            
            # 等待一段时间再启动下一优先级的模块
            if priority < max(priority_groups.keys()):
                time.sleep(3)
        
        print()  # 换行
        
        startup_time = time.time() - self.startup_start_time
        self.logger.info(f"模块启动完成，耗时: {startup_time:.2f}秒，成功启动: {len(self.running_processes)}/{total_modules}")
        
        return len(self.running_processes) > 0
    
    def start_system(self) -> bool:
        """启动整个系统"""
        try:
            # 打印横幅
            self.print_banner()
            
            # 检查系统要求
            if not self.check_system_requirements():
                return False
            
            # 安装依赖
            if not self.install_dependencies():
                self.logger.warning("依赖安装失败，但继续启动...")
            
            # 初始化组件
            self._init_components()
            
            # 并行启动模块
            if not self.parallel_startup():
                self.logger.error("模块启动失败")
                return False
            
            # 启动健康监控
            if self.health_monitor:
                self.health_monitor.start_monitoring(self.running_processes)
            
            self.logger.info("系统启动完成！")
            print("\n🎉 系统启动成功！所有模块正在运行中...")
            print("\n📊 访问地址:")
            print("   - 主入口: http://localhost:8080")
            print("   - API网关: http://localhost:8080/api")
            print("\n💡 按 Ctrl+C 优雅关闭系统")
            
            return True
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            return False
    
    def _init_components(self):
        """初始化组件"""
        try:
            if StartupManager:
                self.startup_manager = StartupManager(self.modules_config)
            
            if HealthMonitor:
                self.health_monitor = HealthMonitor(self.global_config)
            
            if PerformanceOptimizer:
                self.performance_optimizer = PerformanceOptimizer()
                self.performance_optimizer.optimize_system()
            
            self.logger.info("组件初始化完成")
            
        except Exception as e:
            self.logger.warning(f"组件初始化部分失败: {e}")
    
    def wait_for_shutdown(self):
        """等待关闭信号"""
        try:
            while not self.is_shutting_down:
                time.sleep(1)
                
                # 检查进程状态
                dead_processes = []
                for module_name, process in self.running_processes.items():
                    if process.poll() is not None:
                        dead_processes.append(module_name)
                        self.logger.warning(f"模块 {module_name} 意外退出")
                
                # 移除已退出的进程
                for module_name in dead_processes:
                    del self.running_processes[module_name]
                
                # 如果所有进程都退出了，自动关闭
                if not self.running_processes:
                    self.logger.info("所有模块都已退出，系统自动关闭")
                    break
                    
        except KeyboardInterrupt:
            self.logger.info("接收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"等待关闭时出错: {e}")
    
    def shutdown(self):
        """优雅关闭系统"""
        if self.is_shutting_down:
            return
        
        self.is_shutting_down = True
        self.logger.info("开始关闭系统...")
        
        try:
            # 停止健康监控
            if self.health_monitor:
                self.health_monitor.stop_monitoring()
            
            # 关闭所有模块进程
            for module_name, process in self.running_processes.items():
                try:
                    self.logger.info(f"关闭模块: {module_name}")
                    process.terminate()
                    
                    # 等待进程优雅退出
                    try:
                        process.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        self.logger.warning(f"模块 {module_name} 未能优雅退出，强制终止")
                        process.kill()
                        
                except Exception as e:
                    self.logger.error(f"关闭模块 {module_name} 时出错: {e}")
            
            self.running_processes.clear()
            
            # 清理资源
            if self.performance_optimizer:
                self.performance_optimizer.cleanup()
            
            self.logger.info("系统关闭完成")
            print("\n👋 系统已安全关闭，感谢使用！")
            
        except Exception as e:
            self.logger.error(f"系统关闭时出错: {e}")
    
    def run(self):
        """运行启动器"""
        try:
            if self.start_system():
                self.wait_for_shutdown()
            else:
                self.logger.error("系统启动失败")
                return False
        except Exception as e:
            self.logger.error(f"运行启动器时出错: {e}")
            return False
        finally:
            self.shutdown()
        
        return True


def main():
    """主函数"""
    try:
        launcher = OptimizedLauncher()
        success = launcher.run()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"启动器运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()