#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控审计模块启动脚本
提供系统监控和服务状态检查功能

启动方式：
    python run.py

服务地址：
    - 主服务: http://localhost:5009
    - 健康检查: http://localhost:5009/api/health
    - 系统状态: http://localhost:5009/api/v1/status/system
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def main():
    """
    启动监控审计模块服务
    """
    try:
        print("="*50)
        print("监控审计模块 (Monitoring Service)")
        print("="*50)
        print("服务信息:")
        print("  - 服务名称: 监控审计模块")
        print("  - 版本: v1.0")
        print("  - 端口: 5009")
        print("  - 主机: 0.0.0.0")
        print("")
        print("服务地址:")
        print("  - 主服务: http://localhost:5009")
        print("  - 健康检查: http://localhost:5009/api/health")
        print("  - 系统状态: http://localhost:5009/api/v1/status/system")
        print("")
        print("主要功能:")
        print("  - 系统资源监控")
        print("  - 服务状态检查")
        print("  - 性能指标收集")
        print("  - 健康状态报告")
        print("")
        print("正在启动服务...")
        print("="*50)
        
        # 创建并启动Flask应用
        app = create_app()
        app.run(
            host='0.0.0.0',
            port=5009,
            debug=False,  # 生产环境关闭调试模式
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()