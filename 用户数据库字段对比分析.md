# 用户数据库字段对比分析

## 一、字段对比表

### 1. 用户导入模板字段（中文）vs 接口规范手册字段（英文）

| 序号 | 用户导入模板字段（中文） | 接口规范手册字段（英文） | 数据类型 | 是否必填 | 备注说明 |
|------|------------------------|------------------------|----------|----------|----------|
| 1    | 序号                   | serial_number          | number   | 否       | 自增序号，非唯一 |
| 2    | 用户名                 | user_name              | string   | 是       | 主键，登录账号，唯一，长度1-50 |
| 3    | 姓名                   | real_name              | string   | 是       | 真实姓名，长度1-50 |
| 4    | 身份证号码             | id_card                | string   | 是       | 18位，加密存储 |
| 5    | 角色                   | role                   | string   | 是       | 枚举值：admin/expert/student/grader/internal_supervisor |
| 6    | 性别                   | gender                 | string   | 否       | 枚举值：male/female/unknown |
| 7    | 出生日期               | date_of_birth          | string   | 否       | 格式：yyyy-mm-dd |
| 8    | 户籍所在地             | household_registration_address | string | 否 | 长度1-200 |
| 9    | 文化程度               | educational_level      | string   | 否       | 枚举值：博士研究生/硕士研究生等 |
| 10   | 申报职业（工种）       | declared_occupation    | string   | 否       | 长度1-100 |
| 11   | 申报级别               | declared_level         | string   | 否       | 枚举值：五级/四级/三级/二级/一级 |
| 12   | 考试类型               | exam_type              | string   | 否       | 枚举值：新考/重考/补考 |
| 13   | 考核科目               | assessment_subject     | string   | 否       | 枚举值：理论/实操/答辩 |
| 14   | 座次序号               | seat_serial_number     | number   | 否       | 整数 |
| 15   | 考试日期               | exam_date              | string   | 否       | 格式：yyyy-MM-dd |
| 16   | 考试期数               | exam_session           | string   | 否       | 长度1-20，如"2025第1期" |
| 17   | 座位号                 | seat_number            | string   | 否       | 长度1-10，如"A01" |
| 18   | 邮箱                   | email                  | string   | 否       | 符合邮箱格式 |
| 19   | 密码                   | password               | string   | 是       | 加密存储，长度≥6 |
| 20   | 手机号码               | phone                  | string   | 是       | 11位数字 |
| 21   | 工作单位               | company                | string   | 否       | 长度1-200 |
| 22   | 部门                   | department             | string   | 否       | 长度1-100 |
| 23   | 状态                   | status                 | string   | 否       | 枚举值：待审核/审核通过/审核未通过 |
| 24   | 头像照片链接           | avatar_photo_link      | string   | 否       | 局域网路径 |
| 25   | 身份证照片链接         | id_card_photo_link     | string   | 否       | 局域网路径 |
| 26   | 毕业证照片链接         | graduation_certificate_photo_link | string | 否 | 局域网路径 |
| 27   | 技能证书照片链接       | skill_certificate_photo_link | string | 否 | 局域网路径 |
| 28   | 题库名称               | question_bank_name     | string   | 否       | 长度1-100 |
| 29   | 试卷ID                 | test_paper_id          | string   | 否       | 长度1-32 |

## 二、差异分析

### 1. 字段完全匹配
用户导入模板的所有29个字段在接口规范手册中都有对应的英文字段定义，**无缺失字段**。

### 2. 接口规范手册中缺失的字段
接口规范手册中定义了一个用户导入模板中没有的字段：
- `major`（所学专业）：string类型，长度1-100，初中、高中、其他无需填

### 3. 字段命名规范
- **中文字段名**：用户导入模板使用中文字段名，便于用户理解和填写
- **英文字段名**：接口规范手册使用英文字段名，采用"全小写+下划线连接"格式，符合数据库命名规范

### 4. 数据类型和约束
所有字段的数据类型和约束条件在两个文档中保持一致，包括：
- 必填字段标识
- 字符串长度限制
- 枚举值定义
- 数据格式要求

## 三、建议

### 1. 字段补充建议
考虑在用户导入模板中增加"所学专业"字段，以保持与接口规范手册的完全一致性。

### 2. 系统实现建议
1. **字段映射**：在系统导入功能中建立中英文字段名的映射关系
2. **数据验证**：严格按照接口规范手册中的约束条件进行数据验证
3. **错误提示**：使用中文字段名进行错误提示，提升用户体验

### 4. 结论
用户导入模板与接口规范手册的字段定义高度一致，仅缺少"所学专业"一个字段。建议以用户导入模板为准，因为它更贴近实际业务需求和用户使用习惯。

---

**文档版本**：V1.0  
**更新时间**：2025年8月4日  
**对比基准**：用户导入模板.xlsx vs 03-《接口规范手册》.md