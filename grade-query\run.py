# -*- coding: utf-8 -*-
"""
成绩查询模块启动脚本

功能说明:
- 配置日志系统
- 初始化数据库
- 启动Flask应用
- 处理Windows编码兼容性
"""

import os
import sys
import logging
from datetime import datetime
from app import create_app, db, safe_print

# 确保logs目录存在
if not os.path.exists('logs'):
    os.makedirs('logs')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/grade_query.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class Config:
    """应用配置类"""
    SECRET_KEY = 'grade_query_secret_key_2024'
    SQLALCHEMY_DATABASE_URI = 'sqlite:///grade_query.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    PERMANENT_SESSION_LIFETIME = 7200  # 2小时
    
    # 数据库配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
    }

def init_database(app):
    """初始化数据库"""
    try:
        with app.app_context():
            # 创建所有表
            db.create_all()
            safe_print("[+] 数据库初始化完成")
            
            # 检查表是否创建成功
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            safe_print(f"[+] 已创建数据表: {', '.join(tables)}")
            
    except Exception as e:
        safe_print(f"[!] 数据库初始化失败: {e}")
        sys.exit(1)

def main():
    """主函数"""
    try:
        # 设置Windows控制台编码
        if sys.platform.startswith('win'):
            os.system('chcp 65001 > nul')
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
        
        safe_print("[*] 成绩查询模块启动中...")
        
        # 创建应用实例
        app = create_app(Config)
        
        # 配置日志
        if not app.debug:
            file_handler = logging.FileHandler('logs/grade_query.log', encoding='utf-8')
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s %(name)s %(message)s'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(logging.INFO)
            app.logger.info('成绩查询模块启动')
        
        # 初始化数据库
        init_database(app)
        
        # 打印启动信息
        safe_print("[*] " + "="*50)
        safe_print("[*] 成绩查询模块 v1.0.0")
        safe_print("[*] " + "="*50)
        safe_print("[*] 服务地址: http://0.0.0.0:5007")
        safe_print("[*] 登录页面: http://localhost:5007")
        safe_print("[*] 成绩查询: http://localhost:5007/grades")
        safe_print("[*] API文档: http://localhost:5007/api/health")
        safe_print("[*] 健康检查: http://localhost:5007/api/health")
        safe_print("[*] " + "="*50)
        safe_print("[*] 主要功能:")
        safe_print("[*] - 成绩查询和统计分析")
        safe_print("[*] - 成绩导出功能")
        safe_print("[*] - 通过率统计")
        safe_print("[*] - 成绩分布分析")
        safe_print("[*] " + "="*50)
        safe_print("[*] 按 Ctrl+C 停止服务")
        safe_print("[*] " + "="*50)
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=5007,
            debug=True,
            use_reloader=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        safe_print("\n[*] 服务已停止")
    except Exception as e:
        safe_print(f"[!] 启动失败: {e}")
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()