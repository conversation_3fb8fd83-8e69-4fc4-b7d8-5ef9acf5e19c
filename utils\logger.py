#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一日志系统
职业技能等级考试系统 - 统一日志管理

功能特性：
- 分级日志记录和管理
- 自动日志轮转和压缩
- 结构化日志格式
- 性能日志分析
- 错误追踪和报告
- 多输出目标支持

作者：系统架构师
版本：2.0.0
日期：2024-01-15
"""

import os
import sys
import json
import time
import gzip
import shutil
import logging
import threading
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, TextIO
from dataclasses import dataclass, field, asdict
from enum import Enum
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
from collections import defaultdict, deque
import traceback
import inspect


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogFormat(Enum):
    """日志格式枚举"""
    SIMPLE = "simple"
    DETAILED = "detailed"
    JSON = "json"
    STRUCTURED = "structured"


@dataclass
class LogEntry:
    """日志条目数据类"""
    timestamp: datetime
    level: str
    logger_name: str
    module: str
    function: str
    line_number: int
    message: str
    extra_data: Dict[str, Any] = field(default_factory=dict)
    exception_info: Optional[str] = None
    process_id: int = 0
    thread_id: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    def to_json(self) -> str:
        """转换为JSON字符串"""
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=None)


@dataclass
class LogStats:
    """日志统计数据类"""
    total_logs: int = 0
    debug_count: int = 0
    info_count: int = 0
    warning_count: int = 0
    error_count: int = 0
    critical_count: int = 0
    start_time: datetime = field(default_factory=datetime.now)
    
    def increment(self, level: str):
        """增加计数"""
        self.total_logs += 1
        level_lower = level.lower()
        if level_lower == 'debug':
            self.debug_count += 1
        elif level_lower == 'info':
            self.info_count += 1
        elif level_lower == 'warning':
            self.warning_count += 1
        elif level_lower == 'error':
            self.error_count += 1
        elif level_lower == 'critical':
            self.critical_count += 1
    
    def get_error_rate(self) -> float:
        """获取错误率"""
        if self.total_logs == 0:
            return 0.0
        return (self.error_count + self.critical_count) / self.total_logs * 100


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, format_type: LogFormat = LogFormat.STRUCTURED, include_extra: bool = True):
        super().__init__()
        self.format_type = format_type
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 获取调用信息
        frame = inspect.currentframe()
        try:
            # 向上查找到实际的调用位置
            while frame and frame.f_code.co_filename.endswith(('logging/__init__.py', 'logger.py')):
                frame = frame.f_back
            
            if frame:
                module_name = frame.f_globals.get('__name__', 'unknown')
                function_name = frame.f_code.co_name
                line_number = frame.f_lineno
            else:
                module_name = record.module
                function_name = record.funcName
                line_number = record.lineno
        finally:
            del frame
        
        # 创建日志条目
        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created),
            level=record.levelname,
            logger_name=record.name,
            module=module_name,
            function=function_name,
            line_number=line_number,
            message=record.getMessage(),
            process_id=record.process,
            thread_id=record.thread
        )
        
        # 添加异常信息
        if record.exc_info:
            log_entry.exception_info = self.formatException(record.exc_info)
        
        # 添加额外数据
        if self.include_extra and hasattr(record, '__dict__'):
            extra_keys = set(record.__dict__.keys()) - {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'exc_info', 'exc_text', 'stack_info'
            }
            
            for key in extra_keys:
                log_entry.extra_data[key] = getattr(record, key)
        
        # 根据格式类型返回相应格式
        if self.format_type == LogFormat.JSON:
            return log_entry.to_json()
        elif self.format_type == LogFormat.SIMPLE:
            return f"[{log_entry.timestamp.strftime('%H:%M:%S')}] {log_entry.level}: {log_entry.message}"
        elif self.format_type == LogFormat.DETAILED:
            return (
                f"[{log_entry.timestamp.strftime('%Y-%m-%d %H:%M:%S')}] "
                f"{log_entry.level} - {log_entry.module}.{log_entry.function}:{log_entry.line_number} - "
                f"{log_entry.message}"
            )
        else:  # STRUCTURED
            structured_msg = (
                f"[{log_entry.timestamp.strftime('%Y-%m-%d %H:%M:%S')}] "
                f"{log_entry.level:<8} | {log_entry.logger_name:<20} | "
                f"{log_entry.module}.{log_entry.function}:{log_entry.line_number} | "
                f"{log_entry.message}"
            )
            
            if log_entry.extra_data:
                extra_str = json.dumps(log_entry.extra_data, ensure_ascii=False)
                structured_msg += f" | Extra: {extra_str}"
            
            if log_entry.exception_info:
                structured_msg += f"\n{log_entry.exception_info}"
            
            return structured_msg


class LogRotationHandler(RotatingFileHandler):
    """自定义日志轮转处理器"""
    
    def __init__(self, filename: str, max_bytes: int = 10*1024*1024, backup_count: int = 5, 
                 compress: bool = True, encoding: str = 'utf-8'):
        super().__init__(filename, maxBytes=max_bytes, backupCount=backup_count, encoding=encoding)
        self.compress = compress
    
    def doRollover(self):
        """执行日志轮转"""
        super().doRollover()
        
        # 压缩旧日志文件
        if self.compress:
            self._compress_old_logs()
    
    def _compress_old_logs(self):
        """压缩旧日志文件"""
        try:
            base_filename = self.baseFilename
            for i in range(1, self.backupCount + 1):
                old_log = f"{base_filename}.{i}"
                compressed_log = f"{old_log}.gz"
                
                if os.path.exists(old_log) and not os.path.exists(compressed_log):
                    with open(old_log, 'rb') as f_in:
                        with gzip.open(compressed_log, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    os.remove(old_log)
        except Exception as e:
            # 压缩失败不应该影响日志记录
            print(f"日志压缩失败: {e}")


class PerformanceLogger:
    """性能日志记录器"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self.performance_data: deque = deque(maxlen=1000)
        self.lock = threading.Lock()
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能数据"""
        with self.lock:
            perf_data = {
                'timestamp': datetime.now().isoformat(),
                'operation': operation,
                'duration_ms': duration * 1000,
                **kwargs
            }
            
            self.performance_data.append(perf_data)
            
            # 记录到日志
            self.logger.info(
                f"性能记录: {operation} 耗时 {duration*1000:.2f}ms",
                extra={'performance_data': perf_data}
            )
    
    def get_performance_stats(self, hours: int = 1) -> Dict[str, Any]:
        """获取性能统计"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            recent_data = [
                data for data in self.performance_data
                if datetime.fromisoformat(data['timestamp']) >= cutoff_time
            ]
            
            if not recent_data:
                return {'error': '没有性能数据'}
            
            # 按操作分组统计
            operation_stats = defaultdict(list)
            for data in recent_data:
                operation_stats[data['operation']].append(data['duration_ms'])
            
            stats = {
                'time_range_hours': hours,
                'total_operations': len(recent_data),
                'operations': {}
            }
            
            for operation, durations in operation_stats.items():
                stats['operations'][operation] = {
                    'count': len(durations),
                    'avg_ms': sum(durations) / len(durations),
                    'max_ms': max(durations),
                    'min_ms': min(durations),
                    'total_ms': sum(durations)
                }
            
            return stats


class ErrorTracker:
    """错误追踪器"""
    
    def __init__(self, logger: logging.Logger, max_errors: int = 100):
        self.logger = logger
        self.max_errors = max_errors
        self.errors: deque = deque(maxlen=max_errors)
        self.error_counts = defaultdict(int)
        self.lock = threading.Lock()
    
    def track_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """追踪错误"""
        with self.lock:
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'error_type': type(error).__name__,
                'error_message': str(error),
                'traceback': traceback.format_exc(),
                'context': context or {}
            }
            
            self.errors.append(error_info)
            self.error_counts[error_info['error_type']] += 1
            
            # 记录到日志
            self.logger.error(
                f"错误追踪: {error_info['error_type']}: {error_info['error_message']}",
                extra={'error_info': error_info},
                exc_info=True
            )
    
    def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误摘要"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            recent_errors = [
                error for error in self.errors
                if datetime.fromisoformat(error['timestamp']) >= cutoff_time
            ]
            
            # 统计错误类型
            error_type_counts = defaultdict(int)
            for error in recent_errors:
                error_type_counts[error['error_type']] += 1
            
            return {
                'time_range_hours': hours,
                'total_errors': len(recent_errors),
                'error_types': dict(error_type_counts),
                'recent_errors': recent_errors[-10:] if recent_errors else []
            }


class UnifiedLogger:
    """
    统一日志系统
    
    提供完整的日志管理功能：
    - 多级别日志记录
    - 多输出目标
    - 自动轮转和压缩
    - 性能监控
    - 错误追踪
    - 统计分析
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化统一日志系统"""
        self.config = config
        self.loggers: Dict[str, logging.Logger] = {}
        self.stats = LogStats()
        self.performance_logger: Optional[PerformanceLogger] = None
        self.error_tracker: Optional[ErrorTracker] = None
        self.lock = threading.Lock()
        
        # 创建日志目录
        self.log_dir = Path(config.get('log_dir', 'logs'))
        self.log_dir.mkdir(exist_ok=True)
        
        # 初始化主日志器
        self._init_main_logger()
        
        # 初始化性能日志器
        if config.get('enable_performance_logging', True):
            self.performance_logger = PerformanceLogger(self.get_logger('performance'))
        
        # 初始化错误追踪器
        if config.get('enable_error_tracking', True):
            self.error_tracker = ErrorTracker(self.get_logger('error_tracker'))
    
    def _init_main_logger(self):
        """初始化主日志器"""
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, self.config.get('level', 'INFO')))
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 添加控制台处理器
        if self.config.get('console_output', True):
            console_handler = logging.StreamHandler(sys.stdout)
            console_formatter = StructuredFormatter(
                format_type=LogFormat(self.config.get('console_format', 'structured'))
            )
            console_handler.setFormatter(console_formatter)
            console_handler.setLevel(getattr(logging, self.config.get('console_level', 'INFO')))
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self.config.get('file_output', True):
            file_config = self.config.get('file', {})
            log_file = self.log_dir / file_config.get('filename', 'system.log')
            
            file_handler = LogRotationHandler(
                filename=str(log_file),
                max_bytes=file_config.get('max_size', 10*1024*1024),
                backup_count=file_config.get('backup_count', 5),
                compress=file_config.get('compress', True)
            )
            
            file_formatter = StructuredFormatter(
                format_type=LogFormat(file_config.get('format', 'structured'))
            )
            file_handler.setFormatter(file_formatter)
            file_handler.setLevel(getattr(logging, file_config.get('level', 'DEBUG')))
            root_logger.addHandler(file_handler)
        
        # 添加自定义过滤器
        root_logger.addFilter(self._log_filter)
    
    def _log_filter(self, record: logging.LogRecord) -> bool:
        """日志过滤器"""
        # 更新统计信息
        self.stats.increment(record.levelname)
        
        # 可以在这里添加自定义过滤逻辑
        return True
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        with self.lock:
            if name not in self.loggers:
                logger = logging.getLogger(name)
                
                # 如果需要为特定模块创建独立的文件处理器
                module_config = self.config.get('modules', {}).get(name)
                if module_config and module_config.get('separate_file', False):
                    self._add_module_file_handler(logger, name, module_config)
                
                self.loggers[name] = logger
            
            return self.loggers[name]
    
    def _add_module_file_handler(self, logger: logging.Logger, module_name: str, config: Dict[str, Any]):
        """为模块添加独立的文件处理器"""
        log_file = self.log_dir / f"{module_name}.log"
        
        file_handler = LogRotationHandler(
            filename=str(log_file),
            max_bytes=config.get('max_size', 5*1024*1024),
            backup_count=config.get('backup_count', 3),
            compress=config.get('compress', True)
        )
        
        formatter = StructuredFormatter(
            format_type=LogFormat(config.get('format', 'structured'))
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(getattr(logging, config.get('level', 'DEBUG')))
        
        logger.addHandler(file_handler)
        logger.setLevel(getattr(logging, config.get('level', 'DEBUG')))
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能数据"""
        if self.performance_logger:
            self.performance_logger.log_performance(operation, duration, **kwargs)
    
    def track_error(self, error: Exception, context: Optional[Dict[str, Any]] = None):
        """追踪错误"""
        if self.error_tracker:
            self.error_tracker.track_error(error, context)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        uptime = datetime.now() - self.stats.start_time
        
        stats_dict = {
            'uptime': str(uptime),
            'total_logs': self.stats.total_logs,
            'error_rate': self.stats.get_error_rate(),
            'level_counts': {
                'debug': self.stats.debug_count,
                'info': self.stats.info_count,
                'warning': self.stats.warning_count,
                'error': self.stats.error_count,
                'critical': self.stats.critical_count
            }
        }
        
        # 添加性能统计
        if self.performance_logger:
            stats_dict['performance'] = self.performance_logger.get_performance_stats()
        
        # 添加错误统计
        if self.error_tracker:
            stats_dict['errors'] = self.error_tracker.get_error_summary()
        
        return stats_dict
    
    def generate_report(self) -> str:
        """生成日志报告"""
        stats = self.get_stats()
        
        report = []
        report.append("=" * 60)
        report.append("系统日志分析报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"系统运行时间: {stats['uptime']}")
        report.append(f"总日志条数: {stats['total_logs']}")
        report.append(f"错误率: {stats['error_rate']:.2f}%")
        report.append("")
        
        # 日志级别统计
        report.append("日志级别统计:")
        for level, count in stats['level_counts'].items():
            percentage = (count / stats['total_logs'] * 100) if stats['total_logs'] > 0 else 0
            report.append(f"  {level.upper()}: {count} ({percentage:.1f}%)")
        report.append("")
        
        # 性能统计
        if 'performance' in stats and 'operations' in stats['performance']:
            report.append("性能统计 (最近1小时):")
            for operation, perf_data in stats['performance']['operations'].items():
                report.append(f"  {operation}:")
                report.append(f"    调用次数: {perf_data['count']}")
                report.append(f"    平均耗时: {perf_data['avg_ms']:.2f}ms")
                report.append(f"    最大耗时: {perf_data['max_ms']:.2f}ms")
            report.append("")
        
        # 错误统计
        if 'errors' in stats and stats['errors']['total_errors'] > 0:
            report.append("错误统计 (最近24小时):")
            report.append(f"  总错误数: {stats['errors']['total_errors']}")
            report.append("  错误类型分布:")
            for error_type, count in stats['errors']['error_types'].items():
                report.append(f"    {error_type}: {count}")
            report.append("")
        
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        cutoff_date = datetime.now() - timedelta(days=days)
        cleaned_count = 0
        
        try:
            for log_file in self.log_dir.glob('*.log*'):
                if log_file.stat().st_mtime < cutoff_date.timestamp():
                    log_file.unlink()
                    cleaned_count += 1
            
            self.get_logger('system').info(f"清理了 {cleaned_count} 个旧日志文件")
            
        except Exception as e:
            self.get_logger('system').error(f"清理旧日志文件失败: {e}")
    
    def shutdown(self):
        """关闭日志系统"""
        # 刷新所有处理器
        for logger in self.loggers.values():
            for handler in logger.handlers:
                handler.flush()
                handler.close()
        
        # 关闭根日志器的处理器
        root_logger = logging.getLogger()
        for handler in root_logger.handlers:
            handler.flush()
            handler.close()
        
        self.get_logger('system').info("日志系统已关闭")


# 性能监控装饰器
def log_performance(operation_name: Optional[str] = None, logger_name: str = 'performance'):
    """性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            operation = operation_name or f"{func.__module__}.{func.__name__}"
            
            try:
                result = func(*args, **kwargs)
                success = True
            except Exception as e:
                result = e
                success = False
            
            duration = time.time() - start_time
            
            # 记录性能数据
            logger = logging.getLogger(logger_name)
            logger.info(
                f"操作 {operation} 完成",
                extra={
                    'operation': operation,
                    'duration_ms': duration * 1000,
                    'success': success,
                    'args_count': len(args),
                    'kwargs_count': len(kwargs)
                }
            )
            
            if not success:
                raise result
            
            return result
        
        return wrapper
    return decorator


# 错误追踪装饰器
def track_errors(context: Optional[Dict[str, Any]] = None, logger_name: str = 'error_tracker'):
    """错误追踪装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 构建上下文信息
                error_context = {
                    'function': f"{func.__module__}.{func.__name__}",
                    'args_count': len(args),
                    'kwargs_count': len(kwargs)
                }
                
                if context:
                    error_context.update(context)
                
                # 记录错误
                logger = logging.getLogger(logger_name)
                logger.error(
                    f"函数 {func.__name__} 执行出错: {str(e)}",
                    extra={'error_context': error_context},
                    exc_info=True
                )
                
                raise
        
        return wrapper
    return decorator


# 全局日志系统实例
_global_logger_system: Optional[UnifiedLogger] = None


def init_logging(config: Dict[str, Any]) -> UnifiedLogger:
    """初始化全局日志系统"""
    global _global_logger_system
    _global_logger_system = UnifiedLogger(config)
    return _global_logger_system


def get_logger(name: str = 'root') -> logging.Logger:
    """获取日志器"""
    if _global_logger_system:
        return _global_logger_system.get_logger(name)
    else:
        # 如果没有初始化，返回标准日志器
        return logging.getLogger(name)


def log_performance_global(operation: str, duration: float, **kwargs):
    """全局性能日志记录"""
    if _global_logger_system:
        _global_logger_system.log_performance(operation, duration, **kwargs)


def track_error_global(error: Exception, context: Optional[Dict[str, Any]] = None):
    """全局错误追踪"""
    if _global_logger_system:
        _global_logger_system.track_error(error, context)


def get_logging_stats() -> Dict[str, Any]:
    """获取全局日志统计"""
    if _global_logger_system:
        return _global_logger_system.get_stats()
    return {}


def shutdown_logging():
    """关闭全局日志系统"""
    if _global_logger_system:
        _global_logger_system.shutdown()