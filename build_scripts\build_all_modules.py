# -*- coding: utf-8 -*-
"""
所有模块 PyInstaller 构建脚本

功能说明：
- 统一构建用户管理模块、题库管理模块和成绩上报模块
- 支持选择性构建或全部构建
- 提供构建进度显示和错误处理
- 生成构建报告和使用说明

使用方法：
1. 构建所有模块: python build_scripts/build_all_modules.py
2. 构建指定模块: python build_scripts/build_all_modules.py --modules user_management,question_bank
3. 查看帮助: python build_scripts/build_all_modules.py --help

作者: 系统构建脚本
日期: 2025-01-17
"""

import os
import sys
import time
import argparse
import subprocess
from pathlib import Path
from datetime import datetime

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
BUILD_SCRIPTS_DIR = PROJECT_ROOT / "build_scripts"
DIST_DIR = PROJECT_ROOT / "dist"

# 模块构建脚本映射
MODULE_SCRIPTS = {
    "user_management": "build_user_management.py",
    "question_bank": "build_question_bank.py",
    "exam_score_reporting": "build_exam_score_reporting.py"
}

# 模块中文名称映射
MODULE_NAMES = {
    "user_management": "用户管理模块",
    "question_bank": "题库管理模块",
    "exam_score_reporting": "成绩上报模块"
}

def safe_print(message):
    """
    安全打印函数，处理 Windows 控制台编码问题
    
    参数:
        message (str): 要打印的消息
    """
    try:
        print(message)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_message = message.encode('ascii', 'ignore').decode('ascii')
        print(safe_message)

def print_banner():
    """
    打印构建脚本横幅
    """
    safe_print("="*80)
    safe_print("局域网在线考试系统 - 模块构建工具")
    safe_print("PyInstaller 批量构建脚本")
    safe_print("="*80)
    safe_print(f"构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    safe_print(f"项目目录: {PROJECT_ROOT}")
    safe_print("="*80)

def check_build_scripts():
    """
    检查所有构建脚本是否存在
    
    返回:
        bool: 检查是否通过
    """
    safe_print("[*] 检查构建脚本...")
    
    missing_scripts = []
    for module, script in MODULE_SCRIPTS.items():
        script_path = BUILD_SCRIPTS_DIR / script
        if script_path.exists():
            safe_print(f"[+] {MODULE_NAMES[module]}: {script}")
        else:
            safe_print(f"[!] 缺失: {script}")
            missing_scripts.append(script)
    
    if missing_scripts:
        safe_print(f"[!] 错误: 缺失构建脚本: {', '.join(missing_scripts)}")
        return False
    
    safe_print("[+] 所有构建脚本检查通过")
    return True

def build_module(module_name):
    """
    构建指定模块
    
    参数:
        module_name (str): 模块名称
    
    返回:
        bool: 构建是否成功
    """
    if module_name not in MODULE_SCRIPTS:
        safe_print(f"[!] 错误: 未知模块 '{module_name}'")
        return False
    
    script_name = MODULE_SCRIPTS[module_name]
    module_display_name = MODULE_NAMES[module_name]
    script_path = BUILD_SCRIPTS_DIR / script_name
    
    safe_print(f"\n[*] 开始构建 {module_display_name}...")
    safe_print(f"[*] 执行脚本: {script_name}")
    
    start_time = time.time()
    
    try:
        # 执行构建脚本
        result = subprocess.run(
            [sys.executable, str(script_path)],
            cwd=PROJECT_ROOT,
            capture_output=True,
            text=True
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            safe_print(f"[+] {module_display_name} 构建成功 (耗时: {duration:.1f}秒)")
            
            # 显示部分输出信息
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                # 显示最后几行重要信息
                for line in lines[-5:]:
                    if line.strip() and ('[+]' in line or '[*]' in line):
                        safe_print(f"    {line.strip()}")
            
            return True
        else:
            safe_print(f"[!] {module_display_name} 构建失败 (耗时: {duration:.1f}秒)")
            
            # 显示错误信息
            if result.stderr:
                safe_print("[!] 错误输出:")
                for line in result.stderr.strip().split('\n')[-10:]:  # 显示最后10行错误
                    if line.strip():
                        safe_print(f"    {line.strip()}")
            
            return False
            
    except Exception as e:
        safe_print(f"[!] {module_display_name} 构建过程中发生异常: {e}")
        return False

def generate_build_report(results):
    """
    生成构建报告
    
    参数:
        results (dict): 构建结果字典
    """
    safe_print("\n" + "="*80)
    safe_print("构建报告")
    safe_print("="*80)
    
    successful_builds = []
    failed_builds = []
    
    for module, success in results.items():
        module_name = MODULE_NAMES[module]
        if success:
            successful_builds.append(module_name)
            safe_print(f"[+] {module_name}: 构建成功")
        else:
            failed_builds.append(module_name)
            safe_print(f"[!] {module_name}: 构建失败")
    
    safe_print(f"\n总计: {len(results)} 个模块")
    safe_print(f"成功: {len(successful_builds)} 个")
    safe_print(f"失败: {len(failed_builds)} 个")
    
    if successful_builds:
        safe_print("\n[+] 构建成功的模块:")
        for module in successful_builds:
            safe_print(f"    - {module}")
    
    if failed_builds:
        safe_print("\n[!] 构建失败的模块:")
        for module in failed_builds:
            safe_print(f"    - {module}")
    
    # 显示输出目录信息
    if successful_builds:
        safe_print("\n[*] 输出目录:")
        for module, success in results.items():
            if success:
                module_dist_dir = DIST_DIR / module
                if module_dist_dir.exists():
                    safe_print(f"    {MODULE_NAMES[module]}: {module_dist_dir}")
    
    safe_print("="*80)

def create_usage_guide():
    """
    创建使用指南文件
    """
    safe_print("[*] 创建使用指南...")
    
    usage_file = DIST_DIR / "使用指南.txt"
    
    try:
        with open(usage_file, 'w', encoding='utf-8') as f:
            f.write("局域网在线考试系统 - 独立模块使用指南\n")
            f.write("="*50 + "\n\n")
            
            f.write("系统架构说明:\n")
            f.write("-" * 20 + "\n")
            f.write("本系统采用模块化架构，包含以下独立模块:\n\n")
            
            f.write("1. 用户管理模块 (user_management)\n")
            f.write("   - 功能: 用户注册、登录、权限管理\n")
            f.write("   - 端口: 8001\n")
            f.write("   - 启动: 运行 user_management/user_management.exe\n\n")
            
            f.write("2. 题库管理模块 (question_bank)\n")
            f.write("   - 功能: 试题管理、题库维护、考试配置\n")
            f.write("   - 端口: 8002\n")
            f.write("   - 启动: 运行 question_bank/question_bank.exe\n\n")
            
            f.write("3. 成绩上报模块 (exam_score_reporting)\n")
            f.write("   - 功能: 成绩收集、数据上报、结果统计\n")
            f.write("   - 端口: 8003\n")
            f.write("   - 启动: 运行 exam_score_reporting/exam_score_reporting.exe\n")
            f.write("   - 特别说明: 这是系统中唯一允许连接互联网的模块\n\n")
            
            f.write("使用方式:\n")
            f.write("-" * 20 + "\n")
            f.write("方式一: 集成系统运行\n")
            f.write("  - 运行主启动器: python main_launcher.py\n")
            f.write("  - 通过主控台统一管理所有模块\n")
            f.write("  - 提供统一的用户认证和权限控制\n\n")
            
            f.write("方式二: 独立模块运行\n")
            f.write("  - 分别启动各个模块的 .exe 文件\n")
            f.write("  - 每个模块独立运行，互不干扰\n")
            f.write("  - 适用于分布式部署场景\n\n")
            
            f.write("配置要求:\n")
            f.write("-" * 20 + "\n")
            f.write("1. 确保各模块的端口不冲突\n")
            f.write("2. 配置各模块的 .env 文件\n")
            f.write("3. 检查数据库连接配置\n")
            f.write("4. 验证网络和防火墙设置\n\n")
            
            f.write("安全注意事项:\n")
            f.write("-" * 20 + "\n")
            f.write("1. 用户管理模块和题库管理模块仅限局域网访问\n")
            f.write("2. 成绩上报模块是唯一允许互联网连接的组件\n")
            f.write("3. 所有模块间通信应使用加密通道\n")
            f.write("4. 定期备份数据库和配置文件\n")
            f.write("5. 监控系统日志和访问记录\n\n")
            
            f.write("故障排除:\n")
            f.write("-" * 20 + "\n")
            f.write("1. 检查端口占用: netstat -an | findstr :端口号\n")
            f.write("2. 查看模块日志文件\n")
            f.write("3. 验证配置文件格式\n")
            f.write("4. 检查数据库连接状态\n")
            f.write("5. 确认防火墙和网络设置\n\n")
            
            f.write(f"构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("版本: 1.0.0\n")
        
        safe_print(f"[+] 使用指南已创建: {usage_file}")
        
    except Exception as e:
        safe_print(f"[!] 创建使用指南失败: {e}")

def parse_arguments():
    """
    解析命令行参数
    
    返回:
        argparse.Namespace: 解析后的参数
    """
    parser = argparse.ArgumentParser(
        description="局域网在线考试系统模块构建工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python build_all_modules.py                                    # 构建所有模块
  python build_all_modules.py --modules user_management          # 只构建用户管理模块
  python build_all_modules.py --modules user_management,question_bank  # 构建指定模块
  python build_all_modules.py --clean                            # 清理后构建所有模块

可用模块:
  user_management     - 用户管理模块
  question_bank       - 题库管理模块
  exam_score_reporting - 成绩上报模块
        """
    )
    
    parser.add_argument(
        "--modules",
        type=str,
        help="指定要构建的模块，用逗号分隔 (默认: 所有模块)"
    )
    
    parser.add_argument(
        "--clean",
        action="store_true",
        help="构建前清理 dist 目录"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="显示详细输出"
    )
    
    return parser.parse_args()

def main():
    """
    主函数：执行完整的构建流程
    
    返回:
        int: 退出代码（0表示成功，1表示失败）
    """
    args = parse_arguments()
    
    print_banner()
    
    # 检查构建脚本
    if not check_build_scripts():
        return 1
    
    # 确定要构建的模块
    if args.modules:
        modules_to_build = [m.strip() for m in args.modules.split(',')]
        # 验证模块名称
        invalid_modules = [m for m in modules_to_build if m not in MODULE_SCRIPTS]
        if invalid_modules:
            safe_print(f"[!] 错误: 无效的模块名称: {', '.join(invalid_modules)}")
            safe_print(f"[!] 可用模块: {', '.join(MODULE_SCRIPTS.keys())}")
            return 1
    else:
        modules_to_build = list(MODULE_SCRIPTS.keys())
    
    safe_print(f"\n[*] 计划构建模块: {', '.join([MODULE_NAMES[m] for m in modules_to_build])}")
    
    # 清理 dist 目录（如果指定）
    if args.clean and DIST_DIR.exists():
        safe_print("[*] 清理 dist 目录...")
        import shutil
        shutil.rmtree(DIST_DIR)
        safe_print("[+] dist 目录已清理")
    
    # 构建模块
    results = {}
    total_start_time = time.time()
    
    for i, module in enumerate(modules_to_build, 1):
        safe_print(f"\n{'='*60}")
        safe_print(f"构建进度: {i}/{len(modules_to_build)} - {MODULE_NAMES[module]}")
        safe_print(f"{'='*60}")
        
        success = build_module(module)
        results[module] = success
        
        if not success:
            safe_print(f"[!] {MODULE_NAMES[module]} 构建失败，继续构建其他模块...")
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 生成构建报告
    generate_build_report(results)
    
    # 创建使用指南
    if any(results.values()):
        create_usage_guide()
    
    safe_print(f"\n[*] 总构建时间: {total_duration:.1f}秒")
    
    # 确定退出代码
    if all(results.values()):
        safe_print("[+] 所有模块构建成功!")
        return 0
    elif any(results.values()):
        safe_print("[!] 部分模块构建成功")
        return 0
    else:
        safe_print("[!] 所有模块构建失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())