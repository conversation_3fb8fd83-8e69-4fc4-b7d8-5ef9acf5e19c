#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考试编排模块启动脚本
提供考试管理的完整功能，包括考试创建、编辑、状态管理、参与者管理等

启动方式：
    python run.py

服务地址：
    - 主服务: http://localhost:5003
    - 健康检查: http://localhost:5003/health
    - Web管理界面: http://localhost:5003/web
    - API文档: http://localhost:5003/api/v1/exams
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app

def main():
    """
    启动考试编排模块服务
    """
    try:
        print("="*50)
        print("考试编排模块 (Exam Management Module)")
        print("="*50)
        print("服务信息:")
        print("  - 服务名称: 考试编排模块")
        print("  - 版本: v3.0")
        print("  - 端口: 5003")
        print("  - 主机: 0.0.0.0")
        print("")
        print("服务地址:")
        print("  - 主服务: http://localhost:5003")
        print("  - 健康检查: http://localhost:5003/health")
        print("  - Web管理界面: http://localhost:5003/web")
        print("  - API文档: http://localhost:5003/api/v1/exams")
        print("")
        print("主要功能:")
        print("  - 考试创建和编辑")
        print("  - 考试状态管理")
        print("  - 参与者管理")
        print("  - 时间冲突检测")
        print("  - 考试结果统计")
        print("  - Web管理界面")
        print("")
        print("正在启动服务...")
        print("="*50)
        
        # 启动Flask应用
        app.run(
            host='0.0.0.0',
            port=5003,
            debug=False,  # 生产环境关闭调试模式
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()