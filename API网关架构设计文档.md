# API网关架构设计文档

## 第一阶段：架构设计期

### 1. 整体架构设计

#### 1.1 架构原则
- **统一入口**：所有外部请求通过API网关统一处理
- **服务解耦**：各功能模块独立部署，通过网关协调
- **安全第一**：集中认证授权，统一安全策略
- **高可用性**：支持负载均衡、熔断降级
- **可观测性**：全链路日志、监控、追踪

#### 1.2 技术架构图
```
┌─────────────────────────────────────────────────────────┐
│                    客户端层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │  管理端Web   │  │  独立客户端   │  │  移动端App   │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                   API网关层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │   路由管理   │  │   认证中心   │  │   权限控制   │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │   限流控制   │  │   日志审计   │  │   监控告警   │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                   业务模块层                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │ 用户管理模块  │  │ 题库管理模块  │  │ 考试管理模块  │    │
│  │ :5001       │  │ :5002       │  │ :5003       │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    │
│  │ 成绩管理模块  │  │ 阅卷中心模块  │  │ 监控模块     │    │
│  │ :5004       │  │ :5005       │  │ :5006       │    │
│  └─────────────┘  └─────────────┘  └─────────────┘    │
│  ┌─────────────┐  ┌─────────────┐                    │
│  │ 日志审计模块  │  │ 其他扩展模块  │                    │
│  │ :5007       │  │ :500x       │                    │
│  └─────────────┘  └─────────────┘                    │
└─────────────────────────────────────────────────────────┘
```

### 2. 统一接口规范设计

#### 2.1 RESTful API标准

**基础URL格式**
```
http://gateway-host:5000/api/v1/{module}/{resource}
```

**HTTP方法规范**
- `GET`：查询资源
- `POST`：创建资源
- `PUT`：完整更新资源
- `PATCH`：部分更新资源
- `DELETE`：删除资源

**状态码规范**
- `200`：成功
- `201`：创建成功
- `400`：请求参数错误
- `401`：未认证
- `403`：权限不足
- `404`：资源不存在
- `429`：请求过于频繁
- `500`：服务器内部错误
- `503`：服务不可用

#### 2.2 统一响应格式

**成功响应格式**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体数据
  },
  "timestamp": "2025-01-01T10:00:00Z",
  "request_id": "req-123456789"
}
```

**错误响应格式**
```json
{
  "code": 400,
  "message": "参数验证失败",
  "error": {
    "type": "ValidationError",
    "details": [
      {
        "field": "username",
        "message": "用户名不能为空"
      }
    ]
  },
  "timestamp": "2025-01-01T10:00:00Z",
  "request_id": "req-123456789"
}
```

**分页响应格式**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  },
  "timestamp": "2025-01-01T10:00:00Z",
  "request_id": "req-123456789"
}
```

#### 2.3 请求头规范

**必需请求头**
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Request-ID: {unique_request_id}
```

**可选请求头**
```http
X-Client-Version: 1.0.0
X-Client-Type: web|desktop|mobile
X-Trace-ID: {trace_id}
```

### 3. 统一认证机制设计

#### 3.1 JWT Token规范

**Token结构**
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": "user-123",
    "username": "admin",
    "role": "administrator",
    "permissions": ["user:read", "user:write", "exam:manage"],
    "exp": 1640995200,
    "iat": 1640991600,
    "iss": "exam-system",
    "aud": "api-gateway"
  }
}
```

**Token生命周期**
- **Access Token**：有效期2小时
- **Refresh Token**：有效期7天
- **自动刷新**：Access Token过期前5分钟自动刷新

#### 3.2 认证流程

**登录认证流程**
```
1. 客户端 → API网关：POST /api/v1/auth/login
2. API网关 → 用户管理模块：验证用户凭据
3. 用户管理模块 → API网关：返回用户信息
4. API网关 → 客户端：返回JWT Token
```

**请求认证流程**
```
1. 客户端 → API网关：携带Authorization头的请求
2. API网关：验证JWT Token有效性
3. API网关：检查用户权限
4. API网关 → 业务模块：转发请求（附加用户信息）
5. 业务模块 → API网关：返回响应
6. API网关 → 客户端：返回最终响应
```

#### 3.3 权限模型设计

**RBAC权限模型**
```
用户(User) → 角色(Role) → 权限(Permission) → 资源(Resource)
```

**角色定义**
- `super_admin`：超级管理员（系统内置）
- `admin`：系统管理员
- `examiner`：考评员
- `student`：考生
- `teacher`：阅卷教师

**权限格式**
```
{module}:{resource}:{action}

示例：
- user:profile:read
- user:list:write
- exam:session:manage
- question:bank:read
- grade:result:write
```

### 4. 模块间通信协议设计

#### 4.1 服务发现机制

**静态配置方式**
```yaml
# gateway_modules.yaml
modules:
  user-management:
    name: "用户管理模块"
    host: "localhost"
    port: 5001
    prefix: "/api/v1/user"
    health_check: "/api/health"
    timeout: 30
    retry: 3
    
  question-bank:
    name: "题库管理模块"
    host: "localhost"
    port: 5002
    prefix: "/api/v1/question"
    health_check: "/api/health"
    timeout: 30
    retry: 3
```

#### 4.2 请求转发规范

**请求头传递**
```http
# 网关向业务模块转发时添加的请求头
X-User-ID: {user_id}
X-User-Role: {user_role}
X-User-Permissions: {permissions_json}
X-Request-ID: {request_id}
X-Gateway-Version: 1.0.0
```

**超时和重试策略**
- **连接超时**：5秒
- **读取超时**：30秒
- **重试次数**：3次
- **重试间隔**：1秒、2秒、4秒（指数退避）

#### 4.3 健康检查机制

**健康检查接口规范**
```http
GET /api/health

响应格式：
{
  "status": "healthy|unhealthy|degraded",
  "timestamp": "2025-01-01T10:00:00Z",
  "version": "1.0.0",
  "dependencies": {
    "database": "healthy",
    "redis": "healthy",
    "external_api": "degraded"
  }
}
```

**检查频率**
- **正常状态**：每30秒检查一次
- **异常状态**：每10秒检查一次
- **连续失败3次**：标记为不健康
- **连续成功2次**：恢复健康状态

### 5. 路由规则和权限控制策略

#### 5.1 路由规则设计

**模块路由映射**
```yaml
routes:
  # 用户管理模块
  - pattern: "/api/v1/user/**"
    target: "user-management"
    methods: ["GET", "POST", "PUT", "DELETE"]
    auth_required: true
    permissions: ["user:*"]
    
  # 题库管理模块
  - pattern: "/api/v1/question/**"
    target: "question-bank"
    methods: ["GET", "POST", "PUT", "DELETE"]
    auth_required: true
    permissions: ["question:*"]
    
  # 考试管理模块
  - pattern: "/api/v1/exam/**"
    target: "exam-management"
    methods: ["GET", "POST", "PUT", "DELETE"]
    auth_required: true
    permissions: ["exam:*"]
    
  # 公开接口（无需认证）
  - pattern: "/api/v1/auth/login"
    target: "user-management"
    methods: ["POST"]
    auth_required: false
    
  - pattern: "/api/health"
    target: "gateway"
    methods: ["GET"]
    auth_required: false
```

#### 5.2 权限控制策略

**权限检查流程**
```python
def check_permission(user_permissions, required_permissions):
    """
    权限检查逻辑
    
    Args:
        user_permissions: 用户拥有的权限列表
        required_permissions: 接口要求的权限列表
    
    Returns:
        bool: 是否有权限访问
    """
    # 超级管理员拥有所有权限
    if "*:*:*" in user_permissions:
        return True
    
    # 检查是否有匹配的权限
    for required in required_permissions:
        if has_permission(user_permissions, required):
            return True
    
    return False

def has_permission(user_permissions, required_permission):
    """
    检查单个权限
    
    支持通配符匹配：
    - user:*:* 匹配 user:profile:read
    - user:profile:* 匹配 user:profile:read
    - * 匹配所有权限
    """
    for user_perm in user_permissions:
        if permission_match(user_perm, required_permission):
            return True
    return False
```

**角色权限映射**
```yaml
role_permissions:
  super_admin:
    - "*:*:*"
    
  admin:
    - "user:*:*"
    - "exam:*:*"
    - "question:*:*"
    - "grade:*:read"
    - "system:config:*"
    
  examiner:
    - "user:profile:read"
    - "exam:session:manage"
    - "exam:monitor:read"
    - "grade:result:read"
    
  teacher:
    - "user:profile:read"
    - "question:bank:read"
    - "grade:manual:write"
    - "grade:result:read"
    
  student:
    - "user:profile:read"
    - "exam:session:join"
    - "grade:result:read"
```

#### 5.3 特殊路由处理

**文件上传路由**
```yaml
- pattern: "/api/v1/upload/**"
  target: "file-service"
  methods: ["POST"]
  auth_required: true
  max_file_size: "10MB"
  allowed_types: ["image/*", "application/pdf", "application/vnd.ms-excel"]
```

**WebSocket路由**
```yaml
- pattern: "/ws/monitor/**"
  target: "monitor-service"
  protocol: "websocket"
  auth_required: true
  permissions: ["monitor:realtime:read"]
```

### 6. 安全策略设计

#### 6.1 限流策略

**全局限流**
- 每个IP每分钟最多100个请求
- 每个用户每分钟最多200个请求
- 登录接口每个IP每分钟最多5次尝试

**接口级限流**
```yaml
rate_limits:
  "/api/v1/auth/login":
    rate: "5/minute"
    burst: 2
    
  "/api/v1/user/**":
    rate: "100/minute"
    burst: 10
    
  "/api/v1/exam/submit":
    rate: "1/minute"
    burst: 1
```

#### 6.2 安全防护

**请求验证**
- 请求大小限制：最大10MB
- 请求头数量限制：最多50个
- URL长度限制：最大2048字符
- 参数深度限制：最大10层嵌套

**安全头设置**
```http
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
```

### 7. 监控和日志设计

#### 7.1 监控指标

**性能指标**
- 请求量（QPS）
- 响应时间（P50、P95、P99）
- 错误率（4xx、5xx）
- 并发连接数

**业务指标**
- 各模块健康状态
- 用户登录成功率
- API调用分布
- 权限拒绝次数

#### 7.2 日志格式

**访问日志**
```json
{
  "timestamp": "2025-01-01T10:00:00.123Z",
  "request_id": "req-123456789",
  "method": "POST",
  "path": "/api/v1/user/login",
  "query": "",
  "user_id": "user-123",
  "user_role": "admin",
  "source_ip": "*************",
  "user_agent": "Mozilla/5.0...",
  "target_module": "user-management",
  "target_url": "http://localhost:5001/api/v1/user/login",
  "response_time": 150,
  "status_code": 200,
  "response_size": 1024,
  "error_message": null
}
```

**错误日志**
```json
{
  "timestamp": "2025-01-01T10:00:00.123Z",
  "level": "ERROR",
  "request_id": "req-123456789",
  "error_type": "AuthenticationError",
  "error_message": "Invalid JWT token",
  "stack_trace": "...",
  "user_id": "user-123",
  "path": "/api/v1/user/profile",
  "module": "api-gateway"
}
```

### 8. 配置管理设计

#### 8.1 配置文件结构

**主配置文件：gateway.yaml**
```yaml
gateway:
  name: "API网关"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 5000
  debug: false
  
security:
  jwt_secret: "${JWT_SECRET}"
  jwt_expire: 7200  # 2小时
  refresh_expire: 604800  # 7天
  
logging:
  level: "INFO"
  file: "logs/gateway.log"
  max_size: "100MB"
  backup_count: 10
  
monitoring:
  metrics_enabled: true
  metrics_port: 9090
  health_check_interval: 30
  
rate_limiting:
  enabled: true
  storage: "redis"
  redis_url: "${REDIS_URL}"
```

**模块配置文件：modules.yaml**
```yaml
modules:
  user-management:
    name: "用户管理模块"
    host: "${USER_SERVICE_HOST:localhost}"
    port: "${USER_SERVICE_PORT:5001}"
    prefix: "/api/v1/user"
    health_check: "/api/health"
    timeout: 30
    retry: 3
    circuit_breaker:
      failure_threshold: 5
      recovery_timeout: 60
      
  question-bank:
    name: "题库管理模块"
    host: "${QUESTION_SERVICE_HOST:localhost}"
    port: "${QUESTION_SERVICE_PORT:5002}"
    prefix: "/api/v1/question"
    health_check: "/health"
    timeout: 30
    retry: 3
```

### 9. 部署和运维设计

#### 9.1 部署架构

**开发环境**
```
API网关 (localhost:5000)
├── 用户管理模块 (localhost:5001)
├── 题库管理模块 (localhost:5002)
├── 考试管理模块 (localhost:5003)
└── 其他模块 (localhost:500x)
```

**生产环境**
```
负载均衡器 (nginx)
├── API网关实例1 (gateway-1:5000)
├── API网关实例2 (gateway-2:5000)
└── API网关实例3 (gateway-3:5000)
    ├── 用户管理集群 (user-service-1,2,3)
    ├── 题库管理集群 (question-service-1,2,3)
    └── 其他服务集群
```

#### 9.2 容器化部署

**Dockerfile**
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制代码
COPY . .

# 暴露端口
EXPOSE 5000 9090

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/api/health || exit 1

# 启动命令
CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

**docker-compose.yml**
```yaml
version: '3.8'

services:
  api-gateway:
    build: .
    ports:
      - "5000:5000"
      - "9090:9090"
    environment:
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
      
  user-service:
    image: user-management:latest
    ports:
      - "5001:5001"
```

### 10. 下一步实施计划

#### 10.1 第二阶段：基础实现（预计2周）
- [ ] 创建API网关项目结构
- [ ] 实现基础路由功能
- [ ] 集成JWT认证机制
- [ ] 实现请求转发逻辑
- [ ] 与用户管理模块集成测试

#### 10.2 第三阶段：功能完善（预计2周）
- [ ] 实现权限控制系统
- [ ] 添加限流和熔断功能
- [ ] 完善监控和日志系统
- [ ] 实现健康检查机制
- [ ] 性能优化和压力测试

#### 10.3 第四阶段：生产就绪（预计1周）
- [ ] 容器化部署配置
- [ ] 安全加固和渗透测试
- [ ] 文档完善和培训
- [ ] 生产环境部署和验证

---

## 总结

本架构设计文档定义了API网关的核心架构、接口规范、认证机制、通信协议和权限控制策略，为职业技能等级考试系统提供了统一、安全、高效的服务入口。

**核心优势：**
1. **统一标准**：所有模块遵循相同的接口规范和认证机制
2. **安全可控**：集中的权限控制和安全防护
3. **高可用性**：支持负载均衡、熔断降级和健康检查
4. **可观测性**：全面的监控、日志和追踪能力
5. **易扩展性**：新模块可快速接入，支持灰度发布

**技术特点：**
- 基于Flask的轻量级实现
- JWT标准的无状态认证
- RBAC模型的细粒度权限控制
- 微服务架构的松耦合设计
- 云原生的容器化部署

此设计为后续的开发和实施提供了清晰的技术路线和实现标准。