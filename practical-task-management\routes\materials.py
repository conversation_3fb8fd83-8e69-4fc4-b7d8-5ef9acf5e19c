# -*- coding: utf-8 -*-
"""
任务素材管理路由模块
提供任务素材文件的上传、下载、版本控制API接口
"""

from flask import Blueprint, request, send_file, current_app
from werkzeug.utils import secure_filename
from services import TaskMaterialService
from auth.permissions import require_permission, Permission
from auth.audit_log import log_operation
from utils.api_response import APIResponse, api_response, validate_request
from datetime import datetime
import os
import json

# 创建蓝图
materials_bp = Blueprint('materials', __name__, url_prefix='/api/v1/materials')
material_service = TaskMaterialService()

# 允许的文件扩展名
ALLOWED_EXTENSIONS = {
    'document': {'pdf', 'doc', 'docx', 'txt', 'md'},
    'image': {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'svg'},
    'video': {'mp4', 'avi', 'mov', 'wmv', 'flv'},
    'archive': {'zip', 'rar', '7z', 'tar', 'gz'},
    'office': {'xls', 'xlsx', 'ppt', 'pptx'},
    'other': {'exe', 'msi', 'dmg', 'deb', 'rpm'}
}

def allowed_file(filename, file_type=None):
    """
    检查文件是否允许上传
    
    参数:
        filename: 文件名
        file_type: 文件类型限制
    
    返回:
        bool: 是否允许上传
    """
    if '.' not in filename:
        return False
    
    ext = filename.rsplit('.', 1)[1].lower()
    
    if file_type and file_type in ALLOWED_EXTENSIONS:
        return ext in ALLOWED_EXTENSIONS[file_type]
    
    # 检查所有允许的扩展名
    all_extensions = set()
    for extensions in ALLOWED_EXTENSIONS.values():
        all_extensions.update(extensions)
    
    return ext in all_extensions

@materials_bp.route('/task/<int:task_id>', methods=['GET'])
@require_permission(Permission.MATERIAL_VIEW)
@log_operation('获取任务素材列表', 'material', 'READ')
@api_response
@validate_request({
    'file_type': 'file_type',
    'version': 'version',
    'page': 'page',
    'page_size': 'page_size'
})
def get_task_materials(task_id):
    """
    获取指定任务的素材列表
    
    参数:
        task_id: 任务ID
    
    查询参数:
        - file_type: 文件类型过滤
        - version: 版本过滤
        - page: 页码 (默认1)
        - page_size: 每页数量 (默认20)
    
    返回:
        JSON格式的素材列表
    """
    # 获取查询参数
    file_type = request.args.get('file_type')
    version = request.args.get('version')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    
    # 构建过滤条件
    filters = {'task_id': task_id}
    if file_type:
        filters['file_type'] = file_type
    if version:
        filters['version'] = version
        
    # 获取素材列表
    materials = material_service.get_materials_with_pagination(
        filters=filters,
        page=page,
        page_size=page_size
    )
    
    return APIResponse.success(materials, '获取素材列表成功')

@materials_bp.route('/<int:material_id>', methods=['GET'])
@require_permission(Permission.MATERIAL_VIEW)
@log_operation('获取素材详情', 'material', 'READ')
@api_response
def get_material(material_id):
    """
    获取指定素材详情
    
    参数:
        material_id: 素材ID
    
    返回:
        JSON格式的素材详细信息
    """
    material = material_service.get_material_by_id(material_id)
    if not material:
        return APIResponse.error('素材不存在', 404)
        
    return APIResponse.success(material, '获取素材详情成功')

@materials_bp.route('/upload', methods=['POST'])
@require_permission(Permission.MATERIAL_UPLOAD)
@log_operation('上传素材文件', 'material', 'CREATE')
@api_response
def upload_material():
    """
    上传任务素材文件
    
    表单数据:
        - file: 文件对象
        - task_id: 任务ID
        - file_type: 文件类型 (document/image/video/archive/office/other)
        - description: 文件描述
        - version: 版本号 (可选)
        - tags: 标签 (JSON数组字符串)
    
    返回:
        JSON格式的上传结果
    """
    # 检查文件是否存在
    if 'file' not in request.files:
        return APIResponse.error('没有选择文件', 400)
    
    file = request.files['file']
    if file.filename == '':
        return APIResponse.error('文件名不能为空', 400)
    
    # 获取表单数据
    task_id = request.form.get('task_id', type=int)
    file_type = request.form.get('file_type', 'other')
    description = request.form.get('description', '')
    version = request.form.get('version', '1.0')
    tags_str = request.form.get('tags', '[]')
    
    # 验证必填字段
    if not task_id:
        return APIResponse.error('缺少任务ID', 400)
    
    # 解析标签
    try:
        tags = json.loads(tags_str)
    except json.JSONDecodeError:
        tags = []
    
    # 检查文件类型
    if not allowed_file(file.filename, file_type):
        return APIResponse.error(f'不支持的文件类型，允许的类型: {ALLOWED_EXTENSIONS.get(file_type, "未知类型")}', 400)
    
    # 上传文件
    material_id = material_service.upload_material(
        file=file,
        task_id=task_id,
        file_type=file_type,
        description=description,
        version=version,
        tags=tags
    )
    
    return APIResponse.success({'material_id': material_id}, '文件上传成功', 201)

@materials_bp.route('/<int:material_id>/download', methods=['GET'])
@require_permission(Permission.MATERIAL_VIEW)
@log_operation('下载素材文件', 'material', 'READ')
def download_material(material_id):
    """
    下载任务素材文件
    
    参数:
        material_id: 素材ID
    
    返回:
        文件流或错误信息
    """
    # 获取素材信息
    material = material_service.get_material_by_id(material_id)
    if not material:
        return APIResponse.error('素材不存在', 404)
    
    # 构建文件路径
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], material['file_path'])
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        return APIResponse.error('文件不存在', 404)
    
    # 返回文件
    return send_file(
        file_path,
        as_attachment=True,
        download_name=material['original_filename']
    )

@materials_bp.route('/<int:material_id>', methods=['PUT'])
@require_permission(Permission.MATERIAL_UPLOAD)
@log_operation('更新素材信息', 'material', 'UPDATE')
@api_response
@validate_request({
    'description': 'description',
    'version': 'version',
    'tags': 'tags',
    'file_type': 'file_type'
})
def update_material(material_id):
    """
    更新素材信息
    
    参数:
        material_id: 素材ID
    
    请求体:
        {
            "title": "素材标题",
            "description": "素材描述",
            "file_type": "文件类型",
            "tags": ["标签1", "标签2"]
        }
    
    返回:
        JSON格式的更新结果
    """
    data = request.get_json()
    
    # 更新素材
    success = material_service.update_material(
        material_id=material_id,
        title=data.get('title'),
        description=data.get('description'),
        file_type=data.get('file_type'),
        tags=data.get('tags')
    )
    
    if success:
        return APIResponse.success({}, '素材更新成功')
    else:
        return APIResponse.error('素材不存在', 404)

@materials_bp.route('/<int:material_id>', methods=['DELETE'])
@require_permission(Permission.MATERIAL_DELETE)
@log_operation('删除素材文件', 'material', 'DELETE')
@api_response
def delete_material(material_id):
    """
    删除素材
    
    参数:
        material_id: 素材ID
    
    返回:
        JSON格式的删除结果
    """
    # 检查素材是否存在
    existing_material = material_service.get_material_by_id(material_id)
    if not existing_material:
        return APIResponse.error('素材不存在', 404)
    
    # 删除素材
    success = material_service.delete_material(material_id)
    if success:
        return APIResponse.success({}, '素材删除成功')
    else:
        return APIResponse.error('素材删除失败', 500)

@materials_bp.route('/<int:material_id>/versions', methods=['GET'])
@require_permission(Permission.MATERIAL_VIEW)
@log_operation('获取素材版本列表', 'material', 'READ')
@api_response
def get_material_versions(material_id):
    """
    获取素材版本历史
    
    参数:
        material_id: 素材ID
    
    返回:
        JSON格式的版本历史列表
    """
    # 检查素材是否存在
    existing_material = material_service.get_material_by_id(material_id)
    if not existing_material:
        return APIResponse.error('素材不存在', 404)
    
    # 获取版本历史
    versions = material_service.get_material_versions(material_id)
    return APIResponse.success(versions, '获取版本历史成功')

@materials_bp.route('/types', methods=['GET'])
@require_permission(Permission.MATERIAL_VIEW)
@log_operation('获取文件类型列表', 'material', 'READ')
@api_response
def get_file_types():
    """
    获取支持的文件类型列表
    
    返回:
        JSON格式的文件类型列表
    """
    file_types = material_service.get_supported_file_types()
    return APIResponse.success(file_types, '获取文件类型成功')

@materials_bp.route('/stats', methods=['GET'])
@require_permission(Permission.MATERIAL_VIEW)
@log_operation('获取素材统计信息', 'material', 'READ')
@api_response
def get_material_stats():
    """
    获取素材统计信息
    
    返回:
        JSON格式的统计数据
    """
    stats = material_service.get_material_statistics()
    return APIResponse.success(stats, '获取统计信息成功')