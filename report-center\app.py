# -*- coding: utf-8 -*-
"""
成绩查询模块 / 报表中心 - 核心服务
"""
from flask import Flask, jsonify
from flask_cors import CORS

# --- Mock Database ---
# In a real system, this service would fetch data from other services
# (like score_management) or a data warehouse. For now, we mock the data.
mock_scores = {
    # exam_id: { student_id: score }
    1: {
        1: 95,
        2: 88,
        3: 55, # Below passing grade
        4: 75,
    },
    2: {
        5: 92
    }
}
PASSING_GRADE = 60

def create_app():
    """Application factory to create and configure the Flask app."""
    app = Flask(__name__)
    CORS(app)

    @app.route('/', methods=['GET'])
    def index():
        return jsonify({'service': 'Report Center API', 'status': 'running'})

    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({'status': 'healthy', 'service': 'report-center'})

    @app.route('/api/v1/reports/exams/<int:exam_id>/summary', methods=['GET'])
    def get_exam_summary(exam_id):
        """Generates and returns a statistical summary for a given exam."""
        if exam_id not in mock_scores:
            return jsonify({'error': 'Exam not found or no scores available'}), 404

        scores = list(mock_scores[exam_id].values())
        if not scores:
            return jsonify({
                'exam_id': exam_id,
                'participant_count': 0,
                'message': 'No scores recorded for this exam.'
            })

        participant_count = len(scores)
        average_score = sum(scores) / participant_count
        passed_count = sum(1 for score in scores if score >= PASSING_GRADE)
        pass_rate = (passed_count / participant_count) * 100

        summary = {
            'exam_id': exam_id,
            'participant_count': participant_count,
            'average_score': round(average_score, 2),
            'pass_rate_percent': round(pass_rate, 2),
            'passing_grade': PASSING_GRADE
        }

        return jsonify(summary)

    @app.route('/api/v1/reports/students/<int:student_id>/exams/<int:exam_id>', methods=['GET'])
    def get_student_exam_report(student_id, exam_id):
        """Returns a specific student's score for a given exam."""
        if exam_id not in mock_scores or student_id not in mock_scores[exam_id]:
            return jsonify({'error': 'Score record not found for this student and exam'}), 404

        score = mock_scores[exam_id][student_id]

        report = {
            'student_id': student_id,
            'exam_id': exam_id,
            'score': score,
            'passed': score >= PASSING_GRADE,
            'passing_grade': PASSING_GRADE
        }

        return jsonify(report)

    return app
