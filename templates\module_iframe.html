{% extends "base.html" %}

{% block title %}{{ module.name }} - {{ system_config.name }}{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body py-2">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="mb-0">
                            <i class="{{ module.icon }} me-2"></i>
                            {{ module.name }}
                        </h5>
                        <small class="text-muted">{{ module.description }}</small>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>
                            返回主控台
                        </a>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshModule()">
                            <i class="fas fa-sync-alt me-1"></i>
                            刷新
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body p-0">
                <iframe id="module-iframe" 
                        src="{{ module_url }}" 
                        style="width: 100%; height: calc(100vh - 200px); border: none;"
                        onload="hideLoading()">
                </iframe>
                <div id="loading-overlay" class="text-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3 text-muted">正在加载 {{ module.name }}...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function hideLoading() {
    document.getElementById('loading-overlay').style.display = 'none';
}

function refreshModule() {
    const iframe = document.getElementById('module-iframe');
    const loading = document.getElementById('loading-overlay');
    
    loading.style.display = 'block';
    iframe.src = iframe.src;
}

// 处理iframe加载错误
document.getElementById('module-iframe').onerror = function() {
    document.getElementById('loading-overlay').innerHTML = `
        <div class="text-center p-5">
            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
            <h5 class="mt-3">模块加载失败</h5>
            <p class="text-muted">{{ module.name }} 可能未正常启动，请返回主控台检查模块状态。</p>
            <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-1"></i>
                返回主控台
            </a>
        </div>
    `;
};
</script>
{% endblock %}