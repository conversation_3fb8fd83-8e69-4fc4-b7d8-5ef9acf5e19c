#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语法错误修复脚本
用于修复app.py文件中的中文字符截断问题
"""

import re
import os

def fix_syntax_errors():
    """修复app.py文件中的语法错误"""
    file_path = 'd:\\60-PHRL_OLE_SYS\\exam_management\\app.py'
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义需要修复的错误映射
    fixes = {
        # HTML标签中的错误
        '参与者管?</a>': '参与者管理</a>',
        '参与者管?</h3>': '参与者管理</h3>',
        '参与者管?</title>': '参与者管理</title>',
        '状态管?</h3>': '状态管理</h3>',
        '时间冲突检?</h3>': '时间冲突检测</h3>',
        '考试创建与编?</h3>': '考试创建与编辑</h3>',
        '总考试?</div>': '总考试数</div>',
        '总参与?</div>': '总参与者</div>',
        '返回主控?</a>': '返回主控台</a>',
        '专业?</p>': '专业版</p>',
        
        # 文本内容中的错误
        '状?</span>': '状态</span>',
        "'?": "'无'",
        '未设?': '未设置',
        '开始时?': '开始时间',
        '未计?': '未计算',
        '最大参与?': '最大参与者',
        '无限?': '无限制',
        '当前参与?': '当前参与者',
        '创建?': '创建者',
        '座位?': '座位号',
        
        # 加载提示中的错误
        '正在加载参与者信?..': '正在加载参与者信息..',
        '操作日志加载?..': '操作日志加载中..',
        '考试状态分布饼?': '考试状态分布饼图',
        '正在加载参与者统?..': '正在加载参与者统计..',
        '成绩分布直方?': '成绩分布直方图',
        '正在加载参与者统计数?..': '正在加载参与者统计数据..',
        '正在加载参与者列?..': '正在加载参与者列表..',
        
        # 注释中的错误
        '计算总参与者数（这里使用模拟数据，实际应该从API获取?': '计算总参与者数（这里使用模拟数据，实际应该从API获取）',
        '使用默认?': '使用默认值',
        '页面加载完成后执?': '页面加载完成后执行',
        
        # 其他文本错误
        '设置。提供完整的考试配置选项，包括题库关联和实操任务设置?': '设置。提供完整的考试配置选项，包括题库关联和实操任务设置。',
        '支持状态转换验证和权限控制?': '支持状态转换验证和权限控制。',
        '提供详细的参与者信息管理?': '提供详细的参与者信息管理。',
        '避免资源冲突和时间重叠?': '避免资源冲突和时间重叠。',
        '成绩统计等多维度数据展示?': '成绩统计等多维度数据展示。',
        '确保不同用户只能访问授权的功能和数据?': '确保不同用户只能访问授权的功能和数据。'
    }
    
    # 应用修复
    for old_text, new_text in fixes.items():
        content = content.replace(old_text, new_text)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("语法错误修复完成！")
    print(f"已修复 {len(fixes)} 个错误")

if __name__ == '__main__':
    fix_syntax_errors()