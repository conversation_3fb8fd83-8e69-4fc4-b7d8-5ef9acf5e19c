# -*- coding: utf-8 -*-
"""
任务分类管理路由模块
提供任务分类的增删改查API接口
"""

from flask import Blueprint, request
from services import TaskCategoryService
from datetime import datetime
import json
from auth.permissions import require_permission, Permission
from auth.audit_log import log_operation
from config.api_gateway_config import (
    APIResponse, api_response, validate_request, ValidationError,
    API_PREFIX, VALIDATION_RULES
)

# 创建蓝图
categories_bp = Blueprint('categories', __name__, url_prefix=f'{API_PREFIX}/categories')
category_service = TaskCategoryService()

@categories_bp.route('', methods=['GET'])
@require_permission(Permission.CATEGORY_VIEW)
@log_operation('查看分类列表', 'category', 'READ')
@api_response
@validate_request({
    'page': 'page',
    'per_page': 'per_page',
    'software_type': 'software_type',
    'difficulty_level': 'difficulty_level',
    'status': 'status'
})
def get_categories():
    """
    获取任务分类列表
    
    查询参数:
        - software_type: 软件类型过滤
        - difficulty_level: 难度等级过滤
        - status: 状态过滤 (active/inactive)
        - page: 页码 (默认1)
        - page_size: 每页数量 (默认20)
    
    返回:
        JSON格式的分类列表和分页信息
    """
    # 获取查询参数
    software_type = request.args.get('software_type')
    difficulty_level = request.args.get('difficulty_level')
    status = request.args.get('status', 'active')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    
    # 构建过滤条件
    filters = {}
    if software_type:
        filters['software_type'] = software_type
    if difficulty_level:
        filters['difficulty_level'] = difficulty_level
    if status:
        filters['status'] = status
        
    # 获取分类列表
    categories = category_service.get_categories_with_pagination(
        filters=filters,
        page=page,
        page_size=page_size
    )
    
    return APIResponse.success(categories, '获取分类列表成功')

@categories_bp.route('/<int:category_id>', methods=['GET'])
@require_permission(Permission.CATEGORY_VIEW)
@log_operation('查看分类详情', 'category', 'READ')
@api_response
def get_category(category_id):
    """
    获取指定分类详情
    
    参数:
        category_id: 分类ID
    
    返回:
        JSON格式的分类详细信息
    """
    category = category_service.get_category_by_id(category_id)
    if not category:
        return APIResponse.error('分类不存在', 404)
        
    return APIResponse.success(category, '获取分类详情成功')

@categories_bp.route('', methods=['POST'])
@require_permission(Permission.CATEGORY_CREATE)
@log_operation('创建分类', 'category', 'CREATE')
@api_response
@validate_request({
    'name': 'category_name',
    'software_type': 'software_type',
    'difficulty_level': 'difficulty_level',
    'description': 'description',
    'icon': 'icon',
    'color': 'color'
})
def create_category():
    """
    创建新的任务分类
    
    请求体:
        {
            "name": "分类名称",
            "description": "分类描述",
            "software_type": "软件类型",
            "difficulty_level": "难度等级",
            "icon": "图标",
            "color": "颜色"
        }
    
    返回:
        JSON格式的创建结果
    """
    data = request.get_json()
    
    # 创建分类
    category_id = category_service.create_category(
        name=data['name'],
        description=data.get('description', ''),
        software_type=data['software_type'],
        difficulty_level=data['difficulty_level'],
        icon=data.get('icon', ''),
        color=data.get('color', '#007bff')
    )
    
    return APIResponse.success({'category_id': category_id}, '分类创建成功', 201)

@categories_bp.route('/<int:category_id>', methods=['PUT'])
@require_permission(Permission.CATEGORY_EDIT)
@log_operation('更新分类', 'category', 'UPDATE')
@api_response
@validate_request({
    'name': 'category_name',
    'software_type': 'software_type',
    'difficulty_level': 'difficulty_level',
    'description': 'description',
    'icon': 'icon',
    'color': 'color',
    'status': 'status'
})
def update_category(category_id):
    """
    更新任务分类
    
    参数:
        category_id: 分类ID
    
    请求体:
        {
            "name": "分类名称",
            "description": "分类描述",
            "software_type": "软件类型",
            "difficulty_level": "难度等级",
            "icon": "图标",
            "color": "颜色",
            "status": "状态"
        }
    
    返回:
        JSON格式的更新结果
    """
    data = request.get_json()
        
    # 检查分类是否存在
    existing_category = category_service.get_category_by_id(category_id)
    if not existing_category:
        return APIResponse.error('分类不存在', 404)
    
    # 更新分类
    success = category_service.update_category(category_id, data)
    if success:
        return APIResponse.success({}, '分类更新成功')
    else:
        return APIResponse.error('分类更新失败', 500)

@categories_bp.route('/<int:category_id>', methods=['DELETE'])
@require_permission(Permission.CATEGORY_DELETE)
@log_operation('删除分类', 'category', 'DELETE')
@api_response
def delete_category(category_id):
    """
    删除任务分类
    
    参数:
        category_id: 分类ID
    
    返回:
        JSON格式的删除结果
    """
    # 检查分类是否存在
    existing_category = category_service.get_category_by_id(category_id)
    if not existing_category:
        return APIResponse.error('分类不存在', 404)
    
    # 检查是否有关联的任务
    if category_service.has_associated_tasks(category_id):
        return APIResponse.error('该分类下还有关联的任务，无法删除', 400)
    
    # 删除分类
    success = category_service.delete_category(category_id)
    if success:
        return APIResponse.success({}, '分类删除成功')
    else:
        return APIResponse.error('分类删除失败', 500)

@categories_bp.route('/software-types', methods=['GET'])
@api_response
def get_software_types():
    """
    获取所有软件类型列表
    
    返回:
        JSON格式的软件类型列表
    """
    software_types = category_service.get_software_types()
    return APIResponse.success(software_types, '获取软件类型成功')

@categories_bp.route('/difficulty-levels', methods=['GET'])
@api_response
def get_difficulty_levels():
    """
    获取所有难度等级列表
    
    返回:
        JSON格式的难度等级列表
    """
    difficulty_levels = category_service.get_difficulty_levels()
    return APIResponse.success(difficulty_levels, '获取难度等级成功')

@categories_bp.route('/stats', methods=['GET'])
@api_response
def get_category_stats():
    """
    获取分类统计信息
    
    返回:
        JSON格式的分类统计数据
    """
    stats = category_service.get_category_stats()
    return APIResponse.success(stats, '获取分类统计成功')