from app import create_app
from app.models import db
from sqlalchemy import text

app = create_app()
with app.app_context():
    # 更新状态值
    db.session.execute(text("UPDATE users SET status = 'approved' WHERE status = 'active'"))
    db.session.execute(text("UPDATE users SET status = 'approved' WHERE status = 'inactive'"))
    db.session.execute(text("UPDATE users SET status = 'rejected' WHERE status = 'suspended'"))
    db.session.execute(text("UPDATE users SET status = 'rejected' WHERE status = 'deleted'"))
    
    # 更新角色值
    db.session.execute(text("UPDATE users SET role = 'expert' WHERE role = 'teacher'"))
    db.session.execute(text("UPDATE users SET role = 'grader' WHERE role = 'examiner'"))
    db.session.execute(text("UPDATE users SET role = 'internal_supervisor' WHERE role = 'guest'"))
    
    db.session.commit()
    print("数据库更新完成")