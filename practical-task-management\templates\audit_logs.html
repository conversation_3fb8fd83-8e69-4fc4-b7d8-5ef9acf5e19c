{% extends "base.html" %}

{% block title %}审计日志 - 实操任务管理系统{% endblock %}

{% block extra_css %}
<style>
    .log-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .log-level-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    
    .log-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .log-details {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 0.75rem;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        max-height: 200px;
        overflow-y: auto;
    }
    
    .log-row {
        transition: background-color 0.2s;
    }
    
    .log-row:hover {
        background-color: #f8f9fa;
    }
    
    .time-filter {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 1rem;
    }
    
    .export-options {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 1rem;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="bi bi-shield-check"></i> 审计日志</h2>
            <p class="text-muted mb-0">查看系统操作记录和安全日志</p>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="refreshLogs()">
                <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
            <button class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#exportOptions">
                <i class="bi bi-download"></i> 导出日志
            </button>
        </div>
    </div>
    
    <!-- 日志统计 -->
    <div class="log-stats">
        <div class="row">
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="total-logs">0</h3>
                    <p class="mb-0">总日志数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="today-logs">0</h3>
                    <p class="mb-0">今日日志</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="error-logs">0</h3>
                    <p class="mb-0">错误日志</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="text-center">
                    <h3 class="mb-1" id="login-logs">0</h3>
                    <p class="mb-0">登录日志</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search-keyword" placeholder="搜索用户、操作或IP...">
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filter-log-type">
                        <option value="">所有类型</option>
                        <option value="operation">操作日志</option>
                        <option value="login">登录日志</option>
                        <option value="system">系统日志</option>
                        <option value="data_change">数据变更</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filter-level">
                        <option value="">所有级别</option>
                        <option value="info">信息</option>
                        <option value="warning">警告</option>
                        <option value="error">错误</option>
                        <option value="critical">严重</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="filter-user">
                        <option value="">所有用户</option>
                        <!-- 用户选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" id="sort-by">
                        <option value="created_at_desc">时间(新到旧)</option>
                        <option value="created_at_asc">时间(旧到新)</option>
                        <option value="level_desc">级别(高到低)</option>
                        <option value="user_asc">用户(A-Z)</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <button class="btn btn-outline-primary w-100" onclick="searchLogs()">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
            
            <!-- 时间筛选 -->
            <div class="time-filter mt-3">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">开始时间</label>
                        <input type="datetime-local" class="form-control" id="start-time">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">结束时间</label>
                        <input type="datetime-local" class="form-control" id="end-time">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">快速选择</label>
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-outline-secondary" onclick="setTimeRange('today')">今天</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setTimeRange('yesterday')">昨天</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setTimeRange('week')">本周</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setTimeRange('month')">本月</button>
                            <button type="button" class="btn btn-outline-secondary" onclick="clearTimeRange()">清除</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 导出选项 -->
    <div class="collapse" id="exportOptions">
        <div class="export-options">
            <h6><i class="bi bi-download"></i> 导出选项</h6>
            <div class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">导出格式</label>
                    <select class="form-select" id="export-format">
                        <option value="xlsx">Excel (.xlsx)</option>
                        <option value="csv">CSV (.csv)</option>
                        <option value="json">JSON (.json)</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">导出范围</label>
                    <select class="form-select" id="export-range">
                        <option value="current">当前筛选结果</option>
                        <option value="all">所有日志</option>
                        <option value="selected">选中的日志</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <button class="btn btn-primary w-100" onclick="exportLogs()">
                        <i class="bi bi-download"></i> 开始导出
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 日志列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="bi bi-list"></i> 日志列表</h5>
            <div>
                <button class="btn btn-sm btn-outline-danger" id="batch-delete-btn" style="display: none;" onclick="batchDeleteLogs()">
                    <i class="bi bi-trash"></i> 批量删除
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="clearOldLogs()">
                    <i class="bi bi-clock-history"></i> 清理旧日志
                </button>
            </div>
        </div>
        <div class="card-body">
            <!-- 加载指示器 -->
            <div id="loading-indicator" class="text-center py-4" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在加载日志数据...</p>
            </div>
            
            <!-- 日志表格 -->
            <div class="table-responsive" id="logs-table-container">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                            </th>
                            <th>时间</th>
                            <th>类型</th>
                            <th>级别</th>
                            <th>用户</th>
                            <th>操作</th>
                            <th>IP地址</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="logs-table-body">
                        <!-- 日志数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 空状态 -->
            <div id="empty-state" class="text-center py-5" style="display: none;">
                <i class="bi bi-journal-text text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-3">暂无日志数据</h5>
                <p class="text-muted">系统还没有生成任何日志记录</p>
            </div>
            
            <!-- 分页 -->
            <nav aria-label="日志分页" id="pagination-container" style="display: none;">
                <ul class="pagination justify-content-center" id="pagination">
                    <!-- 分页按钮将通过JavaScript动态生成 -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">日志详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>时间:</strong>
                        <p id="detail-timestamp"></p>
                    </div>
                    <div class="col-md-6">
                        <strong>类型:</strong>
                        <p id="detail-log-type"></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <strong>级别:</strong>
                        <p id="detail-level"></p>
                    </div>
                    <div class="col-md-6">
                        <strong>用户:</strong>
                        <p id="detail-user"></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <strong>IP地址:</strong>
                        <p id="detail-ip"></p>
                    </div>
                    <div class="col-md-6">
                        <strong>用户代理:</strong>
                        <p id="detail-user-agent"></p>
                    </div>
                </div>
                <div class="mb-3">
                    <strong>操作:</strong>
                    <p id="detail-action"></p>
                </div>
                <div class="mb-3">
                    <strong>资源:</strong>
                    <p id="detail-resource"></p>
                </div>
                <div class="mb-3">
                    <strong>详细信息:</strong>
                    <div class="log-details" id="detail-details"></div>
                </div>
                <div class="mb-3" id="detail-changes-container" style="display: none;">
                    <strong>数据变更:</strong>
                    <div class="log-details" id="detail-changes"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 清理旧日志模态框 -->
<div class="modal fade" id="clearLogsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">清理旧日志</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="clearLogsForm">
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        此操作将永久删除指定时间之前的日志，请谨慎操作。
                    </div>
                    <div class="mb-3">
                        <label for="clear-before-date" class="form-label">删除此日期之前的日志 <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="clear-before-date" required>
                        <div class="form-text">建议保留至少30天的日志记录</div>
                    </div>
                    <div class="mb-3">
                        <label for="clear-log-types" class="form-label">日志类型</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="clear-operation" value="operation" checked>
                            <label class="form-check-label" for="clear-operation">操作日志</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="clear-login" value="login">
                            <label class="form-check-label" for="clear-login">登录日志</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="clear-system" value="system">
                            <label class="form-check-label" for="clear-system">系统日志</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="clear-data-change" value="data_change">
                            <label class="form-check-label" for="clear-data-change">数据变更日志</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-warning">确认清理</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    let currentPage = 1;
    let totalPages = 1;
    let selectedLogs = new Set();
    
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadLogs();
        loadLogStats();
        loadUsers();
        
        // 绑定搜索事件
        document.getElementById('search-keyword').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchLogs();
            }
        });
        
        // 绑定表单提交事件
        document.getElementById('clearLogsForm').addEventListener('submit', handleClearLogs);
    });
    
    // 加载日志统计
    async function loadLogStats() {
        try {
            const response = await apiRequest('/api/v1/auth/audit-logs/stats');
            if (response.code === 200) {
                const stats = response.data;
                document.getElementById('total-logs').textContent = stats.total || 0;
                document.getElementById('today-logs').textContent = stats.today || 0;
                document.getElementById('error-logs').textContent = stats.error || 0;
                document.getElementById('login-logs').textContent = stats.login || 0;
            }
        } catch (error) {
            console.error('加载日志统计失败:', error);
        }
    }
    
    // 加载用户列表（用于筛选）
    async function loadUsers() {
        try {
            const response = await apiRequest('/api/v1/auth/users?per_page=100');
            if (response.code === 200) {
                const select = document.getElementById('filter-user');
                const users = response.data.users;
                
                users.forEach(user => {
                    const option = document.createElement('option');
                    option.value = user.id;
                    option.textContent = `${user.full_name || user.username} (@${user.username})`;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('加载用户列表失败:', error);
        }
    }
    
    // 加载日志列表
    async function loadLogs(page = 1) {
        showLoading(true);
        
        try {
            const params = new URLSearchParams({
                page: page,
                per_page: 20,
                keyword: document.getElementById('search-keyword').value,
                log_type: document.getElementById('filter-log-type').value,
                level: document.getElementById('filter-level').value,
                user_id: document.getElementById('filter-user').value,
                start_time: document.getElementById('start-time').value,
                end_time: document.getElementById('end-time').value,
                sort_by: document.getElementById('sort-by').value
            });
            
            const response = await apiRequest(`/api/v1/auth/audit-logs?${params}`);
            
            if (response.code === 200) {
                const data = response.data;
                renderLogs(data.logs);
                renderPagination(data.pagination);
                currentPage = page;
                totalPages = data.pagination.total_pages;
            } else {
                showMessage(response.msg || '加载日志列表失败', 'error');
            }
        } catch (error) {
            console.error('加载日志列表失败:', error);
            showMessage('加载日志列表失败', 'error');
        } finally {
            showLoading(false);
        }
    }
    
    // 渲染日志列表
    function renderLogs(logs) {
        const tbody = document.getElementById('logs-table-body');
        const emptyState = document.getElementById('empty-state');
        const tableContainer = document.getElementById('logs-table-container');
        
        if (!logs || logs.length === 0) {
            tbody.innerHTML = '';
            tableContainer.style.display = 'none';
            emptyState.style.display = 'block';
            return;
        }
        
        tableContainer.style.display = 'block';
        emptyState.style.display = 'none';
        
        tbody.innerHTML = logs.map(log => `
            <tr class="log-row">
                <td>
                    <input type="checkbox" class="log-checkbox" value="${log.id}" onchange="updateSelection()">
                </td>
                <td>
                    <small>${formatDateTime(log.timestamp)}</small>
                </td>
                <td>
                    <span class="badge log-type-badge ${getLogTypeBadgeClass(log.log_type)}">
                        ${getLogTypeName(log.log_type)}
                    </span>
                </td>
                <td>
                    <span class="badge log-level-badge ${getLevelBadgeClass(log.level)}">
                        ${getLevelName(log.level)}
                    </span>
                </td>
                <td>
                    <small>${log.username || '系统'}</small>
                </td>
                <td>
                    <small>${log.action || '-'}</small>
                </td>
                <td>
                    <small>${log.ip_address || '-'}</small>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetail(${log.id})" title="查看详情">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }
    
    // 获取日志类型徽章样式
    function getLogTypeBadgeClass(logType) {
        const classes = {
            'operation': 'bg-primary',
            'login': 'bg-success',
            'system': 'bg-info',
            'data_change': 'bg-warning text-dark'
        };
        return classes[logType] || 'bg-secondary';
    }
    
    // 获取日志类型名称
    function getLogTypeName(logType) {
        const names = {
            'operation': '操作',
            'login': '登录',
            'system': '系统',
            'data_change': '数据变更'
        };
        return names[logType] || logType;
    }
    
    // 获取级别徽章样式
    function getLevelBadgeClass(level) {
        const classes = {
            'info': 'bg-info',
            'warning': 'bg-warning text-dark',
            'error': 'bg-danger',
            'critical': 'bg-dark'
        };
        return classes[level] || 'bg-secondary';
    }
    
    // 获取级别名称
    function getLevelName(level) {
        const names = {
            'info': '信息',
            'warning': '警告',
            'error': '错误',
            'critical': '严重'
        };
        return names[level] || level;
    }
    
    // 渲染分页
    function renderPagination(pagination) {
        const container = document.getElementById('pagination-container');
        const paginationEl = document.getElementById('pagination');
        
        if (pagination.total_pages <= 1) {
            container.style.display = 'none';
            return;
        }
        
        container.style.display = 'block';
        
        let html = '';
        
        // 上一页
        html += `
            <li class="page-item ${pagination.current_page === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadLogs(${pagination.current_page - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;
        
        // 页码
        for (let i = 1; i <= pagination.total_pages; i++) {
            if (i === 1 || i === pagination.total_pages || 
                (i >= pagination.current_page - 2 && i <= pagination.current_page + 2)) {
                html += `
                    <li class="page-item ${i === pagination.current_page ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="loadLogs(${i})">${i}</a>
                    </li>
                `;
            } else if (i === pagination.current_page - 3 || i === pagination.current_page + 3) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // 下一页
        html += `
            <li class="page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="loadLogs(${pagination.current_page + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;
        
        paginationEl.innerHTML = html;
    }
    
    // 搜索日志
    function searchLogs() {
        currentPage = 1;
        loadLogs(1);
    }
    
    // 刷新日志
    function refreshLogs() {
        loadLogs(currentPage);
        loadLogStats();
    }
    
    // 显示加载状态
    function showLoading(show) {
        const loading = document.getElementById('loading-indicator');
        const table = document.getElementById('logs-table-container');
        const empty = document.getElementById('empty-state');
        
        if (show) {
            loading.style.display = 'block';
            table.style.display = 'none';
            empty.style.display = 'none';
        } else {
            loading.style.display = 'none';
        }
    }
    
    // 设置时间范围
    function setTimeRange(range) {
        const now = new Date();
        const startTime = document.getElementById('start-time');
        const endTime = document.getElementById('end-time');
        
        let start, end;
        
        switch (range) {
            case 'today':
                start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
                break;
            case 'yesterday':
                start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
                end = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59);
                break;
            case 'week':
                const weekStart = now.getDate() - now.getDay();
                start = new Date(now.getFullYear(), now.getMonth(), weekStart);
                end = new Date(now.getFullYear(), now.getMonth(), weekStart + 6, 23, 59, 59);
                break;
            case 'month':
                start = new Date(now.getFullYear(), now.getMonth(), 1);
                end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
                break;
        }
        
        if (start && end) {
            startTime.value = start.toISOString().slice(0, 16);
            endTime.value = end.toISOString().slice(0, 16);
        }
    }
    
    // 清除时间范围
    function clearTimeRange() {
        document.getElementById('start-time').value = '';
        document.getElementById('end-time').value = '';
    }
    
    // 全选/取消全选
    function toggleSelectAll() {
        const selectAll = document.getElementById('select-all');
        const checkboxes = document.querySelectorAll('.log-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll.checked;
        });
        
        updateSelection();
    }
    
    // 更新选择状态
    function updateSelection() {
        const checkboxes = document.querySelectorAll('.log-checkbox:checked');
        const batchDeleteBtn = document.getElementById('batch-delete-btn');
        
        selectedLogs.clear();
        checkboxes.forEach(checkbox => {
            selectedLogs.add(parseInt(checkbox.value));
        });
        
        if (selectedLogs.size > 0) {
            batchDeleteBtn.style.display = 'inline-block';
        } else {
            batchDeleteBtn.style.display = 'none';
        }
        
        // 更新全选状态
        const allCheckboxes = document.querySelectorAll('.log-checkbox');
        const selectAll = document.getElementById('select-all');
        selectAll.checked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;
    }
    
    // 查看日志详情
    async function viewLogDetail(logId) {
        try {
            const response = await apiRequest(`/api/v1/auth/audit-logs/${logId}`);
            
            if (response.code === 200) {
                const log = response.data.log;
                
                document.getElementById('detail-timestamp').textContent = formatDateTime(log.timestamp);
                document.getElementById('detail-log-type').innerHTML = `<span class="badge ${getLogTypeBadgeClass(log.log_type)}">${getLogTypeName(log.log_type)}</span>`;
                document.getElementById('detail-level').innerHTML = `<span class="badge ${getLevelBadgeClass(log.level)}">${getLevelName(log.level)}</span>`;
                document.getElementById('detail-user').textContent = log.username || '系统';
                document.getElementById('detail-ip').textContent = log.ip_address || '-';
                document.getElementById('detail-user-agent').textContent = log.user_agent || '-';
                document.getElementById('detail-action').textContent = log.action || '-';
                document.getElementById('detail-resource').textContent = log.resource || '-';
                document.getElementById('detail-details').textContent = JSON.stringify(log.details, null, 2) || '-';
                
                // 显示数据变更信息（如果有）
                const changesContainer = document.getElementById('detail-changes-container');
                if (log.changes) {
                    document.getElementById('detail-changes').textContent = JSON.stringify(log.changes, null, 2);
                    changesContainer.style.display = 'block';
                } else {
                    changesContainer.style.display = 'none';
                }
                
                new bootstrap.Modal(document.getElementById('logDetailModal')).show();
            } else {
                showMessage(response.msg || '获取日志详情失败', 'error');
            }
        } catch (error) {
            console.error('获取日志详情失败:', error);
            showMessage('获取日志详情失败', 'error');
        }
    }
    
    // 批量删除日志
    async function batchDeleteLogs() {
        if (selectedLogs.size === 0) {
            showMessage('请选择要删除的日志', 'warning');
            return;
        }
        
        if (!confirm(`确定要删除选中的 ${selectedLogs.size} 条日志吗？此操作不可撤销。`)) {
            return;
        }
        
        try {
            const response = await apiRequest('/api/v1/auth/audit-logs/batch-delete', {
                method: 'POST',
                body: JSON.stringify({ log_ids: Array.from(selectedLogs) })
            });
            
            if (response.code === 200) {
                showMessage(`成功删除 ${selectedLogs.size} 条日志`, 'success');
                selectedLogs.clear();
                document.getElementById('batch-delete-btn').style.display = 'none';
                document.getElementById('select-all').checked = false;
                loadLogs(currentPage);
                loadLogStats();
            } else {
                showMessage(response.msg || '批量删除失败', 'error');
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            showMessage('批量删除失败', 'error');
        }
    }
    
    // 清理旧日志
    function clearOldLogs() {
        // 设置默认日期为30天前
        const date = new Date();
        date.setDate(date.getDate() - 30);
        document.getElementById('clear-before-date').value = date.toISOString().split('T')[0];
        
        new bootstrap.Modal(document.getElementById('clearLogsModal')).show();
    }
    
    // 处理清理旧日志
    async function handleClearLogs(e) {
        e.preventDefault();
        
        const beforeDate = document.getElementById('clear-before-date').value;
        const logTypes = [];
        
        if (document.getElementById('clear-operation').checked) logTypes.push('operation');
        if (document.getElementById('clear-login').checked) logTypes.push('login');
        if (document.getElementById('clear-system').checked) logTypes.push('system');
        if (document.getElementById('clear-data-change').checked) logTypes.push('data_change');
        
        if (logTypes.length === 0) {
            showMessage('请至少选择一种日志类型', 'warning');
            return;
        }
        
        try {
            const response = await apiRequest('/api/v1/auth/audit-logs/clear-old', {
                method: 'POST',
                body: JSON.stringify({
                    before_date: beforeDate,
                    log_types: logTypes
                })
            });
            
            if (response.code === 200) {
                showMessage(`成功清理 ${response.data.deleted_count} 条旧日志`, 'success');
                bootstrap.Modal.getInstance(document.getElementById('clearLogsModal')).hide();
                loadLogs(currentPage);
                loadLogStats();
            } else {
                showMessage(response.msg || '清理旧日志失败', 'error');
            }
        } catch (error) {
            console.error('清理旧日志失败:', error);
            showMessage('清理旧日志失败', 'error');
        }
    }
    
    // 导出日志
    async function exportLogs() {
        const format = document.getElementById('export-format').value;
        const range = document.getElementById('export-range').value;
        
        try {
            let url = '/api/v1/auth/audit-logs/export';
            let body = {
                format: format,
                range: range
            };
            
            // 根据导出范围设置参数
            if (range === 'current') {
                body.keyword = document.getElementById('search-keyword').value;
                body.log_type = document.getElementById('filter-log-type').value;
                body.level = document.getElementById('filter-level').value;
                body.user_id = document.getElementById('filter-user').value;
                body.start_time = document.getElementById('start-time').value;
                body.end_time = document.getElementById('end-time').value;
                body.sort_by = document.getElementById('sort-by').value;
            } else if (range === 'selected') {
                if (selectedLogs.size === 0) {
                    showMessage('请选择要导出的日志', 'warning');
                    return;
                }
                body.log_ids = Array.from(selectedLogs);
            }
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')}`
                },
                body: JSON.stringify(body)
            });
            
            if (response.ok) {
                const blob = await response.blob();
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = downloadUrl;
                a.download = `audit_logs_${new Date().toISOString().split('T')[0]}.${format}`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(downloadUrl);
                document.body.removeChild(a);
                
                showMessage('日志导出成功', 'success');
                bootstrap.Collapse.getInstance(document.getElementById('exportOptions')).hide();
            } else {
                showMessage('导出失败', 'error');
            }
        } catch (error) {
            console.error('导出失败:', error);
            showMessage('导出失败', 'error');
        }
    }
</script>
{% endblock %}