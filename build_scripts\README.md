# 局域网在线考试系统 - PyInstaller 构建脚本

## 概述

本目录包含用于将局域网在线考试系统的各个模块打包为独立可执行文件的 PyInstaller 构建脚本。系统采用模块化架构，每个模块都可以独立打包和部署。

## 系统架构

### 模块组成

1. **用户管理模块** (`user-management`)
   - 功能：用户注册、登录、权限管理
   - 端口：8001
   - 主入口：`user-management/run.py`

2. **题库管理模块** (`question_bank`)
   - 功能：试题管理、题库维护、考试配置
   - 端口：8002
   - 主入口：`question_bank/run.py`

3. **成绩上报模块** (`exam_score_reporting_interface`)
   - 功能：成绩收集、数据上报、结果统计
   - 端口：8003
   - 主入口：`exam_score_reporting_interface/run.py`
   - **特别说明**：这是系统中唯一允许连接互联网的模块

## 构建脚本说明

### 单独构建脚本

| 脚本文件 | 目标模块 | 说明 |
|---------|---------|------|
| `build_user_management.py` | 用户管理模块 | 构建用户管理模块的独立可执行文件 |
| `build_question_bank.py` | 题库管理模块 | 构建题库管理模块的独立可执行文件 |
| `build_exam_score_reporting.py` | 成绩上报模块 | 构建成绩上报模块的独立可执行文件 |

### 批量构建脚本

| 脚本文件 | 说明 |
|---------|------|
| `build_all_modules.py` | 批量构建所有模块或指定模块 |

## 环境要求

### 系统要求
- Windows 10/11 或 Windows Server 2016+
- Python 3.8+
- 至少 4GB 可用磁盘空间

### Python 依赖
```bash
pip install pyinstaller>=6.0.0
```

### 模块依赖
每个模块的具体依赖请参考对应的 `requirements.txt` 文件：
- `user-management/requirements.txt`
- `question_bank/requirements.txt`
- `exam_score_reporting_interface/requirements.txt`

## 使用方法

### 1. 构建所有模块

```bash
# 在项目根目录执行
python build_scripts/build_all_modules.py
```

### 2. 构建指定模块

```bash
# 只构建用户管理模块
python build_scripts/build_all_modules.py --modules user_management

# 构建用户管理和题库管理模块
python build_scripts/build_all_modules.py --modules user_management,question_bank

# 构建成绩上报模块
python build_scripts/build_all_modules.py --modules exam_score_reporting
```

### 3. 清理构建

```bash
# 清理 dist 目录后重新构建
python build_scripts/build_all_modules.py --clean
```

### 4. 单独构建模块

```bash
# 构建用户管理模块
python build_scripts/build_user_management.py

# 构建题库管理模块
python build_scripts/build_question_bank.py

# 构建成绩上报模块
python build_scripts/build_exam_score_reporting.py
```

## 输出结构

构建完成后，可执行文件将位于 `dist/` 目录下：

```
dist/
├── user_management/
│   ├── user_management.exe          # 主可执行文件
│   ├── start_user_management.bat    # 启动脚本
│   ├── config/                      # 配置文件目录
│   ├── tools/                       # 工具脚本
│   └── _internal/                   # PyInstaller 内部文件
├── question_bank/
│   ├── question_bank.exe
│   ├── start_question_bank.bat
│   ├── config/
│   ├── tools/
│   └── _internal/
├── exam_score_reporting/
│   ├── exam_score_reporting.exe
│   ├── start_exam_score_reporting.bat
│   ├── config/
│   ├── tools/
│   ├── SECURITY.txt                 # 安全说明
│   └── _internal/
└── 使用指南.txt                      # 系统使用指南
```

## 配置说明

### 环境变量配置

每个模块都需要配置相应的环境变量，配置文件位于各模块的 `config/` 目录：

1. **用户管理模块** (`.env`)
   ```
   DATABASE_URL=sqlite:///users.db
   SECRET_KEY=your_secret_key_here
   JWT_SECRET_KEY=your_jwt_secret_here
   ```

2. **题库管理模块** (`.env`)
   ```
   DATABASE_URL=sqlite:///questions.db
   UPLOAD_FOLDER=uploads
   MAX_CONTENT_LENGTH=********
   ```

3. **成绩上报模块** (`.env`)
   ```
   DATABASE_URL=sqlite:///exam_scores.db
   REPORT_SERVER_URL=https://api.example.com/scores
   REPORT_API_KEY=your_api_key_here
   ENCRYPTION_KEY=your_encryption_key_here
   ```

### 端口配置

确保各模块使用不同的端口，避免冲突：
- 用户管理模块：8001
- 题库管理模块：8002
- 成绩上报模块：8003
- API网关：8080
- 主控台：8000

## 部署方式

### 方式一：集成系统部署

1. 使用主启动器 `main_launcher.py` 统一管理
2. 通过主控台界面控制所有模块
3. 提供统一的用户认证和权限控制
4. 适用于单机或小规模部署

### 方式二：分布式部署

1. 将各模块的可执行文件分别部署到不同服务器
2. 每个模块独立运行，通过网络通信
3. 需要配置模块间的网络连接
4. 适用于大规模或高可用部署

## 安全注意事项

### 网络安全

1. **局域网限制**：用户管理模块和题库管理模块仅限局域网访问
2. **互联网连接**：成绩上报模块是唯一允许连接互联网的组件
3. **防火墙配置**：确保适当的防火墙规则
4. **加密通信**：模块间通信应使用加密通道

### 数据安全

1. **数据库加密**：敏感数据应加密存储
2. **备份策略**：定期备份数据库和配置文件
3. **访问控制**：实施基于角色的访问控制
4. **审计日志**：记录所有重要操作

### 成绩上报安全

1. **传输加密**：所有成绩数据传输必须使用加密通道
2. **数据完整性**：传输前进行数据完整性验证
3. **权限控制**：仅限授权用户操作
4. **操作审计**：详细记录操作日志
5. **二次确认**：上报操作需要二次确认

## 故障排除

### 常见问题

#### 1. 构建失败

**问题**：PyInstaller 构建过程中出错

**解决方案**：
```bash
# 检查 PyInstaller 版本
pip show pyinstaller

# 更新 PyInstaller
pip install --upgrade pyinstaller

# 清理缓存
pyinstaller --clean your_script.py
```

#### 2. 模块导入错误

**问题**：运行时提示模块导入失败

**解决方案**：
- 检查 `--hidden-import` 参数是否包含所有必要模块
- 查看构建脚本中的隐藏导入列表
- 使用 `--collect-all` 参数包含整个包

#### 3. 文件路径错误

**问题**：运行时找不到模板或静态文件

**解决方案**：
- 检查 `--add-data` 参数是否正确
- 确认文件路径使用相对路径
- 验证目标目录结构

#### 4. 端口冲突

**问题**：模块启动时提示端口被占用

**解决方案**：
```bash
# 检查端口占用
netstat -an | findstr :8001

# 终止占用进程
taskkill /PID <进程ID> /F
```

#### 5. 数据库连接失败

**问题**：模块启动时数据库连接失败

**解决方案**：
- 检查数据库文件路径
- 验证数据库权限
- 确认数据库文件存在
- 检查 SQLite 版本兼容性

### 调试技巧

#### 1. 启用详细输出

```bash
# 构建时启用详细输出
python build_scripts/build_all_modules.py --verbose

# PyInstaller 调试模式
pyinstaller --debug=all your_script.py
```

#### 2. 检查依赖

```bash
# 分析模块依赖
pyinstaller --collect-all your_module --dry-run your_script.py

# 检查导入
python -c "import your_module; print(your_module.__file__)"
```

#### 3. 日志分析

- 查看构建日志：`build.log`
- 查看运行日志：各模块的日志文件
- 查看系统事件日志

## 性能优化

### 构建优化

1. **排除不必要模块**：使用 `--exclude-module` 参数
2. **压缩可执行文件**：使用 `--upx-dir` 参数（需要 UPX）
3. **并行构建**：同时构建多个模块

### 运行时优化

1. **内存优化**：调整 Python 内存参数
2. **启动优化**：预加载常用模块
3. **缓存优化**：合理使用缓存机制

## 版本管理

### 构建版本

每次构建都会在输出目录生成版本信息文件，包含：
- 构建时间
- Python 版本
- PyInstaller 版本
- 模块版本
- 依赖版本

### 更新流程

1. 更新源代码
2. 更新依赖包
3. 测试功能
4. 重新构建
5. 部署验证

## 技术支持

### 文档资源

- [PyInstaller 官方文档](https://pyinstaller.readthedocs.io/)
- [Flask 部署指南](https://flask.palletsprojects.com/en/2.0.x/deploying/)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)

### 联系方式

如遇到技术问题，请提供以下信息：
- 操作系统版本
- Python 版本
- PyInstaller 版本
- 错误日志
- 复现步骤

---

**注意**：本文档会随着系统更新而更新，请定期查看最新版本。