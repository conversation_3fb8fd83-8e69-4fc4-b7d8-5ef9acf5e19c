# -*- coding: utf-8 -*-
"""
API网关规范配置
定义符合API网关标准的接口规范、错误处理和数据验证
"""

import re
from typing import Dict, List, Any, Optional
from functools import wraps
from flask import request, jsonify, current_app
import logging

# API版本配置
API_VERSION = 'v1'
API_PREFIX = f'/api/{API_VERSION}'

# 模块标识
MODULE_NAME = 'practical-task-management'
MODULE_CODE = 'PTM'

# 错误代码定义（符合API网关规范）
ERROR_CODES = {
    # 成功
    'SUCCESS': {'code': 200, 'msg': '操作成功'},
    
    # 客户端错误 (4xx)
    'BAD_REQUEST': {'code': 400, 'msg': '请求参数错误'},
    'UNAUTHORIZED': {'code': 401, 'msg': '未授权访问'},
    'FORBIDDEN': {'code': 403, 'msg': '权限不足'},
    'NOT_FOUND': {'code': 404, 'msg': '资源不存在'},
    'METHOD_NOT_ALLOWED': {'code': 405, 'msg': '请求方法不允许'},
    'CONFLICT': {'code': 409, 'msg': '资源冲突'},
    'VALIDATION_ERROR': {'code': 422, 'msg': '数据验证失败'},
    'TOO_MANY_REQUESTS': {'code': 429, 'msg': '请求过于频繁'},
    
    # 服务器错误 (5xx)
    'INTERNAL_ERROR': {'code': 500, 'msg': '服务器内部错误'},
    'NOT_IMPLEMENTED': {'code': 501, 'msg': '功能未实现'},
    'BAD_GATEWAY': {'code': 502, 'msg': '网关错误'},
    'SERVICE_UNAVAILABLE': {'code': 503, 'msg': '服务不可用'},
    'GATEWAY_TIMEOUT': {'code': 504, 'msg': '网关超时'},
    
    # 业务错误 (自定义)
    'TASK_NOT_FOUND': {'code': 10001, 'msg': '任务不存在'},
    'CATEGORY_NOT_FOUND': {'code': 10002, 'msg': '分类不存在'},
    'MATERIAL_NOT_FOUND': {'code': 10003, 'msg': '素材不存在'},
    'EXECUTION_NOT_FOUND': {'code': 10004, 'msg': '执行记录不存在'},
    'USER_NOT_FOUND': {'code': 10005, 'msg': '用户不存在'},
    'PERMISSION_DENIED': {'code': 10006, 'msg': '权限被拒绝'},
    'INVALID_FILE_TYPE': {'code': 10007, 'msg': '不支持的文件类型'},
    'FILE_TOO_LARGE': {'code': 10008, 'msg': '文件大小超出限制'},
    'DUPLICATE_NAME': {'code': 10009, 'msg': '名称已存在'},
    'INVALID_STATUS': {'code': 10010, 'msg': '状态无效'},
    'OPERATION_FAILED': {'code': 10011, 'msg': '操作失败'},
    'DATA_INTEGRITY_ERROR': {'code': 10012, 'msg': '数据完整性错误'},
    'CONCURRENT_MODIFICATION': {'code': 10013, 'msg': '并发修改冲突'},
    'QUOTA_EXCEEDED': {'code': 10014, 'msg': '配额超出限制'},
    'DEPENDENCY_ERROR': {'code': 10015, 'msg': '依赖关系错误'}
}

# 数据验证规则
VALIDATION_RULES = {
    # 通用规则
    'id': {
        'type': 'integer',
        'min': 1,
        'required': True,
        'description': 'ID必须为正整数'
    },
    'name': {
        'type': 'string',
        'min_length': 1,
        'max_length': 100,
        'required': True,
        'pattern': r'^[\u4e00-\u9fa5a-zA-Z0-9_\-\s]+$',
        'description': '名称长度1-100字符，支持中文、英文、数字、下划线、连字符和空格'
    },
    'description': {
        'type': 'string',
        'max_length': 1000,
        'required': False,
        'description': '描述最大长度1000字符'
    },
    'status': {
        'type': 'string',
        'enum': ['active', 'inactive', 'draft', 'published', 'archived'],
        'required': True,
        'description': '状态必须为指定值之一'
    },
    'page': {
        'type': 'integer',
        'min': 1,
        'default': 1,
        'description': '页码必须为正整数'
    },
    'per_page': {
        'type': 'integer',
        'min': 1,
        'max': 100,
        'default': 20,
        'description': '每页数量1-100'
    },
    'sort_by': {
        'type': 'string',
        'enum': ['id', 'name', 'created_at', 'updated_at'],
        'default': 'created_at',
        'description': '排序字段'
    },
    'sort_order': {
        'type': 'string',
        'enum': ['asc', 'desc'],
        'default': 'desc',
        'description': '排序方向'
    },
    
    # 任务相关
    'task_title': {
        'type': 'string',
        'min_length': 1,
        'max_length': 200,
        'required': True,
        'description': '任务标题长度1-200字符'
    },
    'task_type': {
        'type': 'string',
        'enum': ['practical', 'theoretical', 'mixed'],
        'required': True,
        'description': '任务类型'
    },
    'difficulty_level': {
        'type': 'integer',
        'min': 1,
        'max': 5,
        'required': True,
        'description': '难度等级1-5'
    },
    'estimated_duration': {
        'type': 'integer',
        'min': 1,
        'max': 480,
        'required': True,
        'description': '预计时长1-480分钟'
    },
    'max_score': {
        'type': 'number',
        'min': 0,
        'max': 1000,
        'required': True,
        'description': '满分分值0-1000'
    },
    
    # 分类相关
    'software_type': {
        'type': 'string',
        'enum': ['office', 'programming', 'design', 'database', 'network', 'system', 'other'],
        'required': True,
        'description': '软件类型'
    },
    
    # 文件相关
    'file_type': {
        'type': 'string',
        'enum': ['document', 'image', 'video', 'audio', 'archive', 'other'],
        'required': True,
        'description': '文件类型'
    },
    'file_size': {
        'type': 'integer',
        'min': 0,
        'max': 104857600,  # 100MB
        'description': '文件大小0-100MB'
    },
    
    # 用户相关
    'username': {
        'type': 'string',
        'min_length': 3,
        'max_length': 50,
        'pattern': r'^[a-zA-Z0-9_]+$',
        'required': True,
        'description': '用户名3-50字符，仅支持英文、数字和下划线'
    },
    'email': {
        'type': 'string',
        'pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
        'required': True,
        'description': '邮箱格式不正确'
    },
    'password': {
        'type': 'string',
        'min_length': 6,
        'max_length': 128,
        'required': True,
        'description': '密码长度6-128字符'
    },
    'role': {
        'type': 'string',
        'enum': ['admin', 'teacher', 'student', 'examiner'],
        'required': True,
        'description': '用户角色'
    }
}

# 接口限流配置
RATE_LIMIT_CONFIG = {
    'default': '100/hour',  # 默认限流
    'auth': '10/minute',    # 认证接口
    'upload': '20/hour',    # 文件上传
    'export': '5/hour',     # 数据导出
    'batch': '10/hour'      # 批量操作
}

# 请求日志配置
LOGGING_CONFIG = {
    'enabled': True,
    'log_request': True,
    'log_response': True,
    'log_headers': False,
    'log_body': False,
    'sensitive_fields': ['password', 'token', 'secret'],
    'max_body_size': 1024  # 最大记录的请求体大小
}

# CORS配置
CORS_CONFIG = {
    'origins': ['http://localhost:8000', 'http://localhost:8080'],
    'methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    'headers': ['Content-Type', 'Authorization', 'X-Requested-With'],
    'credentials': True
}

class APIResponse:
    """API响应格式化类"""
    
    @staticmethod
    def success(data=None, msg='操作成功', code=200):
        """成功响应"""
        return {
            'code': code,
            'msg': msg,
            'data': data or {},
            'timestamp': int(time.time() * 1000),
            'module': MODULE_NAME
        }
    
    @staticmethod
    def error(error_key='INTERNAL_ERROR', msg=None, data=None, details=None):
        """错误响应"""
        error_info = ERROR_CODES.get(error_key, ERROR_CODES['INTERNAL_ERROR'])
        
        response = {
            'code': error_info['code'],
            'msg': msg or error_info['msg'],
            'data': data or {},
            'timestamp': int(time.time() * 1000),
            'module': MODULE_NAME
        }
        
        if details:
            response['details'] = details
            
        return response
    
    @staticmethod
    def paginated(data, pagination):
        """分页响应"""
        return APIResponse.success({
            'items': data,
            'pagination': pagination
        })

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_field(value, rule_name, custom_rule=None):
        """验证单个字段"""
        rule = custom_rule or VALIDATION_RULES.get(rule_name)
        if not rule:
            return True, None
        
        # 必填验证
        if rule.get('required', False) and (value is None or value == ''):
            return False, f"{rule_name}为必填项"
        
        # 如果不是必填且值为空，跳过其他验证
        if not rule.get('required', False) and (value is None or value == ''):
            return True, None
        
        # 类型验证
        field_type = rule.get('type')
        if field_type == 'string' and not isinstance(value, str):
            return False, f"{rule_name}必须为字符串"
        elif field_type == 'integer' and not isinstance(value, int):
            try:
                value = int(value)
            except (ValueError, TypeError):
                return False, f"{rule_name}必须为整数"
        elif field_type == 'number' and not isinstance(value, (int, float)):
            try:
                value = float(value)
            except (ValueError, TypeError):
                return False, f"{rule_name}必须为数字"
        
        # 长度验证
        if field_type == 'string':
            if 'min_length' in rule and len(value) < rule['min_length']:
                return False, f"{rule_name}长度不能少于{rule['min_length']}字符"
            if 'max_length' in rule and len(value) > rule['max_length']:
                return False, f"{rule_name}长度不能超过{rule['max_length']}字符"
        
        # 数值范围验证
        if field_type in ['integer', 'number']:
            if 'min' in rule and value < rule['min']:
                return False, f"{rule_name}不能小于{rule['min']}"
            if 'max' in rule and value > rule['max']:
                return False, f"{rule_name}不能大于{rule['max']}"
        
        # 枚举值验证
        if 'enum' in rule and value not in rule['enum']:
            return False, f"{rule_name}必须为以下值之一: {', '.join(rule['enum'])}"
        
        # 正则表达式验证
        if 'pattern' in rule and field_type == 'string':
            if not re.match(rule['pattern'], value):
                return False, rule.get('description', f"{rule_name}格式不正确")
        
        return True, None
    
    @staticmethod
    def validate_data(data, rules):
        """验证数据对象"""
        errors = {}
        
        for field_name, rule_name in rules.items():
            value = data.get(field_name)
            is_valid, error_msg = DataValidator.validate_field(value, rule_name)
            
            if not is_valid:
                errors[field_name] = error_msg
        
        return len(errors) == 0, errors

def api_response(func):
    """API响应装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            
            # 如果返回的是字典且包含code字段，直接返回
            if isinstance(result, dict) and 'code' in result:
                return jsonify(result)
            
            # 否则包装为成功响应
            return jsonify(APIResponse.success(result))
            
        except ValidationError as e:
            current_app.logger.warning(f"数据验证失败: {str(e)}")
            return jsonify(APIResponse.error('VALIDATION_ERROR', str(e))), 422
        
        except PermissionError as e:
            current_app.logger.warning(f"权限不足: {str(e)}")
            return jsonify(APIResponse.error('FORBIDDEN', str(e))), 403
        
        except FileNotFoundError as e:
            current_app.logger.warning(f"资源不存在: {str(e)}")
            return jsonify(APIResponse.error('NOT_FOUND', str(e))), 404
        
        except ValueError as e:
            current_app.logger.warning(f"参数错误: {str(e)}")
            return jsonify(APIResponse.error('BAD_REQUEST', str(e))), 400
        
        except Exception as e:
            current_app.logger.error(f"服务器内部错误: {str(e)}", exc_info=True)
            return jsonify(APIResponse.error('INTERNAL_ERROR')), 500
    
    return wrapper

def validate_request(rules):
    """请求验证装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取请求数据
            if request.method in ['POST', 'PUT', 'PATCH']:
                data = request.get_json() or {}
            else:
                data = request.args.to_dict()
            
            # 验证数据
            is_valid, errors = DataValidator.validate_data(data, rules)
            
            if not is_valid:
                return jsonify(APIResponse.error('VALIDATION_ERROR', '数据验证失败', errors)), 422
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator

class ValidationError(Exception):
    """数据验证异常"""
    pass

class PermissionError(Exception):
    """权限异常"""
    pass

# 导入时间模块
import time