# Project Setup Guide

This document provides instructions on how to set up the development environment for this project. The project uses a centralized dependency management system for all its Python-based microservices.

## 1. Environment Requirements

- Python 3.8+
- A virtual environment tool (like `venv` or `virtualenv`)

## 2. Environment Setup

Follow these steps to create a clean, isolated environment for the project.

### Step 1: Create a Virtual Environment

It is highly recommended to use a virtual environment to manage project-specific dependencies. From the project's root directory, run the following command:

```bash
# For Linux/macOS
python3 -m venv venv

# For Windows
python -m venv venv
```

This will create a `venv` directory in the project root.

### Step 2: Activate the Virtual Environment

Before installing dependencies, you must activate the virtual environment.

```bash
# For Linux/macOS
source venv/bin/activate

# For Windows
.\venv\Scripts\activate
```

Once activated, your shell prompt should be prefixed with `(venv)`.

### Step 3: Install Dependencies

All required Python packages for every microservice are listed in the `requirements.txt` file in the root directory. Install them using pip:

```bash
pip install -r requirements.txt
```

This command will install all necessary libraries into your virtual environment.

## 3. Running the Services

With the environment set up, you can now run the individual microservices. Each service (e.g., `user-management`, `api-gateway`) has its own startup script (e.g., `run.py` or `app.py`).

Refer to the `docker-compose.yml` file or the individual service's documentation for more details on how to launch the entire application stack.
