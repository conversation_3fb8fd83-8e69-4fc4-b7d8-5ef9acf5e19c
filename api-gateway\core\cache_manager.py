#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存管理器 - 提供多层缓存和性能优化

功能:
1. 内存缓存 (LRU)
2. Redis缓存 (可选)
3. 文件缓存
4. 缓存预热
5. 缓存统计和监控
6. 自动过期和清理

作者: SOLO Coding
创建时间: 2025-08-15
"""

import os
import json
import time
import pickle
import hashlib
import logging
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Callable
from functools import wraps, lru_cache
from collections import OrderedDict
from dataclasses import dataclass, asdict
import weakref

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    sets: int = 0
    deletes: int = 0
    evictions: int = 0
    
    @property
    def hit_rate(self) -> float:
        """命中率"""
        total = self.hits + self.misses
        return self.hits / total if total > 0 else 0.0
    
    def reset(self):
        """重置统计"""
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self.deletes = 0
        self.evictions = 0

@dataclass
class CacheEntry:
    """缓存条目"""
    value: Any
    created_at: datetime
    accessed_at: datetime
    access_count: int = 0
    ttl: Optional[int] = None  # 生存时间(秒)
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return (datetime.now() - self.created_at).total_seconds() > self.ttl
    
    def touch(self):
        """更新访问时间"""
        self.accessed_at = datetime.now()
        self.access_count += 1

class LRUCache:
    """LRU内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[int] = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self.stats = CacheStats()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self.stats.misses += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self.stats.misses += 1
                self.stats.evictions += 1
                return None
            
            # 更新访问信息
            entry.touch()
            
            # 移动到末尾(最近使用)
            self._cache.move_to_end(key)
            
            self.stats.hits += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self._lock:
            # 使用默认TTL
            if ttl is None:
                ttl = self.default_ttl
            
            # 创建缓存条目
            entry = CacheEntry(
                value=value,
                created_at=datetime.now(),
                accessed_at=datetime.now(),
                ttl=ttl
            )
            
            # 如果key已存在，更新它
            if key in self._cache:
                self._cache[key] = entry
                self._cache.move_to_end(key)
            else:
                # 检查是否需要淘汰
                if len(self._cache) >= self.max_size:
                    # 淘汰最久未使用的条目
                    oldest_key = next(iter(self._cache))
                    del self._cache[oldest_key]
                    self.stats.evictions += 1
                
                self._cache[key] = entry
            
            self.stats.sets += 1
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self.stats.deletes += 1
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self.stats.reset()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)
    
    def cleanup_expired(self) -> int:
        """清理过期条目"""
        with self._lock:
            expired_keys = []
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
                self.stats.evictions += 1
            
            return len(expired_keys)

class FileCache:
    """文件缓存实现"""
    
    def __init__(self, cache_dir: str, max_size_mb: int = 100):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_size_mb = max_size_mb
        self._lock = threading.RLock()
        self.stats = CacheStats()
    
    def _get_cache_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希避免文件名问题
        hash_key = hashlib.md5(key.encode('utf-8')).hexdigest()
        return self.cache_dir / f"{hash_key}.cache"
    
    def _get_meta_path(self, key: str) -> Path:
        """获取元数据文件路径"""
        hash_key = hashlib.md5(key.encode('utf-8')).hexdigest()
        return self.cache_dir / f"{hash_key}.meta"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            cache_path = self._get_cache_path(key)
            meta_path = self._get_meta_path(key)
            
            if not cache_path.exists() or not meta_path.exists():
                self.stats.misses += 1
                return None
            
            try:
                # 读取元数据
                with open(meta_path, 'r', encoding='utf-8') as f:
                    meta = json.load(f)
                
                # 检查是否过期
                if meta.get('ttl'):
                    created_at = datetime.fromisoformat(meta['created_at'])
                    if (datetime.now() - created_at).total_seconds() > meta['ttl']:
                        # 删除过期文件
                        cache_path.unlink(missing_ok=True)
                        meta_path.unlink(missing_ok=True)
                        self.stats.misses += 1
                        self.stats.evictions += 1
                        return None
                
                # 读取缓存数据
                with open(cache_path, 'rb') as f:
                    value = pickle.load(f)
                
                # 更新访问时间
                meta['accessed_at'] = datetime.now().isoformat()
                meta['access_count'] = meta.get('access_count', 0) + 1
                
                with open(meta_path, 'w', encoding='utf-8') as f:
                    json.dump(meta, f)
                
                self.stats.hits += 1
                return value
                
            except Exception as e:
                logger.warning(f"读取文件缓存失败 {key}: {e}")
                # 清理损坏的文件
                cache_path.unlink(missing_ok=True)
                meta_path.unlink(missing_ok=True)
                self.stats.misses += 1
                return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self._lock:
            try:
                cache_path = self._get_cache_path(key)
                meta_path = self._get_meta_path(key)
                
                # 写入缓存数据
                with open(cache_path, 'wb') as f:
                    pickle.dump(value, f)
                
                # 写入元数据
                meta = {
                    'key': key,
                    'created_at': datetime.now().isoformat(),
                    'accessed_at': datetime.now().isoformat(),
                    'access_count': 0,
                    'ttl': ttl
                }
                
                with open(meta_path, 'w', encoding='utf-8') as f:
                    json.dump(meta, f)
                
                self.stats.sets += 1
                
                # 检查缓存大小
                self._cleanup_if_needed()
                
            except Exception as e:
                logger.error(f"写入文件缓存失败 {key}: {e}")
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            cache_path = self._get_cache_path(key)
            meta_path = self._get_meta_path(key)
            
            deleted = False
            if cache_path.exists():
                cache_path.unlink()
                deleted = True
            
            if meta_path.exists():
                meta_path.unlink()
                deleted = True
            
            if deleted:
                self.stats.deletes += 1
            
            return deleted
    
    def _cleanup_if_needed(self):
        """如果需要则清理缓存"""
        total_size = sum(f.stat().st_size for f in self.cache_dir.glob('*.cache') if f.is_file())
        max_size_bytes = self.max_size_mb * 1024 * 1024
        
        if total_size > max_size_bytes:
            # 按访问时间排序，删除最久未访问的文件
            files_info = []
            for cache_file in self.cache_dir.glob('*.cache'):
                meta_file = cache_file.with_suffix('.meta')
                if meta_file.exists():
                    try:
                        with open(meta_file, 'r', encoding='utf-8') as f:
                            meta = json.load(f)
                        accessed_at = datetime.fromisoformat(meta.get('accessed_at', meta['created_at']))
                        files_info.append((accessed_at, cache_file, meta_file))
                    except:
                        # 删除损坏的文件
                        cache_file.unlink(missing_ok=True)
                        meta_file.unlink(missing_ok=True)
            
            # 按访问时间排序
            files_info.sort(key=lambda x: x[0])
            
            # 删除最久未访问的文件直到大小合适
            current_size = total_size
            for _, cache_file, meta_file in files_info:
                if current_size <= max_size_bytes * 0.8:  # 保留20%空间
                    break
                
                file_size = cache_file.stat().st_size
                cache_file.unlink(missing_ok=True)
                meta_file.unlink(missing_ok=True)
                current_size -= file_size
                self.stats.evictions += 1

class CacheManager:
    """智能缓存管理器 - 统一管理多层缓存"""
    
    def __init__(self, 
                 memory_cache_size: int = 1000,
                 file_cache_dir: str = None,
                 file_cache_size_mb: int = 100,
                 default_ttl: Optional[int] = 3600):
        
        # 初始化内存缓存
        self.memory_cache = LRUCache(max_size=memory_cache_size, default_ttl=default_ttl)
        
        # 初始化文件缓存
        if file_cache_dir is None:
            file_cache_dir = Path.cwd() / 'cache'
        self.file_cache = FileCache(file_cache_dir, file_cache_size_mb)
        
        # 缓存层级: memory -> file -> source
        self.default_ttl = default_ttl
        self._cleanup_thread = None
        self._cleanup_interval = 300  # 5分钟清理一次
        self._start_cleanup_thread()
        
        logger.info("智能缓存管理器初始化完成")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值 - 多层查找"""
        # 1. 先查内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # 2. 查文件缓存
        value = self.file_cache.get(key)
        if value is not None:
            # 回写到内存缓存
            self.memory_cache.set(key, value)
            return value
        
        return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, 
            memory_only: bool = False) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        # 设置内存缓存
        self.memory_cache.set(key, value, ttl)
        
        # 设置文件缓存(除非指定仅内存)
        if not memory_only:
            self.file_cache.set(key, value, ttl)
    
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        memory_deleted = self.memory_cache.delete(key)
        file_deleted = self.file_cache.delete(key)
        return memory_deleted or file_deleted
    
    def clear(self, pattern: str = None):
        """
        清理缓存
        
        Args:
            pattern: 匹配模式，如果为None则清理所有缓存
        """
        if pattern is None:
            # 清理所有缓存
            self.memory_cache.clear()
            # 文件缓存清理
            for cache_file in self.file_cache.cache_dir.glob('*'):
                if cache_file.is_file():
                    cache_file.unlink()
        else:
            # 按模式清理内存缓存
            keys_to_remove = [key for key in self.memory_cache._cache.keys() if pattern in key]
            for key in keys_to_remove:
                self.memory_cache.delete(key)
            
            # 按模式清理文件缓存
            for meta_file in self.file_cache.cache_dir.glob('*.meta'):
                try:
                    with open(meta_file, 'r', encoding='utf-8') as f:
                        meta = json.load(f)
                    if pattern in meta.get('key', ''):
                        cache_file = meta_file.with_suffix('.cache')
                        meta_file.unlink(missing_ok=True)
                        cache_file.unlink(missing_ok=True)
                except:
                    continue
    
    def clear_all(self):
        """
        清理所有缓存
        """
        self.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'memory_cache': {
                'size': self.memory_cache.size(),
                'max_size': self.memory_cache.max_size,
                'stats': asdict(self.memory_cache.stats)
            },
            'file_cache': {
                'stats': asdict(self.file_cache.stats)
            }
        }
    
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_loop():
            while not getattr(self, '_stop_cleanup', False):
                try:
                    # 清理过期的内存缓存
                    expired_count = self.memory_cache.cleanup_expired()
                    if expired_count > 0:
                        logger.debug(f"清理了 {expired_count} 个过期的内存缓存条目")
                    
                    time.sleep(self._cleanup_interval)
                except Exception as e:
                    logger.error(f"缓存清理线程异常: {e}")
                    time.sleep(60)  # 出错时等待1分钟
        
        self._cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        self._cleanup_thread.start()
    
    def start_cleanup_scheduler(self):
        """启动清理调度器"""
        if not hasattr(self, '_cleanup_thread') or not self._cleanup_thread.is_alive():
            self._stop_cleanup = False
            self._start_cleanup_thread()
            logger.info("缓存清理调度器已启动")
    
    def stop_cleanup_scheduler(self):
        """停止清理调度器"""
        self._stop_cleanup = True
        if hasattr(self, '_cleanup_thread') and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5)
            logger.info("缓存清理调度器已停止")
    
    def cache_function(self, ttl: Optional[int] = None, key_func: Optional[Callable] = None):
        """函数缓存装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 生成缓存键
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    # 默认键生成策略
                    key_parts = [func.__name__]
                    key_parts.extend(str(arg) for arg in args)
                    key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                    cache_key = ":".join(key_parts)
                
                # 尝试从缓存获取
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator
    
    def warm_up(self, warm_up_data: Dict[str, Any]):
        """缓存预热"""
        logger.info(f"开始缓存预热，预热 {len(warm_up_data)} 个条目")
        
        for key, value in warm_up_data.items():
            self.set(key, value)
        
        logger.info("缓存预热完成")

# 全局缓存管理器实例
_global_cache_manager = None

def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    global _global_cache_manager
    if _global_cache_manager is None:
        _global_cache_manager = CacheManager()
    return _global_cache_manager

# 便捷的装饰器
def cached(ttl: Optional[int] = None, key_func: Optional[Callable] = None):
    """缓存装饰器 - 使用全局缓存管理器"""
    return get_cache_manager().cache_function(ttl=ttl, key_func=key_func)

def main():
    """测试缓存管理器"""
    print("="*60)
    print("智能缓存管理器测试")
    print("="*60)
    
    # 创建缓存管理器
    cache = CacheManager(memory_cache_size=100, file_cache_size_mb=10)
    
    # 测试基本功能
    print("\n1. 测试基本缓存功能...")
    cache.set("test_key", "test_value")
    value = cache.get("test_key")
    print(f"缓存值: {value}")
    
    # 测试函数缓存
    print("\n2. 测试函数缓存...")
    
    @cache.cache_function(ttl=60)
    def expensive_function(x, y):
        print(f"执行昂贵计算: {x} + {y}")
        time.sleep(1)  # 模拟耗时操作
        return x + y
    
    # 第一次调用
    start_time = time.time()
    result1 = expensive_function(1, 2)
    time1 = time.time() - start_time
    print(f"第一次调用结果: {result1}, 耗时: {time1:.2f}秒")
    
    # 第二次调用(应该从缓存获取)
    start_time = time.time()
    result2 = expensive_function(1, 2)
    time2 = time.time() - start_time
    print(f"第二次调用结果: {result2}, 耗时: {time2:.2f}秒")
    
    # 显示统计信息
    print("\n3. 缓存统计信息:")
    stats = cache.get_stats()
    print(json.dumps(stats, indent=2, ensure_ascii=False))
    
    print("\n缓存管理器测试完成")

if __name__ == '__main__':
    main()