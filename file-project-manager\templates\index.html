<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件与项目管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/main.css') }}" rel="stylesheet">
</head>
<body>
    <!-- 返回主控台按钮 -->
    <div class="container-fluid mt-2 mb-2">
        <div class="row">
            <div class="col-12">
                <button class="btn btn-outline-primary" onclick="window.parent.location.href='/dashboard'">
                    <i class="fas fa-arrow-left me-2"></i>返回主控台
                </button>
            </div>
        </div>
    </div>
    
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-folder-open me-2"></i>文件与项目管理
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#dashboard" data-tab="dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#files" data-tab="files">
                            <i class="fas fa-file me-1"></i>文件管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#projects" data-tab="projects">
                            <i class="fas fa-project-diagram me-1"></i>项目管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#tasks" data-tab="tasks">
                            <i class="fas fa-tasks me-1"></i>任务管理
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <input type="text" class="form-control" id="globalSearch" placeholder="全局搜索...">
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>用户
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logoutBtn"><i class="fas fa-sign-out-alt me-2"></i>退出</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container-fluid mt-3">
        <!-- 仪表板 -->
        <div id="dashboard-content" class="tab-content active">
            <div class="row">
                <!-- 统计卡片 -->
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title" id="totalProjects">0</h4>
                                    <p class="card-text">总项目数</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-project-diagram fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title" id="totalTasks">0</h4>
                                    <p class="card-text">总任务数</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-tasks fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title" id="totalFiles">0</h4>
                                    <p class="card-text">总文件数</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-file fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title" id="storageUsed">0 MB</h4>
                                    <p class="card-text">存储使用</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-hdd fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <!-- 最近项目 -->
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock me-2"></i>最近项目
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="recentProjects" class="list-group list-group-flush">
                                <!-- 动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 最近任务 -->
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list me-2"></i>最近任务
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="recentTasks" class="list-group list-group-flush">
                                <!-- 动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件管理 -->
        <div id="files-content" class="tab-content">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-file me-2"></i>文件管理
                            </h5>
                            <div>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
                                    <i class="fas fa-upload me-1"></i>上传文件
                                </button>
                                <button class="btn btn-outline-secondary" id="refreshFiles">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 文件筛选和搜索 -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" id="fileSearch" placeholder="搜索文件...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="fileTypeFilter">
                                        <option value="">所有类型</option>
                                        <option value="document">文档</option>
                                        <option value="image">图片</option>
                                        <option value="archive">压缩包</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="projectFilter">
                                        <option value="">所有项目</option>
                                        <!-- 动态加载项目选项 -->
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary w-100" id="searchFiles">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 文件列表 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>文件名</th>
                                            <th>类型</th>
                                            <th>大小</th>
                                            <th>项目</th>
                                            <th>上传时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="filesTable">
                                        <!-- 动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <nav>
                                <ul class="pagination justify-content-center" id="filesPagination">
                                    <!-- 动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目管理 -->
        <div id="projects-content" class="tab-content">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-project-diagram me-2"></i>项目管理
                            </h5>
                            <div>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#projectModal">
                                    <i class="fas fa-plus me-1"></i>新建项目
                                </button>
                                <button class="btn btn-outline-secondary" id="refreshProjects">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 项目筛选和搜索 -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <input type="text" class="form-control" id="projectSearch" placeholder="搜索项目...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="projectStatusFilter">
                                        <option value="">所有状态</option>
                                        <option value="active">进行中</option>
                                        <option value="completed">已完成</option>
                                        <option value="paused">已暂停</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="projectSortBy">
                                        <option value="create_time">创建时间</option>
                                        <option value="update_time">更新时间</option>
                                        <option value="project_name">项目名称</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary w-100" id="searchProjects">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 项目列表 -->
                            <div class="row" id="projectsList">
                                <!-- 动态加载 -->
                            </div>
                            
                            <!-- 分页 -->
                            <nav>
                                <ul class="pagination justify-content-center" id="projectsPagination">
                                    <!-- 动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务管理 -->
        <div id="tasks-content" class="tab-content">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-tasks me-2"></i>任务管理
                            </h5>
                            <div>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#taskModal">
                                    <i class="fas fa-plus me-1"></i>新建任务
                                </button>
                                <button class="btn btn-outline-secondary" id="refreshTasks">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 任务筛选和搜索 -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="taskSearch" placeholder="搜索任务...">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="taskStatusFilter">
                                        <option value="">所有状态</option>
                                        <option value="pending">待处理</option>
                                        <option value="in_progress">进行中</option>
                                        <option value="completed">已完成</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-select" id="taskPriorityFilter">
                                        <option value="">所有优先级</option>
                                        <option value="high">高</option>
                                        <option value="medium">中</option>
                                        <option value="low">低</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select" id="taskProjectFilter">
                                        <option value="">所有项目</option>
                                        <!-- 动态加载项目选项 -->
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-primary w-100" id="searchTasks">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 任务列表 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>任务名称</th>
                                            <th>项目</th>
                                            <th>优先级</th>
                                            <th>状态</th>
                                            <th>截止时间</th>
                                            <th>分配给</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="tasksTable">
                                        <!-- 动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <nav>
                                <ul class="pagination justify-content-center" id="tasksPagination">
                                    <!-- 动态生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件上传模态框 -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">上传文件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="fileInput" class="form-label">选择文件</label>
                            <input type="file" class="form-control" id="fileInput" name="file" multiple>
                            <div class="form-text">支持多文件上传，单个文件最大100MB</div>
                        </div>
                        <div class="mb-3">
                            <label for="uploadProjectId" class="form-label">关联项目（可选）</label>
                            <select class="form-select" id="uploadProjectId" name="project_id">
                                <option value="">不关联项目</option>
                                <!-- 动态加载项目选项 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="fileDescription" class="form-label">文件描述（可选）</label>
                            <textarea class="form-control" id="fileDescription" name="description" rows="3"></textarea>
                        </div>
                    </form>
                    <div id="uploadProgress" class="mt-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="uploadBtn">上传</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目模态框 -->
    <div class="modal fade" id="projectModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="projectModalTitle">新建项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="projectForm">
                        <input type="hidden" id="projectId" name="id">
                        <div class="mb-3">
                            <label for="projectName" class="form-label">项目名称 *</label>
                            <input type="text" class="form-control" id="projectName" name="project_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="projectDescription" class="form-label">项目描述</label>
                            <textarea class="form-control" id="projectDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="projectStartDate" class="form-label">开始时间</label>
                                    <input type="date" class="form-control" id="projectStartDate" name="start_date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="projectEndDate" class="form-label">结束时间</label>
                                    <input type="date" class="form-control" id="projectEndDate" name="end_date">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="projectStatus" class="form-label">项目状态</label>
                            <select class="form-select" id="projectStatus" name="status">
                                <option value="active">进行中</option>
                                <option value="paused">已暂停</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveProjectBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务模态框 -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="taskModalTitle">新建任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <input type="hidden" id="taskId" name="id">
                        <div class="mb-3">
                            <label for="taskName" class="form-label">任务名称 *</label>
                            <input type="text" class="form-control" id="taskName" name="task_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="taskProjectId" class="form-label">所属项目 *</label>
                            <select class="form-select" id="taskProjectId" name="project_id" required>
                                <option value="">请选择项目</option>
                                <!-- 动态加载项目选项 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="taskDescription" class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" name="description" rows="3"></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="taskPriority" class="form-label">优先级</label>
                                    <select class="form-select" id="taskPriority" name="priority">
                                        <option value="low">低</option>
                                        <option value="medium" selected>中</option>
                                        <option value="high">高</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="taskStatus" class="form-label">任务状态</label>
                                    <select class="form-select" id="taskStatus" name="status">
                                        <option value="pending" selected>待处理</option>
                                        <option value="in_progress">进行中</option>
                                        <option value="completed">已完成</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="taskDueDate" class="form-label">截止时间</label>
                            <input type="datetime-local" class="form-control" id="taskDueDate" name="due_date">
                        </div>
                        <div class="mb-3">
                            <label for="taskAssignedTo" class="form-label">分配给</label>
                            <input type="text" class="form-control" id="taskAssignedTo" name="assigned_to" placeholder="用户ID">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveTaskBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background: rgba(0,0,0,0.5); z-index: 9999; display: none !important;">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>