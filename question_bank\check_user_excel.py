import pandas as pd
import os

# 检查用户Excel文件的实际结构
file_path = 'error_reports/questions_sample.xlsx'

if not os.path.exists(file_path):
    print(f"文件不存在: {file_path}")
    # 列出error_reports目录下的所有文件
    print("\nerror_reports目录下的文件:")
    for f in os.listdir('error_reports'):
        if f.endswith('.xlsx'):
            print(f"  {f}")
else:
    try:
        # 读取前10行，不设置表头
        try:
            df = pd.read_excel(file_path, nrows=10, header=None, engine='openpyxl')
        except:
            df = pd.read_excel(file_path, nrows=10, header=None, engine='xlrd')
        print(f"成功读取文件: {file_path}")
        print(f"文件形状: {df.shape}")
        print("\n前10行内容:")
        print(df)
        
        print("\n各行的详细值:")
        for i in range(min(10, len(df))):
            row_values = df.iloc[i].tolist()
            print(f"第{i+1}行: {row_values}")
            
        # 检查哪一行包含表头关键词
        header_keywords = ['question_bank_name', 'question_id', 'question_type_code', 'question_content']
        print("\n表头关键词检测:")
        for i in range(min(5, len(df))):
            row_str = ' '.join([str(val) for val in df.iloc[i].tolist() if pd.notna(val)])
            contains_keywords = any(keyword in row_str for keyword in header_keywords)
            print(f"第{i+1}行包含表头关键词: {contains_keywords} - {row_str[:100]}...")
            
    except Exception as e:
        print(f"读取文件时出错: {e}")