# -*- coding: utf-8 -*-
"""
成绩上报模块 PyInstaller 构建脚本

功能说明：
- 使用 PyInstaller 将成绩上报模块打包为独立的可执行文件
- 包含所有必要的依赖包、配置文件、模板文件和静态资源
- 支持 Windows 环境下的独立运行
- 包含网络通信、数据加密、文件上传等功能
- 特别注意：这是系统中唯一允许连接互联网的模块

使用方法：
1. 确保已安装 PyInstaller: pip install pyinstaller
2. 在项目根目录运行: python build_scripts/build_exam_score_reporting.py
3. 生成的可执行文件位于 dist/exam_score_reporting/ 目录

作者: 系统构建脚本
日期: 2025-01-17
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
EXAM_SCORE_DIR = PROJECT_ROOT / "exam_score_reporting_interface"
BUILD_DIR = PROJECT_ROOT / "build"
DIST_DIR = PROJECT_ROOT / "dist"

def safe_print(message):
    """
    安全打印函数，处理 Windows 控制台编码问题
    
    参数:
        message (str): 要打印的消息
    """
    try:
        print(message)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_message = message.encode('ascii', 'ignore').decode('ascii')
        print(safe_message)

def check_dependencies():
    """
    检查构建依赖是否满足
    
    返回:
        bool: 依赖检查是否通过
    """
    safe_print("[*] 检查构建依赖...")
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        safe_print(f"[+] PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        safe_print("[!] 错误: 未安装 PyInstaller")
        safe_print("[!] 请运行: pip install pyinstaller")
        return False
    
    # 检查成绩上报模块目录
    if not EXAM_SCORE_DIR.exists():
        safe_print(f"[!] 错误: 成绩上报模块目录不存在: {EXAM_SCORE_DIR}")
        return False
    
    # 检查主入口文件
    main_file = EXAM_SCORE_DIR / "run.py"
    if not main_file.exists():
        safe_print(f"[!] 错误: 主入口文件不存在: {main_file}")
        return False
    
    safe_print("[+] 依赖检查通过")
    return True

def clean_build_dirs():
    """
    清理之前的构建目录
    """
    safe_print("[*] 清理构建目录...")
    
    # 清理 build 目录
    if BUILD_DIR.exists():
        shutil.rmtree(BUILD_DIR)
        safe_print("[+] 已清理 build 目录")
    
    # 清理 dist/exam_score_reporting 目录
    exam_score_dist = DIST_DIR / "exam_score_reporting"
    if exam_score_dist.exists():
        shutil.rmtree(exam_score_dist)
        safe_print("[+] 已清理 dist/exam_score_reporting 目录")

def build_executable():
    """
    使用 PyInstaller 构建可执行文件
    
    返回:
        bool: 构建是否成功
    """
    safe_print("[*] 开始构建成绩上报模块可执行文件...")
    
    # 构建命令参数
    cmd = [
        "pyinstaller",
        "--name=exam_score_reporting",  # 可执行文件名称
        "--onedir",  # 生成目录形式的分发包
        "--windowed",  # Windows下不显示控制台窗口
        "--noconfirm",  # 覆盖输出目录而不询问
        "--clean",  # 清理临时文件
        
        # 添加数据文件
        f"--add-data={EXAM_SCORE_DIR / 'templates'};templates",
        f"--add-data={EXAM_SCORE_DIR / 'requirements.txt'};.",
        f"--add-data={EXAM_SCORE_DIR / 'config.py'};.",
        f"--add-data={EXAM_SCORE_DIR / 'models.py'};.",
        f"--add-data={EXAM_SCORE_DIR / 'README.md'};.",
        f"--add-data={EXAM_SCORE_DIR / 'DEVELOPMENT.md'};.",
        f"--add-data={EXAM_SCORE_DIR / 'QUICK_START.md'};.",
        
        # 添加数据目录（如果存在）
        f"--add-data={EXAM_SCORE_DIR / 'data'};data",
        f"--add-data={EXAM_SCORE_DIR / 'instance'};instance",
        f"--add-data={EXAM_SCORE_DIR / 'outputs'};outputs",
        
        # 添加子模块
        f"--add-data={EXAM_SCORE_DIR / 'routes'};routes",
        f"--add-data={EXAM_SCORE_DIR / 'services'};services",
        f"--add-data={EXAM_SCORE_DIR / 'validators'};validators",
        f"--add-data={EXAM_SCORE_DIR / 'middleware'};middleware",
        
        # 添加隐藏导入
        "--hidden-import=flask",
        "--hidden-import=flask_sqlalchemy",
        "--hidden-import=werkzeug",
        "--hidden-import=sqlalchemy",
        "--hidden-import=jinja2",
        "--hidden-import=markupsafe",
        "--hidden-import=itsdangerous",
        "--hidden-import=click",
        "--hidden-import=blinker",
        "--hidden-import=requests",
        "--hidden-import=urllib3",
        "--hidden-import=certifi",
        "--hidden-import=charset_normalizer",
        "--hidden-import=idna",
        "--hidden-import=cryptography",
        "--hidden-import=cffi",
        "--hidden-import=pycparser",
        "--hidden-import=json",
        "--hidden-import=csv",
        "--hidden-import=datetime",
        "--hidden-import=uuid",
        "--hidden-import=hashlib",
        "--hidden-import=base64",
        "--hidden-import=hmac",
        "--hidden-import=ssl",
        "--hidden-import=socket",
        "--hidden-import=http.client",
        "--hidden-import=urllib.parse",
        "--hidden-import=urllib.request",
        
        # 排除不需要的模块
        "--exclude-module=tkinter",
        "--exclude-module=matplotlib",
        "--exclude-module=numpy",
        "--exclude-module=pandas",
        "--exclude-module=scipy",
        "--exclude-module=IPython",
        "--exclude-module=jupyter",
        
        # 主入口文件
        str(EXAM_SCORE_DIR / "run.py")
    ]
    
    try:
        # 执行构建命令
        safe_print(f"[*] 执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, cwd=PROJECT_ROOT, capture_output=True, text=True)
        
        if result.returncode == 0:
            safe_print("[+] PyInstaller 构建成功")
            return True
        else:
            safe_print("[!] PyInstaller 构建失败")
            safe_print(f"[!] 错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        safe_print(f"[!] 构建过程中发生异常: {e}")
        return False

def copy_additional_files():
    """
    复制额外的配置文件和资源
    """
    safe_print("[*] 复制额外文件...")
    
    dist_dir = DIST_DIR / "exam_score_reporting"
    if not dist_dir.exists():
        safe_print("[!] 错误: 分发目录不存在")
        return False
    
    try:
        # 复制Python工具脚本
        tool_scripts = [
            "init_db.py",
            "check_logs.py",
            "data_import_tool.py",
            "json_validator.py"
        ]
        
        tools_dir = dist_dir / "tools"
        tools_dir.mkdir(exist_ok=True)
        
        for tool_script in tool_scripts:
            src_file = EXAM_SCORE_DIR / tool_script
            if src_file.exists():
                dst_file = tools_dir / tool_script
                shutil.copy2(src_file, dst_file)
                safe_print(f"[+] 已复制工具脚本: {tool_script}")
        
        # 创建配置目录
        config_dir = dist_dir / "config"
        config_dir.mkdir(exist_ok=True)
        
        # 创建示例配置文件
        example_env = config_dir / ".env.example"
        with open(example_env, 'w', encoding='utf-8') as f:
            f.write("# 成绩上报模块配置文件示例\n")
            f.write("# 复制此文件为 .env 并填入实际配置\n\n")
            f.write("# 数据库配置\n")
            f.write("DATABASE_URL=sqlite:///exam_scores.db\n\n")
            f.write("# 上报服务器配置\n")
            f.write("REPORT_SERVER_URL=https://api.example.com/scores\n")
            f.write("REPORT_API_KEY=your_api_key_here\n\n")
            f.write("# 加密配置\n")
            f.write("ENCRYPTION_KEY=your_encryption_key_here\n\n")
            f.write("# 网络配置\n")
            f.write("REQUEST_TIMEOUT=30\n")
            f.write("MAX_RETRIES=3\n")
        
        # 创建启动脚本
        start_script = dist_dir / "start_exam_score_reporting.bat"
        with open(start_script, 'w', encoding='utf-8') as f:
            f.write("@echo off\n")
            f.write("echo 启动成绩上报模块...\n")
            f.write("echo 注意: 这是系统中唯一允许连接互联网的模块\n")
            f.write("echo 请确保网络连接正常且配置文件已正确设置\n")
            f.write("pause\n")
            f.write("exam_score_reporting.exe\n")
            f.write("pause\n")
        
        safe_print("[+] 已创建启动脚本: start_exam_score_reporting.bat")
        
        # 创建工具使用说明
        tools_readme = tools_dir / "README.txt"
        with open(tools_readme, 'w', encoding='utf-8') as f:
            f.write("成绩上报模块工具脚本说明\n")
            f.write("=========================\n\n")
            f.write("init_db.py - 数据库初始化工具\n")
            f.write("check_logs.py - 日志检查工具\n")
            f.write("data_import_tool.py - 数据导入工具\n")
            f.write("json_validator.py - JSON数据验证工具\n\n")
            f.write("注意事项:\n")
            f.write("1. 这些脚本需要Python环境才能运行\n")
            f.write("2. 成绩上报模块是系统中唯一允许连接互联网的模块\n")
            f.write("3. 使用前请确保网络配置和API密钥正确设置\n")
            f.write("4. 所有数据传输都应使用加密通道\n")
        
        # 创建安全说明文件
        security_readme = dist_dir / "SECURITY.txt"
        with open(security_readme, 'w', encoding='utf-8') as f:
            f.write("成绩上报模块安全说明\n")
            f.write("==================\n\n")
            f.write("重要安全提醒:\n")
            f.write("1. 本模块是系统中唯一允许连接互联网的组件\n")
            f.write("2. 仅在所有局域网内考试完成后才进行数据传输\n")
            f.write("3. 所有成绩数据传输必须使用加密通道\n")
            f.write("4. 传输前必须进行数据完整性验证\n")
            f.write("5. 需要特殊权限，仅限授权用户操作\n")
            f.write("6. 所有操作都会详细记录在日志中\n")
            f.write("7. 上报操作需要二次确认机制\n\n")
            f.write("配置要求:\n")
            f.write("- 确保 .env 文件中的API密钥和服务器地址正确\n")
            f.write("- 验证加密密钥的安全性\n")
            f.write("- 检查网络连接和防火墙设置\n")
            f.write("- 确认服务器证书的有效性\n")
        
        return True
        
    except Exception as e:
        safe_print(f"[!] 复制文件时发生错误: {e}")
        return False

def verify_build():
    """
    验证构建结果
    
    返回:
        bool: 验证是否通过
    """
    safe_print("[*] 验证构建结果...")
    
    dist_dir = DIST_DIR / "exam_score_reporting"
    exe_file = dist_dir / "exam_score_reporting.exe"
    
    if not exe_file.exists():
        safe_print("[!] 错误: 可执行文件不存在")
        return False
    
    # 检查文件大小
    file_size = exe_file.stat().st_size / (1024 * 1024)  # MB
    safe_print(f"[+] 可执行文件大小: {file_size:.2f} MB")
    
    # 检查必要的目录和文件
    required_items = [
        "templates",
        "tools",
        "config",
        "_internal",
        "SECURITY.txt"
    ]
    
    for item in required_items:
        item_path = dist_dir / item
        if item_path.exists():
            safe_print(f"[+] 已包含: {item}")
        else:
            safe_print(f"[!] 缺失: {item}")
            return False
    
    safe_print("[+] 构建验证通过")
    return True

def main():
    """
    主函数：执行完整的构建流程
    
    返回:
        int: 退出代码（0表示成功，1表示失败）
    """
    safe_print("="*60)
    safe_print("成绩上报模块 PyInstaller 构建脚本")
    safe_print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 清理构建目录
    clean_build_dirs()
    
    # 构建可执行文件
    if not build_executable():
        return 1
    
    # 复制额外文件
    if not copy_additional_files():
        return 1
    
    # 验证构建结果
    if not verify_build():
        return 1
    
    safe_print("="*60)
    safe_print("[+] 成绩上报模块构建完成!")
    safe_print(f"[+] 输出目录: {DIST_DIR / 'exam_score_reporting'}")
    safe_print(f"[+] 可执行文件: {DIST_DIR / 'exam_score_reporting' / 'exam_score_reporting.exe'}")
    safe_print("[+] 使用 start_exam_score_reporting.bat 启动应用")
    safe_print("[+] 工具脚本位于 tools/ 目录")
    safe_print("[!] 重要: 请阅读 SECURITY.txt 了解安全要求")
    safe_print("[!] 重要: 配置 config/.env 文件后再使用")
    safe_print("="*60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())