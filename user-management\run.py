import os
from app import create_app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 从环境变量获取配置，默认为开发环境
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5001))
    debug = os.getenv('DEBUG', 'True').lower() == 'true'
    
    print(f"用户管理服务启动中...")
    print(f"服务地址: http://{host}:{port}")
    print(f"API文档: http://{host}:{port}/docs/")
    print(f"健康检查: http://{host}:{port}/api/v1/health")
    
    app.run(host=host, port=port, debug=debug)