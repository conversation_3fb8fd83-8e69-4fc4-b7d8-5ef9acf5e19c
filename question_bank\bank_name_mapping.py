#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
题库名称映射配置
用于将组卷规则中的中文题库名称映射到数据库中的实际题库名称
"""

# 题库名称映射表：中文名称 -> 数据库中的实际名称
BANK_NAME_MAPPING = {
    # 保卫管理员题库
    '保卫管理员（三级）理论': 'BWGL-3-LL',
    '保卫管理员三级理论': 'BWGL-3-LL',
    '保卫管理员（3级）理论': 'BWGL-3-LL',
    '保卫管理员3级理论': 'BWGL-3-LL',
    'BWGL-3-LL': 'BWGL-3-LL',  # 直接使用代码名称也支持
    
    # 可以根据需要添加更多映射
    # '其他题库中文名': '对应的数据库名称',
}

def get_actual_bank_name(display_name: str) -> str:
    """
    根据显示名称获取数据库中的实际题库名称
    
    Args:
        display_name (str): 组卷规则中使用的题库名称
        
    Returns:
        str: 数据库中的实际题库名称
    """
    # 先尝试精确匹配
    if display_name in BANK_NAME_MAPPING:
        return BANK_NAME_MAPPING[display_name]
    
    # 如果没有找到映射，返回原名称（可能直接使用的就是数据库名称）
    return display_name

def get_display_name(actual_name: str) -> str:
    """
    根据数据库中的实际名称获取显示名称（反向映射）
    
    Args:
        actual_name (str): 数据库中的实际题库名称
        
    Returns:
        str: 用于显示的题库名称
    """
    # 查找反向映射
    for display_name, db_name in BANK_NAME_MAPPING.items():
        if db_name == actual_name:
            # 返回第一个匹配的显示名称（通常是最完整的中文名称）
            return display_name
    
    # 如果没有找到映射，返回原名称
    return actual_name

def list_all_mappings() -> dict:
    """
    获取所有题库名称映射
    
    Returns:
        dict: 完整的映射字典
    """
    return BANK_NAME_MAPPING.copy()

def add_mapping(display_name: str, actual_name: str):
    """
    动态添加题库名称映射
    
    Args:
        display_name (str): 显示名称
        actual_name (str): 数据库中的实际名称
    """
    BANK_NAME_MAPPING[display_name] = actual_name

def remove_mapping(display_name: str):
    """
    移除题库名称映射
    
    Args:
        display_name (str): 要移除的显示名称
    """
    if display_name in BANK_NAME_MAPPING:
        del BANK_NAME_MAPPING[display_name]