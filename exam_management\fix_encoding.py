#!/usr/bin/env python
# -*- coding: utf-8 -*-

import codecs
import os

def fix_file_encoding(filename):
    """修复文件编码问题"""
    try:
        # 尝试用不同编码读取文件
        encodings = ['utf-8', 'gbk', 'latin-1', 'cp1252']
        content = None
        
        for encoding in encodings:
            try:
                with open(filename, 'r', encoding=encoding, errors='ignore') as f:
                    content = f.read()
                print(f"成功用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print("无法读取文件")
            return False
        
        # 用UTF-8编码重新写入文件
        backup_filename = filename + '.backup'
        os.rename(filename, backup_filename)
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"文件已修复并保存为UTF-8编码")
        print(f"原文件备份为: {backup_filename}")
        return True
        
    except Exception as e:
        print(f"修复文件时出错: {e}")
        return False

if __name__ == '__main__':
    fix_file_encoding('app.py')