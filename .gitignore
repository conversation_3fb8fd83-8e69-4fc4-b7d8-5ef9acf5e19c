# 虚拟环境
.venv/
venv/
venv_*/
env/
ENV/
env.bak/
venv.bak/
__pycache__/

# 依赖包
*.pyc
*.pyo
*.pyd
.Python
pip-log.txt
pip-delete-this-directory.txt

# 数据库文件
*.sqlite3
*.db
local_dev.db

# 日志文件
*.log

# 上传的文件
uploads/
templates/题库模板.xlsx

# 构建文件
build/
dist/
*.egg-info/

# IDE相关文件
.idea/
.vscode/
*.swp
*.swo

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# 压缩文件和缓存
*.rar
*.zip
*.tar.gz
*.7z
node_modules/
.cache/
cache/

# 测试相关
.pytest_cache/
htmlcov/
.coverage
test_results/
.tox/

# 环境配置文件
.env
.env.local
.env.production
config.ini
settings.ini
secrets.json

# 临时文件和备份
temp/
tmp/
*.tmp
*.bak
*.swp
*~
backup_*/

# 项目特定
data/temp/
data/cache/
data/logs/
exports/temp/
imports/temp/
logs/

# 大文件检查脚本（临时）
check_large_files.py


# PH&RL System - Auto Generated Ignore Rules

# Database files
*.db
*.db-wal
*.db-shm
*.sqlite
*.sqlite3

# Log files
*.log
logs/

# Cache and temporary files
__pycache__/
*.pyc
*.pyo
.pytest_cache/
*.tmp
*.temp

# Sensitive files
*password*
*secret*
*private*
*.env
exam_backup_*.json
*_credentials.json

# Large media files
*.mp4
*.avi
*.mov
*.wmv
*.flv

# Backup files
*.bak
*.backup
*_backup*

# Development environment
node_modules/
venv/
env/
.venv/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# System files
.DS_Store
Thumbs.db
desktop.ini

# Generated reports (keep only essential ones)
*_REPORT.md
*_GUIDE.md
paper_validation_reports/

# Test and debug files
test_*.py
debug_*.py
minimal_*.py
quick_*.py
simple_*.py
