# -*- coding: utf-8 -*-
"""
认证相关API路由
处理用户登录、注销、权限验证等功能
"""

import hashlib
import sqlite3
from datetime import datetime
import hashlib
import sqlite3
from datetime import datetime
from flask import Blueprint, request, g
from auth.permissions import auth_manager, require_permission, require_role, Permission, Role
from auth.audit_log import audit_logger, log_operation
from utils.api_response import APIResponse, api_response
from utils.api_response import validate_request

# 创建蓝图
auth_bp = Blueprint('auth', __name__, url_prefix='/api/v1/auth')

def hash_password(password: str) -> str:
    """
    密码哈希函数
    
    Args:
        password (str): 明文密码
        
    Returns:
        str: 哈希后的密码
    """
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, password_hash: str) -> bool:
    """
    验证密码
    
    Args:
        password (str): 明文密码
        password_hash (str): 哈希密码
        
    Returns:
        bool: 密码是否正确
    """
    return hash_password(password) == password_hash

@auth_bp.route('/login', methods=['POST'])
@api_response
@validate_request({
    'username': {'type': 'string', 'required': True, 'min_length': 1},
    'password': {'type': 'string', 'required': True, 'min_length': 1}
})
def login():
    """
    用户登录
    
    请求体:
        {
            "username": "用户名",
            "password": "密码"
        }
    
    返回:
        {
            "code": 200,
            "msg": "登录成功",
            "data": {
                "token": "JWT Token",
                "user": {
                    "id": 1,
                    "username": "admin",
                    "role": "admin",
                    "full_name": "系统管理员"
                }
            }
        }
    """
    data = request.get_json()
    username = data.get('username', '').strip()
    password = data.get('password', '')
        
    # 查询用户
    conn = sqlite3.connect(auth_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, username, password_hash, email, full_name, role, is_active
        FROM auth_users
        WHERE username = ?
    ''', (username,))
    
    user = cursor.fetchone()
    conn.close()
    
    if not user:
        audit_logger.log_login(username, 'WEB', 'FAILURE', failure_reason='用户不存在')
        return APIResponse.error('用户名或密码错误', 401)
    
    user_id, db_username, password_hash, email, full_name, role, is_active = user
    
    if not is_active:
        audit_logger.log_login(username, 'WEB', 'FAILURE', user_id=user_id, failure_reason='用户已被禁用')
        return APIResponse.error('用户已被禁用', 401)
    
    if not verify_password(password, password_hash):
        audit_logger.log_login(username, 'WEB', 'FAILURE', user_id=user_id, failure_reason='密码错误')
        return APIResponse.error('用户名或密码错误', 401)
        
    # 生成Token
    user_data = {
        'id': user_id,
        'username': db_username,
        'role': role,
        'email': email,
        'full_name': full_name
    }
    
    token = auth_manager.generate_token(user_data)
    
    # 更新最后登录时间
    conn = sqlite3.connect(auth_manager.db_path)
    cursor = conn.cursor()
    cursor.execute('UPDATE auth_users SET last_login = ? WHERE id = ?', (datetime.now(), user_id))
    conn.commit()
    conn.close()
    
    # 记录登录成功日志
    audit_logger.log_login(username, 'WEB', 'SUCCESS', user_id=user_id)
    
    return APIResponse.success({
        'token': token,
        'user': {
            'id': user_id,
            'username': db_username,
            'role': role,
            'email': email,
            'full_name': full_name
        }
    }, '登录成功')

@auth_bp.route('/logout', methods=['POST'])
@require_permission(Permission.TASK_VIEW)  # 需要基本权限
@log_operation('用户登出', 'auth', 'UPDATE')
@api_response
def logout():
    """
    用户注销
    
    返回:
        {
            "code": 200,
            "msg": "注销成功",
            "data": {}
        }
    """
    token = request.headers.get('Authorization', '').replace('Bearer ', '')
    if not token:
        return APIResponse.error('未提供认证令牌', 401)
    
    # 验证Token
    user_data = auth_manager.verify_token(token)
    if not user_data:
        return APIResponse.error('无效的认证令牌', 401)
    
    # 记录登出日志
    audit_logger.log_logout(user_data.get('username', 'unknown'), 'WEB', user_id=user_data.get('id'))
    
    return APIResponse.success({}, '登出成功')

@auth_bp.route('/profile', methods=['GET'])
@require_permission(Permission.TASK_VIEW)
@log_operation('获取用户信息', 'auth', 'READ')
@api_response
def get_profile():
    """
    获取当前用户信息
    
    返回:
        {
            "code": 200,
            "msg": "获取成功",
            "data": {
                "user": {
                    "id": 1,
                    "username": "admin",
                    "role": "admin",
                    "email": "<EMAIL>",
                    "full_name": "系统管理员",
                    "permissions": ["task:view", "task:create", ...]
                }
            }
        }
    """
    current_user = g.current_user
    user_id = current_user['user_id']
    
    # 获取用户详细信息
    conn = sqlite3.connect(auth_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, username, email, full_name, role, created_at, last_login
        FROM auth_users
        WHERE id = ? AND is_active = 1
    ''', (user_id,))
    
    user = cursor.fetchone()
    conn.close()
    
    if not user:
        return APIResponse.error('用户不存在', 404)
    
    # 获取用户权限
    permissions = auth_manager.get_user_permissions(user_id)
    
    user_info = {
        'id': user[0],
        'username': user[1],
        'email': user[2],
        'full_name': user[3],
        'role': user[4],
        'created_at': user[5],
        'last_login': user[6],
        'permissions': permissions
    }
    
    return APIResponse.success({
        'user': user_info
    }, '获取成功')

@auth_bp.route('/change-password', methods=['POST'])
@require_permission(Permission.TASK_VIEW)
@log_operation('修改密码', 'user', 'UPDATE')
@api_response
@validate_request({
    'old_password': {'type': 'string', 'required': True, 'min_length': 1},
    'new_password': {'type': 'string', 'required': True, 'min_length': 6}
})
def change_password():
    """
    修改密码
    
    请求体:
        {
            "old_password": "旧密码",
            "new_password": "新密码"
        }
    
    返回:
        {
            "code": 200,
            "msg": "密码修改成功",
            "data": {}
        }
    """
    current_user = g.current_user
    user_id = current_user['user_id']
    
    data = request.get_json()
    old_password = data.get('old_password', '')
    new_password = data.get('new_password', '')
        
    # 验证旧密码
    conn = sqlite3.connect(auth_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT password_hash FROM auth_users WHERE id = ?', (user_id,))
    result = cursor.fetchone()
    
    if not result or not verify_password(old_password, result[0]):
        conn.close()
        return APIResponse.error('旧密码错误', 400)
    
    # 更新密码
    new_password_hash = hash_password(new_password)
    cursor.execute('''
        UPDATE auth_users 
        SET password_hash = ?, updated_at = ?
        WHERE id = ?
    ''', (new_password_hash, datetime.now(), user_id))
    
    conn.commit()
    conn.close()
    
    # 记录操作日志
    audit_logger.log_operation(
        action='修改密码',
        resource_type='user',
        operation='UPDATE',
        resource_id=str(user_id)
    )
    
    return APIResponse.success({}, '密码修改成功')

@auth_bp.route('/users', methods=['GET'])
@require_permission(Permission.USER_MANAGE)
@api_response
@validate_request({
    'page': {'type': 'integer', 'required': False, 'min': 1},
    'per_page': {'type': 'integer', 'required': False, 'min': 1, 'max': 100},
    'keyword': {'type': 'string', 'required': False},
    'role': {'type': 'string', 'required': False},
    'is_active': {'type': 'string', 'required': False}
})
def get_users():
    """
    获取用户列表（管理员权限）
    
    查询参数:
        - page: 页码（默认1）
        - per_page: 每页数量（默认20）
        - keyword: 搜索关键词
        - role: 角色筛选
        - is_active: 状态筛选
    
    返回:
        {
            "code": 200,
            "msg": "获取成功",
            "data": {
                "users": [...],
                "total": 100,
                "page": 1,
                "per_page": 20,
                "pages": 5
            }
        }
    """
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    search = request.args.get('search', '').strip()
    role = request.args.get('role', '').strip()
    
    # 构建查询条件
    where_conditions = ['is_active = 1']
    params = []
    
    if search:
        where_conditions.append('(username LIKE ? OR email LIKE ? OR full_name LIKE ?)')
        search_param = f'%{search}%'
        params.extend([search_param, search_param, search_param])
    
    if role:
        where_conditions.append('role = ?')
        params.append(role)
    
    where_clause = ' AND '.join(where_conditions)
    
    # 获取总数
    conn = sqlite3.connect(auth_manager.db_path)
    cursor = conn.cursor()
    
    count_sql = f'SELECT COUNT(*) FROM auth_users WHERE {where_clause}'
    cursor.execute(count_sql, params)
    total = cursor.fetchone()[0]
    
    # 获取用户列表
    offset = (page - 1) * per_page
    list_sql = f'''
        SELECT id, username, email, full_name, role, created_at, last_login
        FROM auth_users
        WHERE {where_clause}
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    '''
    
    cursor.execute(list_sql, params + [per_page, offset])
    users = cursor.fetchall()
    conn.close()
    
    # 格式化用户数据
    user_list = []
    for user in users:
        user_list.append({
            'id': user[0],
            'username': user[1],
            'email': user[2],
            'full_name': user[3],
            'role': user[4],
            'created_at': user[5],
            'last_login': user[6]
        })
    
    return APIResponse.success({
        'users': user_list,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': (total + per_page - 1) // per_page
        }
    }, '获取用户列表成功')

@auth_bp.route('/users', methods=['POST'])
@require_permission(Permission.USER_MANAGE)
@log_operation('创建用户', 'user', 'CREATE')
@api_response
@validate_request({
    'username': {'type': 'string', 'required': True, 'min_length': 1},
    'password': {'type': 'string', 'required': True, 'min_length': 6},
    'email': {'type': 'string', 'required': False},
    'full_name': {'type': 'string', 'required': False},
    'role': {'type': 'string', 'required': True}
})
def create_user():
    """
    创建用户（管理员权限）
    
    请求体:
        {
            "username": "用户名",
            "password": "密码",
            "email": "邮箱",
            "full_name": "姓名",
            "role": "角色"
        }
    
    返回:
        {
            "code": 200,
            "msg": "创建成功",
            "data": {
                "user_id": 1
            }
        }
    """
    data = request.get_json()
    username = data.get('username', '').strip()
    password = data.get('password', '')
    email = data.get('email', '').strip()
    full_name = data.get('full_name', '').strip()
    role = data.get('role', 'user').strip()
        
    # 检查用户名是否已存在
    conn = sqlite3.connect(auth_manager.db_path)
    cursor = conn.cursor()
    
    cursor.execute('SELECT id FROM auth_users WHERE username = ?', (username,))
    if cursor.fetchone():
        conn.close()
        return APIResponse.error('用户名已存在', 400)
    
    # 检查邮箱是否已存在
    cursor.execute('SELECT id FROM auth_users WHERE email = ?', (email,))
    if cursor.fetchone():
        conn.close()
        return APIResponse.error('邮箱已存在', 400)
    
    # 创建用户
    password_hash = hash_password(password)
    cursor.execute('''
        INSERT INTO auth_users (username, password_hash, email, full_name, role, is_active, created_at)
        VALUES (?, ?, ?, ?, ?, 1, ?)
    ''', (username, password_hash, email, full_name, role, datetime.now()))
    
    user_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    return APIResponse.success({
        'user_id': user_id
    }, '用户创建成功')

@auth_bp.route('/users/<int:user_id>', methods=['PUT'])
@require_permission(Permission.USER_MANAGE)
@log_operation('更新用户', 'user', 'UPDATE')
@api_response
@validate_request({
    'email': {'type': 'string', 'required': False},
    'full_name': {'type': 'string', 'required': False},
    'role': {'type': 'string', 'required': False},
    'is_active': {'type': 'boolean', 'required': False}
})
def update_user(user_id):
    """
    更新用户信息（管理员权限）
    
    请求体:
        {
            "email": "邮箱",
            "full_name": "姓名",
            "role": "角色",
            "is_active": true
        }
    
    返回:
        {
            "code": 200,
            "msg": "更新成功",
            "data": {}
        }
    """
    data = request.get_json()
    
    # 获取要更新的字段
    email = data.get('email', '').strip()
    full_name = data.get('full_name', '').strip()
    role = data.get('role', '').strip()
    is_active = data.get('is_active')
    
    conn = sqlite3.connect(auth_manager.db_path)
    cursor = conn.cursor()
    
    # 检查用户是否存在
    cursor.execute('SELECT username, email, full_name, role, is_active FROM auth_users WHERE id = ?', (user_id,))
    user = cursor.fetchone()
    
    if not user:
        conn.close()
        return APIResponse.error('用户不存在', 404)
    
    old_username, old_email, old_full_name, old_role, old_is_active = user
    
    # 构建更新字段
    update_fields = []
    update_params = []
    
    if email and email != old_email:
        # 检查邮箱是否已被其他用户使用
        cursor.execute('SELECT id FROM auth_users WHERE email = ? AND id != ?', (email, user_id))
        if cursor.fetchone():
            conn.close()
            return APIResponse.error('邮箱已被其他用户使用', 400)
        update_fields.append('email = ?')
        update_params.append(email)
    
    if full_name and full_name != old_full_name:
        update_fields.append('full_name = ?')
        update_params.append(full_name)
    
    if role and role != old_role:
        update_fields.append('role = ?')
        update_params.append(role)
    
    if is_active is not None and bool(is_active) != bool(old_is_active):
        update_fields.append('is_active = ?')
        update_params.append(1 if is_active else 0)
    
    if not update_fields:
        conn.close()
        return APIResponse.error('没有需要更新的字段', 400)
    
    # 执行更新
    update_fields.append('updated_at = ?')
    update_params.append(datetime.now())
    update_params.append(user_id)
    
    update_sql = f'UPDATE auth_users SET {", ".join(update_fields)} WHERE id = ?'
    cursor.execute(update_sql, update_params)
    
    conn.commit()
    conn.close()
    
    return APIResponse.success({}, '用户信息更新成功')

@auth_bp.route('/permissions', methods=['GET'])
@require_permission(Permission.USER_MANAGE)
@api_response
def get_permissions():
    """
    获取权限列表（管理员权限）
    
    返回:
        {
            "code": 200,
            "msg": "获取成功",
            "data": {
                "permissions": [...],
                "roles": {
                    "admin": [...],
                    "teacher": [...],
                    ...
                }
            }
        }
    """
    permissions = Permission.get_all_permissions()
    roles = {}
    
    for role in Role.get_all_roles():
        roles[role] = Role.get_role_permissions(role)
    
    return APIResponse.success({
        'permissions': permissions,
        'roles': roles
    }, '获取成功')

@auth_bp.route('/logs/audit', methods=['GET'])
@require_permission(Permission.LOG_VIEW)
@log_operation('获取审计日志', 'audit', 'READ')
@api_response
@validate_request({
    'page': {'type': 'integer', 'required': False, 'min': 1},
    'per_page': {'type': 'integer', 'required': False, 'min': 1, 'max': 100},
    'user_id': {'type': 'string', 'required': False},
    'action': {'type': 'string', 'required': False},
    'resource_type': {'type': 'string', 'required': False},
    'start_date': {'type': 'string', 'required': False},
    'end_date': {'type': 'string', 'required': False}
})
def get_audit_logs():
    """
    获取审计日志（管理员权限）
    
    查询参数:
        - page: 页码（默认1）
        - per_page: 每页数量（默认50）
        - user_id: 用户ID筛选
        - action: 操作筛选
        - resource_type: 资源类型筛选
        - start_date: 开始日期
        - end_date: 结束日期
    
    返回:
        {
            "code": 200,
            "msg": "获取成功",
            "data": {
                "logs": [...],
                "total": 100,
                "page": 1,
                "per_page": 50,
                "pages": 2
            }
        }
    """
    # 获取查询参数
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 100)
    user_id = request.args.get('user_id')
    action = request.args.get('action')
    resource_type = request.args.get('resource_type')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 转换用户ID
    if user_id:
        try:
            user_id = int(user_id)
        except ValueError:
            user_id = None
    
    # 获取审计日志
    result = audit_logger.get_audit_logs(
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        start_date=start_date,
        end_date=end_date,
        page=page,
        per_page=per_page
    )
    
    return APIResponse.success(result, '获取审计日志成功')

@auth_bp.route('/logs/login', methods=['GET'])
@require_permission(Permission.LOG_VIEW)
@log_operation('获取登录日志', 'audit', 'READ')
@api_response
@validate_request({
    'page': {'type': 'integer', 'required': False, 'min': 1},
    'per_page': {'type': 'integer', 'required': False, 'min': 1, 'max': 100},
    'user_id': {'type': 'string', 'required': False},
    'username': {'type': 'string', 'required': False},
    'start_date': {'type': 'string', 'required': False},
    'end_date': {'type': 'string', 'required': False}
})
def get_login_logs():
    """
    获取登录日志（管理员权限）
    
    查询参数:
        - page: 页码（默认1）
        - per_page: 每页数量（默认50）
        - user_id: 用户ID筛选
        - username: 用户名筛选
        - start_date: 开始日期
        - end_date: 结束日期
    
    返回:
        {
            "code": 200,
            "msg": "获取成功",
            "data": {
                "logs": [...],
                "total": 100,
                "page": 1,
                "per_page": 50,
                "pages": 2
            }
        }
    """
    # 获取查询参数
    page = int(request.args.get('page', 1))
    per_page = min(int(request.args.get('per_page', 50)), 100)
    user_id = request.args.get('user_id')
    username = request.args.get('username')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    
    # 转换用户ID
    if user_id:
        try:
            user_id = int(user_id)
        except ValueError:
            user_id = None
    
    # 获取登录日志
    result = audit_logger.get_login_logs(
        user_id=user_id,
        username=username,
        start_date=start_date,
        end_date=end_date,
        page=page,
        per_page=per_page
    )
    
    return APIResponse.success(result, '获取登录日志成功')