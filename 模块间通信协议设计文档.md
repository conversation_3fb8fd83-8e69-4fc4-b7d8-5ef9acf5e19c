# 模块间通信协议设计文档

## 1. 通信架构概述

### 1.1 整体通信架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        客户端层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │  Web前端     │  │  桌面客户端   │  │  移动端App   │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
                            │ HTTP/WebSocket
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                       API网关层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │  路由管理    │  │  负载均衡    │  │  协议转换    │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
                            │ HTTP/gRPC/消息队列
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                      业务模块层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │ 用户管理模块  │  │ 题库管理模块  │  │ 考试管理模块  │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │ 成绩管理模块  │  │ 监控模块     │  │ 日志审计模块  │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
                            │ 数据库连接/缓存
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                      数据存储层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │  MySQL/SQLite│  │  Redis缓存   │  │  文件存储    │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 通信模式

#### 同步通信
- **HTTP RESTful API**：标准的请求-响应模式
- **gRPC**：高性能的RPC调用（可选）
- **直接数据库访问**：模块内部数据访问

#### 异步通信
- **消息队列**：事件驱动的异步处理
- **WebSocket**：实时双向通信
- **事件总线**：模块间事件通知

### 1.3 设计原则

#### 松耦合原则
- **接口标准化**：统一的API接口规范
- **协议无关性**：支持多种通信协议
- **服务自治**：每个模块独立部署和运行

#### 高可用原则
- **故障隔离**：单个模块故障不影响整体系统
- **优雅降级**：关键功能的备用方案
- **超时处理**：防止长时间阻塞

#### 性能优化原则
- **连接复用**：HTTP连接池和长连接
- **数据压缩**：减少网络传输开销
- **缓存策略**：减少重复请求

## 2. 服务发现机制

### 2.1 服务注册中心

#### 注册中心架构
```python
class ServiceRegistry:
    def __init__(self, storage_backend='redis'):
        self.storage = self._init_storage(storage_backend)
        self.services = {}  # 本地缓存
        self.health_checker = HealthChecker()
        
    def register_service(self, service_info):
        """
        注册服务
        
        Args:
            service_info: {
                'name': 'user-management',
                'version': '1.0.0',
                'host': '*************',
                'port': 5001,
                'protocol': 'http',
                'health_check_url': '/api/health',
                'metadata': {
                    'environment': 'production',
                    'region': 'us-west-1',
                    'capabilities': ['user_crud', 'auth']
                },
                'tags': ['user', 'auth', 'core']
            }
        """
        service_id = f"{service_info['name']}-{service_info['host']}-{service_info['port']}"
        
        # 添加注册时间和TTL
        service_info.update({
            'service_id': service_id,
            'registered_at': int(time.time()),
            'last_heartbeat': int(time.time()),
            'ttl': 30,  # 30秒TTL
            'status': 'healthy'
        })
        
        # 存储到注册中心
        self.storage.hset(f"service:{service_id}", mapping=service_info)
        self.storage.expire(f"service:{service_id}", service_info['ttl'])
        
        # 添加到服务列表
        self.storage.sadd(f"services:{service_info['name']}", service_id)
        
        # 更新本地缓存
        self.services[service_id] = service_info
        
        return service_id
        
    def discover_service(self, service_name, version=None, tags=None):
        """
        发现服务
        
        Args:
            service_name: 服务名称
            version: 服务版本（可选）
            tags: 服务标签过滤（可选）
            
        Returns:
            list: 匹配的服务实例列表
        """
        # 获取服务实例列表
        service_ids = self.storage.smembers(f"services:{service_name}")
        
        available_services = []
        for service_id in service_ids:
            service_info = self.storage.hgetall(f"service:{service_id}")
            
            if not service_info:
                continue
                
            # 版本过滤
            if version and service_info.get('version') != version:
                continue
                
            # 标签过滤
            if tags:
                service_tags = service_info.get('tags', [])
                if not all(tag in service_tags for tag in tags):
                    continue
                    
            # 健康状态检查
            if service_info.get('status') == 'healthy':
                available_services.append(service_info)
                
        return available_services
        
    def heartbeat(self, service_id):
        """
        服务心跳
        
        定期调用以维持服务注册状态
        """
        service_key = f"service:{service_id}"
        
        if self.storage.exists(service_key):
            # 更新心跳时间
            self.storage.hset(service_key, 'last_heartbeat', int(time.time()))
            # 重置TTL
            self.storage.expire(service_key, 30)
            return True
            
        return False
        
    def unregister_service(self, service_id):
        """
        注销服务
        """
        service_info = self.storage.hgetall(f"service:{service_id}")
        
        if service_info:
            # 从服务列表中移除
            self.storage.srem(f"services:{service_info['name']}", service_id)
            # 删除服务信息
            self.storage.delete(f"service:{service_id}")
            # 清理本地缓存
            self.services.pop(service_id, None)
            
        return True
```

### 2.2 负载均衡策略

#### 负载均衡器
```python
class LoadBalancer:
    def __init__(self, strategy='round_robin'):
        self.strategy = strategy
        self.strategies = {
            'round_robin': self._round_robin,
            'weighted_round_robin': self._weighted_round_robin,
            'least_connections': self._least_connections,
            'random': self._random,
            'consistent_hash': self._consistent_hash
        }
        self.counters = {}  # 轮询计数器
        self.connections = {}  # 连接计数
        
    def select_service(self, services, request_context=None):
        """
        选择服务实例
        
        Args:
            services: 可用服务列表
            request_context: 请求上下文（用于一致性哈希等）
            
        Returns:
            dict: 选中的服务实例
        """
        if not services:
            raise NoAvailableServiceError("No available services")
            
        strategy_func = self.strategies.get(self.strategy, self._round_robin)
        return strategy_func(services, request_context)
        
    def _round_robin(self, services, context=None):
        """轮询策略"""
        service_name = services[0]['name']
        counter_key = f"rr:{service_name}"
        
        if counter_key not in self.counters:
            self.counters[counter_key] = 0
            
        index = self.counters[counter_key] % len(services)
        self.counters[counter_key] += 1
        
        return services[index]
        
    def _weighted_round_robin(self, services, context=None):
        """加权轮询策略"""
        # 根据服务权重进行选择
        total_weight = sum(int(s.get('weight', 1)) for s in services)
        
        if total_weight == 0:
            return self._round_robin(services, context)
            
        import random
        weight_sum = 0
        random_weight = random.randint(1, total_weight)
        
        for service in services:
            weight_sum += int(service.get('weight', 1))
            if random_weight <= weight_sum:
                return service
                
        return services[0]
        
    def _least_connections(self, services, context=None):
        """最少连接策略"""
        min_connections = float('inf')
        selected_service = None
        
        for service in services:
            service_id = service['service_id']
            connections = self.connections.get(service_id, 0)
            
            if connections < min_connections:
                min_connections = connections
                selected_service = service
                
        return selected_service or services[0]
        
    def _consistent_hash(self, services, context=None):
        """一致性哈希策略"""
        if not context or 'hash_key' not in context:
            return self._round_robin(services, context)
            
        import hashlib
        hash_key = context['hash_key']
        hash_value = int(hashlib.md5(hash_key.encode()).hexdigest(), 16)
        
        # 简单的一致性哈希实现
        index = hash_value % len(services)
        return services[index]
        
    def increment_connections(self, service_id):
        """增加连接计数"""
        self.connections[service_id] = self.connections.get(service_id, 0) + 1
        
    def decrement_connections(self, service_id):
        """减少连接计数"""
        if service_id in self.connections:
            self.connections[service_id] = max(0, self.connections[service_id] - 1)
```

### 2.3 健康检查机制

#### 健康检查器
```python
class HealthChecker:
    def __init__(self, check_interval=10):
        self.check_interval = check_interval
        self.checkers = {
            'http': self._http_health_check,
            'tcp': self._tcp_health_check,
            'custom': self._custom_health_check
        }
        self.running = False
        
    def start_health_checks(self, service_registry):
        """启动健康检查"""
        self.running = True
        self.service_registry = service_registry
        
        import threading
        self.health_check_thread = threading.Thread(target=self._health_check_loop)
        self.health_check_thread.daemon = True
        self.health_check_thread.start()
        
    def _health_check_loop(self):
        """健康检查循环"""
        while self.running:
            try:
                self._check_all_services()
            except Exception as e:
                logger.error(f"Health check error: {e}")
                
            time.sleep(self.check_interval)
            
    def _check_all_services(self):
        """检查所有注册的服务"""
        # 获取所有服务
        all_services = self.service_registry.get_all_services()
        
        for service_id, service_info in all_services.items():
            try:
                is_healthy = self._check_service_health(service_info)
                self._update_service_status(service_id, is_healthy)
            except Exception as e:
                logger.warning(f"Health check failed for {service_id}: {e}")
                self._update_service_status(service_id, False)
                
    def _check_service_health(self, service_info):
        """检查单个服务健康状态"""
        check_type = service_info.get('health_check_type', 'http')
        checker = self.checkers.get(check_type, self._http_health_check)
        
        return checker(service_info)
        
    def _http_health_check(self, service_info):
        """HTTP健康检查"""
        import requests
        
        health_url = f"{service_info['protocol']}://{service_info['host']}:{service_info['port']}{service_info.get('health_check_url', '/api/health')}"
        
        try:
            response = requests.get(health_url, timeout=5)
            return response.status_code == 200
        except Exception:
            return False
            
    def _tcp_health_check(self, service_info):
        """TCP健康检查"""
        import socket
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((service_info['host'], service_info['port']))
            sock.close()
            return result == 0
        except Exception:
            return False
            
    def _update_service_status(self, service_id, is_healthy):
        """更新服务健康状态"""
        status = 'healthy' if is_healthy else 'unhealthy'
        self.service_registry.update_service_status(service_id, status)
```

## 3. 统一消息格式

### 3.1 请求消息格式

#### HTTP请求格式
```json
{
  "request_id": "req-123456789",
  "timestamp": "2025-01-15T10:30:00Z",
  "version": "1.0",
  "source": {
    "service": "api-gateway",
    "instance_id": "gateway-001",
    "user_id": "user-123",
    "session_id": "session-456"
  },
  "target": {
    "service": "user-management",
    "operation": "get_user_profile",
    "version": "1.0"
  },
  "headers": {
    "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "Content-Type": "application/json",
    "Accept": "application/json",
    "X-Request-ID": "req-123456789",
    "X-Correlation-ID": "corr-987654321",
    "X-Client-Type": "web",
    "X-Client-Version": "1.2.0"
  },
  "data": {
    "user_id": "user-123",
    "fields": ["id", "username", "email", "profile"]
  },
  "context": {
    "trace_id": "trace-abc123",
    "span_id": "span-def456",
    "client_ip": "*************",
    "user_agent": "Mozilla/5.0..."
  }
}
```

#### 消息队列格式
```json
{
  "message_id": "msg-123456789",
  "timestamp": "2025-01-15T10:30:00Z",
  "version": "1.0",
  "event_type": "user.profile.updated",
  "source": {
    "service": "user-management",
    "instance_id": "user-mgmt-001",
    "version": "1.0.0"
  },
  "data": {
    "user_id": "user-123",
    "changes": {
      "email": {
        "old_value": "<EMAIL>",
        "new_value": "<EMAIL>"
      },
      "profile.phone": {
        "old_value": "+1234567890",
        "new_value": "+0987654321"
      }
    },
    "updated_at": "2025-01-15T10:30:00Z",
    "updated_by": "user-123"
  },
  "metadata": {
    "correlation_id": "corr-987654321",
    "causation_id": "req-123456789",
    "retry_count": 0,
    "max_retries": 3,
    "ttl": 3600
  }
}
```

### 3.2 响应消息格式

#### 成功响应格式
```json
{
  "request_id": "req-123456789",
  "timestamp": "2025-01-15T10:30:01Z",
  "version": "1.0",
  "status": {
    "code": 200,
    "message": "Success",
    "details": "User profile retrieved successfully"
  },
  "data": {
    "user": {
      "id": "user-123",
      "username": "john_doe",
      "email": "<EMAIL>",
      "profile": {
        "first_name": "John",
        "last_name": "Doe",
        "phone": "+1234567890",
        "department": "Engineering"
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2025-01-15T10:30:00Z"
    }
  },
  "metadata": {
    "processing_time_ms": 150,
    "cache_hit": true,
    "source_service": "user-management",
    "instance_id": "user-mgmt-001"
  }
}
```

#### 错误响应格式
```json
{
  "request_id": "req-123456789",
  "timestamp": "2025-01-15T10:30:01Z",
  "version": "1.0",
  "status": {
    "code": 404,
    "message": "User Not Found",
    "details": "The requested user with ID 'user-999' does not exist"
  },
  "error": {
    "type": "UserNotFoundError",
    "code": "USER_NOT_FOUND",
    "message": "User not found",
    "details": "The requested user with ID 'user-999' does not exist",
    "field_errors": null,
    "stack_trace": null,
    "help_url": "https://docs.example.com/errors/user-not-found"
  },
  "metadata": {
    "processing_time_ms": 50,
    "source_service": "user-management",
    "instance_id": "user-mgmt-001",
    "retry_after": null
  }
}
```

### 3.3 消息验证和序列化

#### 消息验证器
```python
from marshmallow import Schema, fields, validate, ValidationError

class RequestMessageSchema(Schema):
    """请求消息验证模式"""
    request_id = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    timestamp = fields.DateTime(required=True, format='iso')
    version = fields.Str(required=True, validate=validate.OneOf(['1.0', '1.1', '2.0']))
    
    source = fields.Nested('SourceSchema', required=True)
    target = fields.Nested('TargetSchema', required=True)
    headers = fields.Dict(keys=fields.Str(), values=fields.Str())
    data = fields.Dict()
    context = fields.Dict()
    
class SourceSchema(Schema):
    """消息源验证模式"""
    service = fields.Str(required=True, validate=validate.Length(min=1, max=50))
    instance_id = fields.Str(required=True)
    user_id = fields.Str(allow_none=True)
    session_id = fields.Str(allow_none=True)
    
class TargetSchema(Schema):
    """消息目标验证模式"""
    service = fields.Str(required=True)
    operation = fields.Str(required=True)
    version = fields.Str(required=True)
    
class ResponseMessageSchema(Schema):
    """响应消息验证模式"""
    request_id = fields.Str(required=True)
    timestamp = fields.DateTime(required=True, format='iso')
    version = fields.Str(required=True)
    status = fields.Nested('StatusSchema', required=True)
    data = fields.Dict()
    error = fields.Nested('ErrorSchema', allow_none=True)
    metadata = fields.Dict()
    
class StatusSchema(Schema):
    """状态验证模式"""
    code = fields.Int(required=True, validate=validate.Range(min=100, max=599))
    message = fields.Str(required=True)
    details = fields.Str(allow_none=True)
    
class ErrorSchema(Schema):
    """错误验证模式"""
    type = fields.Str(required=True)
    code = fields.Str(required=True)
    message = fields.Str(required=True)
    details = fields.Str(allow_none=True)
    field_errors = fields.Dict(allow_none=True)
    stack_trace = fields.Str(allow_none=True)
    help_url = fields.Str(allow_none=True)

class MessageValidator:
    def __init__(self):
        self.request_schema = RequestMessageSchema()
        self.response_schema = ResponseMessageSchema()
        
    def validate_request(self, message_data):
        """
        验证请求消息
        
        Args:
            message_data: 消息数据字典
            
        Returns:
            dict: 验证后的消息数据
            
        Raises:
            ValidationError: 验证失败
        """
        try:
            return self.request_schema.load(message_data)
        except ValidationError as e:
            raise MessageValidationError(f"Request validation failed: {e.messages}")
            
    def validate_response(self, message_data):
        """
        验证响应消息
        
        Args:
            message_data: 消息数据字典
            
        Returns:
            dict: 验证后的消息数据
            
        Raises:
            ValidationError: 验证失败
        """
        try:
            return self.response_schema.load(message_data)
        except ValidationError as e:
            raise MessageValidationError(f"Response validation failed: {e.messages}")
```

## 4. 错误处理机制

### 4.1 错误分类和编码

#### 错误分类体系
```python
class ErrorCategory:
    """错误分类"""
    
    # 客户端错误 (4xx)
    CLIENT_ERROR = {
        'BAD_REQUEST': {
            'code': 400,
            'type': 'BadRequestError',
            'description': '请求格式错误或参数无效'
        },
        'UNAUTHORIZED': {
            'code': 401,
            'type': 'UnauthorizedError',
            'description': '未授权访问'
        },
        'FORBIDDEN': {
            'code': 403,
            'type': 'ForbiddenError',
            'description': '权限不足'
        },
        'NOT_FOUND': {
            'code': 404,
            'type': 'NotFoundError',
            'description': '资源不存在'
        },
        'CONFLICT': {
            'code': 409,
            'type': 'ConflictError',
            'description': '资源冲突'
        },
        'VALIDATION_ERROR': {
            'code': 422,
            'type': 'ValidationError',
            'description': '数据验证失败'
        },
        'RATE_LIMITED': {
            'code': 429,
            'type': 'RateLimitError',
            'description': '请求频率超限'
        }
    }
    
    # 服务器错误 (5xx)
    SERVER_ERROR = {
        'INTERNAL_ERROR': {
            'code': 500,
            'type': 'InternalServerError',
            'description': '内部服务器错误'
        },
        'SERVICE_UNAVAILABLE': {
            'code': 503,
            'type': 'ServiceUnavailableError',
            'description': '服务不可用'
        },
        'GATEWAY_TIMEOUT': {
            'code': 504,
            'type': 'GatewayTimeoutError',
            'description': '网关超时'
        },
        'DATABASE_ERROR': {
            'code': 500,
            'type': 'DatabaseError',
            'description': '数据库操作失败'
        },
        'EXTERNAL_SERVICE_ERROR': {
            'code': 502,
            'type': 'ExternalServiceError',
            'description': '外部服务错误'
        }
    }
    
    # 业务错误 (自定义)
    BUSINESS_ERROR = {
        'USER_NOT_FOUND': {
            'code': 404,
            'type': 'UserNotFoundError',
            'description': '用户不存在'
        },
        'EXAM_NOT_AVAILABLE': {
            'code': 409,
            'type': 'ExamNotAvailableError',
            'description': '考试不可用'
        },
        'INSUFFICIENT_PERMISSIONS': {
            'code': 403,
            'type': 'InsufficientPermissionsError',
            'description': '权限不足'
        },
        'EXAM_TIME_EXPIRED': {
            'code': 410,
            'type': 'ExamTimeExpiredError',
            'description': '考试时间已过期'
        }
    }
```

### 4.2 统一异常处理

#### 异常处理器
```python
class ExceptionHandler:
    def __init__(self):
        self.error_mappings = self._build_error_mappings()
        self.logger = logging.getLogger(__name__)
        
    def _build_error_mappings(self):
        """构建异常映射表"""
        mappings = {}
        
        # 添加所有错误分类
        for category in [ErrorCategory.CLIENT_ERROR, ErrorCategory.SERVER_ERROR, ErrorCategory.BUSINESS_ERROR]:
            for error_code, error_info in category.items():
                mappings[error_info['type']] = {
                    'code': error_code,
                    'http_status': error_info['code'],
                    'description': error_info['description']
                }
                
        return mappings
        
    def handle_exception(self, exception, request_context=None):
        """
        处理异常并生成标准错误响应
        
        Args:
            exception: 异常对象
            request_context: 请求上下文
            
        Returns:
            dict: 标准错误响应
        """
        error_type = type(exception).__name__
        error_mapping = self.error_mappings.get(error_type)
        
        if error_mapping:
            # 已知异常类型
            error_response = self._create_error_response(
                error_mapping['code'],
                error_mapping['http_status'],
                str(exception),
                error_type,
                request_context
            )
        else:
            # 未知异常类型，归类为内部错误
            error_response = self._create_error_response(
                'INTERNAL_ERROR',
                500,
                'An unexpected error occurred',
                'InternalServerError',
                request_context
            )
            
        # 记录错误日志
        self._log_exception(exception, error_response, request_context)
        
        return error_response
        
    def _create_error_response(self, error_code, http_status, message, error_type, request_context):
        """
        创建标准错误响应
        """
        request_id = request_context.get('request_id') if request_context else None
        
        return {
            'request_id': request_id,
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'version': '1.0',
            'status': {
                'code': http_status,
                'message': 'Error',
                'details': message
            },
            'error': {
                'type': error_type,
                'code': error_code,
                'message': message,
                'details': self._get_error_details(error_code),
                'field_errors': self._extract_field_errors(exception),
                'help_url': self._get_help_url(error_code)
            },
            'metadata': {
                'processing_time_ms': request_context.get('processing_time') if request_context else None,
                'source_service': request_context.get('service_name') if request_context else None,
                'instance_id': request_context.get('instance_id') if request_context else None
            }
        }
        
    def _log_exception(self, exception, error_response, request_context):
        """
        记录异常日志
        """
        log_data = {
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'error_code': error_response['error']['code'],
            'http_status': error_response['status']['code'],
            'request_id': error_response.get('request_id'),
            'user_id': request_context.get('user_id') if request_context else None,
            'service_name': request_context.get('service_name') if request_context else None
        }
        
        if error_response['status']['code'] >= 500:
            # 服务器错误，记录详细信息
            self.logger.error(
                f"Server error: {exception}",
                extra=log_data,
                exc_info=True
            )
        else:
            # 客户端错误，记录基本信息
            self.logger.warning(
                f"Client error: {exception}",
                extra=log_data
            )
```

### 4.3 重试机制

#### 智能重试器
```python
class RetryManager:
    def __init__(self):
        self.retry_policies = {
            'exponential_backoff': self._exponential_backoff,
            'linear_backoff': self._linear_backoff,
            'fixed_interval': self._fixed_interval
        }
        
    def retry_request(self, func, *args, **kwargs):
        """
        执行带重试的请求
        
        Args:
            func: 要执行的函数
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
        """
        retry_config = kwargs.pop('retry_config', {})
        
        max_retries = retry_config.get('max_retries', 3)
        retry_policy = retry_config.get('policy', 'exponential_backoff')
        retryable_errors = retry_config.get('retryable_errors', [
            'ConnectionError',
            'TimeoutError',
            'ServiceUnavailableError',
            'GatewayTimeoutError'
        ])
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                # 检查是否为可重试错误
                if not self._is_retryable_error(e, retryable_errors):
                    raise e
                    
                # 最后一次尝试，不再重试
                if attempt == max_retries:
                    break
                    
                # 计算等待时间
                wait_time = self._calculate_wait_time(
                    attempt, retry_policy, retry_config
                )
                
                # 记录重试日志
                logger.warning(
                    f"Request failed, retrying in {wait_time}s (attempt {attempt + 1}/{max_retries}): {e}"
                )
                
                time.sleep(wait_time)
                
        # 所有重试都失败了
        raise last_exception
        
    def _is_retryable_error(self, exception, retryable_errors):
        """检查异常是否可重试"""
        exception_type = type(exception).__name__
        return exception_type in retryable_errors
        
    def _calculate_wait_time(self, attempt, policy, config):
        """计算等待时间"""
        policy_func = self.retry_policies.get(policy, self._exponential_backoff)
        return policy_func(attempt, config)
        
    def _exponential_backoff(self, attempt, config):
        """指数退避"""
        base_delay = config.get('base_delay', 1.0)
        max_delay = config.get('max_delay', 60.0)
        multiplier = config.get('multiplier', 2.0)
        jitter = config.get('jitter', True)
        
        delay = base_delay * (multiplier ** attempt)
        delay = min(delay, max_delay)
        
        if jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # 添加50%的随机抖动
            
        return delay
        
    def _linear_backoff(self, attempt, config):
        """线性退避"""
        base_delay = config.get('base_delay', 1.0)
        increment = config.get('increment', 1.0)
        max_delay = config.get('max_delay', 60.0)
        
        delay = base_delay + (attempt * increment)
        return min(delay, max_delay)
        
    def _fixed_interval(self, attempt, config):
        """固定间隔"""
        return config.get('interval', 1.0)
```

## 5. 超时和熔断机制

### 5.1 超时控制

#### 超时管理器
```python
class TimeoutManager:
    def __init__(self):
        self.default_timeouts = {
            'connection_timeout': 5.0,    # 连接超时
            'read_timeout': 30.0,         # 读取超时
            'total_timeout': 60.0         # 总超时
        }
        
    def execute_with_timeout(self, func, timeout_config=None, *args, **kwargs):
        """
        执行带超时控制的函数
        
        Args:
            func: 要执行的函数
            timeout_config: 超时配置
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
            
        Raises:
            TimeoutError: 超时异常
        """
        import signal
        import threading
        
        timeout = self._get_timeout(timeout_config)
        
        if threading.current_thread() is threading.main_thread():
            # 主线程中使用signal
            return self._execute_with_signal_timeout(func, timeout, *args, **kwargs)
        else:
            # 子线程中使用threading
            return self._execute_with_thread_timeout(func, timeout, *args, **kwargs)
            
    def _get_timeout(self, timeout_config):
        """获取超时时间"""
        if timeout_config is None:
            return self.default_timeouts['total_timeout']
            
        if isinstance(timeout_config, (int, float)):
            return timeout_config
            
        return timeout_config.get('total_timeout', self.default_timeouts['total_timeout'])
        
    def _execute_with_signal_timeout(self, func, timeout, *args, **kwargs):
        """使用signal实现超时控制"""
        def timeout_handler(signum, frame):
            raise TimeoutError(f"Function execution timed out after {timeout} seconds")
            
        # 设置超时信号
        old_handler = signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(int(timeout))
        
        try:
            result = func(*args, **kwargs)
            signal.alarm(0)  # 取消超时
            return result
        except TimeoutError:
            raise
        finally:
            signal.signal(signal.SIGALRM, old_handler)
            
    def _execute_with_thread_timeout(self, func, timeout, *args, **kwargs):
        """使用线程实现超时控制"""
        import queue
        import threading
        
        result_queue = queue.Queue()
        exception_queue = queue.Queue()
        
        def target():
            try:
                result = func(*args, **kwargs)
                result_queue.put(result)
            except Exception as e:
                exception_queue.put(e)
                
        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(timeout)
        
        if thread.is_alive():
            # 超时了
            raise TimeoutError(f"Function execution timed out after {timeout} seconds")
            
        # 检查是否有异常
        if not exception_queue.empty():
            raise exception_queue.get()
            
        # 获取结果
        if not result_queue.empty():
            return result_queue.get()
            
        raise RuntimeError("Function completed but no result available")
```

### 5.2 熔断器模式

#### 熔断器实现
```python
class CircuitBreaker:
    def __init__(self, failure_threshold=5, recovery_timeout=60, expected_exception=Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        
        self._lock = threading.Lock()
        
    def __call__(self, func):
        """装饰器模式"""
        def wrapper(*args, **kwargs):
            return self.call(func, *args, **kwargs)
        return wrapper
        
    def call(self, func, *args, **kwargs):
        """
        执行函数调用
        
        Args:
            func: 要执行的函数
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
            
        Raises:
            CircuitBreakerOpenError: 熔断器开启时的异常
        """
        with self._lock:
            if self.state == 'OPEN':
                if self._should_attempt_reset():
                    self.state = 'HALF_OPEN'
                else:
                    raise CircuitBreakerOpenError(
                        f"Circuit breaker is OPEN. Retry after {self.recovery_timeout} seconds"
                    )
                    
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
            
    def _should_attempt_reset(self):
        """检查是否应该尝试重置熔断器"""
        return (
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
        
    def _on_success(self):
        """成功时的处理"""
        with self._lock:
            self.failure_count = 0
            self.state = 'CLOSED'
            
    def _on_failure(self):
        """失败时的处理"""
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                
    def get_state(self):
        """获取熔断器状态"""
        return {
            'state': self.state,
            'failure_count': self.failure_count,
            'failure_threshold': self.failure_threshold,
            'last_failure_time': self.last_failure_time,
            'recovery_timeout': self.recovery_timeout
        }
        
    def reset(self):
        """手动重置熔断器"""
        with self._lock:
            self.failure_count = 0
            self.last_failure_time = None
            self.state = 'CLOSED'

class CircuitBreakerOpenError(Exception):
    """熔断器开启异常"""
    pass
```

### 5.3 服务降级

#### 降级策略管理器
```python
class FallbackManager:
    def __init__(self):
        self.fallback_strategies = {}
        
    def register_fallback(self, service_name, operation, fallback_func):
        """
        注册降级策略
        
        Args:
            service_name: 服务名称
            operation: 操作名称
            fallback_func: 降级函数
        """
        key = f"{service_name}.{operation}"
        self.fallback_strategies[key] = fallback_func
        
    def execute_with_fallback(self, service_name, operation, primary_func, *args, **kwargs):
        """
        执行带降级的函数调用
        
        Args:
            service_name: 服务名称
            operation: 操作名称
            primary_func: 主要函数
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
        """
        try:
            return primary_func(*args, **kwargs)
        except Exception as e:
            # 检查是否需要降级
            if self._should_fallback(e):
                return self._execute_fallback(service_name, operation, e, *args, **kwargs)
            else:
                raise e
                
    def _should_fallback(self, exception):
        """检查是否应该执行降级"""
        fallback_exceptions = [
            'ServiceUnavailableError',
            'GatewayTimeoutError',
            'CircuitBreakerOpenError',
            'ConnectionError',
            'TimeoutError'
        ]
        
        return type(exception).__name__ in fallback_exceptions
        
    def _execute_fallback(self, service_name, operation, original_exception, *args, **kwargs):
        """执行降级策略"""
        key = f"{service_name}.{operation}"
        fallback_func = self.fallback_strategies.get(key)
        
        if fallback_func:
            try:
                logger.warning(
                    f"Executing fallback for {service_name}.{operation} due to: {original_exception}"
                )
                return fallback_func(*args, **kwargs)
            except Exception as fallback_error:
                logger.error(
                    f"Fallback execution failed for {service_name}.{operation}: {fallback_error}"
                )
                raise original_exception
        else:
            # 没有降级策略，抛出原始异常
            raise original_exception

# 降级策略示例
class UserServiceFallbacks:
    @staticmethod
    def get_user_profile_fallback(user_id, *args, **kwargs):
        """用户资料获取降级策略"""
        return {
            'user_id': user_id,
            'username': 'Unknown User',
            'email': '<EMAIL>',
            'profile': {
                'first_name': 'Unknown',
                'last_name': 'User',
                'avatar': '/default-avatar.png'
            },
            '_fallback': True,
            '_message': 'User service temporarily unavailable, showing cached data'
        }
        
    @staticmethod
    def get_user_list_fallback(*args, **kwargs):
        """用户列表获取降级策略"""
        return {
            'users': [],
            'total': 0,
            'page': 1,
            'per_page': 10,
            '_fallback': True,
            '_message': 'User service temporarily unavailable'
        }
```

---

## 总结

本模块间通信协议设计文档为职业技能等级考试系统建立了完整的通信框架，包括：

### 🔗 **核心特性**
1. **服务发现**：自动化的服务注册、发现和健康检查
2. **负载均衡**：多种负载均衡策略和连接管理
3. **统一消息格式**：标准化的请求/响应格式和验证
4. **错误处理**：完整的异常分类、处理和重试机制
5. **超时熔断**：智能的超时控制和熔断保护
6. **服务降级**：优雅的降级策略和故障恢复

### 🛡️ **可靠性保障**
- **故障隔离**：单点故障不影响整体系统
- **自动恢复**：智能的健康检查和故障恢复
- **优雅降级**：关键功能的备用方案
- **完整监控**：全面的通信状态监控和告警

### 📈 **性能优化**
- **连接复用**：高效的连接池管理
- **智能路由**：基于负载和健康状态的路由选择
- **缓存策略**：减少重复请求和网络开销
- **异步处理**：支持高并发的异步通信模式

本设计确保了系统各模块间的高效、可靠通信，为整个考试系统的稳定运行提供了坚实的基础。