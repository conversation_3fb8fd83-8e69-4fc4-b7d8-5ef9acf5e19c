<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件与项目管理工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 1.8rem;
            font-weight: 300;
        }
        
        .nav-tabs {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 0 2rem;
            display: flex;
            gap: 0;
        }
        
        .nav-tab {
            padding: 1rem 1.5rem;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .nav-tab:hover {
            color: #667eea;
            background: #f8f9fa;
        }
        
        .nav-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            overflow: hidden;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            color: #495057;
        }
        
        .card-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 1rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            margin: 0.25rem;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        .table tbody tr:hover {
            background: #f8f9fa;
        }
        
        .alert {
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
        
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: -0.5rem;
        }
        
        .col {
            flex: 1;
            padding: 0.5rem;
        }
        
        .col-6 {
            flex: 0 0 50%;
            max-width: 50%;
            padding: 0.5rem;
        }
        
        .col-4 {
            flex: 0 0 33.333333%;
            max-width: 33.333333%;
            padding: 0.5rem;
        }
        
        .col-3 {
            flex: 0 0 25%;
            max-width: 25%;
            padding: 0.5rem;
        }
        
        .progress {
            height: 1rem;
            background-color: #e9ecef;
            border-radius: 0.25rem;
            overflow: hidden;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.6s ease;
        }
        
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 700;
            line-height: 1;
            text-align: center;
            white-space: nowrap;
            vertical-align: baseline;
            border-radius: 0.25rem;
        }
        
        .badge-primary { background-color: #667eea; color: white; }
        .badge-success { background-color: #28a745; color: white; }
        .badge-warning { background-color: #ffc107; color: #212529; }
        .badge-danger { background-color: #dc3545; color: white; }
        .badge-info { background-color: #17a2b8; color: white; }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .file-upload-area {
            border: 2px dashed #ced4da;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload-area:hover {
            border-color: #667eea;
            background: #e7f3ff;
        }
        
        .file-upload-area.dragover {
            border-color: #667eea;
            background: #e7f3ff;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            margin: 0;
            font-size: 1.25rem;
        }
        
        .close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #aaa;
        }
        
        .close:hover {
            color: #000;
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .modal-footer {
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .nav-tabs {
                padding: 0 1rem;
                overflow-x: auto;
            }
            
            .row {
                flex-direction: column;
            }
            
            .col-6, .col-4, .col-3 {
                flex: 1;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <h1>📁 文件与项目管理工具</h1>
            <button class="btn btn-secondary btn-sm" onclick="returnToMainConsole()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3);">
                🏠 返回主控台
            </button>
        </div>
    </div>
    
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('dashboard')">📊 仪表板</button>
        <button class="nav-tab" onclick="showTab('files')">📁 文件管理</button>
        <button class="nav-tab" onclick="showTab('projects')">📋 项目管理</button>
        <button class="nav-tab" onclick="showTab('tasks')">✅ 任务管理</button>
        <button class="nav-tab" onclick="showTab('api-test')">🔧 API测试</button>
    </div>
    
    <div class="container">
        <!-- 仪表板 -->
        <div id="dashboard" class="tab-content active">
            <div class="row">
                <div class="col-3">
                    <div class="card">
                        <div class="card-body" style="text-align: center;">
                            <h3 id="totalFiles">-</h3>
                            <p>总文件数</p>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card">
                        <div class="card-body" style="text-align: center;">
                            <h3 id="totalProjects">-</h3>
                            <p>总项目数</p>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card">
                        <div class="card-body" style="text-align: center;">
                            <h3 id="totalTasks">-</h3>
                            <p>总任务数</p>
                        </div>
                    </div>
                </div>
                <div class="col-3">
                    <div class="card">
                        <div class="card-body" style="text-align: center;">
                            <h3 id="pendingTasks">-</h3>
                            <p>待处理任务</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">📈 系统状态</div>
                <div class="card-body">
                    <div id="systemStatus">正在检查系统状态...</div>
                </div>
            </div>
        </div>
        
        <!-- 文件管理 -->
        <div id="files" class="tab-content">
            <div class="card">
                <div class="card-header">📁 文件上传</div>
                <div class="card-body">
                    <div class="file-upload-area" id="fileUploadArea">
                        <p>📤 点击或拖拽文件到此处上传</p>
                        <input type="file" id="fileInput" multiple style="display: none;">
                    </div>
                    <div class="row" style="margin-top: 1rem;">
                        <div class="col-6">
                            <div class="form-group">
                                <label>文件描述</label>
                                <input type="text" id="fileDescription" class="form-control" placeholder="输入文件描述">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>标签</label>
                                <input type="text" id="fileTags" class="form-control" placeholder="输入标签，逗号分隔">
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="uploadFiles()">上传文件</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    📋 文件列表
                    <button class="btn btn-sm btn-primary" style="float: right;" onclick="loadFiles()">刷新</button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <input type="text" id="fileSearch" class="form-control" placeholder="搜索文件..." onkeyup="searchFiles()">
                        </div>
                        <div class="col-6">
                            <select id="fileTypeFilter" class="form-control" onchange="filterFiles()">
                                <option value="">所有类型</option>
                                <option value="image">图片</option>
                                <option value="document">文档</option>
                                <option value="video">视频</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div id="filesList">正在加载文件列表...</div>
                </div>
            </div>
        </div>
        
        <!-- 项目管理 -->
        <div id="projects" class="tab-content">
            <div class="card">
                <div class="card-header">📋 创建项目</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>项目名称 *</label>
                                <input type="text" id="projectName" class="form-control" placeholder="输入项目名称">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>项目描述</label>
                                <input type="text" id="projectDescription" class="form-control" placeholder="输入项目描述">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>开始日期</label>
                                <input type="date" id="projectStartDate" class="form-control">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>结束日期</label>
                                <input type="date" id="projectEndDate" class="form-control">
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="createProject()">创建项目</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    📊 项目列表
                    <button class="btn btn-sm btn-primary" style="float: right;" onclick="loadProjects()">刷新</button>
                </div>
                <div class="card-body">
                    <input type="text" id="projectSearch" class="form-control" placeholder="搜索项目..." onkeyup="searchProjects()">
                    <div id="projectsList">正在加载项目列表...</div>
                </div>
            </div>
        </div>
        
        <!-- 任务管理 -->
        <div id="tasks" class="tab-content">
            <div class="card">
                <div class="card-header">✅ 创建任务</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>任务名称 *</label>
                                <input type="text" id="taskName" class="form-control" placeholder="输入任务名称">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>所属项目 *</label>
                                <select id="taskProject" class="form-control">
                                    <option value="">选择项目</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>任务描述</label>
                        <textarea id="taskDescription" class="form-control" rows="3" placeholder="输入任务描述"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>优先级</label>
                                <select id="taskPriority" class="form-control">
                                    <option value="low">低</option>
                                    <option value="medium" selected>中</option>
                                    <option value="high">高</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>截止日期</label>
                                <input type="date" id="taskDueDate" class="form-control">
                            </div>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="createTask()">创建任务</button>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    📝 任务列表
                    <button class="btn btn-sm btn-primary" style="float: right;" onclick="loadTasks()">刷新</button>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <input type="text" id="taskSearch" class="form-control" placeholder="搜索任务..." onkeyup="searchTasks()">
                        </div>
                        <div class="col-6">
                            <select id="taskStatusFilter" class="form-control" onchange="filterTasks()">
                                <option value="">所有状态</option>
                                <option value="pending">待处理</option>
                                <option value="in_progress">进行中</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                    </div>
                    <div id="tasksList">正在加载任务列表...</div>
                </div>
            </div>
        </div>
        
        <!-- API测试 -->
        <div id="api-test" class="tab-content">
            <div class="card">
                <div class="card-header">🔧 API接口测试</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-4">
                            <button class="btn btn-primary btn-sm" onclick="testAPI('/api/health', 'GET')">健康检查</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-primary btn-sm" onclick="testAPI('/api/info', 'GET')">系统信息</button>
                        </div>
                        <div class="col-4">
                            <button class="btn btn-primary btn-sm" onclick="testAPI('/api/v1/files/list?page=1&per_page=5', 'GET')">文件列表</button>
                        </div>
                    </div>
                    <div id="apiTestResult" style="margin-top: 1rem;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="modalTitle">标题</h4>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                内容
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">关闭</button>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let currentFiles = [];
        let currentProjects = [];
        let currentTasks = [];
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            setupFileUpload();
        });
        
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 移除所有标签页按钮的激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 根据标签页加载相应数据
            switch(tabName) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'files':
                    loadFiles();
                    break;
                case 'projects':
                    loadProjects();
                    loadProjectsForTasks();
                    break;
                case 'tasks':
                    loadTasks();
                    loadProjectsForTasks();
                    break;
            }
        }
        
        // 加载仪表板数据
        async function loadDashboard() {
            try {
                // 检查系统状态
                const healthResponse = await fetch('/api/health');
                const healthData = await healthResponse.json();
                
                if (healthData.status === 'healthy') {
                    document.getElementById('systemStatus').innerHTML = 
                        '<div class="alert alert-success">✅ 系统运行正常</div>';
                } else {
                    document.getElementById('systemStatus').innerHTML = 
                        '<div class="alert alert-danger">❌ 系统异常</div>';
                }
                
                // 加载统计数据
                const [filesRes, projectsRes, tasksRes] = await Promise.all([
                    fetch('/api/v1/files/list?page=1&per_page=1'),
                    fetch('/api/v1/projects?page=1&per_page=1'),
                    fetch('/api/v1/tasks?page=1&per_page=1')
                ]);
                
                const [filesData, projectsData, tasksData] = await Promise.all([
                    filesRes.json(),
                    projectsRes.json(),
                    tasksRes.json()
                ]);
                
                document.getElementById('totalFiles').textContent = filesData.total || 0;
                document.getElementById('totalProjects').textContent = projectsData.total || 0;
                document.getElementById('totalTasks').textContent = tasksData.total || 0;
                
                // 计算待处理任务数
                const pendingTasksRes = await fetch('/api/v1/tasks?status=pending&page=1&per_page=1');
                const pendingTasksData = await pendingTasksRes.json();
                document.getElementById('pendingTasks').textContent = pendingTasksData.total || 0;
                
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
                document.getElementById('systemStatus').innerHTML = 
                    '<div class="alert alert-danger">❌ 无法连接到服务器</div>';
            }
        }
        
        // 文件上传相关功能
        function setupFileUpload() {
            const uploadArea = document.getElementById('fileUploadArea');
            const fileInput = document.getElementById('fileInput');
            
            uploadArea.addEventListener('click', () => fileInput.click());
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                fileInput.files = e.dataTransfer.files;
                updateFileUploadDisplay();
            });
            
            fileInput.addEventListener('change', updateFileUploadDisplay);
        }
        
        function updateFileUploadDisplay() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('fileUploadArea');
            
            if (fileInput.files.length > 0) {
                const fileNames = Array.from(fileInput.files).map(f => f.name).join(', ');
                uploadArea.innerHTML = `<p>📁 已选择 ${fileInput.files.length} 个文件</p><p style="font-size: 0.9em; color: #666;">${fileNames}</p>`;
            } else {
                uploadArea.innerHTML = '<p>📤 点击或拖拽文件到此处上传</p>';
            }
        }
        
        async function uploadFiles() {
            const fileInput = document.getElementById('fileInput');
            const description = document.getElementById('fileDescription').value;
            const tags = document.getElementById('fileTags').value;
            
            if (!fileInput.files.length) {
                showAlert('请选择要上传的文件', 'danger');
                return;
            }
            
            const formData = new FormData();
            for (let file of fileInput.files) {
                formData.append('files', file);
            }
            
            if (description) formData.append('description', description);
            if (tags) formData.append('tags', tags);
            
            try {
                showAlert('正在上传文件...', 'info');
                
                const response = await fetch('/api/v1/files/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer test-token-123456789'
                    },
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('文件上传成功！', 'success');
                    // 清空表单
                    fileInput.value = '';
                    document.getElementById('fileDescription').value = '';
                    document.getElementById('fileTags').value = '';
                    updateFileUploadDisplay();
                    loadFiles();
                } else {
                    showAlert(`上传失败: ${data.msg || '未知错误'}`, 'danger');
                }
            } catch (error) {
                showAlert(`上传失败: ${error.message}`, 'danger');
            }
        }
        
        // 加载文件列表
        async function loadFiles() {
            try {
                const response = await fetch('/api/v1/files/list?page=1&per_page=50');
                const data = await response.json();
                
                if (response.ok) {
                    currentFiles = data.files || [];
                    displayFiles(currentFiles);
                } else {
                    document.getElementById('filesList').innerHTML = 
                        `<div class="alert alert-danger">加载失败: ${data.msg}</div>`;
                }
            } catch (error) {
                document.getElementById('filesList').innerHTML = 
                    `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
            }
        }
        
        function displayFiles(files) {
            const container = document.getElementById('filesList');
            
            if (!files.length) {
                container.innerHTML = '<div class="alert alert-info">暂无文件</div>';
                return;
            }
            
            const table = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>类型</th>
                            <th>上传时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${files.map(file => `
                            <tr>
                                <td>${file.filename}</td>
                                <td>${formatFileSize(file.file_size)}</td>
                                <td><span class="badge badge-info">${file.file_type}</span></td>
                                <td>${formatDate(file.upload_time)}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="downloadFile(${file.id})">下载</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteFile(${file.id})">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }
        
        // 搜索文件
        function searchFiles() {
            const keyword = document.getElementById('fileSearch').value.toLowerCase();
            const filtered = currentFiles.filter(file => 
                file.filename.toLowerCase().includes(keyword) ||
                (file.description && file.description.toLowerCase().includes(keyword))
            );
            displayFiles(filtered);
        }
        
        // 筛选文件
        function filterFiles() {
            const type = document.getElementById('fileTypeFilter').value;
            const filtered = type ? currentFiles.filter(file => file.file_type === type) : currentFiles;
            displayFiles(filtered);
        }
        
        // 创建项目
        async function createProject() {
            const name = document.getElementById('projectName').value.trim();
            const description = document.getElementById('projectDescription').value.trim();
            const startDate = document.getElementById('projectStartDate').value;
            const endDate = document.getElementById('projectEndDate').value;
            
            if (!name) {
                showAlert('请输入项目名称', 'danger');
                return;
            }
            
            const projectData = {
                project_name: name,
                description: description,
                start_date: startDate,
                end_date: endDate
            };
            
            try {
                const response = await fetch('/api/v1/projects', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token-123456789'
                    },
                    body: JSON.stringify(projectData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('项目创建成功！', 'success');
                    // 清空表单
                    document.getElementById('projectName').value = '';
                    document.getElementById('projectDescription').value = '';
                    document.getElementById('projectStartDate').value = '';
                    document.getElementById('projectEndDate').value = '';
                    loadProjects();
                    loadProjectsForTasks();
                } else {
                    showAlert(`创建失败: ${data.msg || '未知错误'}`, 'danger');
                }
            } catch (error) {
                showAlert(`创建失败: ${error.message}`, 'danger');
            }
        }
        
        // 加载项目列表
        async function loadProjects() {
            try {
                const response = await fetch('/api/v1/projects?page=1&per_page=50');
                const data = await response.json();
                
                if (response.ok) {
                    currentProjects = data.projects || [];
                    displayProjects(currentProjects);
                } else {
                    document.getElementById('projectsList').innerHTML = 
                        `<div class="alert alert-danger">加载失败: ${data.msg}</div>`;
                }
            } catch (error) {
                document.getElementById('projectsList').innerHTML = 
                    `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
            }
        }
        
        function displayProjects(projects) {
            const container = document.getElementById('projectsList');
            
            if (!projects.length) {
                container.innerHTML = '<div class="alert alert-info">暂无项目</div>';
                return;
            }
            
            const table = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>项目名称</th>
                            <th>描述</th>
                            <th>开始日期</th>
                            <th>结束日期</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${projects.map(project => `
                            <tr>
                                <td>${project.project_name}</td>
                                <td>${project.description || '-'}</td>
                                <td>${project.start_date || '-'}</td>
                                <td>${project.end_date || '-'}</td>
                                <td><span class="badge badge-${getStatusColor(project.status)}">${getStatusText(project.status)}</span></td>
                                <td>
                                    <button class="btn btn-sm btn-primary" onclick="viewProject(${project.id})">查看</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteProject(${project.id})">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }
        
        // 搜索项目
        function searchProjects() {
            const keyword = document.getElementById('projectSearch').value.toLowerCase();
            const filtered = currentProjects.filter(project => 
                project.project_name.toLowerCase().includes(keyword) ||
                (project.description && project.description.toLowerCase().includes(keyword))
            );
            displayProjects(filtered);
        }
        
        // 为任务创建加载项目列表
        async function loadProjectsForTasks() {
            try {
                const response = await fetch('/api/v1/projects?page=1&per_page=100');
                const data = await response.json();
                
                if (response.ok) {
                    const select = document.getElementById('taskProject');
                    select.innerHTML = '<option value="">选择项目</option>';
                    
                    (data.projects || []).forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.id;
                        option.textContent = project.project_name;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('加载项目列表失败:', error);
            }
        }
        
        // 创建任务
        async function createTask() {
            const name = document.getElementById('taskName').value.trim();
            const projectId = document.getElementById('taskProject').value;
            const description = document.getElementById('taskDescription').value.trim();
            const priority = document.getElementById('taskPriority').value;
            const dueDate = document.getElementById('taskDueDate').value;
            
            if (!name) {
                showAlert('请输入任务名称', 'danger');
                return;
            }
            
            if (!projectId) {
                showAlert('请选择所属项目', 'danger');
                return;
            }
            
            const taskData = {
                task_name: name,
                project_id: parseInt(projectId),
                description: description,
                priority: priority,
                due_date: dueDate
            };
            
            try {
                const response = await fetch('/api/v1/tasks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer test-token-123456789'
                    },
                    body: JSON.stringify(taskData)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showAlert('任务创建成功！', 'success');
                    // 清空表单
                    document.getElementById('taskName').value = '';
                    document.getElementById('taskProject').value = '';
                    document.getElementById('taskDescription').value = '';
                    document.getElementById('taskPriority').value = 'medium';
                    document.getElementById('taskDueDate').value = '';
                    loadTasks();
                } else {
                    showAlert(`创建失败: ${data.msg || '未知错误'}`, 'danger');
                }
            } catch (error) {
                showAlert(`创建失败: ${error.message}`, 'danger');
            }
        }
        
        // 加载任务列表
        async function loadTasks() {
            try {
                const response = await fetch('/api/v1/tasks?page=1&per_page=50');
                const data = await response.json();
                
                if (response.ok) {
                    currentTasks = data.tasks || [];
                    displayTasks(currentTasks);
                } else {
                    document.getElementById('tasksList').innerHTML = 
                        `<div class="alert alert-danger">加载失败: ${data.msg}</div>`;
                }
            } catch (error) {
                document.getElementById('tasksList').innerHTML = 
                    `<div class="alert alert-danger">加载失败: ${error.message}</div>`;
            }
        }
        
        function displayTasks(tasks) {
            const container = document.getElementById('tasksList');
            
            if (!tasks.length) {
                container.innerHTML = '<div class="alert alert-info">暂无任务</div>';
                return;
            }
            
            const table = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>所属项目</th>
                            <th>优先级</th>
                            <th>状态</th>
                            <th>截止日期</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${tasks.map(task => `
                            <tr>
                                <td>${task.task_name}</td>
                                <td>${task.project_name || '-'}</td>
                                <td><span class="badge badge-${getPriorityColor(task.priority)}">${getPriorityText(task.priority)}</span></td>
                                <td><span class="badge badge-${getStatusColor(task.status)}">${getStatusText(task.status)}</span></td>
                                <td>${task.due_date || '-'}</td>
                                <td>
                                    <button class="btn btn-sm btn-success" onclick="updateTaskStatus(${task.id}, 'completed')">完成</button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteTask(${task.id})">删除</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            container.innerHTML = table;
        }
        
        // 搜索任务
        function searchTasks() {
            const keyword = document.getElementById('taskSearch').value.toLowerCase();
            const filtered = currentTasks.filter(task => 
                task.task_name.toLowerCase().includes(keyword) ||
                (task.description && task.description.toLowerCase().includes(keyword))
            );
            displayTasks(filtered);
        }
        
        // 筛选任务
        function filterTasks() {
            const status = document.getElementById('taskStatusFilter').value;
            const filtered = status ? currentTasks.filter(task => task.status === status) : currentTasks;
            displayTasks(filtered);
        }
        
        // API测试
        async function testAPI(url, method = 'GET') {
            const resultDiv = document.getElementById('apiTestResult');
            resultDiv.innerHTML = '<div class="loading"></div> 请求中...';
            
            try {
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                const statusClass = response.ok ? 'success' : 'danger';
                
                resultDiv.innerHTML = `
                    <div class="alert alert-${statusClass}">
                        <strong>状态:</strong> ${response.status} ${response.statusText}<br>
                        <strong>URL:</strong> ${url}<br>
                        <strong>响应:</strong>
                        <pre style="margin-top: 10px; background: #f8f9fa; padding: 10px; border-radius: 4px;">${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>错误:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function formatDate(dateString) {
            if (!dateString) return '-';
            return new Date(dateString).toLocaleString('zh-CN');
        }
        
        function getStatusColor(status) {
            const colors = {
                'active': 'success',
                'completed': 'primary',
                'pending': 'warning',
                'in_progress': 'info'
            };
            return colors[status] || 'secondary';
        }
        
        function getStatusText(status) {
            const texts = {
                'active': '进行中',
                'completed': '已完成',
                'pending': '待处理',
                'in_progress': '进行中'
            };
            return texts[status] || status;
        }
        
        function getPriorityColor(priority) {
            const colors = {
                'high': 'danger',
                'medium': 'warning',
                'low': 'success'
            };
            return colors[priority] || 'secondary';
        }
        
        function getPriorityText(priority) {
            const texts = {
                'high': '高',
                'medium': '中',
                'low': '低'
            };
            return texts[priority] || priority;
        }
        
        function showAlert(message, type = 'info') {
            // 创建临时提示
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.position = 'fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.style.minWidth = '300px';
            
            document.body.appendChild(alert);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 3000);
        }
        
        function showModal(title, content) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalBody').innerHTML = content;
            document.getElementById('modal').style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target === modal) {
                closeModal();
            }
        }
        
        // 占位函数（实际功能需要后端支持）
        function downloadFile(id) {
            showAlert('下载功能开发中...', 'info');
        }
        
        function deleteFile(id) {
            if (confirm('确定要删除这个文件吗？')) {
                showAlert('删除功能开发中...', 'info');
            }
        }
        
        function viewProject(id) {
            showAlert('项目详情功能开发中...', 'info');
        }
        
        function deleteProject(id) {
            if (confirm('确定要删除这个项目吗？')) {
                showAlert('删除功能开发中...', 'info');
            }
        }
        
        function updateTaskStatus(id, status) {
            showAlert('任务状态更新功能开发中...', 'info');
        }
        
        function deleteTask(id) {
            if (confirm('确定要删除这个任务吗？')) {
                showAlert('删除功能开发中...', 'info');
            }
        }
        
        // 返回主控台函数
        function returnToMainConsole() {
            try {
                // 尝试通过postMessage与父窗口通信（iframe模式）
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'returnToMainConsole',
                        source: 'file-project-manager'
                    }, '*');
                } else {
                    // 直接跳转到主控台
                    window.location.href = 'http://localhost:8000/dashboard';
                }
            } catch (error) {
                console.error('返回主控台失败:', error);
                // 备用方案：直接跳转
                window.location.href = 'http://localhost:8000/dashboard';
            }
        }
    </script>
</body>
</html>