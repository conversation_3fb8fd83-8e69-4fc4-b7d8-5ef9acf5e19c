<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线考试 - 局域网在线考试系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        .exam-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .exam-title {
            font-size: 18px;
            font-weight: 500;
        }
        
        .exam-info {
            display: flex;
            align-items: center;
            gap: 20px;
            font-size: 14px;
        }
        
        .timer {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 12px;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .timer.warning {
            background: #dc3545;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .main-content {
            margin-top: 80px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            padding: 20px;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 20px;
        }
        
        .question-area {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .question-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .question-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .question-counter {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
        
        .nav-buttons {
            display: flex;
            gap: 10px;
        }
        
        .nav-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .nav-btn:hover {
            background: #f8f9fa;
        }
        
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .question-content {
            padding: 30px;
        }
        
        .question-text {
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            margin-bottom: 20px;
        }
        
        .question-type {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        .question-score {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        .options-container {
            margin-top: 20px;
        }
        
        .option {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .option:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .option.selected {
            border-color: #667eea;
            background: #e3f2fd;
        }
        
        .option input[type="radio"],
        .option input[type="checkbox"] {
            margin-right: 12px;
            margin-top: 2px;
        }
        
        .option-text {
            flex: 1;
            line-height: 1.5;
        }
        
        .text-answer {
            width: 100%;
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            font-family: inherit;
        }
        
        .text-answer:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .progress-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .progress-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
        }
        
        .progress-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 500;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s;
        }
        
        .question-grid {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .grid-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 15px;
        }
        
        .questions-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 8px;
        }
        
        .question-item {
            width: 40px;
            height: 40px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .question-item:hover {
            border-color: #667eea;
        }
        
        .question-item.current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .question-item.answered {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }
        
        .question-item.flagged {
            background: #ffc107;
            color: #212529;
            border-color: #ffc107;
        }
        
        .action-buttons {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .action-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 10px;
        }
        
        .save-btn {
            background: #28a745;
            color: white;
        }
        
        .save-btn:hover {
            background: #218838;
        }
        
        .flag-btn {
            background: #ffc107;
            color: #212529;
        }
        
        .flag-btn:hover {
            background: #e0a800;
        }
        
        .submit-btn {
            background: #dc3545;
            color: white;
        }
        
        .submit-btn:hover {
            background: #c82333;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                margin-top: 100px;
            }
            
            .header-content {
                flex-direction: column;
                gap: 10px;
            }
            
            .exam-info {
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="exam-header">
        <div class="header-content">
            <div class="exam-title" id="examTitle">正在加载考试...</div>
            <div class="exam-info">
                <span>考生: {{ student_name }}</span>
                <div class="timer" id="timer">00:00:00</div>
            </div>
        </div>
    </div>
    
    <div class="main-content">
        <div class="question-area">
            <div id="alert" class="alert"></div>
            
            <div id="loading" class="loading">
                <div class="loading-spinner"></div>
                <p>正在加载考试内容...</p>
            </div>
            
            <div id="examContent" style="display: none;">
                <div class="question-header">
                    <div class="question-nav">
                        <div class="question-counter" id="questionCounter">第 1 题 / 共 0 题</div>
                        <div class="nav-buttons">
                            <button class="nav-btn" id="prevBtn" onclick="previousQuestion()" disabled>上一题</button>
                            <button class="nav-btn" id="nextBtn" onclick="nextQuestion()">下一题</button>
                        </div>
                    </div>
                </div>
                
                <div class="question-content">
                    <div id="questionContainer">
                        <!-- 题目内容将在这里动态加载 -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="progress-panel">
                <div class="progress-title">答题进度</div>
                <div class="progress-stats">
                    <div class="stat-item">
                        <div class="stat-number" id="answeredCount">0</div>
                        <div class="stat-label">已答题</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalCount">0</div>
                        <div class="stat-label">总题数</div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="question-grid">
                <div class="grid-title">题目导航</div>
                <div class="questions-grid" id="questionsGrid">
                    <!-- 题目导航将在这里动态生成 -->
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="action-btn save-btn" onclick="saveCurrentAnswer()">保存答案</button>
                <button class="action-btn flag-btn" onclick="toggleFlag()">标记题目</button>
                <button class="action-btn submit-btn" onclick="submitExam()">提交试卷</button>
            </div>
        </div>
    </div>
    
    <script>
        // 全局变量
        let examData = null;
        let questions = [];
        let currentQuestionIndex = 0;
        let answers = {};
        let flaggedQuestions = new Set();
        let sessionId = null;
        let examTimer = null;
        let examEndTime = null;
        
        // 页面加载时初始化
        window.addEventListener('load', function() {
            initializeExam();
        });
        
        // 初始化考试
        async function initializeExam() {
            const examId = {{ exam_id }};
            
            try {
                // 开始考试会话
                const sessionResponse = await fetch(`/api/v1/exams/${examId}/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const sessionData = await sessionResponse.json();
                
                if (sessionData.code !== 200) {
                    showAlert(sessionData.msg || '无法开始考试', 'error');
                    return;
                }
                
                sessionId = sessionData.data.id;
                
                // 获取考试题目
                const questionsResponse = await fetch(`/api/v1/exams/${examId}/questions`);
                const questionsData = await questionsResponse.json();
                
                if (questionsData.code !== 200) {
                    showAlert(questionsData.msg || '获取题目失败', 'error');
                    return;
                }
                
                questions = questionsData.data;
                
                if (questions.length === 0) {
                    showAlert('该考试暂无题目', 'error');
                    return;
                }
                
                // 恢复已保存的答案
                if (sessionData.data.answers) {
                    answers = sessionData.data.answers;
                }
                
                // 初始化界面
                initializeUI();
                loadQuestion(0);
                
                // 启动计时器
                startTimer();
                
                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
                document.getElementById('examContent').style.display = 'block';
                
            } catch (error) {
                console.error('初始化考试错误:', error);
                showAlert('网络连接失败，请刷新页面重试', 'error');
            }
        }
        
        // 初始化界面
        function initializeUI() {
            // 更新题目计数
            document.getElementById('totalCount').textContent = questions.length;
            
            // 生成题目导航
            generateQuestionGrid();
            
            // 更新进度
            updateProgress();
        }
        
        // 生成题目导航网格
        function generateQuestionGrid() {
            const grid = document.getElementById('questionsGrid');
            grid.innerHTML = '';
            
            questions.forEach((question, index) => {
                const item = document.createElement('div');
                item.className = 'question-item';
                item.textContent = index + 1;
                item.onclick = () => loadQuestion(index);
                
                // 设置状态
                if (index === currentQuestionIndex) {
                    item.classList.add('current');
                } else if (answers[question.id] !== undefined) {
                    item.classList.add('answered');
                }
                
                if (flaggedQuestions.has(question.id)) {
                    item.classList.add('flagged');
                }
                
                grid.appendChild(item);
            });
        }
        
        // 加载题目
        function loadQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            
            currentQuestionIndex = index;
            const question = questions[index];
            
            // 更新题目计数器
            document.getElementById('questionCounter').textContent = 
                `第 ${index + 1} 题 / 共 ${questions.length} 题`;
            
            // 更新导航按钮状态
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').disabled = index === questions.length - 1;
            
            // 渲染题目内容
            renderQuestion(question);
            
            // 更新题目导航
            generateQuestionGrid();
        }
        
        // 渲染题目内容
        function renderQuestion(question) {
            const container = document.getElementById('questionContainer');
            
            let html = `
                <div class="question-type">${getQuestionTypeText(question.type)}</div>
                <div class="question-score">本题 ${question.score || 0} 分</div>
                <div class="question-text">${question.content || '题目内容加载失败'}</div>
            `;
            
            // 根据题目类型渲染选项
            if (question.type === 'single_choice' || question.type === 'multiple_choice') {
                html += '<div class="options-container">';
                
                const options = question.options || [];
                const inputType = question.type === 'single_choice' ? 'radio' : 'checkbox';
                const savedAnswer = answers[question.id];
                
                options.forEach((option, index) => {
                    const optionId = `option_${question.id}_${index}`;
                    const isChecked = question.type === 'single_choice' ? 
                        savedAnswer === option.key : 
                        (Array.isArray(savedAnswer) && savedAnswer.includes(option.key));
                    
                    html += `
                        <div class="option ${isChecked ? 'selected' : ''}" onclick="selectOption(this, '${question.id}', '${option.key}', '${question.type}')">
                            <input type="${inputType}" id="${optionId}" name="question_${question.id}" value="${option.key}" ${isChecked ? 'checked' : ''}>
                            <div class="option-text">${option.key}. ${option.text}</div>
                        </div>
                    `;
                });
                
                html += '</div>';
            } else if (question.type === 'text' || question.type === 'essay') {
                const savedAnswer = answers[question.id] || '';
                html += `
                    <textarea class="text-answer" 
                              placeholder="请在此输入您的答案..." 
                              onchange="saveTextAnswer('${question.id}', this.value)"
                              oninput="saveTextAnswer('${question.id}', this.value)">${savedAnswer}</textarea>
                `;
            }
            
            container.innerHTML = html;
        }
        
        // 获取题目类型文本
        function getQuestionTypeText(type) {
            const typeMap = {
                'single_choice': '单选题',
                'multiple_choice': '多选题',
                'text': '填空题',
                'essay': '问答题'
            };
            return typeMap[type] || '未知题型';
        }
        
        // 选择选项
        function selectOption(element, questionId, optionKey, questionType) {
            const input = element.querySelector('input');
            
            if (questionType === 'single_choice') {
                // 单选题：清除其他选项的选中状态
                const container = element.parentElement;
                container.querySelectorAll('.option').forEach(opt => {
                    opt.classList.remove('selected');
                    opt.querySelector('input').checked = false;
                });
                
                // 选中当前选项
                element.classList.add('selected');
                input.checked = true;
                answers[questionId] = optionKey;
                
            } else if (questionType === 'multiple_choice') {
                // 多选题：切换选中状态
                input.checked = !input.checked;
                element.classList.toggle('selected', input.checked);
                
                // 更新答案数组
                if (!Array.isArray(answers[questionId])) {
                    answers[questionId] = [];
                }
                
                if (input.checked) {
                    if (!answers[questionId].includes(optionKey)) {
                        answers[questionId].push(optionKey);
                    }
                } else {
                    answers[questionId] = answers[questionId].filter(key => key !== optionKey);
                }
            }
            
            // 自动保存答案
            saveCurrentAnswer();
            
            // 更新进度
            updateProgress();
        }
        
        // 保存文本答案
        function saveTextAnswer(questionId, value) {
            answers[questionId] = value;
            
            // 延迟保存，避免频繁请求
            clearTimeout(window.saveTimeout);
            window.saveTimeout = setTimeout(() => {
                saveCurrentAnswer();
            }, 1000);
            
            updateProgress();
        }
        
        // 保存当前答案
        async function saveCurrentAnswer() {
            if (!sessionId) return;
            
            const question = questions[currentQuestionIndex];
            const answer = answers[question.id];
            
            if (answer === undefined || answer === '' || (Array.isArray(answer) && answer.length === 0)) {
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/sessions/${sessionId}/answers`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question_id: question.id,
                        answer: answer
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    // 显示保存成功提示（可选）
                    // showAlert('答案已保存', 'success');
                } else {
                    showAlert(data.msg || '保存答案失败', 'error');
                }
                
            } catch (error) {
                console.error('保存答案错误:', error);
                showAlert('网络连接失败，答案可能未保存', 'error');
            }
        }
        
        // 上一题
        function previousQuestion() {
            if (currentQuestionIndex > 0) {
                loadQuestion(currentQuestionIndex - 1);
            }
        }
        
        // 下一题
        function nextQuestion() {
            if (currentQuestionIndex < questions.length - 1) {
                loadQuestion(currentQuestionIndex + 1);
            }
        }
        
        // 标记题目
        function toggleFlag() {
            const question = questions[currentQuestionIndex];
            
            if (flaggedQuestions.has(question.id)) {
                flaggedQuestions.delete(question.id);
            } else {
                flaggedQuestions.add(question.id);
            }
            
            generateQuestionGrid();
        }
        
        // 更新进度
        function updateProgress() {
            const answeredCount = Object.keys(answers).filter(key => {
                const answer = answers[key];
                return answer !== undefined && answer !== '' && 
                       !(Array.isArray(answer) && answer.length === 0);
            }).length;
            
            document.getElementById('answeredCount').textContent = answeredCount;
            
            const progress = (answeredCount / questions.length) * 100;
            document.getElementById('progressFill').style.width = `${progress}%`;
        }
        
        // 启动计时器
        function startTimer() {
            // 这里应该从服务器获取考试剩余时间
            // 暂时设置为2小时
            const duration = 120 * 60; // 120分钟
            examEndTime = new Date(Date.now() + duration * 1000);
            
            examTimer = setInterval(updateTimer, 1000);
            updateTimer();
        }
        
        // 更新计时器
        function updateTimer() {
            const now = new Date();
            const remaining = Math.max(0, examEndTime - now);
            
            if (remaining === 0) {
                // 时间到，自动提交
                clearInterval(examTimer);
                showAlert('考试时间已到，正在自动提交...', 'error');
                submitExam(true);
                return;
            }
            
            const hours = Math.floor(remaining / (1000 * 60 * 60));
            const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
            
            const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            const timerElement = document.getElementById('timer');
            timerElement.textContent = timeStr;
            
            // 最后10分钟显示警告
            if (remaining <= 10 * 60 * 1000) {
                timerElement.classList.add('warning');
            }
        }
        
        // 提交考试
        async function submitExam(autoSubmit = false) {
            if (!autoSubmit) {
                const answeredCount = Object.keys(answers).filter(key => {
                    const answer = answers[key];
                    return answer !== undefined && answer !== '' && 
                           !(Array.isArray(answer) && answer.length === 0);
                }).length;
                
                const unansweredCount = questions.length - answeredCount;
                
                if (unansweredCount > 0) {
                    if (!confirm(`还有 ${unansweredCount} 道题未作答，确定要提交试卷吗？提交后将无法修改。`)) {
                        return;
                    }
                } else {
                    if (!confirm('确定要提交试卷吗？提交后将无法修改。')) {
                        return;
                    }
                }
            }
            
            try {
                const response = await fetch(`/api/v1/sessions/${sessionId}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    clearInterval(examTimer);
                    showAlert('试卷提交成功！正在跳转...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = data.data.redirect_url || '/dashboard';
                    }, 2000);
                } else {
                    showAlert(data.msg || '提交失败', 'error');
                }
                
            } catch (error) {
                console.error('提交考试错误:', error);
                showAlert('网络连接失败，请稍后重试', 'error');
            }
        }
        
        // 显示提示信息
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
            
            // 3秒后自动隐藏成功提示
            if (type === 'success') {
                setTimeout(hideAlert, 3000);
            }
        }
        
        // 隐藏提示信息
        function hideAlert() {
            const alert = document.getElementById('alert');
            alert.style.display = 'none';
        }
        
        // 防止页面刷新或关闭时丢失数据
        window.addEventListener('beforeunload', function(e) {
            if (sessionId && examTimer) {
                e.preventDefault();
                e.returnValue = '考试正在进行中，确定要离开吗？未保存的答案可能会丢失。';
                return e.returnValue;
            }
        });
        
        // 定期自动保存
        setInterval(() => {
            if (sessionId) {
                saveCurrentAnswer();
            }
        }, 30000); // 每30秒自动保存一次
    </script>
</body>
</html>