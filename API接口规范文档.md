# API接口规范文档

## 1. 接口设计原则

### 1.1 RESTful设计原则
- **资源导向**：URL表示资源，HTTP方法表示操作
- **无状态**：每个请求包含处理所需的所有信息
- **统一接口**：使用标准HTTP方法和状态码
- **分层系统**：客户端无需知道是否直接连接到端服务器
- **可缓存**：响应应明确标识是否可缓存

### 1.2 命名规范
- **URL路径**：使用小写字母，单词间用连字符分隔
- **资源名称**：使用复数形式（如 `/users` 而不是 `/user`）
- **参数名称**：使用下划线命名法（如 `user_id`）
- **JSON字段**：使用下划线命名法

## 2. 全局接口规范

### 2.1 基础URL结构
```
{protocol}://{host}:{port}/api/{version}/{module}/{resource}

示例：
http://localhost:5000/api/v1/user/users
http://localhost:5000/api/v1/question/banks
http://localhost:5000/api/v1/exam/sessions
```

### 2.2 版本控制
- **当前版本**：v1
- **版本策略**：URL路径版本控制
- **兼容性**：向后兼容至少2个版本
- **废弃策略**：提前6个月通知，逐步迁移

### 2.3 HTTP方法映射

| HTTP方法 | 操作类型 | 示例 | 说明 |
|---------|---------|------|------|
| GET | 查询 | `GET /api/v1/user/users` | 获取用户列表 |
| GET | 查询单个 | `GET /api/v1/user/users/123` | 获取特定用户 |
| POST | 创建 | `POST /api/v1/user/users` | 创建新用户 |
| PUT | 完整更新 | `PUT /api/v1/user/users/123` | 完整更新用户信息 |
| PATCH | 部分更新 | `PATCH /api/v1/user/users/123` | 部分更新用户信息 |
| DELETE | 删除 | `DELETE /api/v1/user/users/123` | 删除用户 |

### 2.4 状态码规范

#### 成功状态码
- **200 OK**：请求成功
- **201 Created**：资源创建成功
- **202 Accepted**：请求已接受，异步处理中
- **204 No Content**：请求成功，无返回内容

#### 客户端错误状态码
- **400 Bad Request**：请求参数错误
- **401 Unauthorized**：未认证或认证失败
- **403 Forbidden**：已认证但权限不足
- **404 Not Found**：资源不存在
- **405 Method Not Allowed**：HTTP方法不被允许
- **409 Conflict**：资源冲突
- **422 Unprocessable Entity**：请求格式正确但语义错误
- **429 Too Many Requests**：请求过于频繁

#### 服务器错误状态码
- **500 Internal Server Error**：服务器内部错误
- **502 Bad Gateway**：网关错误
- **503 Service Unavailable**：服务不可用
- **504 Gateway Timeout**：网关超时

## 3. 请求规范

### 3.1 请求头规范

#### 必需请求头
```http
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Request-ID: {unique_request_id}
```

#### 可选请求头
```http
Accept: application/json
Accept-Language: zh-CN,en-US
X-Client-Version: 1.0.0
X-Client-Type: web|desktop|mobile
X-Trace-ID: {trace_id}
X-Forwarded-For: {client_ip}
```

### 3.2 请求参数规范

#### 查询参数（Query Parameters）
```http
GET /api/v1/user/users?page=1&size=20&sort=created_at&order=desc&status=active

通用查询参数：
- page: 页码（从1开始）
- size: 每页数量（默认20，最大100）
- sort: 排序字段
- order: 排序方向（asc|desc）
- search: 搜索关键词
- filter: 过滤条件
```

#### 路径参数（Path Parameters）
```http
GET /api/v1/user/users/{user_id}
GET /api/v1/question/banks/{bank_id}/questions/{question_id}

规范：
- 使用大括号标识路径参数
- 参数名称使用下划线命名法
- 必须是资源的唯一标识符
```

#### 请求体参数（Request Body）
```json
{
  "username": "admin",
  "password": "password123",
  "email": "<EMAIL>",
  "role": "administrator",
  "profile": {
    "real_name": "管理员",
    "phone": "***********"
  }
}

规范：
- 使用JSON格式
- 字段名使用下划线命名法
- 支持嵌套对象
- 必填字段不能为null
```

### 3.3 请求验证规范

#### 参数验证
```json
{
  "username": {
    "type": "string",
    "required": true,
    "min_length": 3,
    "max_length": 50,
    "pattern": "^[a-zA-Z0-9_]+$"
  },
  "email": {
    "type": "string",
    "required": true,
    "format": "email"
  },
  "age": {
    "type": "integer",
    "required": false,
    "minimum": 0,
    "maximum": 150
  }
}
```

## 4. 响应规范

### 4.1 统一响应格式

#### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    // 具体业务数据
  },
  "timestamp": "2025-01-01T10:00:00.123Z",
  "request_id": "req-123456789"
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "请求参数验证失败",
  "error": {
    "type": "ValidationError",
    "details": [
      {
        "field": "username",
        "message": "用户名长度必须在3-50个字符之间",
        "code": "INVALID_LENGTH"
      },
      {
        "field": "email",
        "message": "邮箱格式不正确",
        "code": "INVALID_FORMAT"
      }
    ]
  },
  "timestamp": "2025-01-01T10:00:00.123Z",
  "request_id": "req-123456789"
}
```

#### 分页响应
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      // 数据项列表
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5,
      "has_next": true,
      "has_prev": false
    }
  },
  "timestamp": "2025-01-01T10:00:00.123Z",
  "request_id": "req-123456789"
}
```

### 4.2 响应头规范

#### 标准响应头
```http
Content-Type: application/json; charset=utf-8
X-Request-ID: req-123456789
X-Response-Time: 150ms
X-Rate-Limit-Remaining: 95
X-Rate-Limit-Reset: 1640995200
```

#### 缓存控制头
```http
Cache-Control: no-cache, no-store, must-revalidate
Pragma: no-cache
Expires: 0

# 对于可缓存的资源
Cache-Control: public, max-age=3600
ETag: "abc123"
Last-Modified: Wed, 01 Jan 2025 10:00:00 GMT
```

## 5. 各模块接口规范

### 5.1 用户管理模块接口

#### 用户认证接口
```http
POST /api/v1/user/auth/login
Content-Type: application/json

请求体：
{
  "username": "admin",
  "password": "password123",
  "remember_me": false
}

响应：
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 7200,
    "user": {
      "id": "user-123",
      "username": "admin",
      "role": "administrator",
      "permissions": ["user:*:*", "exam:*:*"]
    }
  }
}
```

#### 用户管理接口
```http
# 获取用户列表
GET /api/v1/user/users?page=1&size=20&role=student&status=active

# 获取用户详情
GET /api/v1/user/users/{user_id}

# 创建用户
POST /api/v1/user/users
{
  "username": "student001",
  "password": "password123",
  "email": "<EMAIL>",
  "role": "student",
  "profile": {
    "real_name": "张三",
    "phone": "***********",
    "id_card": "110101199001011234"
  }
}

# 更新用户
PUT /api/v1/user/users/{user_id}
PATCH /api/v1/user/users/{user_id}

# 删除用户
DELETE /api/v1/user/users/{user_id}

# 批量导入用户
POST /api/v1/user/users/import
Content-Type: multipart/form-data

# 重置密码
POST /api/v1/user/users/{user_id}/reset-password
```

### 5.2 题库管理模块接口

#### 题库管理接口
```http
# 获取题库列表
GET /api/v1/question/banks?subject=计算机&difficulty=medium

# 获取题库详情
GET /api/v1/question/banks/{bank_id}

# 创建题库
POST /api/v1/question/banks
{
  "name": "计算机基础题库",
  "subject": "计算机",
  "description": "计算机基础知识题库",
  "tags": ["基础", "理论"]
}

# 更新题库
PUT /api/v1/question/banks/{bank_id}

# 删除题库
DELETE /api/v1/question/banks/{bank_id}
```

#### 试题管理接口
```http
# 获取试题列表
GET /api/v1/question/banks/{bank_id}/questions?type=single_choice&difficulty=easy

# 获取试题详情
GET /api/v1/question/questions/{question_id}

# 创建试题
POST /api/v1/question/banks/{bank_id}/questions
{
  "type": "single_choice",
  "title": "以下哪个是Python的特点？",
  "content": "Python是一种...",
  "options": [
    {"key": "A", "value": "编译型语言"},
    {"key": "B", "value": "解释型语言"},
    {"key": "C", "value": "汇编语言"},
    {"key": "D", "value": "机器语言"}
  ],
  "answer": "B",
  "explanation": "Python是解释型语言...",
  "difficulty": "easy",
  "points": 2,
  "tags": ["Python", "基础"]
}

# 更新试题
PUT /api/v1/question/questions/{question_id}

# 删除试题
DELETE /api/v1/question/questions/{question_id}

# 批量导入试题
POST /api/v1/question/banks/{bank_id}/questions/import
```

### 5.3 考试管理模块接口

#### 考试管理接口
```http
# 获取考试列表
GET /api/v1/exam/sessions?status=scheduled&start_date=2025-01-01

# 获取考试详情
GET /api/v1/exam/sessions/{session_id}

# 创建考试
POST /api/v1/exam/sessions
{
  "name": "计算机基础考试",
  "description": "2025年第一次计算机基础考试",
  "start_time": "2025-01-15T09:00:00Z",
  "end_time": "2025-01-15T11:00:00Z",
  "duration": 120,
  "question_banks": ["bank-123", "bank-456"],
  "participants": ["user-123", "user-456"],
  "settings": {
    "shuffle_questions": true,
    "shuffle_options": true,
    "allow_review": false,
    "auto_submit": true
  }
}

# 更新考试
PUT /api/v1/exam/sessions/{session_id}

# 删除考试
DELETE /api/v1/exam/sessions/{session_id}

# 开始考试
POST /api/v1/exam/sessions/{session_id}/start

# 结束考试
POST /api/v1/exam/sessions/{session_id}/end
```

#### 考试参与接口
```http
# 获取考生的考试列表
GET /api/v1/exam/my-sessions?status=available

# 加入考试
POST /api/v1/exam/sessions/{session_id}/join

# 获取考试试卷
GET /api/v1/exam/sessions/{session_id}/paper

# 提交答案
POST /api/v1/exam/sessions/{session_id}/submit
{
  "answers": [
    {
      "question_id": "q-123",
      "answer": "B",
      "time_spent": 30
    },
    {
      "question_id": "q-456",
      "answer": "这是主观题的答案...",
      "time_spent": 120
    }
  ],
  "submit_time": "2025-01-15T10:30:00Z"
}

# 保存草稿
POST /api/v1/exam/sessions/{session_id}/save-draft
```

### 5.4 成绩管理模块接口

#### 成绩查询接口
```http
# 获取成绩列表
GET /api/v1/grade/results?session_id=session-123&user_id=user-456

# 获取成绩详情
GET /api/v1/grade/results/{result_id}

# 获取成绩统计
GET /api/v1/grade/statistics?session_id=session-123

响应：
{
  "code": 200,
  "data": {
    "total_participants": 100,
    "submitted_count": 95,
    "average_score": 78.5,
    "pass_rate": 0.85,
    "score_distribution": {
      "0-60": 15,
      "60-70": 20,
      "70-80": 25,
      "80-90": 20,
      "90-100": 15
    }
  }
}
```

#### 阅卷管理接口
```http
# 获取待阅卷列表
GET /api/v1/grade/pending-reviews?subject=计算机&type=subjective

# 提交阅卷结果
POST /api/v1/grade/reviews
{
  "result_id": "result-123",
  "question_id": "q-456",
  "score": 8,
  "max_score": 10,
  "comment": "答案基本正确，但缺少部分要点",
  "reviewer_id": "teacher-123"
}

# 申请复评
POST /api/v1/grade/reviews/{review_id}/appeal
```

### 5.5 监控模块接口

#### 实时监控接口
```http
# 获取考试监控数据
GET /api/v1/monitor/sessions/{session_id}/status

# 获取考生状态
GET /api/v1/monitor/sessions/{session_id}/participants

# 获取异常行为记录
GET /api/v1/monitor/violations?session_id=session-123&type=screen_switch

# WebSocket实时监控
WS /ws/monitor/sessions/{session_id}
```

### 5.6 日志审计模块接口

#### 日志查询接口
```http
# 获取操作日志
GET /api/v1/audit/logs?user_id=user-123&action=login&start_date=2025-01-01

# 获取系统日志
GET /api/v1/audit/system-logs?level=error&module=user-management

# 获取安全事件
GET /api/v1/audit/security-events?type=failed_login&severity=high
```

## 6. 错误处理规范

### 6.1 错误码定义

#### 通用错误码
```json
{
  "INVALID_REQUEST": {
    "code": "E001",
    "message": "请求格式错误"
  },
  "AUTHENTICATION_FAILED": {
    "code": "E002",
    "message": "认证失败"
  },
  "PERMISSION_DENIED": {
    "code": "E003",
    "message": "权限不足"
  },
  "RESOURCE_NOT_FOUND": {
    "code": "E004",
    "message": "资源不存在"
  },
  "RATE_LIMIT_EXCEEDED": {
    "code": "E005",
    "message": "请求过于频繁"
  }
}
```

#### 业务错误码
```json
{
  "USER_ALREADY_EXISTS": {
    "code": "U001",
    "message": "用户已存在"
  },
  "INVALID_PASSWORD": {
    "code": "U002",
    "message": "密码格式不正确"
  },
  "EXAM_NOT_STARTED": {
    "code": "E101",
    "message": "考试尚未开始"
  },
  "EXAM_ALREADY_SUBMITTED": {
    "code": "E102",
    "message": "考试已提交"
  }
}
```

### 6.2 错误响应示例

#### 参数验证错误
```json
{
  "code": 422,
  "message": "参数验证失败",
  "error": {
    "type": "ValidationError",
    "code": "VALIDATION_FAILED",
    "details": [
      {
        "field": "username",
        "message": "用户名不能为空",
        "code": "REQUIRED_FIELD"
      },
      {
        "field": "email",
        "message": "邮箱格式不正确",
        "code": "INVALID_FORMAT"
      }
    ]
  },
  "timestamp": "2025-01-01T10:00:00.123Z",
  "request_id": "req-123456789"
}
```

#### 业务逻辑错误
```json
{
  "code": 409,
  "message": "用户名已存在",
  "error": {
    "type": "BusinessError",
    "code": "USER_ALREADY_EXISTS",
    "details": {
      "username": "admin",
      "suggestion": "请尝试其他用户名"
    }
  },
  "timestamp": "2025-01-01T10:00:00.123Z",
  "request_id": "req-123456789"
}
```

## 7. 安全规范

### 7.1 认证规范

#### JWT Token格式
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "user_id": "user-123",
    "username": "admin",
    "role": "administrator",
    "permissions": ["user:*:*", "exam:*:*"],
    "exp": 1640995200,
    "iat": 1640991600,
    "iss": "exam-system",
    "aud": "api-gateway"
  }
}
```

#### 权限验证
```http
# 请求头中携带Token
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# 权限不足时的响应
{
  "code": 403,
  "message": "权限不足",
  "error": {
    "type": "PermissionError",
    "code": "PERMISSION_DENIED",
    "required_permissions": ["user:write"],
    "user_permissions": ["user:read"]
  }
}
```

### 7.2 数据安全规范

#### 敏感数据处理
```json
{
  "user": {
    "id": "user-123",
    "username": "admin",
    "email": "ad***@example.com",  // 邮箱脱敏
    "phone": "138****8000",        // 手机号脱敏
    "id_card": "110101****1234",   // 身份证脱敏
    "password": "[HIDDEN]"         // 密码隐藏
  }
}
```

#### 输入验证
```json
{
  "validation_rules": {
    "username": {
      "type": "string",
      "min_length": 3,
      "max_length": 50,
      "pattern": "^[a-zA-Z0-9_]+$",
      "sanitize": true
    },
    "content": {
      "type": "string",
      "max_length": 10000,
      "html_escape": true,
      "sql_injection_check": true
    }
  }
}
```

## 8. 性能规范

### 8.1 响应时间要求
- **查询接口**：< 200ms
- **创建/更新接口**：< 500ms
- **批量操作接口**：< 2s
- **文件上传接口**：< 10s

### 8.2 并发处理
- **单个接口**：支持1000 QPS
- **系统总体**：支持10000 QPS
- **数据库连接池**：最大100个连接
- **缓存命中率**：> 90%

### 8.3 限流规范
```yaml
rate_limits:
  global:
    rate: "1000/minute"
    burst: 100
    
  per_user:
    rate: "200/minute"
    burst: 20
    
  per_ip:
    rate: "100/minute"
    burst: 10
    
  login:
    rate: "5/minute"
    burst: 2
```

## 9. 文档规范

### 9.1 接口文档格式
每个接口必须包含以下信息：
- **接口描述**：功能说明
- **请求方法**：HTTP方法
- **请求URL**：完整的URL路径
- **请求参数**：参数说明和示例
- **响应格式**：成功和失败响应示例
- **错误码**：可能的错误码和说明
- **权限要求**：所需权限
- **限流规则**：频率限制

### 9.2 变更管理
- **版本记录**：记录每次接口变更
- **兼容性说明**：向后兼容性保证
- **迁移指南**：版本升级指导
- **废弃通知**：提前通知废弃接口

## 10. 测试规范

### 10.1 接口测试
- **单元测试**：覆盖率 > 80%
- **集成测试**：模块间接口测试
- **性能测试**：压力测试和负载测试
- **安全测试**：渗透测试和漏洞扫描

### 10.2 测试用例
每个接口需要包含：
- **正常流程测试**：正确参数的测试
- **异常流程测试**：错误参数的测试
- **边界值测试**：极限值的测试
- **权限测试**：不同权限的测试
- **并发测试**：高并发场景测试

---

## 总结

本接口规范文档为职业技能等级考试系统定义了统一的API标准，确保各模块间的一致性和互操作性。通过遵循这些规范，可以：

1. **提高开发效率**：统一的标准减少沟通成本
2. **保证系统质量**：规范的错误处理和验证机制
3. **增强安全性**：统一的认证和权限控制
4. **便于维护**：清晰的文档和版本管理
5. **支持扩展**：标准化的接口便于新模块接入

所有开发团队应严格遵循本规范，确保系统的一致性和可维护性。