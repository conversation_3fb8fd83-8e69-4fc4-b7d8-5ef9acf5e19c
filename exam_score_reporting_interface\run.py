# -*- coding: utf-8 -*-
"""
考试成绩上报接口系统 - 启动脚本
"""
import os
from app import create_app
from config import config

# Get the environment from an environment variable, default to 'development'
config_name = os.getenv('FLASK_ENV') or 'development'
app = create_app(config[config_name])

if __name__ == '__main__':
    port = 5008
    print(f"--- Exam Score Reporting Interface Service ---")
    print(f"Starting server at: http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port)
