# 题库管理模块 (Question Bank Management)

## 📝 模块概述

题库管理模块是 PH&RL 在线考试系统的核心组件之一，负责试题和试卷的全生命周期管理。本模块采用Web界面实现，基于Python Flask框架开发，提供直观的用户界面和强大的后台管理功能，支持多种题型管理、智能组卷和试卷导出等核心功能。

## 🎯 主要功能

### 题库管理
- **试题管理**：增删改查试题，支持多种题型（单选、多选、判断、填空、简答、编程等）
- **批量操作**：支持Excel批量导入导出试题
- **分类管理**：按学科、章节、难度等多维度分类管理
- **高级搜索**：支持关键词、题型、难度等条件组合搜索
- **试题预览**：支持预览试题的实际显示效果
- **版本控制**：记录试题修改历史，支持版本回溯

### 组卷功能
- **手动组卷**：手动选择试题组成试卷
- **智能组卷**：根据试卷结构、难度分布、知识点覆盖等条件自动组卷
- **试卷模板**：支持创建和使用试卷模板
- **试卷预览**：支持预览试卷的实际显示效果
- **试卷导出**：支持导出试卷为Word、PDF等格式
- **试卷分析**：分析试卷的难度、知识点覆盖等指标
- **分数设置**：设置试卷总分和及格分数/及格百分比
- **校核报告**：生成试卷校核报告，验证组卷结果与规则的一致性

## 🎯 组卷规则系统

### 组卷规则模板结构

系统支持通过Excel模板定义组卷规则，模板包含两个工作表：

#### 1. 题型分布表（Sheet1）
```
题库名称          | 题型        | 题量 | 分值
Bank Name        | Question Type| Count| Score
保卫管理员（三级）理论 | B（单选题）   | 10   | 2
```

#### 2. 知识点分布表（Sheet2）
```
1级代码 | 1级比重(%) | 2级代码 | 2级比重(%) | 3级代码 | 3级比重(%)
Level 1 | Weight(%) | Level 2 | Weight(%) | Level 3 | Weight(%)
A      | 50        | A-B      | 60        | A-B-C      | 100
```

### 知识点分布执行逻辑

系统按照以下逻辑执行知识点分布要求：

1. **题目ID解析**：解析题目ID中的层级代码结构
   - 题目ID格式：`BWGL-3-LL-B-A-A-A-001-001`
   - 提取三级代码：第5-7段（A-A-A）

2. **三级代码层级分解逻辑**：
   - 三级代码采用逐级分解结构，如"A-B-C"表示：
     - 第一级代码：A
     - 第二级代码：A分解出的B组（完整路径：A-B）
     - 第三级代码：A-B分解出的C组（完整路径：A-B-C）
   - **核心原理**：只要第三级代码的分布符合要求，反推回去第二级和第一级代码必然符合要求

3. **知识点匹配**：
   - 系统只需检查题目的完整三级代码路径（如A-B-C）是否在知识点分布配置中
   - 无需单独验证一级、二级代码，因为三级代码已包含完整的层级信息

4. **题目筛选优先级**：
   - **第一优先级**：三级代码完全匹配知识点分布要求的题目
   - **第二优先级**：仅符合重复率限制的题目
   - **第三优先级**：所有可用题目（当前两级都不足时）

5. **重复率控制**：
- 基于试题ID计算重复率，确保任意两套试卷之间的题目重复率低于设定阈值
- 默认重复率限制：9%（可配置）
   - 多套试卷生成时避免过度重复

### 校核报告机制

系统提供试卷校核报告功能，用于验证生成的试卷是否符合组卷规则：

1. **格式一致性**：校核报告与组卷规则模板格式完全一致
2. **数据完整性**：包含所有层级代码组合（4×4×6=96行）
3. **零值填充**：统计数据为0的地方显示为0，不省略
4. **精确统计**：统计数据精确到小数点后1位

### 组卷规则配置示例

```json
{
  "A": {
    "ratio": 25.0,
    "children": {
      "A": {
        "ratio": 30.0,
        "children": {
          "A": 20.0,
          "B": 25.0,
          "C": 30.0,
          "D": 15.0,
          "E": 10.0,
          "F": 0.0
        }
      }
    }
  }
}
```

### 使用流程

1. **下载模板**：从系统下载组卷规则Excel模板
2. **填写规则**：按照模板格式填写题型分布和知识点分布
3. **上传组卷**：上传Excel文件，系统自动解析并生成试卷
4. **校核验证**：下载校核报告，验证组卷结果
5. **调整优化**：根据校核结果调整组卷规则

### 数据分析
- **使用统计**：统计试题使用频率、正确率等数据
- **难度分析**：分析试题实际难度与设定难度的偏差
- **知识点覆盖**：分析知识点覆盖情况
- **试卷质量**：评估试卷的区分度、信度、效度等指标

## 📊 数据规范

### 试题结构
```json
{
  "id": 1,
  "type": "single_choice",
  "stem": "以下哪个选项是Python的基本数据类型？",
  "options": [
    {"key": "A", "content": "Integer"},
    {"key": "B", "content": "Float"},
    {"key": "C", "content": "Dictionary"},
    {"key": "D", "content": "以上都是"}
  ],
  "answer": "D",
  "analysis": "Python的基本数据类型包括整数(Integer)、浮点数(Float)、字典(Dictionary)等。",
  "difficulty": 2,
  "score": 5,
  "category": "Python基础",
  "tags": ["数据类型", "基础概念"],
  "created_at": "2024-01-01 10:00:00",
  "updated_at": "2024-01-02 11:00:00",
  "created_by": "admin"
}
```

### 题型支持
- **单选题**：一个问题，多个选项，只有一个正确答案
- **多选题**：一个问题，多个选项，有多个正确答案
- **判断题**：一个陈述，判断对错
- **填空题**：句子中留空，填入正确答案
- **简答题**：开放性问题，需要文字回答
- **编程题**：编写代码解决问题，支持多种编程语言

### 媒体支持
- **文本**：支持富文本编辑，包括字体、颜色、大小等样式
- **图片**：支持JPG、PNG等格式图片
- **公式**：支持LaTeX数学公式
- **代码**：支持代码高亮显示
- **音频**：支持MP3等音频文件（用于听力题）

### 元数据
- **基本信息**：创建时间、修改时间、创建者等
- **使用统计**：使用次数、正确率、区分度等
- **关联信息**：关联的知识点、章节、课程等

## 🔧 技术架构

### 前端技术
- **基础框架**：HTML5 + CSS3 + JavaScript
- **UI框架**：Bootstrap 5
- **交互增强**：jQuery + AJAX
- **富文本编辑**：CKEditor
- **数学公式**：MathJax
- **图表展示**：ECharts

### 后端技术
- **Web框架**：Python Flask
- **ORM框架**：SQLAlchemy
- **模板引擎**：Jinja2
- **文件处理**：Werkzeug
- **Excel处理**：openpyxl
- **PDF生成**：ReportLab

### 数据存储
- **关系型数据库**：MySQL 8.0
- **文件存储**：本地文件系统（图片、附件等）
- **缓存系统**：Redis（可选）

### 系统架构
- **MVC模式**：模型-视图-控制器分离
- **RESTful API**：提供标准化的API接口
- **模块化设计**：功能模块化，便于扩展
- **权限控制**：基于角色的访问控制

## 🏗️ 文件结构

```
question_bank_web/
├── app.py                 # 应用入口
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── models/                # 数据模型
│   ├── __init__.py
│   ├── question.py        # 试题模型
│   ├── paper.py           # 试卷模型
│   └── category.py        # 分类模型
├── controllers/           # 控制器
│   ├── __init__.py
│   ├── question_controller.py
│   ├── paper_controller.py
│   └── category_controller.py
├── services/              # 业务逻辑
│   ├── __init__.py
│   ├── question_service.py
│   ├── paper_service.py
│   └── auto_paper_service.py
├── static/                # 静态资源
│   ├── css/
│   ├── js/
│   ├── images/
│   └── uploads/           # 上传文件
├── templates/             # 模板文件
│   ├── base.html
│   ├── questions/
│   ├── papers/
│   └── categories/
├── utils/                 # 工具类
│   ├── __init__.py
│   ├── excel_handler.py   # Excel处理
│   ├── validators.py      # 数据验证
│   ├── file_handler.py    # 文件处理
│   └── pdf_generator.py   # PDF生成
└── tests/                 # 单元测试
    ├── __init__.py
    ├── test_models.py
    └── test_services.py
```

## 🚀 快速开始

### 环境要求
- **Python**：3.8+
- **数据库**：MySQL 8.0+
- **依赖库**：Flask, SQLAlchemy, openpyxl, ReportLab等

### 安装依赖

```bash
# 创建专用虚拟环境（推荐）
python -m venv venv_qb

# 激活虚拟环境
.\venv_qb\Scripts\activate  # Windows
source venv_qb/bin/activate   # Linux/Mac

# 安装依赖包
pip install -r requirements.txt
```

### 依赖冲突解决方案

#### numpy导入问题

在运行题库管理模块时，可能会遇到以下错误：

```
ImportError: Error importing numpy: you should not try to import numpy from its source directory; please exit the numpy source tree, and relaunch your python interpreter from there.
```

**问题原因**：
- 当前工作目录或Python路径中存在与`numpy`包同名的目录或文件
- 在模块化开发环境中，项目结构可能导致Python解释器错误地尝试从当前目录导入`numpy`
- 全局虚拟环境中的`numpy`包可能与模块的导入机制产生冲突

**解决方案**：
1. 使用专用虚拟环境：为题库管理模块创建独立的虚拟环境，避免与全局环境冲突
2. 在正确的目录中运行：确保在题库管理模块目录下运行应用
3. 确保激活正确的虚拟环境：运行前验证当前激活的是题库管理模块的专用虚拟环境

**对Windows可执行文件打包的影响**：
- 使用PyInstaller打包时，需要在专用虚拟环境中进行
- 打包配置中需要特别处理`numpy`相关的依赖路径
- 可能需要在`.spec`文件中添加特定的排除项，以避免路径冲突

### 配置数据库

1. 创建MySQL数据库
```sql
CREATE DATABASE question_bank CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改`config.py`中的数据库连接信息
```python
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://username:password@localhost/question_bank'
```

### 启动方式

#### 方式一：通过主控台启动

在主控台界面中点击"题库管理"模块的"启动"按钮。

#### 方式二：直接启动

```bash
# 进入题库管理目录
cd question_bank_web

# 初始化数据库
python -c "from app import db; db.create_all()"

# 启动Flask应用
python app.py
```

### 访问系统

启动成功后，在浏览器中访问：http://localhost:5000

## 🔄 使用流程

### 1. 试题管理
1. 登录题库管理系统
2. 进入"试题管理"页面
3. 添加、编辑或删除试题
4. 使用批量导入功能批量添加试题

### 2. 组卷管理
1. 进入"组卷管理"页面
2. 选择手动组卷或智能组卷
3. 设置试卷结构和参数
4. 设置试卷总分和及格分数/及格百分比
5. 生成试卷并预览
6. 导出或保存试卷

## 🔄 与其他模块的集成

### 数据交互
- **考试管理模块**：提供试卷数据，接收考试结果数据
- **用户管理模块**：获取用户权限信息，控制访问权限
- **阅卷中心**：提供试题答案和评分标准
- **成绩统计**：接收试题和试卷的统计数据

### API接口
- **试题查询**：`GET /api/questions`
- **试题详情**：`GET /api/questions/{id}`
- **试卷查询**：`GET /api/papers`
- **试卷详情**：`GET /api/papers/{id}`
- **智能组卷**：`POST /api/papers/auto-generate`

## 🐛 问题排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 确认数据库连接信息是否正确
   - 验证数据库用户权限

2. **图片上传失败**
   - 检查上传目录权限
   - 确认文件大小是否超限
   - 验证文件格式是否支持

3. **Excel导入失败**
   - 检查Excel文件格式
   - 确认数据格式是否符合要求
   - 查看详细错误日志

### 故障排除

1. **查看日志**：检查应用日志文件
2. **数据库检查**：直接查询数据库验证数据
3. **重启服务**：重新启动应用程序
4. **检查端口**：确保端口5000未被占用

## 🔄 版本历史

### v1.0.0 (2024-01-10)
- ✅ 基础试题管理功能
- ✅ 手动组卷功能
- ✅ Excel导入导出
- ✅ 试卷预览和导出

### v1.1.0 (计划中)
- 🔄 智能组卷算法优化
- 🔄 数据分析功能增强
- 🔄 UI/UX改进
- 🔄 API完善

## 📞 技术支持

如有使用问题或建议，请联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567
- 工作时间：周一至周五 9:00-18:00

## 📦 Windows可执行文件打包

### 🎯 打包概述

本项目已成功打包为Windows可执行文件，支持在任何Windows系统上独立运行，无需安装Python环境或依赖包。

### 🚀 快速使用

#### 方法一：直接运行
1. 下载并解压打包文件
2. 双击 `PHRL题库管理系统.exe` 启动程序
3. 程序会自动打开浏览器访问 http://localhost:5000

#### 方法二：自动安装
1. 运行 `install.bat` 自动安装脚本
2. 按提示选择安装目录
3. 安装完成后可通过桌面快捷方式启动

### 🔧 打包详细过程

#### 第一步：环境准备
```bash
# 安装PyInstaller
python -m pip install pyinstaller
```

#### 第二步：配置spec文件
创建并配置 `run.spec` 文件，包含以下关键配置：

```python
# -*- mode: python ; coding: utf-8 -*-
a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('standard_field_config.json', '.'),
        ('error_reports', 'error_reports'),
    ],
    hiddenimports=[
        'sqlalchemy.dialects.sqlite',
        'flask',
        'openpyxl',
        'docx',
        'pandas',
        'numpy'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='PHRL题库管理系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='PHRL题库管理系统'
)
```

#### 第三步：代码优化
修改 `run.py` 文件以适应打包环境：

1. **添加打包环境检测**：
```python
def is_packaged():
    """检测是否在打包环境中运行"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')
```

2. **跳过依赖检查**：
```python
if is_packaged():
    print("检测到打包环境，跳过依赖检查...")
else:
    check_pip()
    install_dependencies()
```

3. **路径处理优化**：
```python
if is_packaged():
    # 在打包环境中，切换到_internal目录
    internal_dir = os.path.join(os.path.dirname(sys.executable), '_internal')
    if os.path.exists(internal_dir):
        os.chdir(internal_dir)
```

#### 第四步：执行打包
```bash
# 使用spec文件打包
python -m PyInstaller run.spec --noconfirm
```

#### 第五步：创建安装工具
生成 `install.bat` 自动安装脚本：
```batch
@echo off
chcp 65001 >nul
echo ========================================
echo    PHRL题库管理系统 - 自动安装工具
echo ========================================
echo.

set "DEFAULT_DIR=D:\PHRL_QuestionBank"
set /p "INSTALL_DIR=请输入安装目录 (默认: %DEFAULT_DIR%): "
if "%INSTALL_DIR%"=="" set "INSTALL_DIR=%DEFAULT_DIR%"

echo.
echo 正在安装到: %INSTALL_DIR%
echo.

if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
)

xcopy /E /I /Y "dist\PHRL题库管理系统" "%INSTALL_DIR%"

echo.
echo 正在创建桌面快捷方式...
set "DESKTOP=%USERPROFILE%\Desktop"
set "SHORTCUT=%DESKTOP%\PHRL题库管理系统.lnk"

powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%SHORTCUT%'); $Shortcut.TargetPath = '%INSTALL_DIR%\PHRL题库管理系统.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()"

echo.
echo ========================================
echo           安装完成！
echo ========================================
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
echo.
echo 使用方法：
echo 1. 双击桌面快捷方式启动
echo 2. 或直接运行: %INSTALL_DIR%\PHRL题库管理系统.exe
echo 3. 程序启动后访问: http://localhost:5000
echo.
pause
```

### ✅ 打包特性

- **🔒 完全离线**：包含所有依赖，无需网络连接
- **🎯 一键启动**：双击即可运行，自动打开浏览器
- **📁 智能路径**：自动处理打包环境的文件路径
- **🖥️ 控制台输出**：显示详细运行状态和错误信息
- **⚡ 快速部署**：支持自动安装脚本和桌面快捷方式
- **🔧 环境适配**：自动检测打包环境，跳过不必要的检查

### 📋 使用说明

#### 系统要求
- Windows 7/8/10/11 (64位)
- 至少 500MB 可用磁盘空间
- 5000端口未被占用

#### 启动流程
1. **环境检测**：自动检测Python版本和打包环境
2. **路径切换**：切换到正确的工作目录
3. **目录创建**：创建必要的数据目录
4. **服务启动**：启动Flask Web服务
5. **浏览器打开**：自动打开默认浏览器访问系统

#### 故障排除
- **端口占用**：如果5000端口被占用，请关闭占用程序或修改配置
- **权限问题**：首次运行可能需要管理员权限
- **防火墙**：确保防火墙允许程序访问网络
- **杀毒软件**：部分杀毒软件可能误报，请添加信任

### 📁 打包文件结构
```
dist/PHRL题库管理系统/
├── PHRL题库管理系统.exe          # 主程序
├── _internal/                    # 内部文件目录
│   ├── templates/               # 模板文件
│   ├── standard_field_config.json # 配置文件
│   ├── error_reports/           # 错误报告目录
│   └── [其他依赖文件]
└── [PyInstaller生成的其他文件]
```

### 🔄 更新和维护

#### 重新打包
```bash
# 清理旧文件
rmdir /s dist
rmdir /s build

# 重新打包
python -m PyInstaller run.spec --noconfirm
```

#### 版本管理
- 修改 `run.spec` 中的版本信息
- 更新 `使用说明.txt` 中的版本说明
- 重新生成安装包

---

---

**题库管理模块** - 让试题管理和组卷更智能、更高效！
