<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考生控制台 - 局域网在线考试系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 500;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-name {
            font-size: 16px;
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        .welcome-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .welcome-section h2 {
            color: #333;
            margin-bottom: 10px;
            font-size: 20px;
        }
        
        .welcome-section p {
            color: #666;
            line-height: 1.6;
        }
        
        .exams-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .section-header h3 {
            color: #333;
            font-size: 18px;
        }
        
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
        }
        
        .exams-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        
        .exam-card {
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .exam-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .exam-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 10px;
        }
        
        .exam-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .exam-info div {
            margin-bottom: 5px;
        }
        
        .exam-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 15px;
        }
        
        .status-available {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-in-progress {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-completed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-expired {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .exam-actions {
            display: flex;
            gap: 10px;
        }
        
        .start-exam-btn {
            flex: 1;
            background: #28a745;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .start-exam-btn:hover {
            background: #218838;
        }
        
        .start-exam-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .continue-exam-btn {
            flex: 1;
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .continue-exam-btn:hover {
            background: #e0a800;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .no-exams {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-exams-icon {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>考生控制台</h1>
            <div class="user-info">
                <span class="user-name">欢迎，{{ student_name }}</span>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="welcome-section">
            <h2>欢迎使用局域网在线考试系统</h2>
            <p>请在下方查看您可以参加的考试。点击"开始考试"按钮进入考试页面，请确保网络连接稳定，考试过程中请勿关闭浏览器或刷新页面。</p>
        </div>
        
        <div class="exams-section">
            <div class="section-header">
                <h3>可参加的考试</h3>
                <button class="refresh-btn" onclick="loadExams()">刷新</button>
            </div>
            
            <div id="alert" class="alert"></div>
            
            <div id="loading" class="loading">
                <div class="loading-spinner"></div>
                <p>正在加载考试列表...</p>
            </div>
            
            <div id="examsContainer" class="exams-grid" style="display: none;"></div>
            
            <div id="noExams" class="no-exams" style="display: none;">
                <div class="no-exams-icon">📝</div>
                <h4>暂无可参加的考试</h4>
                <p>请联系管理员或稍后再试</p>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载时获取考试列表
        window.addEventListener('load', function() {
            loadExams();
        });
        
        // 加载考试列表
        async function loadExams() {
            const loading = document.getElementById('loading');
            const examsContainer = document.getElementById('examsContainer');
            const noExams = document.getElementById('noExams');
            const alert = document.getElementById('alert');
            
            // 显示加载状态
            loading.style.display = 'block';
            examsContainer.style.display = 'none';
            noExams.style.display = 'none';
            hideAlert();
            
            try {
                const response = await fetch('/api/v1/exams/available');
                const data = await response.json();
                
                if (data.code === 200) {
                    const exams = data.data;
                    
                    if (exams && exams.length > 0) {
                        renderExams(exams);
                        examsContainer.style.display = 'grid';
                    } else {
                        noExams.style.display = 'block';
                    }
                } else {
                    showAlert(data.msg || '获取考试列表失败', 'error');
                    noExams.style.display = 'block';
                }
                
            } catch (error) {
                console.error('加载考试列表错误:', error);
                showAlert('网络连接失败，请检查网络设置', 'error');
                noExams.style.display = 'block';
            } finally {
                loading.style.display = 'none';
            }
        }
        
        // 渲染考试列表
        function renderExams(exams) {
            const container = document.getElementById('examsContainer');
            container.innerHTML = '';
            
            exams.forEach(exam => {
                const examCard = createExamCard(exam);
                container.appendChild(examCard);
            });
        }
        
        // 创建考试卡片
        function createExamCard(exam) {
            const card = document.createElement('div');
            card.className = 'exam-card';
            
            const statusClass = getStatusClass(exam.status);
            const statusText = getStatusText(exam.status);
            const isAvailable = exam.status === 'available' || exam.status === 'in_progress';
            
            card.innerHTML = `
                <div class="exam-title">${exam.title || '未命名考试'}</div>
                <div class="exam-info">
                    <div><strong>科目:</strong> ${exam.subject || '未指定'}</div>
                    <div><strong>时长:</strong> ${exam.duration || 0} 分钟</div>
                    <div><strong>开始时间:</strong> ${formatDateTime(exam.start_time)}</div>
                    <div><strong>结束时间:</strong> ${formatDateTime(exam.end_time)}</div>
                </div>
                <div class="exam-status ${statusClass}">${statusText}</div>
                <div class="exam-actions">
                    ${exam.status === 'in_progress' ? 
                        `<button class="continue-exam-btn" onclick="continueExam(${exam.id})">继续考试</button>` :
                        `<button class="start-exam-btn" ${!isAvailable ? 'disabled' : ''} onclick="startExam(${exam.id})">
                            ${isAvailable ? '开始考试' : '不可参加'}
                        </button>`
                    }
                </div>
            `;
            
            return card;
        }
        
        // 获取状态样式类
        function getStatusClass(status) {
            const statusMap = {
                'available': 'status-available',
                'in_progress': 'status-in-progress',
                'completed': 'status-completed',
                'expired': 'status-expired'
            };
            return statusMap[status] || 'status-expired';
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'available': '可参加',
                'in_progress': '进行中',
                'completed': '已完成',
                'expired': '已过期'
            };
            return statusMap[status] || '未知状态';
        }
        
        // 格式化日期时间
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未设置';
            
            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                return '格式错误';
            }
        }
        
        // 开始考试
        async function startExam(examId) {
            if (!confirm('确定要开始这场考试吗？开始后将无法重新开始。')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/exams/${examId}/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showAlert('考试开始，正在跳转到考试页面...', 'success');
                    setTimeout(() => {
                        window.location.href = `/exam/${examId}`;
                    }, 1000);
                } else {
                    showAlert(data.msg || '开始考试失败', 'error');
                }
                
            } catch (error) {
                console.error('开始考试错误:', error);
                showAlert('网络连接失败，请稍后重试', 'error');
            }
        }
        
        // 继续考试
        function continueExam(examId) {
            window.location.href = `/exam/${examId}`;
        }
        
        // 退出登录
        async function logout() {
            if (!confirm('确定要退出登录吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/api/v1/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    window.location.href = data.data.redirect_url || '/';
                } else {
                    showAlert('退出登录失败', 'error');
                }
                
            } catch (error) {
                console.error('退出登录错误:', error);
                // 即使请求失败也跳转到登录页
                window.location.href = '/';
            }
        }
        
        // 显示提示信息
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
            
            // 3秒后自动隐藏成功提示
            if (type === 'success') {
                setTimeout(hideAlert, 3000);
            }
        }
        
        // 隐藏提示信息
        function hideAlert() {
            const alert = document.getElementById('alert');
            alert.style.display = 'none';
        }
    </script>
</body>
</html>