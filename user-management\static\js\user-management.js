// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let accessToken = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    console.log('页面加载完成，开始初始化...');
    
    // 从localStorage获取token
    accessToken = localStorage.getItem('access_token');
    console.log('从localStorage获取token:', accessToken ? '已获取' : '未获取');
    
    // 如果没有token，尝试管理员登录
     if (!accessToken) {
         console.log('没有token，尝试管理员登录...');
         const loginSuccess = await loginAsAdmin();
         if (loginSuccess) {
             console.log('自动登录成功，加载用户列表');
             loadUserList();
             loadStatistics();
         } else {
             console.log('自动登录失败，显示提示信息');
             showAlert('无法连接到用户管理服务，请检查服务状态', 'warning');
         }
     } else {
         console.log('已有token，直接加载用户列表');
         loadUserList();
         loadStatistics();
     }
    
    // 绑定表单提交事件
    document.getElementById('addUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createUser();
    });
    
    // 绑定搜索框回车事件
    document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });
});

// 管理员登录（用于获取token）
async function loginAsAdmin() {
    try {
        console.log('正在尝试登录获取新token...');
        const response = await fetch('/api/v1/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        const data = await response.json();
        console.log('登录响应:', data);
        
        if (data.success) {
            accessToken = data.data.access_token;
            localStorage.setItem('access_token', accessToken);
            console.log('新token已获取并保存');
            return true;
        } else {
            console.error('登录失败:', data.message);
            return false;
        }
    } catch (error) {
        console.error('登录错误:', error);
        return false;
    }
}

// 显示用户列表页面
function showUserList() {
    document.getElementById('userListPage').style.display = 'block';
    document.getElementById('addUserPage').style.display = 'none';
    document.getElementById('statisticsPage').style.display = 'none';
    
    // 更新导航状态
    updateNavigation('userList');
    loadUserList();
}

// 显示添加用户页面
function showAddUser() {
    document.getElementById('userListPage').style.display = 'none';
    document.getElementById('addUserPage').style.display = 'block';
    document.getElementById('statisticsPage').style.display = 'none';
    
    // 更新导航状态
    updateNavigation('addUser');
    resetForm();
}

// 显示统计信息页面
function showStatistics() {
    document.getElementById('userListPage').style.display = 'none';
    document.getElementById('addUserPage').style.display = 'none';
    document.getElementById('statisticsPage').style.display = 'block';
    
    // 更新导航状态
    updateNavigation('statistics');
    loadStatistics();
}

// 更新导航状态
function updateNavigation(activeItem) {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    
    switch(activeItem) {
        case 'userList':
            navLinks[0].classList.add('active');
            break;
        case 'addUser':
            navLinks[1].classList.add('active');
            break;
        case 'statistics':
            navLinks[2].classList.add('active');
            break;
    }
}

// 加载用户列表
async function loadUserList(page = 1) {
    if (!accessToken) {
        showAlert('请先登录', 'warning');
        return;
    }
    
    try {
        console.log(`加载用户列表，页码: ${page}`);
        
        const keyword = document.getElementById('searchKeyword').value;
        const role = document.getElementById('roleFilter').value;
        const status = document.getElementById('statusFilter').value;
        
        // 限制page_size最大值为100，避免API返回400错误
        const limitedPageSize = Math.min(pageSize, 100);
        let url = `/api/v1/users?page=${page}&page_size=${limitedPageSize}`;
        if (keyword) url += `&keyword=${encodeURIComponent(keyword)}`;
        if (role) url += `&role=${role}`;
        if (status) url += `&status=${status}`;
        
        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        const data = await response.json();
        
        if (response.status === 401) {
            console.log('Token无效，尝试重新登录...');
            const loginSuccess = await loginAsAdmin();
            if (loginSuccess) {
                console.log('重新登录成功，重新加载用户列表');
                return loadUserList(page); // 递归调用
            } else {
                showAlert('认证失败，请刷新页面重试', 'danger');
                return;
            }
        }
        
        if (data.success) {
            renderUserTable(data.data.items);
            renderPagination(data.data.pagination);
            currentPage = page;
        } else {
            showAlert('加载用户列表失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('加载用户列表错误:', error);
        showAlert('加载用户列表失败，请检查网络连接', 'danger');
    }
}

// 渲染用户表格
function renderUserTable(users) {
    const tbody = document.getElementById('userTableBody');
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="user-checkbox" name="userSelect" value="${user.id}" onchange="updateBatchDeleteButton()">
            </td>
            <td>
                <div class="user-avatar">
                    ${user.real_name ? user.real_name.charAt(0).toUpperCase() : user.username.charAt(0).toUpperCase()}
                </div>
            </td>
            <td>${user.username}</td>
            <td>${user.real_name || '-'}</td>
            <td>${user.email}</td>
            <td>
                <span class="badge bg-${getRoleBadgeColor(user.role)}">
                    ${getRoleDisplayName(user.role)}
                </span>
            </td>
            <td>
                <span class="badge bg-${getStatusBadgeColor(user.status)} status-badge">
                    ${getStatusDisplayName(user.status)}
                </span>
            </td>
            <td>${user.department || '-'}</td>
            <td>${formatDateTime(user.created_time)}</td>
            <td class="table-actions">
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editUser(${user.id})" title="编辑">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-warning me-1" onclick="resetPassword(${user.id})" title="重置密码">
                    <i class="bi bi-key"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})" title="删除">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
    
    // 重置全选状态
    document.getElementById('selectAllUsers').checked = false;
    updateBatchDeleteButton();
}

// 渲染分页
function renderPagination(pagination) {
    const paginationEl = document.getElementById('pagination');
    paginationEl.innerHTML = '';
    
    totalPages = pagination.pages;
    const currentPage = pagination.page;
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserList(${currentPage - 1})">上一页</a>`;
    paginationEl.appendChild(prevLi);
    
    // 页码
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadUserList(${i})">${i}</a>`;
        paginationEl.appendChild(li);
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserList(${currentPage + 1})">下一页</a>`;
    paginationEl.appendChild(nextLi);
}

// 搜索用户
function searchUsers() {
    loadUserList(1);
}

// 创建用户
async function createUser() {
    if (!accessToken) {
        showAlert('请先登录', 'warning');
        return;
    }
    
    const formData = {
        username: document.getElementById('username').value,
        email: document.getElementById('email').value,
        real_name: document.getElementById('realName').value,
        phone: document.getElementById('phone').value,
        password: document.getElementById('password').value,
        role: document.getElementById('role').value,
        department: document.getElementById('department').value,
        position: document.getElementById('position').value
    };
    
    try {
        const response = await fetch('/api/v1/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('用户创建成功', 'success');
            resetForm();
            showUserList();
        } else {
            showAlert('创建用户失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('创建用户错误:', error);
        showAlert('创建用户失败，请检查网络连接', 'danger');
    }
}

// 编辑用户
async function editUser(userId) {
    if (!accessToken) {
        showAlert('请先登录', 'warning');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/users/${userId}`, {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            const user = data.data;
            
            // 填充编辑表单
            document.getElementById('editUserId').value = user.id;
            document.getElementById('editEmail').value = user.email;
            document.getElementById('editRealName').value = user.real_name || '';
            document.getElementById('editPhone').value = user.phone || '';
            document.getElementById('editRole').value = user.role;
            document.getElementById('editStatus').value = user.status;
            document.getElementById('editDepartment').value = user.department || '';
            document.getElementById('editPosition').value = user.position || '';
            
            // 填充申报相关字段
            document.getElementById('editDeclaredOccupation').value = user.declared_occupation || '';
            document.getElementById('editDeclaredLevel').value = user.declared_level || '';
            document.getElementById('editExamType').value = user.exam_type || '';
            document.getElementById('editAssessmentSubject').value = user.assessment_subject || '';
            document.getElementById('editQuestionBankName').value = user.question_bank_name || '';
            document.getElementById('editTestPaperId').value = user.test_paper_id || '';
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        } else {
            showAlert('获取用户信息失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('获取用户信息错误:', error);
        showAlert('获取用户信息失败，请检查网络连接', 'danger');
    }
}

// 更新用户
async function updateUser() {
    if (!accessToken) {
        showAlert('请先登录', 'warning');
        return;
    }
    
    const userId = document.getElementById('editUserId').value;
    const formData = {
        email: document.getElementById('editEmail').value,
        real_name: document.getElementById('editRealName').value,
        phone: document.getElementById('editPhone').value,
        role: document.getElementById('editRole').value,
        status: document.getElementById('editStatus').value,
        department: document.getElementById('editDepartment').value,
        position: document.getElementById('editPosition').value,
        declared_occupation: document.getElementById('editDeclaredOccupation').value,
        declared_level: document.getElementById('editDeclaredLevel').value,
        exam_type: document.getElementById('editExamType').value,
        assessment_subject: document.getElementById('editAssessmentSubject').value,
        question_bank_name: document.getElementById('editQuestionBankName').value,
        test_paper_id: document.getElementById('editTestPaperId').value
    };
    
    try {
        const response = await fetch(`/api/v1/users/${userId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify(formData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('用户信息更新成功', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
            modal.hide();
            
            // 刷新用户列表
            loadUserList(currentPage);
        } else {
            showAlert('更新用户信息失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('更新用户信息错误:', error);
        showAlert('更新用户信息失败，请检查网络连接', 'danger');
    }
}

// 重置密码
async function resetPassword(userId) {
    if (!accessToken) {
        showAlert('请先登录', 'warning');
        return;
    }
    
    const newPassword = prompt('请输入新密码（6-128位）:');
    if (!newPassword) return;
    
    if (newPassword.length < 6 || newPassword.length > 128) {
        showAlert('密码长度必须在6-128位之间', 'warning');
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/users/${userId}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                new_password: newPassword
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('密码重置成功', 'success');
        } else {
            showAlert('重置密码失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('重置密码错误:', error);
        showAlert('重置密码失败，请检查网络连接', 'danger');
    }
}

// 删除用户
async function deleteUser(userId) {
    if (!accessToken) {
        showAlert('请先登录', 'warning');
        return;
    }
    
    if (!confirm('确定要删除这个用户吗？此操作不可恢复。')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/v1/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('用户删除成功', 'success');
            loadUserList(currentPage);
        } else {
            showAlert('删除用户失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('删除用户错误:', error);
        showAlert('删除用户失败，请检查网络连接', 'danger');
    }
}

// 加载统计信息
async function loadStatistics() {
    if (!accessToken) return;
    
    try {
        const response = await fetch('/api/v1/users?page_size=100', {
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        const data = await response.json();
        
        if (response.status === 401) {
            console.log('Token无效，尝试重新登录...');
            const loginSuccess = await loginAsAdmin();
            if (loginSuccess) {
                console.log('重新登录成功，重新加载统计信息');
                return loadStatistics(); // 递归调用
            } else {
                console.error('重新登录失败');
                return;
            }
        }
        
        if (data.success) {
            const users = data.data.items;
            
            const totalUsers = users.length;
            const activeUsers = users.filter(u => u.status === 'active').length;
            const studentUsers = users.filter(u => u.role === 'student').length;
            const teacherUsers = users.filter(u => u.role === 'teacher').length;
            
            document.getElementById('totalUsers').textContent = totalUsers;
            document.getElementById('activeUsers').textContent = activeUsers;
            document.getElementById('studentUsers').textContent = studentUsers;
            document.getElementById('teacherUsers').textContent = teacherUsers;
        }
    } catch (error) {
        console.error('加载统计信息错误:', error);
    }
}

// 重置表单
function resetForm() {
    document.getElementById('addUserForm').reset();
}

// 显示提示信息
function showAlert(message, type = 'info') {
    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// 工具函数
function getRoleBadgeColor(role) {
    switch(role) {
        case 'admin': return 'danger';
        case 'teacher': return 'warning';
        case 'student': return 'primary';
        default: return 'secondary';
    }
}

function getRoleDisplayName(role) {
    switch(role) {
        case 'admin': return '管理员';
        case 'expert': return '专家';
        case 'student': return '考生';
        case 'grader': return '考评员';
        case 'internal_supervisor': return '内部督导员';
        default: return role;
    }
}

function getStatusBadgeColor(status) {
    switch(status) {
        case 'active': return 'success';
        case 'inactive': return 'warning';
        case 'deleted': return 'danger';
        default: return 'secondary';
    }
}

function getStatusDisplayName(status) {
    switch(status) {
        case 'pending': return '待审核';
        case 'approved': return '审核通过';
        case 'rejected': return '审核未通过';
        default: return status;
    }
}

function formatDateTime(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 批量操作相关函数

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    
    userCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });
    
    updateBatchDeleteButton();
}

// 更新批量删除按钮状态
function updateBatchDeleteButton() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    const batchEditBtn = document.getElementById('batchEditBtn');
    
    if (selectedCheckboxes.length > 0) {
        batchDeleteBtn.disabled = false;
        batchDeleteBtn.innerHTML = `<i class="bi bi-trash"></i> 批量删除 (${selectedCheckboxes.length})`;
        if (batchEditBtn) {
            batchEditBtn.disabled = false;
            batchEditBtn.innerHTML = `<i class="bi bi-pencil"></i> 批量修改 (${selectedCheckboxes.length})`;
        }
    } else {
        batchDeleteBtn.disabled = true;
        batchDeleteBtn.innerHTML = '<i class="bi bi-trash"></i> 批量删除';
        if (batchEditBtn) {
            batchEditBtn.disabled = true;
            batchEditBtn.innerHTML = '<i class="bi bi-pencil"></i> 批量修改';
        }
    }
    
    // 更新全选状态
    const allCheckboxes = document.querySelectorAll('.user-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllUsers');
    
    if (allCheckboxes.length > 0) {
        selectAllCheckbox.checked = selectedCheckboxes.length === allCheckboxes.length;
        selectAllCheckbox.indeterminate = selectedCheckboxes.length > 0 && selectedCheckboxes.length < allCheckboxes.length;
    }
}

// 显示批量导入模态框
function showBatchImportModal() {
    const modal = new bootstrap.Modal(document.getElementById('batchImportModal'));
    
    // 重置表单
    document.getElementById('importFile').value = '';
    document.getElementById('importProgress').style.display = 'none';
    document.getElementById('importResult').style.display = 'none';
    document.getElementById('importBtn').disabled = false;
    
    modal.show();
}

// 下载模板文件
async function downloadTemplate() {
    if (!accessToken) {
        showAlert('请先登录', 'warning');
        return;
    }
    
    console.log('开始下载模板，token:', accessToken ? accessToken.substring(0, 20) + '...' : 'null');
    
    try {
        const response = await fetch('/api/v1/users/download-template', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });
        
        console.log('下载响应状态:', response.status);
        console.log('下载响应头:', response.headers);
        
        if (response.ok) {
            const blob = await response.blob();
            console.log('Blob大小:', blob.size, '类型:', blob.type);
            
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = '用户导入模板.xlsx';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            console.log('下载完成');
            showAlert('模板下载成功', 'success');
        } else {
            console.error('下载失败，状态码:', response.status);
            try {
                const data = await response.json();
                console.error('错误响应:', data);
                showAlert('下载模板失败：' + (data.message || '未知错误'), 'danger');
            } catch (parseError) {
                console.error('解析错误响应失败:', parseError);
                const text = await response.text();
                console.error('响应文本:', text);
                showAlert('下载模板失败：服务器响应错误', 'danger');
            }
        }
    } catch (error) {
        console.error('下载模板错误:', error);
        showAlert('下载模板失败：' + error.message, 'danger');
    }
}

// 开始批量导入
async function startBatchImport() {
    console.log('开始批量导入用户');
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];
    
    if (!file) {
        console.log('未选择文件');
        showAlert('请选择要导入的文件', 'warning');
        return;
    }
    
    console.log('选择的文件:', file.name, '大小:', file.size, '类型:', file.type);
    
    // 检查文件类型
    const allowedTypes = ['.xlsx', '.xls', '.csv'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    console.log('文件扩展名:', fileExtension);
    
    if (!allowedTypes.includes(fileExtension)) {
        console.log('不支持的文件格式:', fileExtension);
        showAlert('不支持的文件格式，请选择Excel或CSV文件', 'danger');
        return;
    }
    
    // 确保有有效的token
    if (!accessToken) {
        console.log('没有token，尝试登录...');
        const loginSuccess = await loginAsAdmin();
        if (!loginSuccess) {
            showAlert('登录失败，无法进行批量导入', 'danger');
            return;
        }
    }
    
    // 显示进度条
    document.getElementById('importProgress').style.display = 'block';
    document.getElementById('importResult').style.display = 'none';
    document.getElementById('importBtn').disabled = true;
    
    const progressBar = document.querySelector('#importProgress .progress-bar');
    const statusDiv = document.getElementById('importStatus');
    
    try {
        // 创建FormData
        const formData = new FormData();
        formData.append('file', file);
        
        console.log('FormData创建完成，准备发送请求');
        console.log('使用的token:', accessToken ? '已设置' : '未设置');
        
        // 更新进度
        progressBar.style.width = '30%';
        statusDiv.textContent = '正在上传文件...';
        
        const response = await fetch('/api/v1/users/batch-import', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${accessToken}`
            },
            body: formData
        });
        
        console.log('请求响应状态:', response.status, response.statusText);
        console.log('响应头:', Object.fromEntries(response.headers.entries()));
        
        progressBar.style.width = '70%';
        statusDiv.textContent = '正在处理数据...';
        
        // 如果是401错误，尝试重新登录
        if (response.status === 401) {
            console.log('Token已过期，尝试重新登录...');
            const loginSuccess = await loginAsAdmin();
            if (!loginSuccess) {
                throw new Error('重新登录失败，请刷新页面重试');
            }
            
            // 重新发送请求
            const retryResponse = await fetch('/api/v1/users/batch-import', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                },
                body: formData
            });
            
            if (!retryResponse.ok) {
                const errorText = await retryResponse.text();
                console.error('重试请求失败，响应内容:', errorText);
                throw new Error(`HTTP ${retryResponse.status}: ${retryResponse.statusText}`);
            }
            
            // 使用重试的响应
            const retryData = await retryResponse.json();
            console.log('重试请求成功，服务器响应数据:', retryData);
            
            // 处理重试响应
            progressBar.style.width = '100%';
            statusDiv.textContent = '导入完成';
            
            document.getElementById('importProgress').style.display = 'none';
            document.getElementById('importResult').style.display = 'block';
            
            if (retryData.success) {
                const result = retryData.data;
                document.getElementById('importSummary').innerHTML = `
                    <div class="alert alert-success">
                        <h6>导入完成</h6>
                        <p>总计: ${result.total} 条记录</p>
                        <p>成功: ${result.success_count} 条</p>
                        <p>失败: ${result.error_count} 条</p>
                    </div>
                `;
                
                if (result.errors && result.errors.length > 0) {
                    let errorHtml = '<div class="mt-3"><h6>错误详情:</h6><ul class="list-group">';
                    result.errors.forEach(error => {
                        errorHtml += `<li class="list-group-item list-group-item-danger">第${error.row}行: ${error.message}</li>`;
                    });
                    errorHtml += '</ul></div>';
                    document.getElementById('importSummary').innerHTML += errorHtml;
                }
                
                if (result.success_count > 0) {
                    loadUserList(); // 刷新用户列表
                }
                
                showAlert(`导入完成！成功 ${result.success_count} 条，失败 ${result.error_count} 条`, 
                         result.error_count > 0 ? 'warning' : 'success');
            } else {
                document.getElementById('importSummary').innerHTML = `
                    <div class="alert alert-danger">
                        <h6>导入失败</h6>
                        <p>${retryData.message}</p>
                    </div>
                `;
                showAlert('导入失败：' + retryData.message, 'danger');
            }
            
            return;
        }
        
        if (!response.ok) {
            const errorText = await response.text();
            console.error('请求失败，响应内容:', errorText);
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('服务器响应数据:', data);
        
        progressBar.style.width = '100%';
        statusDiv.textContent = '导入完成';
        
        // 显示结果
        setTimeout(() => {
            document.getElementById('importProgress').style.display = 'none';
            document.getElementById('importResult').style.display = 'block';
            
            const summaryDiv = document.getElementById('importSummary');
            
            if (data.success) {
                const result = data.data;
                summaryDiv.innerHTML = `
                    <div class="text-success">✓ 总计处理：${result.total} 条记录</div>
                    <div class="text-success">✓ 成功导入：${result.success_count} 条记录</div>
                    ${result.failed_count > 0 ? `<div class="text-danger">✗ 导入失败：${result.failed_count} 条记录</div>` : ''}
                    ${result.errors && result.errors.length > 0 ? `
                        <div class="mt-2">
                            <strong>错误详情：</strong>
                            <ul class="mb-0">
                                ${result.errors.map(error => `<li class="text-danger small">${error}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                `;
                
                if (result.success_count > 0) {
                    showAlert(`成功导入 ${result.success_count} 个用户`, 'success');
                    loadUserList(); // 刷新用户列表
                }
            } else {
                summaryDiv.innerHTML = `<div class="text-danger">导入失败：${data.message}</div>`;
                showAlert('批量导入失败：' + data.message, 'danger');
            }
            
            document.getElementById('importBtn').disabled = false;
        }, 1000);
        
    } catch (error) {
        console.error('批量导入错误:', error);
        console.error('错误详情:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
        
        document.getElementById('importProgress').style.display = 'none';
        document.getElementById('importBtn').disabled = false;
        
        let errorMessage = '批量导入失败';
        if (error.message.includes('HTTP')) {
            errorMessage += '：' + error.message;
        } else if (error.message.includes('Failed to fetch')) {
            errorMessage += '：网络连接失败，请检查服务器状态';
        } else {
            errorMessage += '：' + error.message;
        }
        
        showAlert(errorMessage, 'danger');
    }
}

// 批量删除用户
async function batchDeleteUsers() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        showAlert('请选择要删除的用户', 'warning');
        return;
    }
    
    const userIds = Array.from(selectedCheckboxes).map(checkbox => parseInt(checkbox.value));
    
    if (!confirm(`确定要删除选中的 ${userIds.length} 个用户吗？此操作不可撤销！`)) {
        return;
    }
    
    try {
        const response = await fetch('/api/v1/users/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                user_ids: userIds
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            const result = data.data;
            showAlert(`成功删除 ${result.success_count} 个用户${result.failed_count > 0 ? `，${result.failed_count} 个用户删除失败` : ''}`, 
                     result.failed_count > 0 ? 'warning' : 'success');
            
            // 刷新用户列表
            loadUserList();
        } else {
            showAlert('批量删除失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('批量删除错误:', error);
        showAlert('批量删除失败，请检查网络连接', 'danger');
    }
}

// 显示批量修改模态框
function showBatchEditModal() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        showAlert('请选择要修改的用户', 'warning');
        return;
    }
    
    // 更新选中用户数量
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
    
    // 重置表单
    document.getElementById('batchEditForm').reset();
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchEditModal'));
    modal.show();
}

// 执行批量修改
async function executeBatchEdit() {
    const selectedCheckboxes = document.querySelectorAll('.user-checkbox:checked');
    
    if (selectedCheckboxes.length === 0) {
        showAlert('请选择要修改的用户', 'warning');
        return;
    }
    
    const userIds = Array.from(selectedCheckboxes).map(checkbox => parseInt(checkbox.value));
    
    // 收集要更新的字段（只包含有值的字段）
    const updateData = {};
    
    const status = document.getElementById('batchStatus').value;
    const declaredOccupation = document.getElementById('batchDeclaredOccupation').value;
    const declaredLevel = document.getElementById('batchDeclaredLevel').value;
    const examType = document.getElementById('batchExamType').value;
    const assessmentSubject = document.getElementById('batchAssessmentSubject').value;
    const questionBankName = document.getElementById('batchQuestionBankName').value;
    const testPaperId = document.getElementById('batchTestPaperId').value;
    
    if (status) updateData.status = status;
    if (declaredOccupation.trim()) updateData.declared_occupation = declaredOccupation.trim();
    if (declaredLevel.trim()) updateData.declared_level = declaredLevel.trim();
    if (examType.trim()) updateData.exam_type = examType.trim();
    if (assessmentSubject.trim()) updateData.assessment_subject = assessmentSubject.trim();
    if (questionBankName.trim()) updateData.question_bank_name = questionBankName.trim();
    if (testPaperId.trim()) updateData.test_paper_id = testPaperId.trim();
    
    if (Object.keys(updateData).length === 0) {
        showAlert('请至少选择一个字段进行修改', 'warning');
        return;
    }
    
    if (!confirm(`确定要批量修改选中的 ${userIds.length} 个用户吗？`)) {
        return;
    }
    
    try {
        const response = await fetch('/api/v1/users/batch-update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                user_ids: userIds,
                update_data: updateData
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            const result = data.data;
            showAlert(`成功修改 ${result.success_count} 个用户${result.failed_count > 0 ? `，${result.failed_count} 个用户修改失败` : ''}`, 
                     result.failed_count > 0 ? 'warning' : 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('batchEditModal'));
            modal.hide();
            
            // 刷新用户列表
            loadUserList(currentPage);
        } else {
            showAlert('批量修改失败：' + data.message, 'danger');
        }
    } catch (error) {
        console.error('批量修改错误:', error);
        showAlert('批量修改失败，请检查网络连接', 'danger');
    }
}