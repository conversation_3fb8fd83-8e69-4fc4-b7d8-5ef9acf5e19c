# 项目需求记录文件

## 三级代码统计功能需求

### 核心要求
1. **只显示第三级代码的对比值**
   - 不需要显示一级代码（如A、B、C、D）
   - 不需要显示二级代码（如A-A、A-B等）
   - 只需要显示完整的三级代码（如A-A-A、A-B-B等）

### 数据源
1. **组卷规则模板文件**：`templates/组卷规则模板.xlsx`
   - Sheet页："知识点分布"
   - 包含三级代码和对应的比重需求
   - 格式：三级代码（如A-A-A）对应比重（如3%）

2. **实际试卷数据**：从数据库中统计试卷的实际三级代码分布

### 显示格式
- **表格列**：
  1. 3级代码 (Level 3 Code)
  2. 实际比重(%) (Actual Proportion %)
  3. 组题需求(%) (Required Proportion %)
  4. 差异 (Difference)

### 颜色区分
- **蓝色背景**：组题需求的三级代码值
- **绿色**：实际比重高于需求比重
- **红色**：实际比重低于需求比重
- **紫色**：实际比重等于需求比重

### 路由信息
- URL：`/paper/<paper_id>/level3_code_stats`
- 模板：内嵌在app.py中的HTML模板

### 重要提醒
- 用户明确要求**只显示第三级代码**，不要显示一级和二级代码
- 数据必须与组卷规则模板中的需求数据进行对比
- 需要从templates和uploads文件夹中查找最新的组卷规则文件

## 更新历史
- 2024-12-24：创建项目需求记录文件
- 用户反馈：当前显示了所有级别的代码，需要修改为只显示第三级代码