# -*- coding: utf-8 -*-
"""
考试编排模块 - 简化版主应用
"""

from flask import Flask, request, jsonify
from datetime import datetime, timezone
import json
import os
from flask_cors import CORS

# 创建Flask应用实例
app = Flask(__name__)
CORS(app)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        "code": 200,
        "msg": "服务正常",
        "data": {
            "service": "exam_management",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "healthy"
        }
    })

@app.route('/', methods=['GET'])
def root():
    """根路径接口"""
    return jsonify({
        "code": 200,
        "msg": "考试编排模块",
        "data": {
            "service": "exam_management",
            "version": "3.0",
            "description": "考试编排模块 - 提供完整的考试管理功能",
            "endpoints": [
                "GET /health - 健康检查",
                "GET / - 服务信息"
            ]
        }
    })

if __name__ == '__main__':
    print("[*] 启动考试编排模块...")
    print("[*] 服务地址: http://localhost:5003")
    app.run(host='0.0.0.0', port=5003, debug=True)