# -*- coding: utf-8 -*-
"""
考生答题模块 - 主应用文件

功能说明：
- 考生登录验证
- 查看可参加的考试
- 在线答题界面
- 答案实时保存
- 试卷提交
- 考试状态管理

作者: SOLO Coding
创建时间: 2024
"""

import os
import sys
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from flask import Flask, request, jsonify, render_template, session, redirect, url_for
from flask_cors import CORS
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import check_password_hash
import requests

# 导入服务类
from services import StudentExamService

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 初始化数据库
db = SQLAlchemy()

# 创建Flask应用
def create_app(config_class=None):
    """应用工厂函数"""
    from config import get_config
    
    app = Flask(__name__)
    
    # 加载配置
    if config_class:
        app.config.from_object(config_class)
    else:
        config = get_config()
        app.config.from_object(config)
    
    # 启用CORS
    CORS(app, supports_credentials=True)
    
    # 初始化数据库
    db.init_app(app)
    
    # 注册路由
    register_routes(app)
    
    return app

def register_routes(app):
    """注册所有路由"""
    # 初始化服务
    student_service = StudentExamService()
    
    @app.route('/')
    def index():
        """首页 - 考生登录页面"""
        return render_template('login.html')

    @app.route('/api/v1/auth/login', methods=['POST'])
    def login():
        """考生登录接口"""
        try:
            data = request.get_json()
            username = data.get('username')
            password = data.get('password')
            
            if not username or not password:
                return jsonify({
                    'code': 400,
                    'msg': '用户名和密码不能为空',
                    'data': {}
                }), 400
            
            # 验证考生身份
            student_info = student_service.authenticate_student(username, password)
            
            if student_info:
                # 设置会话
                session['student_id'] = student_info['id']
                session['student_name'] = student_info['name']
                session['logged_in'] = True
                
                return jsonify({
                    'code': 200,
                    'msg': '登录成功',
                    'data': {
                        'student_id': student_info['id'],
                        'student_name': student_info['name'],
                        'redirect_url': '/dashboard'
                    }
                })
            else:
                return jsonify({
                    'code': 401,
                    'msg': '用户名或密码错误',
                    'data': {}
                }), 401
                
        except Exception as e:
            logger.error(f"登录失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '登录失败，请稍后重试',
                'data': {}
            }), 500

    @app.route('/dashboard')
    def dashboard():
        """考生控制台"""
        if not session.get('logged_in'):
            return redirect(url_for('index'))
        
        return render_template('dashboard.html', 
                             student_name=session.get('student_name'))

    @app.route('/api/v1/exams/available', methods=['GET'])
    def get_available_exams():
        """获取可参加的考试列表"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            student_id = session.get('student_id')
            exams = student_service.get_available_exams(student_id)
            
            return jsonify({
                'code': 200,
                'msg': '获取成功',
                'data': exams
            })
            
        except Exception as e:
            logger.error(f"获取可用考试失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '获取考试列表失败',
                'data': {}
            }), 500

    @app.route('/api/v1/exams/<int:exam_id>/start', methods=['POST'])
    def start_exam(exam_id):
        """开始考试"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            student_id = session.get('student_id')
            exam_session = student_service.start_exam(student_id, exam_id)
            
            if exam_session:
                return jsonify({
                    'code': 200,
                    'msg': '考试开始',
                    'data': exam_session
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '无法开始考试',
                    'data': {}
                }), 400
                
        except Exception as e:
            logger.error(f"开始考试失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '开始考试失败',
                'data': {}
            }), 500

    @app.route('/exam/<int:exam_id>')
    def exam_page(exam_id):
        """考试页面"""
        if not session.get('logged_in'):
            return redirect(url_for('index'))
        
        return render_template('exam.html', 
                             exam_id=exam_id,
                             student_name=session.get('student_name'))

    @app.route('/api/v1/exams/<int:exam_id>/questions', methods=['GET'])
    def get_exam_questions(exam_id):
        """获取考试题目"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            questions = student_service.get_exam_questions(exam_id)
            
            return jsonify({
                'code': 200,
                'msg': '获取成功',
                'data': questions
            })
            
        except Exception as e:
            logger.error(f"获取考试题目失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '获取题目失败',
                'data': {}
            }), 500

    @app.route('/api/v1/sessions/<int:session_id>/answers', methods=['POST'])
    def save_answer(session_id):
        """保存答案"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            data = request.get_json()
            question_id = data.get('question_id')
            answer = data.get('answer')
            
            if question_id is None or answer is None:
                return jsonify({
                    'code': 400,
                    'msg': '题目ID和答案不能为空',
                    'data': {}
                }), 400
            
            success = student_service.save_answer(session_id, question_id, answer)
            
            if success:
                return jsonify({
                    'code': 200,
                    'msg': '答案保存成功',
                    'data': {}
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '答案保存失败',
                    'data': {}
                }), 400
                
        except Exception as e:
            logger.error(f"保存答案失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '保存答案失败',
                'data': {}
            }), 500

    @app.route('/api/v1/sessions/<int:session_id>/submit', methods=['POST'])
    def submit_exam(session_id):
        """提交考试"""
        try:
            if not session.get('logged_in'):
                return jsonify({
                    'code': 401,
                    'msg': '请先登录',
                    'data': {}
                }), 401
            
            success = student_service.submit_exam(session_id)
            
            if success:
                return jsonify({
                    'code': 200,
                    'msg': '考试提交成功',
                    'data': {'redirect_url': '/dashboard'}
                })
            else:
                return jsonify({
                    'code': 400,
                    'msg': '考试提交失败',
                    'data': {}
                }), 400
                
        except Exception as e:
            logger.error(f"提交考试失败: {e}")
            return jsonify({
                'code': 500,
                'msg': '提交考试失败',
                'data': {}
            }), 500

    @app.route('/api/v1/logout', methods=['POST'])
    def logout():
        """退出登录"""
        session.clear()
        return jsonify({
            'code': 200,
            'msg': '退出成功',
            'data': {'redirect_url': '/'}
        })

    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        return jsonify({
            'status': 'healthy',
            'service': 'student-exam',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0'
        })

# 创建应用实例
app = create_app()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('student_exam.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# API网关配置
API_GATEWAY_URL = 'http://localhost:8080'
USER_MANAGEMENT_URL = 'http://localhost:5002'
EXAM_MANAGEMENT_URL = 'http://localhost:5003'
QUESTION_BANK_URL = 'http://localhost:5004'

class ExamSession(db.Model):
    """考试会话表 - 记录考生的考试状态"""
    __tablename__ = 'exam_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, nullable=False, comment='考生ID')
    exam_id = db.Column(db.Integer, nullable=False, comment='考试ID')
    start_time = db.Column(db.DateTime, nullable=False, comment='开始时间')
    end_time = db.Column(db.DateTime, comment='结束时间')
    status = db.Column(db.String(20), default='in_progress', comment='状态：in_progress, completed, timeout')
    current_question = db.Column(db.Integer, default=1, comment='当前题目序号')
    answers = db.Column(db.Text, comment='答案JSON')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'student_id': self.student_id,
            'exam_id': self.exam_id,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'current_question': self.current_question,
            'answers': json.loads(self.answers) if self.answers else {},
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class StudentExamService:
    """考生答题服务类"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def authenticate_student(self, username: str, password: str) -> Optional[Dict]:
        """
        验证考生身份
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            考生信息字典或None
        """
        try:
            # 调用用户管理模块进行身份验证
            response = requests.post(
                f'{USER_MANAGEMENT_URL}/api/v1/auth/login',
                json={'username': username, 'password': password},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200 and data.get('data', {}).get('role') == 'student':
                    return data['data']
            
            return None
            
        except Exception as e:
            self.logger.error(f"学生身份验证失败: {e}")
            return None
    
    def get_available_exams(self, student_id: int) -> List[Dict]:
        """
        获取考生可参加的考试列表
        
        Args:
            student_id: 考生ID
            
        Returns:
            考试列表
        """
        try:
            # 调用考试管理模块获取考试列表
            response = requests.get(
                f'{EXAM_MANAGEMENT_URL}/api/v1/exams/available',
                params={'student_id': student_id},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    return data.get('data', [])
            
            return []
            
        except Exception as e:
            self.logger.error(f"获取可用考试失败: {e}")
            return []
    
    def start_exam(self, student_id: int, exam_id: int) -> Optional[Dict]:
        """
        开始考试
        
        Args:
            student_id: 考生ID
            exam_id: 考试ID
            
        Returns:
            考试会话信息
        """
        try:
            # 检查是否已有进行中的考试会话
            existing_session = ExamSession.query.filter_by(
                student_id=student_id,
                exam_id=exam_id,
                status='in_progress'
            ).first()
            
            if existing_session:
                return existing_session.to_dict()
            
            # 创建新的考试会话
            session = ExamSession(
                student_id=student_id,
                exam_id=exam_id,
                start_time=datetime.utcnow(),
                status='in_progress',
                answers='{}'
            )
            
            db.session.add(session)
            db.session.commit()
            
            self.logger.info(f"考生 {student_id} 开始考试 {exam_id}")
            return session.to_dict()
            
        except Exception as e:
            self.logger.error(f"开始考试失败: {e}")
            db.session.rollback()
            return None
    
    def get_exam_questions(self, exam_id: int) -> List[Dict]:
        """
        获取考试题目
        
        Args:
            exam_id: 考试ID
            
        Returns:
            题目列表
        """
        try:
            # 调用考试管理模块获取考试题目
            response = requests.get(
                f'{EXAM_MANAGEMENT_URL}/api/v1/exams/{exam_id}/questions',
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    return data.get('data', [])
            
            return []
            
        except Exception as e:
            self.logger.error(f"获取考试题目失败: {e}")
            return []
    
    def save_answer(self, session_id: int, question_id: int, answer: Any) -> bool:
        """
        保存答案
        
        Args:
            session_id: 考试会话ID
            question_id: 题目ID
            answer: 答案
            
        Returns:
            是否保存成功
        """
        try:
            session = ExamSession.query.get(session_id)
            if not session or session.status != 'in_progress':
                return False
            
            # 解析现有答案
            answers = json.loads(session.answers) if session.answers else {}
            
            # 更新答案
            answers[str(question_id)] = answer
            
            # 保存到数据库
            session.answers = json.dumps(answers, ensure_ascii=False)
            session.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            self.logger.info(f"保存答案: 会话 {session_id}, 题目 {question_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存答案失败: {e}")
            db.session.rollback()
            return False
    
    def submit_exam(self, session_id: int) -> bool:
        """
        提交考试
        
        Args:
            session_id: 考试会话ID
            
        Returns:
            是否提交成功
        """
        try:
            session = ExamSession.query.get(session_id)
            if not session or session.status != 'in_progress':
                return False
            
            # 更新考试状态
            session.status = 'completed'
            session.end_time = datetime.utcnow()
            session.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            # 通知评分管理模块
            try:
                requests.post(
                    f'{API_GATEWAY_URL}/api/v1/scoring/submit',
                    json={
                        'session_id': session_id,
                        'student_id': session.student_id,
                        'exam_id': session.exam_id,
                        'answers': json.loads(session.answers) if session.answers else {}
                    },
                    timeout=10
                )
            except Exception as e:
                self.logger.warning(f"通知评分模块失败: {e}")
            
            self.logger.info(f"考试提交成功: 会话 {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"提交考试失败: {e}")
            db.session.rollback()
            return False

# 初始化服务（在register_routes函数中使用）

def safe_print(text):
    """安全打印函数，处理Windows控制台编码问题"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

if __name__ == '__main__':
    # 创建数据库表
    with app.app_context():
        db.create_all()
        safe_print("[*] 数据库表创建完成")
    
    safe_print("[*] 考生答题模块启动中...")
    safe_print("[*] 服务地址: http://0.0.0.0:5005")
    safe_print("[*] 健康检查: http://0.0.0.0:5005/api/health")
    
    app.run(
        host='0.0.0.0',
        port=5005,
        debug=True,
        use_reloader=False
    )