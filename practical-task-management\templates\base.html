<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}实操任务管理系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('web.index') }}">
                <i class="bi bi-gear-fill"></i> 实操任务管理系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('web.index') }}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('web.tasks') }}">任务管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('web.categories') }}">分类管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('web.executions') }}">执行记录</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">
                            <i class="bi bi-people"></i> 用户管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/audit-logs">
                            <i class="bi bi-shield-check"></i> 审计日志
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="http://localhost:8000" target="_blank">
                            <i class="bi bi-house-door"></i> 返回主控台
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <span id="username-display">用户</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" id="profile-link"><i class="bi bi-person"></i> 个人资料</a></li>
                            <li><a class="dropdown-item" href="#" id="change-password-link"><i class="bi bi-key"></i> 修改密码</a></li>
                            <li><a class="dropdown-item" href="#" id="settings-link"><i class="bi bi-gear"></i> 设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout" id="logout-link"><i class="bi bi-box-arrow-right"></i> 退出</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-3">
        <!-- 面包屑导航 -->
        {% block breadcrumb %}
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('web.index') }}">首页</a></li>
                {% block breadcrumb_items %}{% endblock %}
            </ol>
        </nav>
        {% endblock %}

        <!-- 消息提示 -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- 页面内容 -->
        {% block content %}{% endblock %}
    </div>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 实操任务管理系统. 版权所有.</p>
        </div>
    </footer>

    <!-- 返回主控台按钮 -->
    {% include 'components/back_to_main.html' %}

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    <script>
        // 页面加载时获取用户信息
        document.addEventListener('DOMContentLoaded', function() {
            loadUserInfo();
        });
        
        // 加载用户信息
        async function loadUserInfo() {
            try {
                const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
                if (!token) {
                    window.location.href = '/login';
                    return;
                }
                
                const response = await fetch('/api/v1/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.code === 200) {
                        const user = result.data.user;
                        document.getElementById('username-display').textContent = user.full_name || user.username;
                        
                        // 设置Cookie以便服务器端验证
                        document.cookie = `auth_token=${token}; path=/`;
                    } else {
                        handleAuthError();
                    }
                } else {
                    handleAuthError();
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                handleAuthError();
            }
        }
        
        // 处理认证错误
        function handleAuthError() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_info');
            sessionStorage.removeItem('auth_token');
            sessionStorage.removeItem('user_info');
            document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            window.location.href = '/login';
        }
        
        // 个人资料模态框
        document.getElementById('profile-link').addEventListener('click', function(e) {
            e.preventDefault();
            showProfileModal();
        });
        
        // 修改密码模态框
        document.getElementById('change-password-link').addEventListener('click', function(e) {
            e.preventDefault();
            showChangePasswordModal();
        });
        
        // 显示个人资料模态框
        async function showProfileModal() {
            try {
                const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
                const response = await fetch('/api/v1/auth/profile', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    if (result.code === 200) {
                        const user = result.data.user;
                        showMessage(`
                            <div class="modal fade" id="profileModal" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">个人资料</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row mb-3">
                                                <label class="col-sm-3 col-form-label">用户名:</label>
                                                <div class="col-sm-9">
                                                    <p class="form-control-plaintext">${user.username}</p>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <label class="col-sm-3 col-form-label">姓名:</label>
                                                <div class="col-sm-9">
                                                    <p class="form-control-plaintext">${user.full_name || '未设置'}</p>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <label class="col-sm-3 col-form-label">邮箱:</label>
                                                <div class="col-sm-9">
                                                    <p class="form-control-plaintext">${user.email || '未设置'}</p>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <label class="col-sm-3 col-form-label">角色:</label>
                                                <div class="col-sm-9">
                                                    <p class="form-control-plaintext">${getRoleName(user.role)}</p>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <label class="col-sm-3 col-form-label">注册时间:</label>
                                                <div class="col-sm-9">
                                                    <p class="form-control-plaintext">${formatDateTime(user.created_at)}</p>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <label class="col-sm-3 col-form-label">最后登录:</label>
                                                <div class="col-sm-9">
                                                    <p class="form-control-plaintext">${user.last_login ? formatDateTime(user.last_login) : '首次登录'}</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `, 'modal');
                    }
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
                showMessage('获取用户信息失败', 'error');
            }
        }
        
        // 显示修改密码模态框
        function showChangePasswordModal() {
            showMessage(`
                <div class="modal fade" id="changePasswordModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">修改密码</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form id="changePasswordForm">
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="oldPassword" class="form-label">当前密码</label>
                                        <input type="password" class="form-control" id="oldPassword" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="newPassword" class="form-label">新密码</label>
                                        <input type="password" class="form-control" id="newPassword" required minlength="6">
                                    </div>
                                    <div class="mb-3">
                                        <label for="confirmPassword" class="form-label">确认新密码</label>
                                        <input type="password" class="form-control" id="confirmPassword" required>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                    <button type="submit" class="btn btn-primary">确认修改</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `, 'modal');
            
            // 绑定表单提交事件
            document.getElementById('changePasswordForm').addEventListener('submit', handleChangePassword);
        }
        
        // 处理修改密码
        async function handleChangePassword(e) {
            e.preventDefault();
            
            const oldPassword = document.getElementById('oldPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (newPassword !== confirmPassword) {
                showMessage('新密码和确认密码不一致', 'error');
                return;
            }
            
            try {
                const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
                const response = await fetch('/api/v1/auth/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                        old_password: oldPassword,
                        new_password: newPassword
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('密码修改成功', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
                } else {
                    showMessage(result.msg || '密码修改失败', 'error');
                }
            } catch (error) {
                console.error('修改密码失败:', error);
                showMessage('修改密码失败', 'error');
            }
        }
        
        // 获取角色名称
        function getRoleName(role) {
            const roleNames = {
                'admin': '系统管理员',
                'teacher': '教师',
                'student': '学生',
                'examiner': '考官'
            };
            return roleNames[role] || role;
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>