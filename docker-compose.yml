version: '3.8'

services:
  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: exam-system-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - exam-network

  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: exam-system-postgres
    environment:
      POSTGRES_DB: exam_system
      POSTGRES_USER: exam_user
      POSTGRES_PASSWORD: exam_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U exam_user -d exam_system"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - exam-network

  # API网关
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: exam-system-gateway
    ports:
      - "8080:8080"
    environment:
      - GATEWAY_HOST=0.0.0.0
      - GATEWAY_PORT=8080
      - FLASK_ENV=production
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./api-gateway/config:/app/config
      - ./api-gateway/logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - exam-network

  # 用户管理服务
  user-management:
    build:
      context: ./user-management
      dockerfile: Dockerfile
    container_name: exam-system-user-mgmt
    ports:
      - "5001:5001"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************************/exam_system
      - REDIS_URL=redis://redis:6379/1
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./user-management/logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - exam-network

  # 题库管理服务
  question-bank:
    build:
      context: ./question-bank
      dockerfile: Dockerfile
    container_name: exam-system-question-bank
    ports:
      - "5002:5002"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************************/exam_system
      - REDIS_URL=redis://redis:6379/2
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./question-bank/logs:/app/logs
      - ./question-bank/uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - exam-network

  # 考试管理服务
  exam-management:
    build:
      context: ./exam-management
      dockerfile: Dockerfile
    container_name: exam-system-exam-mgmt
    ports:
      - "5003:5003"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************************/exam_system
      - REDIS_URL=redis://redis:6379/3
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./exam-management/logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      user-management:
        condition: service_healthy
      question-bank:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5003/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - exam-network

  # 成绩管理服务
  score-management:
    build:
      context: ./score-management
      dockerfile: Dockerfile
    container_name: exam-system-score-mgmt
    ports:
      - "5004:5004"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************************/exam_system
      - REDIS_URL=redis://redis:6379/4
      - JWT_SECRET_KEY=your-secret-key-change-in-production
    volumes:
      - ./score-management/logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      exam-management:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5004/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - exam-network

  # 监控服务
  monitoring:
    build:
      context: ./monitoring
      dockerfile: Dockerfile
    container_name: exam-system-monitoring
    ports:
      - "5005:5005"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************************/exam_system
      - REDIS_URL=redis://redis:6379/5
    volumes:
      - ./monitoring/logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - exam-network

  # 审计服务
  auditing:
    build:
      context: ./auditing
      dockerfile: Dockerfile
    container_name: exam-system-auditing
    ports:
      - "5006:5006"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=**************************************************/exam_system
      - REDIS_URL=redis://redis:6379/6
    volumes:
      - ./auditing/logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5006/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - exam-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: exam-system-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - exam-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: exam-system-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - exam-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: exam-system-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api-gateway
    networks:
      - exam-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  exam-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16