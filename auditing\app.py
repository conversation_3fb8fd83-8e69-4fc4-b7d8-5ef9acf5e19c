# -*- coding: utf-8 -*-
"""
审计日志模块 - 核心服务
"""
from flask import Flask, jsonify, request
from flask_cors import CORS
from datetime import datetime, timezone

# --- In-Memory Database for Logs ---
db = {
    "logs": [
        {"id": 1, "user_id": "admin", "action": "login_success", "details": {"ip_address": "127.0.0.1"}, "timestamp": "2024-08-14T10:00:00Z"},
        {"id": 2, "user_id": "expert", "action": "create_question", "details": {"question_id": 101}, "timestamp": "2024-08-14T10:05:00Z"}
    ],
    "next_log_id": 3
}

def create_app():
    """Application factory to create and configure the Flask app."""
    app = Flask(__name__)
    CORS(app)

    def get_utc_now_iso():
        return datetime.now(timezone.utc).isoformat()

    @app.route('/', methods=['GET'])
    def index():
        return jsonify({'service': 'Auditing API', 'status': 'running'})

    @app.route('/api/health', methods=['GET'])
    def health_check():
        return jsonify({'status': 'healthy', 'service': 'auditing'})

    @app.route('/api/v1/logs', methods=['POST'])
    def create_log_entry():
        """Creates a new audit log entry."""
        if not request.is_json:
            return jsonify({"error": "Request must be JSON"}), 400

        data = request.get_json()
        required_fields = ['user_id', 'action']
        errors = [f"'{field}' is a required field." for field in required_fields if field not in data]
        if errors:
            return jsonify({"error": "Validation failed", "messages": errors}), 400

        new_log_id = db['next_log_id']

        log_entry = {
            'id': new_log_id,
            'user_id': data['user_id'],
            'action': data['action'],
            'details': data.get('details', {}),
            'timestamp': get_utc_now_iso()
        }

        db['logs'].append(log_entry)
        db['next_log_id'] += 1

        return jsonify(log_entry), 201

    @app.route('/api/v1/logs', methods=['GET'])
    def get_logs():
        """Retrieves audit logs, with optional filtering."""
        # Get query parameters for filtering
        user_id_filter = request.args.get('user_id')
        action_filter = request.args.get('action')

        # Start with all logs
        filtered_logs = db['logs']

        # Apply filters if they are provided
        if user_id_filter:
            filtered_logs = [log for log in filtered_logs if log['user_id'] == user_id_filter]

        if action_filter:
            filtered_logs = [log for log in filtered_logs if log['action'] == action_filter]

        # Return the most recent logs first
        return jsonify(sorted(filtered_logs, key=lambda x: x['id'], reverse=True))

    return app

if __name__ == '__main__':
    app = create_app()
    # The port is configured to 5010 to match main_launcher.py configuration
    port = 5010
    print("--- Auditing Service ---")
    print(f"Starting server at: http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=True)
