 ## **基于 “模块化开发 + 接口驱动集成” 的设计与实施**

是**模块化开发 + 接口驱动集成**的方法，非常适合降低复杂系统的开发难度。这种方式能有效拆分任务、控制风险，但需要通过科学的规划避免后期集成时出现 “接口孤岛” 或 “连接成本过高” 的问题。

## 一、方案概述

### 1. 系统定位

本系统是面向局域网环境的综合性考试平台，支持理论知识考试与非集成型软件操作考试（如 Premiere、CAD 等专业软件实操），通过 “模块化开发 + 接口驱动集成” 模式，实现考试全流程的标准化管理与灵活扩展。

### 2. 核心目标

- 支持多类型考试：覆盖选择 / 判断等理论题型，以及需外部软件完成的实操题型。
- 模块化架构：各功能模块独立开发、按需集成，降低复杂系统的开发与维护成本。
- 接口标准化：通过统一接口规范实现模块间协同，兼容理论考试与非集成型实操考试场景。


### 2. 架构核心特点

- **模块化拆分**：按业务边界划分为独立模块，支持并行开发与单独部署。
- **接口驱动集成**：模块间通过标准化接口通信，无关乎内部实现（如 Python/Java 均可）。
- **兼容非集成场景**：通过 “任务管理 + 文件交互” 模式衔接系统与外部专业软件，无需嵌入软件内核。

## 三、功能模块划分（按业务职责边界）

### 1. 核心模块清单（13 大模块）

| 模块名称        | 核心功能                                     | 适用场景       | 用户权限  |
| ----------- | ---------------------------------------- | ---------- |---------- |
| 1. 用户管理模块   | 账号创建、登录认证、角色分配（管理员 / 专家/考生 / 考评员）、权限控制   | 所有考试类型     | 管理员 |
| 2. 题库管理模块   | 理论试题录入（单选 / 多选等）、分类管理、批量导入导出（Excel/Word） | 理论考试       | 管理员 / 专家  / 内部督导员  |
| 3. 实操任务管理模块 | 非集成型试题管理（操作步骤、素材地址、成果要求）、软件版本标注          | 非集成型软件操作考试 | 管理员 / 考评员 / 内部督导员  |
| 4. 考试编排模块   | 创建考试（设置时间 / 考生范围）、关联试题（理论 / 实操）、发布考试     | 所有考试类型     | 管理员 / 考评员 / 内部督导员  |
| 5. 考生答题模块   | 理论题作答、实时保存；实操任务查看、素材下载、成果上传              | 所有考试类型     | 管理员 / 考评员 / 考生 |
| 6. 成果文件管理模块 | 实操成果文件存储（如工程文件 / 视频）、加密、权限控制下载           | 非集成型软件操作考试 |  管理员 / 考评员 / 内部督导员   |
| 7. 评分管理模块   | 理论题自动评分、实操成果人工评分、分数校准                    | 所有考试类型     | 管理员 /考评员 / 内部督导员 |
| 8. 成绩查询模块   | 考生查分、管理员统计（通过率 / 平均分）、成绩导出               | 所有考试类型     | 管理员 / 考评员 / 内部督导员  |
| 9. 监控审计模块   | 操作日志记录（登录 / 交卷 / 评分）、异常行为监控（如切屏 / 文件替换）  | 所有考试类型     |  管理员 / 考评员  / 内部督导员  |
| 10. 系统配置模块  | 基础参数设置（浏览器兼容 / 防作弊阈值）、服务器资源监控            | 所有考试类型     | 管理员 |
| 11. 成绩上报模块  | 上报成绩数据到外部系统（如政府开放的数据库系统）            | 所有考试类型     | 管理员 |
| 12. API网关模块  | 本系统内所有模块连接端口、统一接口，提供外部系统调用            | 所有考试类型     | 管理员 |
| 13. 文件与项目管理工具模块  | 借用本系统框架搭建的文件与项目管理工具，用于管理文件、项目、任务等。实现简单项目管理功能。| 文件系统  | 管理员 / 考评员 |


局域网在线考试系统操作需求说明​
一、用户登录​
访问系统：打开浏览器，在地址栏输入局域网在线考试系统的访问地址。​
身份验证：在登录页面，输入分配的用户名和密码。用户名可能为工号、学号或自定义账号，密码区分大小写。选择对应的用户角色（管理员、教师、考生等）后，点击 “登录” 按钮。​
二、管理员操作​
（一）用户管理​
批量添加用户：登录后，进入 “用户管理” 模块。点击 “批量添加用户” 按钮，导入excel表格方式的用户的详细信息，包括姓名、用户名、密码、用户角色、所属部门等，点击 “保存”。​
编辑用户信息：在用户列表中，找到需编辑的用户，点击 “编辑”，修改相关信息后保存。​
删除用户：勾选要删除的用户，点击 “删除” 按钮，确认删除操作。​
（二）题库管理​
添加题库：导入excel表格方式的题库信息​
组卷规则组卷：上传excel表格的组卷规则，按最·规则设置组卷。​
（三）试卷管理​
创建试卷：在 “试卷管理” 中点击 “创建试卷”，输入试卷名称、考试时长等基本信息。从题库管理模块中选择试卷。​
编辑试卷：选择需编辑的试卷，可修改试卷基本信息，增减试题或调整分值。​
删除试卷：勾选要删除的试卷，确认删除。​
（四）考试安排​
设置考试：进入 “考试安排” 模块，点击 “设置考试”，选择已创建的试卷，设定考试开始时间、结束时间、允许参加考试的用户范围等，保存设置。​
发布考试通知：考试设置完成后，可点击 “发布通知”，系统自动向考生发送考试通知，通知内容包括考试时间、科目等。​
三、考评员操作​
（一）试题录入与管理​
与管理员的题库管理中添加、编辑、删除试题操作类似，考评员可在自己有权限的题库类别中进行相应操作。​
（二）试卷创建与编辑​
考评员可根据教学需求创建试卷，操作同管理员的试卷管理。对于已有的试卷，若教师有编辑权限，可对试卷内容进行修改。​
（三）阅卷评分​
客观题自动评分：考试结束后，系统自动对客观题（如选择题、判断题）进行评分。​
主观题手动阅卷：三个考评员同时分别登录系统，参与客观题评分。系统取平均值，差异过大重新评阅。
（四）查看成绩与分析​
成绩查询：在 “成绩管理” 中，可按考试科目、等级等条件查询学生的考试成绩。​
成绩分析：系统提供成绩分析功能，简单统计通过率。​
四、考生操作​
（一）参加考试​
查看考试通知：登录系统后，在首页或 “考试通知” 模块查看即将参加的考试信息。​
进入考试：在规定的考试时间内，点击 “参加考试” 按钮，进入考试页面。阅读考试须知后，点击 “开始考试”。​
答题操作：按题目顺序答题，对于选择题点击选项作答，填空题输入答案，主观题在答题区域输入文字内容。答题过程中注意考试剩余时间。​
提交试卷：完成答题后，检查无误，点击 “提交试卷”。提交后不可再修改答案。​
（二）查看成绩​
考试结束不显示成绩。待考试结束后且考评员完成阅卷后，考评员导出成绩文件，并上传至服务器。​同时将成绩数据发给考生的公司或部门。

这个程序中，用户管理模块、题库管理模块、成绩上报模块都是独立性很强的子模块，既可以是这个程序的一部分，也可以单独打包及运行。我希望使用PyInstaller，为这三个模块分别创建一个独立的构建脚本。 此脚本将调用PyInstaller，告知其主入口点（例如，user_management/run.py），并确保所有必要文件（如.env文件或配置模板）与可执行文件打包在一起。 main_launcher.py 仍然会作为集成系统的一部分运行服务，但需要一个单独的流程来构建独立的 .exe 文件。请先创建各自独立的脚本。
