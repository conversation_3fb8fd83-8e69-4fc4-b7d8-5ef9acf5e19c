import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from app.config.config import config
from app.models import db
from app.api import create_api

migrate = Migrate()
jwt = JWTManager()

def register_routes(app):
    """注册基础路由"""
    from flask import redirect, url_for, render_template
    
    @app.route('/')
    def index():
        """用户管理主页面"""
        return render_template('index.html')
    
    @app.route('/docs')
    def docs():
        """API文档重定向"""
        return redirect('/docs/')
    
    @app.route('/health')
    def health():
        """简单健康检查"""
        return {'status': 'ok', 'service': 'user-management'}
    
    @app.route('/api/health')
    def api_health():
        """标准API健康检查端点"""
        return {'status': 'ok', 'service': 'user-management'}

def create_app(config_name=None):
    """应用工厂函数"""
    # 设置模板和静态文件目录
    import os
    template_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'templates'))
    static_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'static'))
    app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)
    
    # 配置
    config_name = config_name or os.getenv('FLASK_ENV', 'development')
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    jwt.init_app(app)
    CORS(app)
    
    # 创建API
    create_api(app)
    
    # 注册根路径路由
    register_routes(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    

    
    # 创建数据库表（异步处理，避免阻塞启动）
    def init_database():
        """异步初始化数据库"""
        try:
            with app.app_context():
                db.create_all()
                # 创建默认管理员用户
                create_default_admin()
                print("[+] 用户管理模块数据库初始化完成")
        except Exception as e:
            print(f"[!] 用户管理模块数据库初始化失败: {str(e)}")
    
    # 在后台线程中初始化数据库
    import threading
    db_init_thread = threading.Thread(target=init_database, daemon=True)
    db_init_thread.start()
    
    return app

def register_error_handlers(app):
    """注册错误处理器"""
    from app.utils import error_response
    
    @app.errorhandler(404)
    def not_found(error):
        return error_response("资源不存在", 404), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return error_response("服务器内部错误", 500), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        return error_response("请求参数错误", 400), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return error_response("未授权访问", 401), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return error_response("禁止访问", 403), 403

def create_default_admin():
    """创建默认管理员用户"""
    from app.models import User, UserRole, UserStatus
    
    # 检查是否已存在管理员用户
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            real_name='系统管理员',
            role=UserRole.ADMIN,
            status=UserStatus.APPROVED
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        
        # 创建测试用户
        test_users = [
            {
                'username': 'expert001',
                'email': '<EMAIL>',
                'real_name': '张专家',
                'role': UserRole.EXPERT,
                'department': '计算机学院',
                'position': '副教授',
                'password': 'expert123'
            },
            {
                'username': 'student001',
                'email': '<EMAIL>',
                'real_name': '李考生',
                'role': UserRole.STUDENT,
                'department': '计算机学院',
                'position': '考生',
                'password': 'student123'
            },
            {
                'username': 'grader001',
                'email': '<EMAIL>',
                'real_name': '王考评员',
                'role': UserRole.GRADER,
                'department': '教务处',
                'position': '考评员',
                'password': 'grader123'
            },
            {
                'username': 'supervisor001',
                'email': '<EMAIL>',
                'real_name': '内部督导员',
                'role': UserRole.INTERNAL_SUPERVISOR,
                'department': '质量监督部',
                'position': '内部督导员',
                'password': 'supervisor123'
            }
        ]
        
        for user_data in test_users:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                real_name=user_data['real_name'],
                role=user_data['role'],
                department=user_data.get('department'),
                position=user_data.get('position'),
                status=UserStatus.APPROVED
            )
            user.set_password(user_data['password'])
            db.session.add(user)
        
        try:
            db.session.commit()
            print("默认用户创建成功")
        except Exception as e:
            db.session.rollback()
            print(f"创建默认用户失败: {str(e)}")
