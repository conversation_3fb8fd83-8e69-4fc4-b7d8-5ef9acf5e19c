#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块并行启动脚本
实现系统所有模块的并行启动、监控和管理
"""

import os
import sys
import time
import yaml
import json
import psutil
import logging
import subprocess
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional, Tuple
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "api-gateway"))

class ModuleStartupManager:
    """
    模块启动管理器
    负责并行启动、监控和管理所有系统模块
    """
    
    def __init__(self, config_dir: str = None):
        """
        初始化模块启动管理器
        
        Args:
            config_dir: 配置文件目录路径
        """
        self.project_root = Path(__file__).parent.parent
        self.config_dir = Path(config_dir) if config_dir else self.project_root / "config"
        
        # 加载配置
        self.modules_config = self._load_config("modules.yaml")
        self.performance_config = self._load_config("performance.yaml")
        self.global_config = self._load_config("global.yaml")
        
        # 运行状态
        self.running_processes: Dict[str, subprocess.Popen] = {}
        self.module_status: Dict[str, Dict] = {}
        self.startup_times: Dict[str, float] = {}
        self.performance_metrics: Dict[str, Dict] = {}
        
        # 配置日志
        self._setup_logging()
        
        # 线程池
        max_workers = self.performance_config.get("concurrency", {}).get("thread_pool", {}).get("max_workers", 10)
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 监控线程
        self.monitoring_active = False
        self.monitoring_thread = None
    
    def _load_config(self, filename: str) -> Dict:
        """
        加载配置文件
        
        Args:
            filename: 配置文件名
            
        Returns:
            配置字典
        """
        config_path = self.config_dir / filename
        if not config_path.exists():
            self.logger.warning(f"配置文件不存在: {config_path}")
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            self.logger.error(f"加载配置文件失败 {filename}: {e}")
            return {}
    
    def _setup_logging(self):
        """
        设置日志配置
        """
        log_config = self.global_config.get("logging", {})
        log_level = log_config.get("level", "INFO")
        
        # 创建日志目录
        log_dir = self.project_root / "logs"
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / "module_startup.log", encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger("ModuleStartup")
    
    def get_module_startup_command(self, module_name: str) -> Optional[List[str]]:
        """
        获取模块启动命令
        
        Args:
            module_name: 模块名称
            
        Returns:
            启动命令列表
        """
        module_config = self.modules_config.get("modules", {}).get(module_name, {})
        if not module_config:
            self.logger.error(f"未找到模块配置: {module_name}")
            return None
        
        # 获取模块路径
        module_path = self.project_root / module_config.get("path", module_name)
        if not module_path.exists():
            self.logger.error(f"模块路径不存在: {module_path}")
            return None
        
        # 构建启动命令
        start_script = module_config.get("start_script", "app.py")
        python_executable = sys.executable
        
        # 检查启动脚本是否存在
        script_path = module_path / start_script
        if not script_path.exists():
            self.logger.error(f"启动脚本不存在: {script_path}")
            return None
        
        return [python_executable, str(script_path)]
    
    def start_single_module(self, module_name: str) -> Tuple[bool, str]:
        """
        启动单个模块
        
        Args:
            module_name: 模块名称
            
        Returns:
            (成功标志, 错误信息)
        """
        try:
            start_time = time.time()
            self.logger.info(f"开始启动模块: {module_name}")
            
            # 获取启动命令
            command = self.get_module_startup_command(module_name)
            if not command:
                return False, f"无法获取模块 {module_name} 的启动命令"
            
            # 获取模块配置
            module_config = self.modules_config.get("modules", {}).get(module_name, {})
            module_path = self.project_root / module_config.get("path", module_name)
            
            # 设置环境变量
            env = os.environ.copy()
            env.update(module_config.get("environment", {}))
            
            # 启动进程
            # 设置进程创建标志，避免创建新的终端窗口
            creation_flags = 0
            if os.name == 'nt':  # Windows系统
                creation_flags = subprocess.CREATE_NO_WINDOW
            
            process = subprocess.Popen(
                command,
                cwd=str(module_path),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                creationflags=creation_flags
            )
            
            # 记录进程信息
            self.running_processes[module_name] = process
            self.startup_times[module_name] = time.time() - start_time
            
            # 等待一小段时间检查进程是否正常启动
            time.sleep(2)
            
            if process.poll() is None:
                # 进程仍在运行
                self.module_status[module_name] = {
                    "status": "running",
                    "pid": process.pid,
                    "start_time": datetime.now().isoformat(),
                    "startup_duration": self.startup_times[module_name]
                }
                self.logger.info(f"模块 {module_name} 启动成功 (PID: {process.pid})")
                return True, ""
            else:
                # 进程已退出
                stdout, stderr = process.communicate()
                error_msg = f"模块 {module_name} 启动失败: {stderr}"
                self.logger.error(error_msg)
                
                self.module_status[module_name] = {
                    "status": "failed",
                    "error": error_msg,
                    "stdout": stdout,
                    "stderr": stderr
                }
                return False, error_msg
                
        except Exception as e:
            error_msg = f"启动模块 {module_name} 时发生异常: {e}"
            self.logger.error(error_msg)
            self.module_status[module_name] = {
                "status": "error",
                "error": error_msg
            }
            return False, error_msg
    
    def start_all_modules(self, parallel: bool = True) -> Dict[str, bool]:
        """
        启动所有模块
        
        Args:
            parallel: 是否并行启动
            
        Returns:
            启动结果字典 {模块名: 成功标志}
        """
        modules = self.modules_config.get("modules", {})
        enabled_modules = [name for name, config in modules.items() 
                          if config.get("enabled", True)]
        
        self.logger.info(f"开始启动 {len(enabled_modules)} 个模块: {enabled_modules}")
        
        results = {}
        
        if parallel:
            # 并行启动
            startup_config = self.performance_config.get("startup", {}).get("parallel_startup", {})
            max_parallel = startup_config.get("max_parallel", 5)
            
            with ThreadPoolExecutor(max_workers=max_parallel) as executor:
                # 提交所有启动任务
                future_to_module = {
                    executor.submit(self.start_single_module, module_name): module_name
                    for module_name in enabled_modules
                }
                
                # 等待完成
                for future in as_completed(future_to_module):
                    module_name = future_to_module[future]
                    try:
                        success, error = future.result()
                        results[module_name] = success
                        if not success:
                            self.logger.error(f"模块 {module_name} 启动失败: {error}")
                    except Exception as e:
                        results[module_name] = False
                        self.logger.error(f"模块 {module_name} 启动异常: {e}")
        else:
            # 串行启动
            for module_name in enabled_modules:
                success, error = self.start_single_module(module_name)
                results[module_name] = success
                if not success:
                    self.logger.error(f"模块 {module_name} 启动失败: {error}")
                    # 可以选择是否继续启动其他模块
                    continue
        
        # 启动性能监控
        if self.performance_config.get("monitoring", {}).get("metrics", {}).get("enabled", False):
            self.start_performance_monitoring()
        
        return results
    
    def check_module_health(self, module_name: str) -> Dict:
        """
        检查模块健康状态
        
        Args:
            module_name: 模块名称
            
        Returns:
            健康状态信息
        """
        if module_name not in self.running_processes:
            return {"status": "not_running", "healthy": False}
        
        process = self.running_processes[module_name]
        
        # 检查进程是否还在运行
        if process.poll() is not None:
            return {"status": "stopped", "healthy": False, "exit_code": process.returncode}
        
        try:
            # 获取进程信息
            ps_process = psutil.Process(process.pid)
            
            # 基本健康检查
            health_info = {
                "status": "running",
                "healthy": True,
                "pid": process.pid,
                "cpu_percent": ps_process.cpu_percent(),
                "memory_info": ps_process.memory_info()._asdict(),
                "create_time": ps_process.create_time(),
                "num_threads": ps_process.num_threads()
            }
            
            # 检查资源使用是否正常
            memory_mb = health_info["memory_info"]["rss"] / 1024 / 1024
            cpu_percent = health_info["cpu_percent"]
            
            # 获取资源限制
            limits = self.performance_config.get("resource_limits", {})
            max_memory = limits.get("memory", {}).get("max_usage_mb", 2048)
            max_cpu = limits.get("cpu", {}).get("max_usage_percent", 80)
            
            # 检查是否超出限制
            if memory_mb > max_memory:
                health_info["warnings"] = health_info.get("warnings", [])
                health_info["warnings"].append(f"内存使用过高: {memory_mb:.1f}MB > {max_memory}MB")
            
            if cpu_percent > max_cpu:
                health_info["warnings"] = health_info.get("warnings", [])
                health_info["warnings"].append(f"CPU使用过高: {cpu_percent:.1f}% > {max_cpu}%")
            
            return health_info
            
        except psutil.NoSuchProcess:
            return {"status": "not_found", "healthy": False}
        except Exception as e:
            return {"status": "error", "healthy": False, "error": str(e)}
    
    def get_system_status(self) -> Dict:
        """
        获取系统整体状态
        
        Returns:
            系统状态信息
        """
        status = {
            "timestamp": datetime.now().isoformat(),
            "total_modules": len(self.modules_config.get("modules", {})),
            "running_modules": len(self.running_processes),
            "modules": {},
            "system_resources": self._get_system_resources()
        }
        
        # 检查每个模块的状态
        for module_name in self.modules_config.get("modules", {}).keys():
            status["modules"][module_name] = self.check_module_health(module_name)
        
        # 统计健康模块数量
        healthy_count = sum(1 for module_status in status["modules"].values() 
                           if module_status.get("healthy", False))
        status["healthy_modules"] = healthy_count
        
        return status
    
    def _get_system_resources(self) -> Dict:
        """
        获取系统资源使用情况
        
        Returns:
            系统资源信息
        """
        try:
            return {
                "cpu_percent": psutil.cpu_percent(interval=1),
                "memory": psutil.virtual_memory()._asdict(),
                "disk": psutil.disk_usage('/')._asdict(),
                "network": psutil.net_io_counters()._asdict(),
                "boot_time": psutil.boot_time()
            }
        except Exception as e:
            self.logger.error(f"获取系统资源信息失败: {e}")
            return {}
    
    def start_performance_monitoring(self):
        """
        启动性能监控
        """
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._performance_monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_performance_monitoring(self):
        """
        停止性能监控
        """
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.logger.info("性能监控已停止")
    
    def _performance_monitoring_loop(self):
        """
        性能监控循环
        """
        monitoring_config = self.performance_config.get("monitoring", {})
        interval = monitoring_config.get("metrics", {}).get("collection_interval", 30)
        
        while self.monitoring_active:
            try:
                # 收集性能指标
                metrics = self._collect_performance_metrics()
                
                # 保存指标
                self._save_performance_metrics(metrics)
                
                # 检查告警
                self._check_alerts(metrics)
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
                time.sleep(interval)
    
    def _collect_performance_metrics(self) -> Dict:
        """
        收集性能指标
        
        Returns:
            性能指标数据
        """
        metrics = {
            "timestamp": datetime.now().isoformat(),
            "system": self._get_system_resources(),
            "modules": {}
        }
        
        # 收集每个模块的指标
        for module_name in self.running_processes.keys():
            health_info = self.check_module_health(module_name)
            if health_info.get("healthy", False):
                metrics["modules"][module_name] = {
                    "cpu_percent": health_info.get("cpu_percent", 0),
                    "memory_mb": health_info.get("memory_info", {}).get("rss", 0) / 1024 / 1024,
                    "num_threads": health_info.get("num_threads", 0)
                }
        
        return metrics
    
    def _save_performance_metrics(self, metrics: Dict):
        """
        保存性能指标
        
        Args:
            metrics: 性能指标数据
        """
        try:
            # 创建指标目录
            metrics_dir = self.project_root / "logs" / "metrics"
            metrics_dir.mkdir(parents=True, exist_ok=True)
            
            # 按日期保存指标文件
            date_str = datetime.now().strftime("%Y-%m-%d")
            metrics_file = metrics_dir / f"metrics_{date_str}.jsonl"
            
            # 追加写入指标数据
            with open(metrics_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(metrics, ensure_ascii=False) + '\n')
                
        except Exception as e:
            self.logger.error(f"保存性能指标失败: {e}")
    
    def _check_alerts(self, metrics: Dict):
        """
        检查告警规则
        
        Args:
            metrics: 性能指标数据
        """
        alerts_config = self.performance_config.get("monitoring", {}).get("alerts", {})
        if not alerts_config.get("enabled", False):
            return
        
        rules = alerts_config.get("rules", {})
        
        # 检查系统级告警
        system_metrics = metrics.get("system", {})
        
        # CPU告警
        if "high_cpu" in rules:
            cpu_rule = rules["high_cpu"]
            cpu_usage = system_metrics.get("cpu_percent", 0)
            if cpu_usage > cpu_rule.get("threshold", 80):
                self.logger.warning(f"CPU使用率过高: {cpu_usage:.1f}%")
        
        # 内存告警
        if "high_memory" in rules:
            memory_rule = rules["high_memory"]
            memory_info = system_metrics.get("memory", {})
            memory_percent = memory_info.get("percent", 0)
            if memory_percent > memory_rule.get("threshold", 85):
                self.logger.warning(f"内存使用率过高: {memory_percent:.1f}%")
    
    def stop_module(self, module_name: str) -> bool:
        """
        停止指定模块
        
        Args:
            module_name: 模块名称
            
        Returns:
            停止成功标志
        """
        if module_name not in self.running_processes:
            self.logger.warning(f"模块 {module_name} 未在运行")
            return False
        
        try:
            process = self.running_processes[module_name]
            
            # 尝试优雅关闭
            process.terminate()
            
            # 等待进程结束
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                # 强制杀死进程
                process.kill()
                process.wait()
            
            # 清理记录
            del self.running_processes[module_name]
            if module_name in self.module_status:
                self.module_status[module_name]["status"] = "stopped"
            
            self.logger.info(f"模块 {module_name} 已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止模块 {module_name} 失败: {e}")
            return False
    
    def stop_all_modules(self):
        """
        停止所有模块
        """
        self.logger.info("开始停止所有模块")
        
        # 停止性能监控
        self.stop_performance_monitoring()
        
        # 停止所有模块
        for module_name in list(self.running_processes.keys()):
            self.stop_module(module_name)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("所有模块已停止")
    
    def restart_module(self, module_name: str) -> bool:
        """
        重启指定模块
        
        Args:
            module_name: 模块名称
            
        Returns:
            重启成功标志
        """
        self.logger.info(f"重启模块: {module_name}")
        
        # 停止模块
        if module_name in self.running_processes:
            self.stop_module(module_name)
        
        # 等待一小段时间
        time.sleep(2)
        
        # 重新启动
        success, error = self.start_single_module(module_name)
        if success:
            self.logger.info(f"模块 {module_name} 重启成功")
        else:
            self.logger.error(f"模块 {module_name} 重启失败: {error}")
        
        return success

def main():
    """
    主函数
    """
    import argparse
    
    parser = argparse.ArgumentParser(description="模块启动管理器")
    parser.add_argument("--config-dir", help="配置文件目录")
    parser.add_argument("--parallel", action="store_true", default=True, help="并行启动模块")
    parser.add_argument("--module", help="启动指定模块")
    parser.add_argument("--stop", help="停止指定模块")
    parser.add_argument("--restart", help="重启指定模块")
    parser.add_argument("--status", action="store_true", help="显示系统状态")
    parser.add_argument("--stop-all", action="store_true", help="停止所有模块")
    
    args = parser.parse_args()
    
    # 创建管理器
    manager = ModuleStartupManager(config_dir=args.config_dir)
    
    try:
        if args.stop_all:
            # 停止所有模块
            manager.stop_all_modules()
            
        elif args.stop:
            # 停止指定模块
            success = manager.stop_module(args.stop)
            sys.exit(0 if success else 1)
            
        elif args.restart:
            # 重启指定模块
            success = manager.restart_module(args.restart)
            sys.exit(0 if success else 1)
            
        elif args.status:
            # 显示状态
            status = manager.get_system_status()
            print(json.dumps(status, indent=2, ensure_ascii=False))
            
        elif args.module:
            # 启动指定模块
            success, error = manager.start_single_module(args.module)
            if success:
                print(f"模块 {args.module} 启动成功")
                
                # 保持运行状态
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n收到中断信号，停止模块...")
                    manager.stop_module(args.module)
            else:
                print(f"模块 {args.module} 启动失败: {error}")
                sys.exit(1)
                
        else:
            # 启动所有模块
            results = manager.start_all_modules(parallel=args.parallel)
            
            # 显示启动结果
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            print(f"\n启动完成: {success_count}/{total_count} 个模块启动成功")
            
            for module_name, success in results.items():
                status = "✓" if success else "✗"
                print(f"  {status} {module_name}")
            
            if success_count > 0:
                print("\n系统正在运行，按 Ctrl+C 停止所有模块")
                
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n收到中断信号，停止所有模块...")
                    manager.stop_all_modules()
            else:
                print("\n没有模块成功启动")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n收到中断信号，停止所有模块...")
        manager.stop_all_modules()
    except Exception as e:
        print(f"运行异常: {e}")
        manager.stop_all_modules()
        sys.exit(1)

if __name__ == "__main__":
    main()