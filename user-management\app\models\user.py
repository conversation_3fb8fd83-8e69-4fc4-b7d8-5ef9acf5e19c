from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
import enum

db = SQLAlchemy()

class UserRole(enum.Enum):
    """用户角色枚举"""
    ADMIN = "admin"  # 管理员
    EXPERT = "expert"  # 专家
    STUDENT = "student"  # 考生
    GRADER = "grader"  # 考评员
    INTERNAL_SUPERVISOR = "internal_supervisor"  # 内部督导员

class UserStatus(enum.Enum):
    """用户状态枚举"""
    PENDING = "pending"  # 待审核
    APPROVED = "approved"  # 审核通过
    REJECTED = "rejected"  # 审核未通过

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    role = db.Column(db.Enum(UserRole), nullable=False, default=UserRole.STUDENT)
    status = db.Column(db.Enum(UserStatus), nullable=False, default=UserStatus.PENDING)
    department = db.Column(db.String(100), nullable=True)
    position = db.Column(db.String(100), nullable=True)
    avatar_url = db.Column(db.String(255), nullable=True)
    last_login_time = db.Column(db.DateTime, nullable=True)
    last_login_ip = db.Column(db.String(45), nullable=True)
    login_count = db.Column(db.Integer, default=0)
    created_time = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    updated_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)
    
    # 申报相关字段
    declared_occupation = db.Column(db.String(100), nullable=True, comment='申报职业(工种)')
    declared_level = db.Column(db.String(50), nullable=True, comment='申报级别')
    exam_type = db.Column(db.String(50), nullable=True, comment='考试类型')
    assessment_subject = db.Column(db.String(100), nullable=True, comment='考核科目')
    question_bank_name = db.Column(db.String(100), nullable=True, comment='题库名称')
    test_paper_id = db.Column(db.String(50), nullable=True, comment='试卷ID')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'phone': self.phone,
            'role': self.role.value if self.role else None,
            'status': self.status.value if self.status else None,
            'department': self.department,
            'position': self.position,
            'avatar_url': self.avatar_url,
            'last_login_time': self.last_login_time.isoformat() if self.last_login_time else None,
            'last_login_ip': self.last_login_ip,
            'login_count': self.login_count,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'updated_time': self.updated_time.isoformat() if self.updated_time else None,
            'created_by': self.created_by,
            'updated_by': self.updated_by,
            'declared_occupation': self.declared_occupation,
            'declared_level': self.declared_level,
            'exam_type': self.exam_type,
            'assessment_subject': self.assessment_subject,
            'question_bank_name': self.question_bank_name,
            'test_paper_id': self.test_paper_id
        }
        
        if include_sensitive:
            data['password_hash'] = self.password_hash
            
        return data
    
    def __repr__(self):
        return f'<User {self.username}>'