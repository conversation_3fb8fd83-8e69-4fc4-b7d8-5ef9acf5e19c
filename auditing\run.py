#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
审计日志模块启动脚本
提供审计日志记录和查询功能

启动方式：
    python run.py

服务地址：
    - 主服务: http://localhost:5010
    - 健康检查: http://localhost:5010/api/health
    - 日志查询: http://localhost:5010/api/v1/logs
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def main():
    """
    启动审计日志模块服务
    """
    try:
        print("="*50)
        print("审计日志模块 (Auditing Service)")
        print("="*50)
        print("服务信息:")
        print("  - 服务名称: 审计日志模块")
        print("  - 版本: v1.0")
        print("  - 端口: 5010")
        print("  - 主机: 0.0.0.0")
        print("")
        print("服务地址:")
        print("  - 主服务: http://localhost:5010")
        print("  - 健康检查: http://localhost:5010/api/health")
        print("  - 日志查询: http://localhost:5010/api/v1/logs")
        print("")
        print("主要功能:")
        print("  - 审计日志记录")
        print("  - 日志查询和过滤")
        print("  - 用户行为追踪")
        print("  - 安全事件监控")
        print("")
        print("正在启动服务...")
        print("="*50)
        
        # 创建并启动Flask应用
        app = create_app()
        app.run(
            host='0.0.0.0',
            port=5010,
            debug=False,  # 生产环境关闭调试模式
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()