#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库事务管理模块
提供完善的事务控制、数据一致性保障和并发控制
"""

import sqlite3
import threading
import time
import functools
from typing import Dict, Any, Optional, Callable, List, Tuple, Union
from datetime import datetime, timed<PERSON>ta
from contextlib import contextmanager
from enum import Enum
import uuid
import json

from exception_handler import (
    ExamException, DatabaseException, ErrorSeverity, ErrorCategory
)

class IsolationLevel(Enum):
    """事务隔离级别枚举"""
    READ_UNCOMMITTED = "READ UNCOMMITTED"
    READ_COMMITTED = "READ COMMITTED"
    REPEATABLE_READ = "REPEATABLE READ"
    SERIALIZABLE = "SERIALIZABLE"

class TransactionStatus(Enum):
    """事务状态枚举"""
    ACTIVE = "active"           # 活跃状态
    COMMITTED = "committed"     # 已提交
    ABORTED = "aborted"         # 已中止
    PREPARING = "preparing"     # 准备中
    PREPARED = "prepared"       # 已准备

class LockType(Enum):
    """锁类型枚举"""
    SHARED = "shared"           # 共享锁
    EXCLUSIVE = "exclusive"     # 排他锁
    UPDATE = "update"           # 更新锁

class TransactionInfo:
    """事务信息类"""
    
    def __init__(self, transaction_id: str, isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED):
        """
        初始化事务信息
        
        Args:
            transaction_id: 事务ID
            isolation_level: 隔离级别
        """
        self.transaction_id = transaction_id
        self.isolation_level = isolation_level
        self.status = TransactionStatus.ACTIVE
        self.start_time = datetime.now()
        self.end_time = None
        self.operations = []
        self.savepoints = {}
        self.locks = set()
        self.connection = None
        self.thread_id = threading.get_ident()
    
    def add_operation(self, operation: str, table: str, data: Dict[str, Any] = None):
        """添加操作记录"""
        self.operations.append({
            'timestamp': datetime.now(),
            'operation': operation,
            'table': table,
            'data': data or {}
        })
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'transaction_id': self.transaction_id,
            'isolation_level': self.isolation_level.value,
            'status': self.status.value,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'duration': (self.end_time - self.start_time).total_seconds() if self.end_time else None,
            'operations_count': len(self.operations),
            'locks_count': len(self.locks),
            'thread_id': self.thread_id
        }

class LockManager:
    """锁管理器"""
    
    def __init__(self):
        """
        初始化锁管理器
        """
        self._locks = {}  # resource_id -> {lock_type, transaction_id, timestamp}
        self._waiting_queue = {}  # resource_id -> [(transaction_id, lock_type, timestamp)]
        self._lock = threading.RLock()
        self._deadlock_timeout = 30  # 死锁检测超时时间（秒）
    
    def acquire_lock(self, resource_id: str, lock_type: LockType, 
                    transaction_id: str, timeout: float = 30.0) -> bool:
        """
        获取锁
        
        Args:
            resource_id: 资源ID
            lock_type: 锁类型
            transaction_id: 事务ID
            timeout: 超时时间（秒）
            
        Returns:
            是否成功获取锁
        """
        start_time = time.time()
        
        with self._lock:
            while True:
                # 检查是否可以获取锁
                if self._can_acquire_lock(resource_id, lock_type, transaction_id):
                    self._locks[resource_id] = {
                        'lock_type': lock_type,
                        'transaction_id': transaction_id,
                        'timestamp': datetime.now()
                    }
                    
                    # 从等待队列中移除
                    if resource_id in self._waiting_queue:
                        self._waiting_queue[resource_id] = [
                            item for item in self._waiting_queue[resource_id]
                            if item[0] != transaction_id
                        ]
                        if not self._waiting_queue[resource_id]:
                            del self._waiting_queue[resource_id]
                    
                    return True
                
                # 检查超时
                if time.time() - start_time > timeout:
                    # 从等待队列中移除
                    if resource_id in self._waiting_queue:
                        self._waiting_queue[resource_id] = [
                            item for item in self._waiting_queue[resource_id]
                            if item[0] != transaction_id
                        ]
                    return False
                
                # 加入等待队列
                if resource_id not in self._waiting_queue:
                    self._waiting_queue[resource_id] = []
                
                # 检查是否已在等待队列中
                if not any(item[0] == transaction_id for item in self._waiting_queue[resource_id]):
                    self._waiting_queue[resource_id].append((
                        transaction_id, lock_type, datetime.now()
                    ))
                
                # 检测死锁
                if self._detect_deadlock(transaction_id):
                    raise DatabaseException(
                        f"检测到死锁，事务 {transaction_id} 被中止",
                        code="DEADLOCK_DETECTED",
                        severity=ErrorSeverity.HIGH
                    )
        
        # 等待一小段时间后重试
        time.sleep(0.1)
    
    def _can_acquire_lock(self, resource_id: str, lock_type: LockType, transaction_id: str) -> bool:
        """检查是否可以获取锁"""
        if resource_id not in self._locks:
            return True
        
        current_lock = self._locks[resource_id]
        
        # 同一事务可以重复获取锁
        if current_lock['transaction_id'] == transaction_id:
            return True
        
        # 共享锁兼容性检查
        if (current_lock['lock_type'] == LockType.SHARED and 
            lock_type == LockType.SHARED):
            return True
        
        return False
    
    def release_lock(self, resource_id: str, transaction_id: str):
        """释放锁"""
        with self._lock:
            if (resource_id in self._locks and 
                self._locks[resource_id]['transaction_id'] == transaction_id):
                del self._locks[resource_id]
    
    def release_all_locks(self, transaction_id: str):
        """释放事务的所有锁"""
        with self._lock:
            resources_to_release = [
                resource_id for resource_id, lock_info in self._locks.items()
                if lock_info['transaction_id'] == transaction_id
            ]
            
            for resource_id in resources_to_release:
                del self._locks[resource_id]
    
    def _detect_deadlock(self, transaction_id: str) -> bool:
        """检测死锁（简化版本）"""
        # 这里实现简化的死锁检测逻辑
        # 在实际应用中，可能需要更复杂的图算法来检测循环等待
        
        # 检查等待时间是否超过阈值
        current_time = datetime.now()
        for resource_id, waiting_list in self._waiting_queue.items():
            for tid, lock_type, timestamp in waiting_list:
                if (tid == transaction_id and 
                    (current_time - timestamp).total_seconds() > self._deadlock_timeout):
                    return True
        
        return False
    
    def get_lock_info(self) -> Dict[str, Any]:
        """获取锁信息"""
        with self._lock:
            return {
                'active_locks': len(self._locks),
                'waiting_transactions': sum(len(queue) for queue in self._waiting_queue.values()),
                'locks_detail': {
                    resource_id: {
                        'lock_type': lock_info['lock_type'].value,
                        'transaction_id': lock_info['transaction_id'],
                        'timestamp': lock_info['timestamp'].isoformat()
                    }
                    for resource_id, lock_info in self._locks.items()
                }
            }

class TransactionManager:
    """事务管理器"""
    
    def __init__(self, db_path: str):
        """
        初始化事务管理器
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self._transactions = {}  # transaction_id -> TransactionInfo
        self._connections = {}   # transaction_id -> connection
        self._lock_manager = LockManager()
        self._manager_lock = threading.RLock()
        self._init_transaction_log()
    
    def _init_transaction_log(self):
        """初始化事务日志表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS transaction_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id TEXT NOT NULL,
                    status TEXT NOT NULL,
                    isolation_level TEXT NOT NULL,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    operations_count INTEGER DEFAULT 0,
                    thread_id INTEGER,
                    error_message TEXT
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS transaction_operations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    transaction_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    operation TEXT NOT NULL,
                    table_name TEXT NOT NULL,
                    data TEXT
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_transaction_id ON transaction_log(transaction_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_trans_ops_id ON transaction_operations(transaction_id)")
    
    def begin_transaction(self, isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED) -> str:
        """
        开始事务
        
        Args:
            isolation_level: 隔离级别
            
        Returns:
            事务ID
        """
        transaction_id = str(uuid.uuid4())
        
        with self._manager_lock:
            # 创建事务信息
            transaction_info = TransactionInfo(transaction_id, isolation_level)
            self._transactions[transaction_id] = transaction_info
            
            # 创建数据库连接
            conn = sqlite3.connect(self.db_path)
            conn.isolation_level = None  # 手动控制事务
            
            # 设置隔离级别
            if isolation_level != IsolationLevel.READ_COMMITTED:
                conn.execute(f"PRAGMA read_uncommitted = {isolation_level == IsolationLevel.READ_UNCOMMITTED}")
            
            conn.execute("BEGIN TRANSACTION")
            
            self._connections[transaction_id] = conn
            transaction_info.connection = conn
            
            # 记录事务开始
            self._log_transaction_start(transaction_info)
            
            return transaction_id
    
    def commit_transaction(self, transaction_id: str):
        """
        提交事务
        
        Args:
            transaction_id: 事务ID
        """
        with self._manager_lock:
            if transaction_id not in self._transactions:
                raise DatabaseException(f"事务 {transaction_id} 不存在")
            
            transaction_info = self._transactions[transaction_id]
            conn = self._connections[transaction_id]
            
            try:
                # 提交事务
                conn.execute("COMMIT")
                transaction_info.status = TransactionStatus.COMMITTED
                transaction_info.end_time = datetime.now()
                
                # 释放所有锁
                self._lock_manager.release_all_locks(transaction_id)
                
                # 记录事务提交
                self._log_transaction_end(transaction_info)
                
            except Exception as e:
                # 提交失败，回滚事务
                self.rollback_transaction(transaction_id)
                raise DatabaseException(f"事务提交失败: {str(e)}")
            
            finally:
                # 清理资源
                conn.close()
                del self._transactions[transaction_id]
                del self._connections[transaction_id]
    
    def rollback_transaction(self, transaction_id: str):
        """
        回滚事务
        
        Args:
            transaction_id: 事务ID
        """
        with self._manager_lock:
            if transaction_id not in self._transactions:
                raise DatabaseException(f"事务 {transaction_id} 不存在")
            
            transaction_info = self._transactions[transaction_id]
            conn = self._connections[transaction_id]
            
            try:
                # 回滚事务
                conn.execute("ROLLBACK")
                transaction_info.status = TransactionStatus.ABORTED
                transaction_info.end_time = datetime.now()
                
                # 释放所有锁
                self._lock_manager.release_all_locks(transaction_id)
                
                # 记录事务回滚
                self._log_transaction_end(transaction_info)
                
            finally:
                # 清理资源
                conn.close()
                del self._transactions[transaction_id]
                del self._connections[transaction_id]
    
    def create_savepoint(self, transaction_id: str, savepoint_name: str):
        """
        创建保存点
        
        Args:
            transaction_id: 事务ID
            savepoint_name: 保存点名称
        """
        if transaction_id not in self._transactions:
            raise DatabaseException(f"事务 {transaction_id} 不存在")
        
        conn = self._connections[transaction_id]
        conn.execute(f"SAVEPOINT {savepoint_name}")
        
        transaction_info = self._transactions[transaction_id]
        transaction_info.savepoints[savepoint_name] = datetime.now()
    
    def rollback_to_savepoint(self, transaction_id: str, savepoint_name: str):
        """
        回滚到保存点
        
        Args:
            transaction_id: 事务ID
            savepoint_name: 保存点名称
        """
        if transaction_id not in self._transactions:
            raise DatabaseException(f"事务 {transaction_id} 不存在")
        
        transaction_info = self._transactions[transaction_id]
        if savepoint_name not in transaction_info.savepoints:
            raise DatabaseException(f"保存点 {savepoint_name} 不存在")
        
        conn = self._connections[transaction_id]
        conn.execute(f"ROLLBACK TO SAVEPOINT {savepoint_name}")
    
    def execute_in_transaction(self, transaction_id: str, sql: str, 
                             params: Tuple = None, table_name: str = None) -> sqlite3.Cursor:
        """
        在事务中执行SQL
        
        Args:
            transaction_id: 事务ID
            sql: SQL语句
            params: 参数
            table_name: 表名（用于锁管理）
            
        Returns:
            游标对象
        """
        if transaction_id not in self._transactions:
            raise DatabaseException(f"事务 {transaction_id} 不存在")
        
        transaction_info = self._transactions[transaction_id]
        conn = self._connections[transaction_id]
        
        # 记录操作
        operation_type = sql.strip().split()[0].upper()
        transaction_info.add_operation(operation_type, table_name or "unknown")
        
        # 如果涉及表操作，获取锁
        if table_name and operation_type in ['UPDATE', 'DELETE', 'INSERT']:
            lock_type = LockType.EXCLUSIVE if operation_type in ['UPDATE', 'DELETE', 'INSERT'] else LockType.SHARED
            if not self._lock_manager.acquire_lock(table_name, lock_type, transaction_id):
                raise DatabaseException(f"无法获取表 {table_name} 的锁")
            transaction_info.locks.add(table_name)
        
        # 执行SQL
        try:
            if params:
                cursor = conn.execute(sql, params)
            else:
                cursor = conn.execute(sql)
            
            # 记录操作到日志
            self._log_transaction_operation(transaction_id, operation_type, table_name or "unknown")
            
            return cursor
        
        except Exception as e:
            raise DatabaseException(f"SQL执行失败: {str(e)}")
    
    def _log_transaction_start(self, transaction_info: TransactionInfo):
        """记录事务开始"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO transaction_log 
                (transaction_id, status, isolation_level, start_time, thread_id)
                VALUES (?, ?, ?, ?, ?)
            """, (
                transaction_info.transaction_id,
                transaction_info.status.value,
                transaction_info.isolation_level.value,
                transaction_info.start_time.isoformat(),
                transaction_info.thread_id
            ))
    
    def _log_transaction_end(self, transaction_info: TransactionInfo):
        """记录事务结束"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                UPDATE transaction_log 
                SET status = ?, end_time = ?, operations_count = ?
                WHERE transaction_id = ?
            """, (
                transaction_info.status.value,
                transaction_info.end_time.isoformat(),
                len(transaction_info.operations),
                transaction_info.transaction_id
            ))
    
    def _log_transaction_operation(self, transaction_id: str, operation: str, table_name: str):
        """记录事务操作"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO transaction_operations 
                (transaction_id, timestamp, operation, table_name)
                VALUES (?, ?, ?, ?)
            """, (
                transaction_id,
                datetime.now().isoformat(),
                operation,
                table_name
            ))
    
    def get_transaction_info(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """获取事务信息"""
        if transaction_id not in self._transactions:
            return None
        
        return self._transactions[transaction_id].to_dict()
    
    def get_active_transactions(self) -> List[Dict[str, Any]]:
        """获取活跃事务列表"""
        with self._manager_lock:
            return [info.to_dict() for info in self._transactions.values()]
    
    def get_transaction_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取事务统计信息"""
        start_time = datetime.now() - timedelta(hours=hours)
        
        with sqlite3.connect(self.db_path) as conn:
            # 事务状态统计
            cursor = conn.execute("""
                SELECT status, COUNT(*) as count
                FROM transaction_log 
                WHERE start_time >= ?
                GROUP BY status
            """, (start_time.isoformat(),))
            status_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            # 平均事务持续时间
            cursor = conn.execute("""
                SELECT AVG(
                    CASE 
                        WHEN end_time IS NOT NULL THEN 
                            (julianday(end_time) - julianday(start_time)) * 86400
                        ELSE NULL
                    END
                ) as avg_duration
                FROM transaction_log 
                WHERE start_time >= ? AND end_time IS NOT NULL
            """, (start_time.isoformat(),))
            avg_duration = cursor.fetchone()[0] or 0
            
            # 操作类型统计
            cursor = conn.execute("""
                SELECT operation, COUNT(*) as count
                FROM transaction_operations 
                WHERE timestamp >= ?
                GROUP BY operation
            """, (start_time.isoformat(),))
            operation_stats = {row[0]: row[1] for row in cursor.fetchall()}
            
            return {
                'status_distribution': status_stats,
                'average_duration_seconds': avg_duration,
                'operation_distribution': operation_stats,
                'active_transactions': len(self._transactions),
                'lock_info': self._lock_manager.get_lock_info(),
                'time_range_hours': hours
            }
    
    @contextmanager
    def transaction(self, isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED):
        """
        事务上下文管理器
        
        Args:
            isolation_level: 隔离级别
            
        Yields:
            事务ID
        """
        transaction_id = self.begin_transaction(isolation_level)
        try:
            yield transaction_id
            self.commit_transaction(transaction_id)
        except Exception:
            self.rollback_transaction(transaction_id)
            raise

def transactional(transaction_manager: TransactionManager, 
                 isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED,
                 auto_retry: bool = False, max_retries: int = 3):
    """
    事务装饰器
    
    Args:
        transaction_manager: 事务管理器
        isolation_level: 隔离级别
        auto_retry: 是否自动重试
        max_retries: 最大重试次数
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    with transaction_manager.transaction(isolation_level) as transaction_id:
                        # 将事务ID传递给函数
                        if 'transaction_id' in func.__code__.co_varnames:
                            kwargs['transaction_id'] = transaction_id
                        return func(*args, **kwargs)
                
                except DatabaseException as e:
                    last_exception = e
                    if not auto_retry or attempt >= max_retries:
                        raise
                    
                    # 等待一段时间后重试
                    time.sleep(0.1 * (2 ** attempt))
            
            raise last_exception
        
        return wrapper
    return decorator

# 全局事务管理器实例
_global_transaction_manager = None

def get_transaction_manager() -> TransactionManager:
    """获取全局事务管理器实例"""
    global _global_transaction_manager
    if _global_transaction_manager is None:
        _global_transaction_manager = TransactionManager("exam_management.db")
    return _global_transaction_manager

@contextmanager
def transaction(isolation_level: IsolationLevel = IsolationLevel.READ_COMMITTED):
    """全局事务上下文管理器"""
    tm = get_transaction_manager()
    with tm.transaction(isolation_level) as transaction_id:
        yield transaction_id

# 使用示例
if __name__ == "__main__":
    # 创建事务管理器
    tm = TransactionManager("test_transactions.db")
    
    # 使用上下文管理器
    try:
        with tm.transaction() as transaction_id:
            cursor = tm.execute_in_transaction(
                transaction_id, 
                "CREATE TABLE IF NOT EXISTS test (id INTEGER, name TEXT)",
                table_name="test"
            )
            
            cursor = tm.execute_in_transaction(
                transaction_id,
                "INSERT INTO test (id, name) VALUES (?, ?)",
                (1, "测试数据"),
                table_name="test"
            )
            
            print("事务执行成功")
    
    except Exception as e:
        print(f"事务执行失败: {e}")
    
    # 查看统计信息
    stats = tm.get_transaction_statistics()
    print(f"事务统计: {stats}")