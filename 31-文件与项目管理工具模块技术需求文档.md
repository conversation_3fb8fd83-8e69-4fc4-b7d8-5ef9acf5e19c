# 嵌入"文件与项目管理工具模块"技术需求文档

## 1. 概述

本文档明确了在局域网在线考试系统中嵌入"文件与项目管理工具模块"的技术需求。该模块主要提供简单的项目进度管理和OFFICE文件传输管理功能，支持文件共享、简单搜索和链接功能。

## 2. 功能需求

### 2.1 文件管理功能

#### 2.1.1 文件上传与下载
- 支持OFFICE文件（Word、Excel、PowerPoint、PDF等）的上传和下载
- 文件大小限制：单个文件不超过100MB
- 支持批量文件上传（最多10个文件）
- 提供文件上传进度显示
- 支持断点续传功能

#### 2.1.2 文件存储与组织
- 按项目分类存储文件
- 支持文件夹层级结构（最多3层）
- 文件版本管理（保留最近5个版本）
- 文件重命名、移动、删除功能
- 文件标签管理（支持自定义标签）

#### 2.1.3 文件搜索功能
- 按文件名进行模糊搜索
- 按文件类型筛选
- 按上传时间范围搜索
- 按项目分类搜索
- 按标签搜索

#### 2.1.4 文件共享与权限
- 文件共享链接生成（支持密码保护）
- 文件访问权限控制（只读、读写、下载）
- 文件分享记录查看
- 支持文件评论功能

### 2.2 项目管理功能

#### 2.2.1 项目基础管理
- 项目创建、编辑、删除
- 项目基本信息管理（名称、描述、负责人、开始/结束时间）
- 项目状态管理（未开始、进行中、已完成、已暂停）
- 项目成员管理（添加、移除成员，设置角色权限）

#### 2.2.2 任务管理
- 任务创建、编辑、删除
- 任务基本信息（标题、描述、负责人、优先级、截止时间）
- 任务状态跟踪（待办、进行中、已完成）
- 任务依赖关系设置（简单的前置任务关系）
- 任务进度更新（百分比进度）

#### 2.2.3 项目进度跟踪
- 项目整体进度展示（基于任务完成情况）
- 任务列表视图（支持按状态、负责人、优先级筛选）
- 项目时间线视图（简单的时间轴展示）
- 进度报告生成（周报、月报）

#### 2.2.4 协作功能
- 项目动态通知（任务分配、状态变更、文件上传等）
- 项目讨论区（简单的留言板功能）
- 文件与任务关联（任务可关联相关文件）
- 项目成员在线状态显示

## 3. 非功能需求

### 3.1 性能要求
- 文件上传速度：局域网环境下不低于10MB/s
- 页面响应时间：不超过2秒
- 并发用户数：支持50个用户同时在线
- 文件搜索响应时间：不超过1秒

### 3.2 可用性要求
- 系统可用性：99%以上
- 界面友好，操作简单直观
- 支持主流浏览器（Chrome、Firefox、Edge）
- 响应式设计，支持不同屏幕尺寸

### 3.3 安全性要求
- 文件传输加密（HTTPS）
- 用户身份认证（集成现有用户系统）
- 文件访问权限控制
- 操作日志记录
- 文件病毒扫描（可选）

### 3.4 兼容性要求
- 与现有考试系统无缝集成
- 不影响现有系统性能
- 支持现有用户权限体系
- 数据格式兼容（JSON/SQLite）

## 4. 接口需求

### 4.1 文件管理接口

#### 4.1.1 文件上传接口
```
POST /api/v1/files/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

参数：
- file: 文件对象
- project_id: 项目ID（可选）
- folder_id: 文件夹ID（可选）
- tags: 标签列表（可选）

返回：
{
  "code": 200,
  "msg": "上传成功",
  "data": {
    "file_id": "uuid",
    "file_name": "document.docx",
    "file_size": 1024000,
    "upload_time": "2024-01-01 12:00:00",
    "file_url": "/files/download/uuid"
  }
}
```

#### 4.1.2 文件列表接口
```
GET /api/v1/files/list
Authorization: Bearer {token}

参数：
- project_id: 项目ID（可选）
- folder_id: 文件夹ID（可选）
- keyword: 搜索关键词（可选）
- file_type: 文件类型（可选）
- page: 页码（默认1）
- limit: 每页数量（默认20）

返回：
{
  "code": 200,
  "msg": "获取成功",
  "data": {
    "total": 100,
    "files": [
      {
        "file_id": "uuid",
        "file_name": "document.docx",
        "file_size": 1024000,
        "file_type": "docx",
        "upload_time": "2024-01-01 12:00:00",
        "uploader": "张三",
        "download_count": 5,
        "tags": ["重要", "草稿"]
      }
    ]
  }
}
```

#### 4.1.3 文件下载接口
```
GET /api/v1/files/download/{file_id}
Authorization: Bearer {token}

返回：文件流
```

### 4.2 项目管理接口

#### 4.2.1 项目创建接口
```
POST /api/v1/projects/create
Content-Type: application/json
Authorization: Bearer {token}

参数：
{
  "project_name": "项目名称",
  "description": "项目描述",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "manager_id": "用户ID",
  "members": ["用户ID1", "用户ID2"]
}

返回：
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "project_id": "uuid",
    "project_name": "项目名称",
    "create_time": "2024-01-01 12:00:00"
  }
}
```

#### 4.2.2 任务管理接口
```
POST /api/v1/tasks/create
Content-Type: application/json
Authorization: Bearer {token}

参数：
{
  "project_id": "项目ID",
  "task_title": "任务标题",
  "description": "任务描述",
  "assignee_id": "负责人ID",
  "priority": "high|medium|low",
  "due_date": "2024-01-15",
  "parent_task_id": "前置任务ID（可选）"
}

返回：
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "task_id": "uuid",
    "task_title": "任务标题",
    "status": "pending",
    "create_time": "2024-01-01 12:00:00"
  }
}
```

## 5. 数据需求

### 5.1 数据库表设计

#### 5.1.1 项目表（projects）
```sql
CREATE TABLE projects (
    project_id VARCHAR(36) PRIMARY KEY,
    project_name VARCHAR(100) NOT NULL,
    description TEXT,
    manager_id VARCHAR(36) NOT NULL,
    start_date DATE,
    end_date DATE,
    status VARCHAR(20) DEFAULT 'active',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 5.1.2 任务表（tasks）
```sql
CREATE TABLE tasks (
    task_id VARCHAR(36) PRIMARY KEY,
    project_id VARCHAR(36) NOT NULL,
    task_title VARCHAR(200) NOT NULL,
    description TEXT,
    assignee_id VARCHAR(36),
    priority VARCHAR(10) DEFAULT 'medium',
    status VARCHAR(20) DEFAULT 'pending',
    progress INTEGER DEFAULT 0,
    due_date DATE,
    parent_task_id VARCHAR(36),
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(project_id),
    FOREIGN KEY (parent_task_id) REFERENCES tasks(task_id)
);
```

#### 5.1.3 文件表（files）
```sql
CREATE TABLE files (
    file_id VARCHAR(36) PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50),
    mime_type VARCHAR(100),
    project_id VARCHAR(36),
    folder_id VARCHAR(36),
    uploader_id VARCHAR(36) NOT NULL,
    download_count INTEGER DEFAULT 0,
    is_deleted BOOLEAN DEFAULT FALSE,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(project_id)
);
```

#### 5.1.4 文件标签表（file_tags）
```sql
CREATE TABLE file_tags (
    tag_id VARCHAR(36) PRIMARY KEY,
    file_id VARCHAR(36) NOT NULL,
    tag_name VARCHAR(50) NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES files(file_id)
);
```

### 5.2 数据存储要求
- 使用SQLite数据库存储结构化数据
- 文件存储在本地文件系统
- 支持数据备份和恢复
- 数据保留期：项目数据保留2年，文件数据保留1年

## 6. 技术实现方案

### 6.1 后端技术栈
- **框架**：Python Flask（轻量级，易于集成）
- **数据库**：SQLite（符合系统要求）
- **文件存储**：本地文件系统
- **认证**：集成现有Token认证机制
- **API文档**：Swagger/OpenAPI

### 6.2 前端技术栈
- **基础技术**：HTML5 + CSS3 + JavaScript（原生）
- **UI框架**：Bootstrap 4（响应式设计）
- **文件上传**：支持拖拽上传、进度显示
- **图表展示**：Chart.js（项目进度图表）

### 6.3 系统集成方案
- **模块化设计**：独立的Flask应用，通过API网关集成
- **路由配置**：`/api/v1/filemanager/*` 和 `/api/v1/projects/*`
- **权限集成**：复用现有用户管理模块的认证和授权
- **数据隔离**：独立的数据库表，不影响现有数据

### 6.4 部署方案
- **部署方式**：作为独立服务部署，通过API网关统一访问
- **文件存储路径**：`/uploads/filemanager/`
- **配置管理**：独立的配置文件，支持环境变量覆盖
- **日志管理**：集成到现有日志系统

## 7. 安全性设计

### 7.1 文件安全
- 文件类型白名单验证
- 文件大小限制
- 文件名安全检查（防止路径遍历）
- 文件存储路径随机化

### 7.2 访问控制
- 基于Token的身份认证
- 项目级别的权限控制
- 文件访问权限验证
- 操作日志记录

### 7.3 数据保护
- 敏感数据加密存储
- 文件传输HTTPS加密
- 定期数据备份
- 数据访问审计

## 8. 性能优化

### 8.1 文件处理优化
- 文件分块上传
- 断点续传支持
- 文件缓存机制
- 异步文件处理

### 8.2 数据库优化
- 关键字段索引
- 查询结果缓存
- 分页查询优化
- 数据库连接池

### 8.3 前端优化
- 文件列表虚拟滚动
- 图片缩略图生成
- 前端资源压缩
- CDN加速（可选）

## 9. 实施计划

### 9.1 开发阶段（4周）
- **第1周**：数据库设计、基础框架搭建
- **第2周**：文件管理功能开发
- **第3周**：项目管理功能开发
- **第4周**：前端界面开发、系统集成

### 9.2 测试阶段（2周）
- **第5周**：功能测试、性能测试
- **第6周**：集成测试、用户验收测试

### 9.3 部署阶段（1周）
- **第7周**：生产环境部署、数据迁移、上线验证

## 10. 风险评估与应对

### 10.1 技术风险
- **风险**：文件上传性能问题
- **应对**：采用分块上传、异步处理

### 10.2 集成风险
- **风险**：与现有系统集成冲突
- **应对**：独立部署、API网关路由隔离

### 10.3 数据风险
- **风险**：文件丢失或损坏
- **应对**：定期备份、文件完整性校验

## 11. 后续扩展

### 11.1 功能扩展
- 文件在线预览（Office文件）
- 文件协同编辑
- 更丰富的项目报表
- 移动端支持

### 11.2 性能扩展
- 分布式文件存储
- 数据库读写分离
- 缓存集群
- 负载均衡

---

**文档版本**：v2.0  
**最后更新**：2024年1月  
**文档状态**：已确认