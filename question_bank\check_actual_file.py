import pandas as pd
import os

def check_actual_excel_structure():
    """检查用户实际使用的Excel文件结构"""
    # 检查uploads目录中的文件
    uploads_dir = "d:/61-PHRL_question_bank/uploads"
    
    print("检查uploads目录中的Excel文件:")
    for file in os.listdir(uploads_dir):
        if file.endswith(('.xlsx', '.xls')):
            newline = '\n'
print(f"{newline}文件: {file}")
            filepath = os.path.join(uploads_dir, file)
            
            try:
                # 读取前10行，不设置表头
                df = pd.read_excel(filepath, engine='openpyxl', nrows=10, header=None)
                print(f"前10行内容:")
                for i in range(min(10, len(df))):
                    row_values = [str(val) for val in df.iloc[i].values if pd.notna(val)]
                    print(f"第{i+1}行: {row_values}")
                    
                    # 检查是否包含中文表头关键词
                    header_keywords = ['题库名称', '试题ID', '题型代码', '试题（题干）']
                    english_keywords = ['question_bank_name', 'question_id', 'question_type_code']
                    
                    row_text = ' '.join(row_values)
                    has_chinese = any(keyword in row_text for keyword in header_keywords)
                    has_english = any(keyword.lower() in row_text.lower() for keyword in english_keywords)
                    
                    if has_chinese or has_english:
                        print(f"  -> 包含表头关键词 (中文: {has_chinese}, 英文: {has_english})")
                    else:
                        print(f"  -> 可能是数据行")
                        
            except Exception as e:
                print(f"读取文件失败: {e}")

if __name__ == "__main__":
    check_actual_excel_structure()