#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
职业技能等级考试系统 - 主启动入口

新一代系统启动器，提供：
- 快速并行启动
- 智能依赖管理
- 实时状态监控
- 友好用户界面
- 完整错误处理

使用方法：
    python start.py              # 启动所有模块
    python start.py --modules user_management,question_bank  # 启动指定模块
    python start.py --dev        # 开发模式启动
    python start.py --status     # 查看系统状态
    python start.py --stop       # 停止所有服务
    python start.py --restart    # 重启系统

作者：系统架构师
版本：2.0.0
日期：2024-01-15
"""

import os
import sys
import time
import signal
import argparse
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from launcher.optimized_launcher import OptimizedLauncher
    from launcher.startup_manager import StartupManager
    from launcher.health_monitor import HealthMonitor
    from utils.logger import init_logging, get_logger, shutdown_logging
    from utils.performance import PerformanceAnalyzer
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有依赖模块已正确安装")
    sys.exit(1)


class SystemController:
    """
    系统控制器
    
    负责整个系统的启动、停止、监控和管理
    """
    
    def __init__(self):
        """初始化系统控制器"""
        self.launcher: Optional[OptimizedLauncher] = None
        self.startup_manager: Optional[StartupManager] = None
        self.health_monitor: Optional[HealthMonitor] = None
        self.performance_analyzer: Optional[PerformanceAnalyzer] = None
        self.logger = None
        self.shutdown_event = threading.Event()
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n🛑 接收到停止信号，正在优雅关闭系统...")
        self.shutdown_event.set()
        self.stop_system()
    
    def _init_logging(self, dev_mode: bool = False):
        """初始化日志系统"""
        log_config = {
            'level': 'DEBUG' if dev_mode else 'INFO',
            'log_dir': 'logs',
            'console_output': True,
            'file_output': True,
            'console_format': 'simple' if dev_mode else 'structured',
            'console_level': 'DEBUG' if dev_mode else 'INFO',
            'file': {
                'filename': 'system.log',
                'format': 'structured',
                'level': 'DEBUG',
                'max_size': 10 * 1024 * 1024,  # 10MB
                'backup_count': 5,
                'compress': True
            },
            'enable_performance_logging': True,
            'enable_error_tracking': True,
            'modules': {
                'startup': {
                    'separate_file': True,
                    'level': 'DEBUG',
                    'max_size': 5 * 1024 * 1024
                },
                'health': {
                    'separate_file': True,
                    'level': 'INFO'
                }
            }
        }
        
        init_logging(log_config)
        self.logger = get_logger('system')
        self.logger.info("系统启动器初始化完成")
    
    def _print_banner(self):
        """打印系统横幅"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                职业技能等级考试系统 v2.0                      ║
║                Professional Skills Assessment System          ║
╠══════════════════════════════════════════════════════════════╣
║  🚀 新一代架构 | 🔧 模块化设计 | ⚡ 高性能启动 | 🛡️ 安全可靠  ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def _load_system_config(self) -> Dict[str, Any]:
        """加载系统配置"""
        config_file = project_root / 'config' / 'global.yaml'
        
        if not config_file.exists():
            self.logger.warning(f"配置文件不存在: {config_file}")
            return self._get_default_config()
        
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            self.logger.info("系统配置加载成功")
            return config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'system': {
                'name': '职业技能等级考试系统',
                'version': '2.0.0',
                'environment': 'production'
            },
            'startup': {
                'parallel_startup': True,
                'startup_timeout': 120,
                'health_check_interval': 30,
                'max_retry_attempts': 3
            },
            'api_gateway': {
                'host': '0.0.0.0',
                'port': 8080,
                'enable': True
            }
        }
    
    def start_system(self, modules: Optional[List[str]] = None, dev_mode: bool = False, 
                    background: bool = False) -> bool:
        """启动系统"""
        try:
            # 初始化日志系统
            self._init_logging(dev_mode)
            
            # 打印横幅
            if not background:
                self._print_banner()
            
            # 加载配置
            config = self._load_system_config()
            
            print("🔧 正在初始化系统组件...")
            
            # 初始化性能分析器
            self.performance_analyzer = PerformanceAnalyzer()
            start_time = time.time()
            
            # 初始化启动管理器
            self.startup_manager = StartupManager(config)
            
            # 初始化优化启动器
            self.launcher = OptimizedLauncher()
            
            # 初始化健康监控器
            self.health_monitor = HealthMonitor(config)
            
            print("✅ 系统组件初始化完成")
            
            # 启动系统
            print("🚀 正在启动系统模块...")
            
            success = self.launcher.start_system()
            
            if not success:
                print("❌ 系统启动失败")
                return False
            
            # 启动健康监控
            self.health_monitor.start_monitoring()
            
            # 记录启动性能
            startup_duration = time.time() - start_time
            self.performance_analyzer.record_metric(
                'system_startup',
                startup_duration,
                {'modules_count': len(modules) if modules else 'all'}
            )
            
            print(f"✅ 系统启动成功！耗时 {startup_duration:.2f} 秒")
            
            # 显示系统信息
            self._show_system_info()
            
            # 如果不是后台模式，进入监控循环
            if not background:
                self._monitoring_loop()
            
            return True
            
        except Exception as e:
            print(f"❌ 系统启动异常: {e}")
            if self.logger:
                self.logger.error(f"系统启动异常: {e}", exc_info=True)
            return False
    
    def stop_system(self) -> bool:
        """停止系统"""
        try:
            print("🛑 正在停止系统...")
            
            # 停止健康监控
            if self.health_monitor:
                self.health_monitor.stop_monitoring()
                print("✅ 健康监控已停止")
            
            # 停止所有模块
            if self.launcher:
                self.launcher.stop_system()
                print("✅ 所有模块已停止")
            
            # 生成性能报告
            if self.performance_analyzer:
                report = self.performance_analyzer.generate_report()
                print("\n📊 性能报告:")
                print(report)
            
            # 关闭日志系统
            if self.logger:
                self.logger.info("系统正常关闭")
            shutdown_logging()
            
            print("✅ 系统已完全停止")
            return True
            
        except Exception as e:
            print(f"❌ 停止系统时出错: {e}")
            return False
    
    def restart_system(self, modules: Optional[List[str]] = None, dev_mode: bool = False) -> bool:
        """重启系统"""
        print("🔄 正在重启系统...")
        
        # 先停止系统
        if not self.stop_system():
            print("❌ 停止系统失败，重启中止")
            return False
        
        # 等待一段时间
        time.sleep(2)
        
        # 重新启动
        return self.start_system(modules, dev_mode)
    
    def show_status(self) -> Dict[str, Any]:
        """显示系统状态"""
        try:
            status = {
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'system_running': False,
                'modules': {},
                'health': {},
                'performance': {}
            }
            
            if self.launcher:
                status['system_running'] = True
                status['modules'] = self.launcher.get_module_status()
            
            if self.health_monitor:
                status['health'] = self.health_monitor.get_health_report()
            
            if self.performance_analyzer:
                status['performance'] = self.performance_analyzer.get_current_metrics()
            
            # 打印状态信息
            print("\n" + "=" * 60)
            print("📊 系统状态报告")
            print("=" * 60)
            print(f"时间: {status['timestamp']}")
            print(f"系统运行: {'✅ 是' if status['system_running'] else '❌ 否'}")
            
            if status['modules']:
                print("\n模块状态:")
                for module_name, module_status in status['modules'].items():
                    status_icon = "✅" if module_status.get('running', False) else "❌"
                    port = module_status.get('port', 'N/A')
                    print(f"  {status_icon} {module_name} (端口: {port})")
            
            if status['health']:
                print("\n健康状态:")
                overall_health = status['health'].get('overall_status', 'unknown')
                health_icon = "✅" if overall_health == 'healthy' else "⚠️" if overall_health == 'warning' else "❌"
                print(f"  {health_icon} 整体状态: {overall_health}")
            
            print("=" * 60)
            
            return status
            
        except Exception as e:
            print(f"❌ 获取系统状态失败: {e}")
            return {'error': str(e)}
    
    def _show_system_info(self):
        """显示系统信息"""
        print("\n" + "=" * 60)
        print("🎯 系统访问信息")
        print("=" * 60)
        print("🌐 API网关: http://localhost:8080")
        print("📊 系统监控: http://localhost:8080/health")
        print("📋 模块状态: http://localhost:8080/status")
        print("\n💡 常用命令:")
        print("  python start.py --status    # 查看系统状态")
        print("  python start.py --stop      # 停止系统")
        print("  python start.py --restart   # 重启系统")
        print("  Ctrl+C                      # 优雅停止")
        print("=" * 60)
    
    def _monitoring_loop(self):
        """监控循环"""
        print("\n🔍 进入系统监控模式 (按 Ctrl+C 停止)")
        
        try:
            while not self.shutdown_event.is_set():
                # 每30秒检查一次系统状态
                self.shutdown_event.wait(30)
                
                if self.shutdown_event.is_set():
                    break
                
                # 检查系统健康状态
                if self.health_monitor:
                    health_report = self.health_monitor.get_health_report()
                    if health_report.get('overall_status') != 'healthy':
                        print(f"⚠️  系统健康状态异常: {health_report.get('overall_status')}")
                
        except KeyboardInterrupt:
            pass
        
        print("\n🛑 退出监控模式")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='职业技能等级考试系统启动器 v2.0',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python start.py                                    # 启动所有模块
  python start.py --modules user_management          # 启动指定模块
  python start.py --dev                              # 开发模式启动
  python start.py --status                           # 查看系统状态
  python start.py --stop                             # 停止所有服务
  python start.py --restart                          # 重启系统
  python start.py --background                       # 后台启动
        """
    )
    
    parser.add_argument(
        '--modules', '-m',
        type=str,
        help='指定要启动的模块，用逗号分隔 (例: user_management,question_bank)'
    )
    
    parser.add_argument(
        '--dev', '-d',
        action='store_true',
        help='开发模式启动 (详细日志输出)'
    )
    
    parser.add_argument(
        '--status', '-s',
        action='store_true',
        help='显示系统状态'
    )
    
    parser.add_argument(
        '--stop',
        action='store_true',
        help='停止所有服务'
    )
    
    parser.add_argument(
        '--restart', '-r',
        action='store_true',
        help='重启系统'
    )
    
    parser.add_argument(
        '--background', '-b',
        action='store_true',
        help='后台模式启动 (不进入监控循环)'
    )
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        help='指定配置文件路径'
    )
    
    parser.add_argument(
        '--version', '-v',
        action='version',
        version='职业技能等级考试系统 v2.0.0'
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 创建系统控制器
        controller = SystemController()
        
        # 处理不同的命令
        if args.status:
            # 显示状态
            controller.show_status()
            return 0
        
        elif args.stop:
            # 停止系统
            success = controller.stop_system()
            return 0 if success else 1
        
        elif args.restart:
            # 重启系统
            modules = args.modules.split(',') if args.modules else None
            success = controller.restart_system(modules, args.dev)
            return 0 if success else 1
        
        else:
            # 启动系统
            modules = args.modules.split(',') if args.modules else None
            success = controller.start_system(
                modules=modules,
                dev_mode=args.dev,
                background=args.background
            )
            return 0 if success else 1
    
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")
        return 0
    
    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())