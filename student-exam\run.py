# -*- coding: utf-8 -*-
"""
考生答题模块启动文件

功能说明:
- 启动Flask应用
- 初始化数据库
- 配置日志
- 设置运行参数

作者: 系统生成
创建时间: 2024
"""

import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from app import create_app, db
from config import get_config

def setup_logging(app):
    """
    设置日志配置
    
    参数:
        app: Flask应用实例
    """
    if not app.debug:
        # 创建日志目录
        log_dir = os.path.dirname(app.config['LOG_FILE'])
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 文件日志处理器
        file_handler = RotatingFileHandler(
            app.config['LOG_FILE'],
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(getattr(logging, app.config['LOG_LEVEL']))
        app.logger.addHandler(file_handler)
    
    # 控制台日志处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    ))
    console_handler.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
    app.logger.addHandler(console_handler)
    
    app.logger.setLevel(getattr(logging, app.config.get('LOG_LEVEL', 'INFO')))
    app.logger.info('考生答题模块启动')

def safe_print(text):
    """
    安全打印函数，处理Windows控制台编码问题
    
    参数:
        text (str): 要打印的文本
    """
    try:
        print(text)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_text = text.encode('ascii', 'ignore').decode('ascii')
        print(safe_text)

def create_tables(app):
    """
    创建数据库表
    
    参数:
        app: Flask应用实例
    """
    with app.app_context():
        try:
            db.create_all()
            safe_print('[*] 数据库表创建成功')
        except Exception as e:
            safe_print(f'[!] 数据库表创建失败: {str(e)}')
            sys.exit(1)

def main():
    """
    主函数
    """
    try:
        # 设置环境变量
        os.environ.setdefault('PYTHONIOENCODING', 'utf-8')
        os.environ.setdefault('PYTHONUTF8', '1')
        
        # 创建应用
        config_class = get_config()
        app = create_app(config_class)
        
        # 设置日志
        setup_logging(app)
        
        # 创建数据库表
        create_tables(app)
        
        # 打印启动信息
        safe_print('=' * 60)
        safe_print('考生答题模块 - 局域网在线考试系统')
        safe_print('=' * 60)
        safe_print(f'[*] 服务地址: http://0.0.0.0:5003')
        safe_print(f'[*] API文档: http://0.0.0.0:5003/docs/')
        safe_print(f'[*] 健康检查: http://0.0.0.0:5003/api/v1/health')
        safe_print(f'[*] 登录页面: http://0.0.0.0:5003/login')
        safe_print(f'[*] 考生控制台: http://0.0.0.0:5003/dashboard')
        safe_print(f'[*] 调试模式: {app.config["DEBUG"]}')
        safe_print(f'[*] 日志级别: {app.config["LOG_LEVEL"]}')
        safe_print('=' * 60)
        safe_print('[*] 按 Ctrl+C 停止服务')
        safe_print('=' * 60)
        
        # 启动应用
        app.run(
            host='0.0.0.0',
            port=5003,
            debug=app.config['DEBUG'],
            threaded=True
        )
        
    except KeyboardInterrupt:
        safe_print('\n[*] 服务已停止')
    except Exception as e:
        safe_print(f'[!] 启动失败: {str(e)}')
        sys.exit(1)

if __name__ == '__main__':
    main()