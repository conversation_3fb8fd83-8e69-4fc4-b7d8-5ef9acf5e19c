from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from flask_jwt_extended import jwt_required, get_jwt_identity
from sqlalchemy import or_
from app.models import db, User, UserRole, UserStatus
from app.utils import success_response, error_response, paginated_response, validate_user_data, validate_pagination_params

# 创建命名空间
users_ns = Namespace('users', description='用户管理相关接口')

# 定义请求模型
user_create_model = users_ns.model('UserCreateRequest', {
    'username': fields.String(required=True, description='用户名'),
    'email': fields.String(required=True, description='邮箱'),
    'password': fields.String(required=True, description='密码'),
    'real_name': fields.String(required=True, description='真实姓名'),
    'phone': fields.String(description='手机号'),
    'role': fields.String(description='用户角色'),
    'department': fields.String(description='部门'),
    'position': fields.String(description='职位')
})

user_update_model = users_ns.model('UserUpdateRequest', {
    'email': fields.String(description='邮箱'),
    'real_name': fields.String(description='真实姓名'),
    'phone': fields.String(description='手机号'),
    'role': fields.String(description='用户角色'),
    'status': fields.String(description='用户状态'),
    'department': fields.String(description='部门'),
    'position': fields.String(description='职位')
})

password_reset_model = users_ns.model('PasswordResetRequest', {
    'new_password': fields.String(required=True, description='新密码')
})

@users_ns.route('')
class UsersAPI(Resource):
    @users_ns.doc('get_users')
    @jwt_required()
    def get(self):
        """获取用户列表"""
        try:
            # 获取查询参数
            page = request.args.get('page', 1, type=int)
            page_size = request.args.get('page_size', 20, type=int)
            keyword = request.args.get('keyword', '').strip()
            role = request.args.get('role', '').strip()
            status = request.args.get('status', '').strip()
            department = request.args.get('department', '').strip()
            
            # 验证分页参数
            pagination_errors = validate_pagination_params(page, page_size)
            if pagination_errors:
                return error_response("参数验证失败", 400), 400
            
            # 构建查询
            query = User.query
            
            # 关键词搜索
            if keyword:
                query = query.filter(
                    or_(
                        User.username.contains(keyword),
                        User.email.contains(keyword),
                        User.real_name.contains(keyword)
                    )
                )
            
            # 角色筛选
            if role:
                try:
                    role_enum = UserRole(role)
                    query = query.filter(User.role == role_enum)
                except ValueError:
                    return error_response("无效的用户角色", 400), 400
            
            # 状态筛选
            if status:
                try:
                    status_enum = UserStatus(status)
                    query = query.filter(User.status == status_enum)
                except ValueError:
                    return error_response("无效的用户状态", 400), 400
            
            # 部门筛选
            if department:
                query = query.filter(User.department.contains(department))
            
            # 排序
            query = query.order_by(User.created_time.desc())
            
            # 分页
            total = query.count()
            users = query.offset((page - 1) * page_size).limit(page_size).all()
            
            # 转换为字典
            users_data = [user.to_dict() for user in users]
            
            return paginated_response(users_data, page, page_size, total), 200
            
        except Exception as e:
            current_app.logger.error(f"获取用户列表失败: {str(e)}")
            return error_response("获取用户列表失败", 500), 500
    
    @users_ns.doc('create_user')
    @users_ns.expect(user_create_model)
    @jwt_required()
    def post(self):
        """创建用户"""
        try:
            data = request.get_json()
            if not data:
                return error_response("请求数据不能为空", 400), 400
            
            # 验证数据
            errors = validate_user_data(data)
            if errors:
                return error_response("参数验证失败", 400), 400
            
            # 检查用户名是否已存在
            if User.query.filter_by(username=data['username']).first():
                return error_response("用户名已存在", 400), 400
            
            # 检查邮箱是否已存在
            if User.query.filter_by(email=data['email']).first():
                return error_response("邮箱已存在", 400), 400
            
            # 创建用户
            user = User(
                username=data['username'],
                email=data['email'],
                real_name=data['real_name'],
                phone=data.get('phone'),
                role=UserRole(data.get('role', 'student')),
                department=data.get('department'),
                position=data.get('position'),
                created_by=get_jwt_identity()
            )
            user.set_password(data['password'])
            
            db.session.add(user)
            db.session.commit()
            
            return success_response(user.to_dict(), "用户创建成功", 201), 201
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建用户失败: {str(e)}")
            return error_response("创建用户失败", 500), 500

@users_ns.route('/<int:user_id>')
class UserAPI(Resource):
    @users_ns.doc('get_user')
    @jwt_required()
    def get(self, user_id):
        """获取用户详情"""
        try:
            user = User.query.get(user_id)
            if not user:
                return error_response("用户不存在", 404), 404
            
            return success_response(user.to_dict(), "获取用户详情成功"), 200
            
        except Exception as e:
            current_app.logger.error(f"获取用户详情失败: {str(e)}")
            return error_response("获取用户详情失败", 500), 500
    
    @users_ns.doc('update_user')
    @users_ns.expect(user_update_model)
    @jwt_required()
    def put(self, user_id):
        """更新用户信息"""
        try:
            user = User.query.get(user_id)
            if not user:
                return error_response("用户不存在", 404), 404
            
            data = request.get_json()
            if not data:
                return error_response("请求数据不能为空", 400), 400
            
            # 验证数据（排除密码验证）
            errors = validate_user_data(data)
            if errors:
                return error_response("参数验证失败", 400), 400
            
            # 检查邮箱是否已被其他用户使用
            if 'email' in data:
                existing_user = User.query.filter(
                    User.email == data['email'],
                    User.id != user_id
                ).first()
                if existing_user:
                    return error_response("邮箱已被其他用户使用", 400), 400
            
            # 更新用户信息
            if 'email' in data:
                user.email = data['email']
            if 'real_name' in data:
                user.real_name = data['real_name']
            if 'phone' in data:
                user.phone = data['phone']
            if 'role' in data:
                user.role = UserRole(data['role'])
            if 'status' in data:
                user.status = UserStatus(data['status'])
            if 'department' in data:
                user.department = data['department']
            if 'position' in data:
                user.position = data['position']
            
            user.updated_by = get_jwt_identity()
            db.session.commit()
            
            return success_response(user.to_dict(), "用户信息更新成功"), 200
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新用户信息失败: {str(e)}")
            return error_response("更新用户信息失败", 500), 500
    
    @users_ns.doc('delete_user')
    @jwt_required()
    def delete(self, user_id):
        """删除用户"""
        try:
            user = User.query.get(user_id)
            if not user:
                return error_response("用户不存在", 404), 404
            
            # 软删除：更新状态为已删除
            user.status = UserStatus.DELETED
            user.updated_by = get_jwt_identity()
            db.session.commit()
            
            return success_response(None, "用户删除成功"), 200
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"删除用户失败: {str(e)}")
            return error_response("删除用户失败", 500), 500

@users_ns.route('/<int:user_id>/reset-password')
class UserPasswordResetAPI(Resource):
    @users_ns.doc('reset_user_password')
    @users_ns.expect(password_reset_model)
    @jwt_required()
    def post(self, user_id):
        """重置用户密码"""
        try:
            user = User.query.get(user_id)
            if not user:
                return error_response("用户不存在", 404), 404
            
            data = request.get_json()
            if not data:
                return error_response("请求数据不能为空", 400), 400
            
            new_password = data.get('new_password')
            if not new_password:
                return error_response("新密码不能为空", 400), 400
            
            if len(new_password) < 6 or len(new_password) > 128:
                return error_response("密码长度必须在6-128位之间", 400), 400
            
            # 重置密码
            user.set_password(new_password)
            user.updated_by = get_jwt_identity()
            db.session.commit()
            
            return success_response(None, "密码重置成功"), 200
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"重置密码失败: {str(e)}")
            return error_response("重置密码失败", 500), 500

@users_ns.route('/batch-import')
class UserBatchImportAPI(Resource):
    @users_ns.doc('batch_import_users')
    @jwt_required()
    def post(self):
        """批量导入用户"""
        try:
            # 检查是否有文件上传
            if 'file' not in request.files:
                return error_response("请选择要导入的文件", 400), 400
            
            file = request.files['file']
            if file.filename == '':
                return error_response("请选择要导入的文件", 400), 400
            
            # 检查文件类型
            allowed_extensions = {'.xlsx', '.xls', '.csv'}
            file_extension = '.' + file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
            
            if file_extension not in allowed_extensions:
                return error_response("不支持的文件格式，请选择Excel或CSV文件", 400), 400
            
            # 导入处理逻辑
            import pandas as pd
            import io
            
            try:
                # 读取文件内容
                if file_extension == '.csv':
                    # 尝试不同的编码方式
                    try:
                        df = pd.read_csv(io.StringIO(file.read().decode('utf-8')))
                    except UnicodeDecodeError:
                        file.seek(0)
                        try:
                            df = pd.read_csv(io.StringIO(file.read().decode('gbk')))
                        except UnicodeDecodeError:
                            file.seek(0)
                            df = pd.read_csv(io.StringIO(file.read().decode('utf-8-sig')))
                else:
                    df = pd.read_excel(file, engine='openpyxl')
                
                # 验证必需的列 - 适配实际文件格式
                required_columns = ['用户名', '姓名']
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    return error_response(f"缺少必需的列: {', '.join(missing_columns)}", 400), 400
                
                # 处理数据
                success_count = 0
                failed_count = 0
                errors = []
                total = len(df)
                
                for index, row in df.iterrows():
                    try:
                        # 验证必需字段 - 适配实际模板格式
                        if pd.isna(row['用户名']) or pd.isna(row['姓名']):
                            errors.append(f"第{index+2}行：用户名、姓名不能为空")
                            failed_count += 1
                            continue
                        
                        # 生成邮箱（如果没有邮箱列，使用用户名@example.com）
                        email = str(row.get('邮箱', '')).strip() if not pd.isna(row.get('邮箱')) else f"{str(row['用户名']).strip()}@example.com"
                        
                        # 检查用户名是否已存在
                        existing_user = User.query.filter(
                            (User.username == str(row['用户名']).strip()) | 
                            (User.email == email)
                        ).first()
                        
                        if existing_user:
                            errors.append(f"第{index+2}行：用户名或邮箱已存在")
                            failed_count += 1
                            continue
                        
                        # 创建新用户 - 适配实际模板字段
                        user_data = {
                            'username': str(row['用户名']).strip(),
                            'email': email,
                            'real_name': str(row['姓名']).strip(),
                            'phone': str(row.get('手机号码', '')).strip() if not pd.isna(row.get('手机号码')) else None,
                            'password': str(row.get('密码', '123456')).strip() if not pd.isna(row.get('密码')) else '123456',
                            'role': str(row.get('角色', 'student')).strip() if not pd.isna(row.get('角色')) else 'student',
                            'department': str(row.get('部门', '')).strip() if not pd.isna(row.get('部门')) else None,
                            'position': str(row.get('职位', '')).strip() if not pd.isna(row.get('职位')) else None
                        }
                        
                        # 验证角色 - 支持中文角色名称映射
                        role_mapping = {
                            '管理员': 'admin',
                            '专家': 'expert',
                            '考生': 'student',
                            '考评员': 'grader',
                            '内部督导员': 'internal_supervisor'
                        }
                        
                        role_value = role_mapping.get(user_data['role'], user_data['role'])
                        try:
                            role_enum = UserRole(role_value)
                        except ValueError:
                            errors.append(f"第{index+2}行：无效的角色值 '{user_data['role']}'，支持的角色：{list(role_mapping.keys())}")
                            failed_count += 1
                            continue
                        
                        # 创建用户对象
                        new_user = User(
                            username=user_data['username'],
                            email=user_data['email'],
                            real_name=user_data['real_name'],
                            phone=user_data['phone'],
                            role=role_enum,
                            department=user_data['department'],
                            position=user_data['position'],
                            created_by=get_jwt_identity()
                        )
                        new_user.set_password(user_data['password'])
                        
                        db.session.add(new_user)
                        success_count += 1
                        
                    except Exception as e:
                        errors.append(f"第{index+2}行：处理失败 - {str(e)}")
                        failed_count += 1
                        continue
                
                # 提交数据库更改
                db.session.commit()
                
                result = {
                    'total': total,
                    'success_count': success_count,
                    'failed_count': failed_count,
                    'errors': errors[:10]  # 只返回前10个错误
                }
                
                return success_response(result, "批量导入完成"), 200
                
            except Exception as e:
                db.session.rollback()
                return error_response(f"文件处理失败: {str(e)}", 400), 400
                
        except Exception as e:
            current_app.logger.error(f"批量导入用户失败: {str(e)}")
            return error_response("批量导入失败", 500), 500


@users_ns.route('/download-template')
class UserTemplateDownloadAPI(Resource):
    @users_ns.doc('download_user_template')
    @jwt_required()
    def get(self):
        """下载用户导入模板"""
        try:
            from flask import make_response, send_file
            import os
            
            # 使用预定义的模板文件
            template_path = os.path.join(current_app.static_folder, 'templates', '用户导入模板.xlsx')
            
            # 检查模板文件是否存在
            if not os.path.exists(template_path):
                current_app.logger.error(f"模板文件不存在: {template_path}")
                return error_response("模板文件不存在", 404), 404
            
            # 发送文件
            return send_file(
                template_path,
                as_attachment=True,
                download_name='用户导入模板.xlsx',
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            
        except Exception as e:
            current_app.logger.error(f"下载模板失败: {str(e)}")
            return error_response("下载模板失败", 500), 500


@users_ns.route('/batch-delete')
class UserBatchDeleteAPI(Resource):
    @users_ns.doc('batch_delete_users')
    @jwt_required()
    def post(self):
        """批量删除用户"""
        try:
            data = request.get_json()
            
            if not data or 'user_ids' not in data:
                return error_response("请提供要删除的用户ID列表", 400), 400
            
            user_ids = data['user_ids']
            
            if not isinstance(user_ids, list) or len(user_ids) == 0:
                return error_response("用户ID列表不能为空", 400), 400
            
            # 验证用户ID
            try:
                user_ids = [int(uid) for uid in user_ids]
            except (ValueError, TypeError):
                return error_response("无效的用户ID格式", 400), 400
            
            success_count = 0
            failed_count = 0
            errors = []
            
            for user_id in user_ids:
                try:
                    user = User.query.get(user_id)
                    if not user:
                        errors.append(f"用户ID {user_id} 不存在")
                        failed_count += 1
                        continue
                    
                    # 检查是否为当前用户
                    current_user_id = get_jwt_identity()
                    if user.id == current_user_id:
                        errors.append(f"不能删除当前登录用户")
                        failed_count += 1
                        continue
                    
                    # 删除用户
                    db.session.delete(user)
                    success_count += 1
                    
                except Exception as e:
                    errors.append(f"删除用户ID {user_id} 失败: {str(e)}")
                    failed_count += 1
                    continue
            
            # 提交数据库更改
            db.session.commit()
            
            result = {
                'success_count': success_count,
                'failed_count': failed_count,
                'errors': errors
            }
            
            return success_response(result, "批量删除完成"), 200
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"批量删除用户失败: {str(e)}")
            return error_response("批量删除失败", 500), 500


@users_ns.route('/batch-update')
class UserBatchUpdateAPI(Resource):
    @users_ns.doc('batch_update_users')
    @jwt_required()
    def post(self):
        """批量更新用户"""
        try:
            data = request.get_json()
            
            if not data or 'user_ids' not in data or 'update_data' not in data:
                return error_response("请提供要更新的用户ID列表和更新数据", 400), 400
            
            user_ids = data['user_ids']
            update_data = data['update_data']
            
            if not isinstance(user_ids, list) or len(user_ids) == 0:
                return error_response("用户ID列表不能为空", 400), 400
            
            if not isinstance(update_data, dict) or len(update_data) == 0:
                return error_response("更新数据不能为空", 400), 400
            
            # 验证用户ID
            try:
                user_ids = [int(uid) for uid in user_ids]
            except (ValueError, TypeError):
                return error_response("无效的用户ID格式", 400), 400
            
            # 验证更新数据中的字段
            allowed_fields = {
                'status', 'declared_occupation', 'declared_level', 
                'exam_type', 'assessment_subject', 'question_bank_name', 'test_paper_id'
            }
            
            invalid_fields = set(update_data.keys()) - allowed_fields
            if invalid_fields:
                return error_response(f"不允许更新的字段: {', '.join(invalid_fields)}", 400), 400
            
            # 验证状态值
            if 'status' in update_data:
                valid_statuses = [status.value for status in UserStatus]
                if update_data['status'] not in valid_statuses:
                    return error_response(f"无效的状态值，允许的值: {', '.join(valid_statuses)}", 400), 400
            
            success_count = 0
            failed_count = 0
            errors = []
            
            for user_id in user_ids:
                try:
                    user = User.query.get(user_id)
                    if not user:
                        errors.append(f"用户ID {user_id} 不存在")
                        failed_count += 1
                        continue
                    
                    # 更新用户字段
                    for field, value in update_data.items():
                        if hasattr(user, field):
                            # 特殊处理枚举字段
                            if field == 'status':
                                # 将字符串转换为枚举对象
                                status_enum = UserStatus(value)
                                setattr(user, field, status_enum)
                            else:
                                setattr(user, field, value)
                    
                    success_count += 1
                    
                except Exception as e:
                    errors.append(f"更新用户ID {user_id} 失败: {str(e)}")
                    failed_count += 1
                    continue
            
            # 提交数据库更改
            db.session.commit()
            
            result = {
                'success_count': success_count,
                'failed_count': failed_count,
                'errors': errors
            }
            
            return success_response(result, "批量更新完成"), 200
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"批量更新用户失败: {str(e)}")
            return error_response("批量更新失败", 500), 500