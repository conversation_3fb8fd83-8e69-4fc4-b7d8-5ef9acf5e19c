/**
 * 实操任务管理系统前端脚本
 * 提供通用的前端交互功能
 */

// 全局配置
const APP_CONFIG = {
    apiBaseUrl: '/api/v1',
    uploadMaxSize: 50 * 1024 * 1024, // 50MB
    allowedFileTypes: {
        image: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
        document: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
        archive: ['zip', 'rar', '7z', 'tar', 'gz'],
        video: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
        audio: ['mp3', 'wav', 'flac', 'aac', 'ogg']
    }
};

// 工具函数
const Utils = {
    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化日期时间
     * @param {string|Date} date 日期
     * @returns {string} 格式化后的日期时间
     */
    formatDateTime(date) {
        if (!date) return '-';
        const d = new Date(date);
        return d.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    /**
     * 获取文件扩展名
     * @param {string} filename 文件名
     * @returns {string} 文件扩展名
     */
    getFileExtension(filename) {
        return filename.split('.').pop().toLowerCase();
    },

    /**
     * 获取文件类型图标
     * @param {string} filename 文件名
     * @returns {string} 图标类名
     */
    getFileIcon(filename) {
        const ext = this.getFileExtension(filename);
        const iconMap = {
            // 图片
            jpg: 'bi-file-earmark-image', jpeg: 'bi-file-earmark-image',
            png: 'bi-file-earmark-image', gif: 'bi-file-earmark-image',
            bmp: 'bi-file-earmark-image', webp: 'bi-file-earmark-image',
            
            // 文档
            pdf: 'bi-file-earmark-pdf', doc: 'bi-file-earmark-word',
            docx: 'bi-file-earmark-word', xls: 'bi-file-earmark-excel',
            xlsx: 'bi-file-earmark-excel', ppt: 'bi-file-earmark-ppt',
            pptx: 'bi-file-earmark-ppt', txt: 'bi-file-earmark-text',
            
            // 压缩包
            zip: 'bi-file-earmark-zip', rar: 'bi-file-earmark-zip',
            '7z': 'bi-file-earmark-zip', tar: 'bi-file-earmark-zip',
            gz: 'bi-file-earmark-zip',
            
            // 视频
            mp4: 'bi-file-earmark-play', avi: 'bi-file-earmark-play',
            mov: 'bi-file-earmark-play', wmv: 'bi-file-earmark-play',
            flv: 'bi-file-earmark-play', mkv: 'bi-file-earmark-play',
            
            // 音频
            mp3: 'bi-file-earmark-music', wav: 'bi-file-earmark-music',
            flac: 'bi-file-earmark-music', aac: 'bi-file-earmark-music',
            ogg: 'bi-file-earmark-music'
        };
        
        return iconMap[ext] || 'bi-file-earmark';
    },

    /**
     * 验证文件类型
     * @param {string} filename 文件名
     * @returns {boolean} 是否为允许的文件类型
     */
    validateFileType(filename) {
        const ext = this.getFileExtension(filename);
        const allTypes = Object.values(APP_CONFIG.allowedFileTypes).flat();
        return allTypes.includes(ext);
    },

    /**
     * 防抖函数
     * @param {Function} func 要防抖的函数
     * @param {number} wait 等待时间
     * @returns {Function} 防抖后的函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 节流函数
     * @param {Function} func 要节流的函数
     * @param {number} limit 限制时间
     * @returns {Function} 节流后的函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 生成随机ID
     * @returns {string} 随机ID
     */
    generateId() {
        return Math.random().toString(36).substr(2, 9);
    },

    /**
     * 深拷贝对象
     * @param {any} obj 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone(obj) {
        return JSON.parse(JSON.stringify(obj));
    }
};

// API 请求封装
const API = {
    /**
     * 发送 GET 请求
     * @param {string} url 请求URL
     * @param {Object} params 查询参数
     * @returns {Promise} 请求Promise
     */
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        try {
            const response = await fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return await response.json();
        } catch (error) {
            console.error('GET request failed:', error);
            throw error;
        }
    },

    /**
     * 发送 POST 请求
     * @param {string} url 请求URL
     * @param {Object} data 请求数据
     * @returns {Promise} 请求Promise
     */
    async post(url, data = {}) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            return await response.json();
        } catch (error) {
            console.error('POST request failed:', error);
            throw error;
        }
    },

    /**
     * 发送 PUT 请求
     * @param {string} url 请求URL
     * @param {Object} data 请求数据
     * @returns {Promise} 请求Promise
     */
    async put(url, data = {}) {
        try {
            const response = await fetch(url, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            return await response.json();
        } catch (error) {
            console.error('PUT request failed:', error);
            throw error;
        }
    },

    /**
     * 发送 DELETE 请求
     * @param {string} url 请求URL
     * @returns {Promise} 请求Promise
     */
    async delete(url) {
        try {
            const response = await fetch(url, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            return await response.json();
        } catch (error) {
            console.error('DELETE request failed:', error);
            throw error;
        }
    },

    /**
     * 上传文件
     * @param {string} url 上传URL
     * @param {FormData} formData 表单数据
     * @param {Function} onProgress 进度回调
     * @returns {Promise} 请求Promise
     */
    async upload(url, formData, onProgress = null) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            if (onProgress) {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }
            
            // 监听请求完成
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('Invalid JSON response'));
                    }
                } else {
                    reject(new Error(`Upload failed with status ${xhr.status}`));
                }
            });
            
            // 监听请求错误
            xhr.addEventListener('error', () => {
                reject(new Error('Upload failed'));
            });
            
            // 发送请求
            xhr.open('POST', url);
            xhr.send(formData);
        });
    }
};

// 消息提示
const Message = {
    /**
     * 显示成功消息
     * @param {string} text 消息文本
     * @param {number} duration 显示时长
     */
    success(text, duration = 3000) {
        this.show(text, 'success', duration);
    },

    /**
     * 显示错误消息
     * @param {string} text 消息文本
     * @param {number} duration 显示时长
     */
    error(text, duration = 5000) {
        this.show(text, 'danger', duration);
    },

    /**
     * 显示警告消息
     * @param {string} text 消息文本
     * @param {number} duration 显示时长
     */
    warning(text, duration = 4000) {
        this.show(text, 'warning', duration);
    },

    /**
     * 显示信息消息
     * @param {string} text 消息文本
     * @param {number} duration 显示时长
     */
    info(text, duration = 3000) {
        this.show(text, 'info', duration);
    },

    /**
     * 显示消息
     * @param {string} text 消息文本
     * @param {string} type 消息类型
     * @param {number} duration 显示时长
     */
    show(text, type = 'info', duration = 3000) {
        // 创建消息容器
        let container = document.getElementById('message-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'message-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
            `;
            document.body.appendChild(container);
        }

        // 创建消息元素
        const messageId = Utils.generateId();
        const messageEl = document.createElement('div');
        messageEl.id = messageId;
        messageEl.className = `alert alert-${type} alert-dismissible fade show`;
        messageEl.style.cssText = `
            margin-bottom: 10px;
            animation: slideInRight 0.3s ease-out;
        `;
        messageEl.innerHTML = `
            ${text}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 添加到容器
        container.appendChild(messageEl);

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.remove(messageId);
            }, duration);
        }
    },

    /**
     * 移除消息
     * @param {string} messageId 消息ID
     */
    remove(messageId) {
        const messageEl = document.getElementById(messageId);
        if (messageEl) {
            messageEl.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                messageEl.remove();
            }, 300);
        }
    },

    /**
     * 清除所有消息
     */
    clear() {
        const container = document.getElementById('message-container');
        if (container) {
            container.innerHTML = '';
        }
    }
};

// 确认对话框
const Confirm = {
    /**
     * 显示确认对话框
     * @param {string} title 标题
     * @param {string} message 消息
     * @param {Function} onConfirm 确认回调
     * @param {Function} onCancel 取消回调
     */
    show(title, message, onConfirm = null, onCancel = null) {
        // 创建模态框
        const modalId = 'confirm-modal-' + Utils.generateId();
        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <p>${message}</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="${modalId}-confirm">确认</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modalEl = document.getElementById(modalId);
        const modal = new bootstrap.Modal(modalEl);

        // 绑定事件
        const confirmBtn = document.getElementById(modalId + '-confirm');
        confirmBtn.addEventListener('click', () => {
            modal.hide();
            if (onConfirm) onConfirm();
        });

        modalEl.addEventListener('hidden.bs.modal', () => {
            modalEl.remove();
            if (onCancel) onCancel();
        });

        // 显示模态框
        modal.show();
    }
};

// 加载指示器
const Loading = {
    /**
     * 显示加载指示器
     * @param {string} target 目标元素选择器
     * @param {string} text 加载文本
     */
    show(target = 'body', text = '加载中...') {
        const targetEl = typeof target === 'string' ? document.querySelector(target) : target;
        if (!targetEl) return;

        const loadingId = 'loading-' + Utils.generateId();
        const loadingHtml = `
            <div id="${loadingId}" class="loading-overlay">
                <div class="loading-content">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">${text}</div>
                </div>
            </div>
        `;

        targetEl.style.position = 'relative';
        targetEl.insertAdjacentHTML('beforeend', loadingHtml);

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .loading-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 255, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            }
            .loading-content {
                text-align: center;
                color: #6c757d;
            }
        `;
        document.head.appendChild(style);

        return loadingId;
    },

    /**
     * 隐藏加载指示器
     * @param {string} loadingId 加载指示器ID
     */
    hide(loadingId) {
        const loadingEl = document.getElementById(loadingId);
        if (loadingEl) {
            loadingEl.remove();
        }
    }
};

// 表单验证
const Validator = {
    /**
     * 验证必填字段
     * @param {string} value 值
     * @returns {boolean} 是否有效
     */
    required(value) {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    },

    /**
     * 验证邮箱格式
     * @param {string} email 邮箱
     * @returns {boolean} 是否有效
     */
    email(email) {
        const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return pattern.test(email);
    },

    /**
     * 验证手机号格式
     * @param {string} phone 手机号
     * @returns {boolean} 是否有效
     */
    phone(phone) {
        const pattern = /^1[3-9]\d{9}$/;
        return pattern.test(phone);
    },

    /**
     * 验证长度范围
     * @param {string} value 值
     * @param {number} min 最小长度
     * @param {number} max 最大长度
     * @returns {boolean} 是否有效
     */
    length(value, min, max) {
        const len = value ? value.toString().length : 0;
        return len >= min && len <= max;
    },

    /**
     * 验证数字范围
     * @param {number} value 值
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @returns {boolean} 是否有效
     */
    range(value, min, max) {
        const num = parseFloat(value);
        return !isNaN(num) && num >= min && num <= max;
    }
};

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});

// 导出全局对象
window.Utils = Utils;
window.API = API;
window.Message = Message;
window.Confirm = Confirm;
window.Loading = Loading;
window.Validator = Validator;
window.APP_CONFIG = APP_CONFIG;