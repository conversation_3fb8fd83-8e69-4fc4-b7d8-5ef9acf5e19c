# -*- coding: utf-8 -*-
"""
考试通知模块 - 数据库模型
定义考试通知相关的数据表结构和操作方法
"""

import sqlite3
import json
from datetime import datetime, timezone
from enum import Enum
from typing import List, Dict, Optional, Any
from transaction_manager import TransactionManager, transaction

class NotificationStatus(Enum):
    """通知状态枚举"""
    DRAFT = "draft"          # 草稿
    SENT = "sent"            # 已发送
    READ = "read"            # 已读
    CANCELLED = "cancelled"  # 已取消

class NotificationType(Enum):
    """通知类型枚举"""
    EXAM_PUBLISHED = "exam_published"    # 考试发布通知
    EXAM_REMINDER = "exam_reminder"      # 考试提醒
    EXAM_CANCELLED = "exam_cancelled"    # 考试取消通知
    EXAM_UPDATED = "exam_updated"        # 考试更新通知
    RESULT_AVAILABLE = "result_available" # 成绩发布通知

class NotificationModel:
    """考试通知数据模型
    
    提供考试通知相关的数据库操作方法
    """
    
    def __init__(self, db_manager, transaction_manager: TransactionManager = None):
        """
        初始化通知模型
        
        Args:
            db_manager: 数据库管理器实例
            transaction_manager: 事务管理器实例
        """
        self.db = db_manager
        self.transaction_manager = transaction_manager
        self.init_notification_tables()
    
    def init_notification_tables(self):
        """初始化通知相关数据表"""
        with self.db.get_connection() as conn:
            # 创建考试通知表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS exam_notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    exam_id INTEGER NOT NULL,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    notification_type TEXT NOT NULL,
                    status TEXT NOT NULL DEFAULT 'draft',
                    target_users TEXT,  -- JSON数组，存储目标用户ID
                    target_roles TEXT,  -- JSON数组，存储目标角色
                    send_time TEXT,     -- 发送时间
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    created_by INTEGER,
                    FOREIGN KEY (exam_id) REFERENCES exams (id) ON DELETE CASCADE
                )
            """)
            
            # 创建通知接收记录表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS notification_recipients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    notification_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    status TEXT NOT NULL DEFAULT 'sent',
                    read_time TEXT,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (notification_id) REFERENCES exam_notifications (id) ON DELETE CASCADE,
                    UNIQUE(notification_id, user_id)
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_notifications_exam_id ON exam_notifications(exam_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_notifications_status ON exam_notifications(status)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_recipients_notification_id ON notification_recipients(notification_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_recipients_user_id ON notification_recipients(user_id)")
            
            conn.commit()
    
    def create_notification(self, notification_data: Dict[str, Any]) -> int:
        """
        创建考试通知
        
        Args:
            notification_data: 通知数据字典
            
        Returns:
            int: 创建的通知ID
        """
        now = self.db.get_utc_now_iso()
        
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                INSERT INTO exam_notifications (
                    exam_id, title, content, notification_type, status,
                    target_users, target_roles, send_time, created_at, updated_at, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                notification_data['exam_id'],
                notification_data['title'],
                notification_data['content'],
                notification_data['notification_type'],
                notification_data.get('status', NotificationStatus.DRAFT.value),
                json.dumps(notification_data.get('target_users', [])),
                json.dumps(notification_data.get('target_roles', [])),
                notification_data.get('send_time'),
                now,
                now,
                notification_data.get('created_by')
            ))
            
            notification_id = cursor.lastrowid
            conn.commit()
            
            return notification_id
    
    def send_notification(self, notification_id: int, recipient_user_ids: List[int]) -> bool:
        """
        发送通知给指定用户
        
        Args:
            notification_id: 通知ID
            recipient_user_ids: 接收用户ID列表
            
        Returns:
            bool: 发送是否成功
        """
        now = self.db.get_utc_now_iso()
        
        try:
            with self.db.get_connection() as conn:
                # 更新通知状态为已发送
                conn.execute("""
                    UPDATE exam_notifications 
                    SET status = ?, send_time = ?, updated_at = ?
                    WHERE id = ?
                """, (NotificationStatus.SENT.value, now, now, notification_id))
                
                # 为每个接收用户创建接收记录
                for user_id in recipient_user_ids:
                    conn.execute("""
                        INSERT OR REPLACE INTO notification_recipients (
                            notification_id, user_id, status, created_at
                        ) VALUES (?, ?, ?, ?)
                    """, (notification_id, user_id, NotificationStatus.SENT.value, now))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"发送通知失败: {str(e)}")
            return False
    
    def get_notifications_by_exam(self, exam_id: int) -> List[Dict[str, Any]]:
        """
        获取指定考试的所有通知
        
        Args:
            exam_id: 考试ID
            
        Returns:
            List[Dict[str, Any]]: 通知列表
        """
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM exam_notifications 
                WHERE exam_id = ? 
                ORDER BY created_at DESC
            """, (exam_id,))
            
            notifications = []
            for row in cursor.fetchall():
                notification = dict(row)
                # 解析JSON字段
                notification['target_users'] = json.loads(notification['target_users'] or '[]')
                notification['target_roles'] = json.loads(notification['target_roles'] or '[]')
                notifications.append(notification)
            
            return notifications
    
    def get_user_notifications(self, user_id: int, status: str = None) -> List[Dict[str, Any]]:
        """
        获取用户的通知列表
        
        Args:
            user_id: 用户ID
            status: 通知状态过滤（可选）
            
        Returns:
            List[Dict[str, Any]]: 通知列表
        """
        with self.db.get_connection() as conn:
            query = """
                SELECT n.*, r.status as recipient_status, r.read_time
                FROM exam_notifications n
                JOIN notification_recipients r ON n.id = r.notification_id
                WHERE r.user_id = ?
            """
            params = [user_id]
            
            if status:
                query += " AND r.status = ?"
                params.append(status)
            
            query += " ORDER BY n.created_at DESC"
            
            cursor = conn.execute(query, params)
            
            notifications = []
            for row in cursor.fetchall():
                notification = dict(row)
                # 解析JSON字段
                notification['target_users'] = json.loads(notification['target_users'] or '[]')
                notification['target_roles'] = json.loads(notification['target_roles'] or '[]')
                notifications.append(notification)
            
            return notifications
    
    def mark_notification_read(self, notification_id: int, user_id: int) -> bool:
        """
        标记通知为已读
        
        Args:
            notification_id: 通知ID
            user_id: 用户ID
            
        Returns:
            bool: 操作是否成功
        """
        now = self.db.get_utc_now_iso()
        
        try:
            with self.db.get_connection() as conn:
                conn.execute("""
                    UPDATE notification_recipients 
                    SET status = ?, read_time = ?
                    WHERE notification_id = ? AND user_id = ?
                """, (NotificationStatus.READ.value, now, notification_id, user_id))
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"标记通知已读失败: {str(e)}")
            return False
    
    def get_notification_by_id(self, notification_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取通知详情
        
        Args:
            notification_id: 通知ID
            
        Returns:
            Optional[Dict[str, Any]]: 通知详情或None
        """
        with self.db.get_connection() as conn:
            cursor = conn.execute("""
                SELECT * FROM exam_notifications WHERE id = ?
            """, (notification_id,))
            
            row = cursor.fetchone()
            if row:
                notification = dict(row)
                # 解析JSON字段
                notification['target_users'] = json.loads(notification['target_users'] or '[]')
                notification['target_roles'] = json.loads(notification['target_roles'] or '[]')
                return notification
            
            return None
    
    def delete_notification(self, notification_id: int) -> bool:
        """
        删除通知
        
        Args:
            notification_id: 通知ID
            
        Returns:
            bool: 删除是否成功
        """
        try:
            with self.db.get_connection() as conn:
                conn.execute("DELETE FROM exam_notifications WHERE id = ?", (notification_id,))
                conn.commit()
                return True
                
        except Exception as e:
            print(f"删除通知失败: {str(e)}")
            return False