<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.ico') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}?v={{ range(1000, 9999) | random }}" rel="stylesheet">
    <style>
        .container-fluid {
            padding: 0;
            margin: 0;
        }
        .row {
            margin: 0;
        }
        body {
            margin: 0;
            padding: 0;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #007bff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .table-actions {
            white-space: nowrap;
        }
        .search-box {
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- 返回主控台按钮 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <button class="btn btn-outline-primary" onclick="window.location.href='http://localhost:8000/dashboard'">
                        <i class="fas fa-arrow-left me-2"></i>返回主控台
                    </button>
                    <h3 class="mb-0"><i class="fas fa-users me-2"></i>用户管理系统</h3>
                    <div></div> <!-- 占位元素保持布局平衡 -->
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar p-3" style="padding-left: 15px !important; padding-right: 15px !important;">
                <h5 class="mb-4"><i class="bi bi-people-fill"></i> 用户管理</h5>
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showUserList()"><i class="bi bi-list"></i> 用户列表</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showAddUser()"><i class="bi bi-person-plus"></i> 添加用户</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showStatistics()"><i class="bi bi-graph-up"></i> 统计信息</a>
                    </li>
                </ul>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-10 main-content">
                <!-- 用户列表页面 -->
                <div id="userListPage">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-people"></i> 用户列表</h2>
                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="showAddUser()">
                                <i class="bi bi-person-plus"></i> 添加用户
                            </button>
                            <button class="btn btn-success" onclick="showBatchImportModal()">
                                <i class="bi bi-upload"></i> 批量导入
                            </button>
                            <button type="button" class="btn btn-warning me-2" id="batchEditBtn" onclick="showBatchEditModal()" disabled>
                                <i class="bi bi-pencil-square"></i> 批量修改
                            </button>
                            <button class="btn btn-danger" onclick="batchDeleteUsers()" id="batchDeleteBtn" disabled>
                                <i class="bi bi-trash"></i> 批量删除
                            </button>
                        </div>
                    </div>

                    <!-- 搜索和筛选 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="text" class="form-control search-box" id="searchKeyword" placeholder="搜索用户名、邮箱、姓名...">
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="roleFilter">
                                <option value="">所有角色</option>
                                <option value="admin">管理员</option>
                                <option value="expert">专家</option>
                                <option value="student">考生</option>
                                <option value="grader">考评员</option>
                                <option value="internal_supervisor">内部督导员</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select class="form-select" id="statusFilter">
                                <option value="">所有状态</option>
                                <option value="pending">待审核</option>
                                <option value="approved">审核通过</option>
                                <option value="rejected">审核未通过</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-primary" onclick="searchUsers()">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                        </div>
                    </div>

                    <!-- 用户表格 -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAllUsers" onchange="toggleSelectAll()">
                                    </th>
                                    <th>头像</th>
                                    <th>用户名</th>
                                    <th>真实姓名</th>
                                    <th>邮箱</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>部门</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="userTableBody">
                                <!-- 用户数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="用户列表分页">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- 分页按钮将通过JavaScript动态生成 -->
                        </ul>
                    </nav>
                </div>

                <!-- 添加用户页面 -->
                <div id="addUserPage" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-person-plus"></i> 添加用户</h2>
                        <button class="btn btn-secondary" onclick="showUserList()">
                            <i class="bi bi-arrow-left"></i> 返回列表
                        </button>
                    </div>

                    <div class="row justify-content-center">
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-body">
                                    <form id="addUserForm">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="username" class="form-label">用户名 *</label>
                                                <input type="text" class="form-control" id="username" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="email" class="form-label">邮箱 *</label>
                                                <input type="email" class="form-control" id="email" required>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="realName" class="form-label">真实姓名 *</label>
                                                <input type="text" class="form-control" id="realName" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="phone" class="form-label">手机号</label>
                                                <input type="tel" class="form-control" id="phone">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="password" class="form-label">密码 *</label>
                                                <input type="password" class="form-control" id="password" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="role" class="form-label">角色</label>
                                                <select class="form-select" id="role">
                                                    <option value="student">考生</option>
                                                    <option value="expert">专家</option>
                                                    <option value="grader">考评员</option>
                                                    <option value="internal_supervisor">内部督导员</option>
                                                    <option value="admin">管理员</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="department" class="form-label">部门</label>
                                                <input type="text" class="form-control" id="department">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="position" class="form-label">职位</label>
                                                <input type="text" class="form-control" id="position">
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <button type="submit" class="btn btn-primary me-2">
                                                <i class="bi bi-check-lg"></i> 创建用户
                                            </button>
                                            <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                                <i class="bi bi-arrow-clockwise"></i> 重置
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息页面 -->
                <div id="statisticsPage" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><i class="bi bi-graph-up"></i> 统计信息</h2>
                    </div>

                    <div class="row">
                        <div class="col-md-3 mb-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-people-fill text-primary" style="font-size: 2rem;"></i>
                                    <h3 class="card-title mt-2" id="totalUsers">0</h3>
                                    <p class="card-text">总用户数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-person-check-fill text-success" style="font-size: 2rem;"></i>
                                    <h3 class="card-title mt-2" id="activeUsers">0</h3>
                                    <p class="card-text">活跃用户</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-mortarboard-fill text-info" style="font-size: 2rem;"></i>
                                    <h3 class="card-title mt-2" id="studentUsers">0</h3>
                                    <p class="card-text">学生用户</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="card text-center">
                                <div class="card-body">
                                    <i class="bi bi-person-workspace text-warning" style="font-size: 2rem;"></i>
                                    <h3 class="card-title mt-2" id="teacherUsers">0</h3>
                                    <p class="card-text">教师用户</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-person-gear"></i> 编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editEmail" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="editEmail">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editRealName" class="form-label">真实姓名</label>
                                <input type="text" class="form-control" id="editRealName">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editPhone" class="form-label">手机号</label>
                                <input type="tel" class="form-control" id="editPhone">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editRole" class="form-label">角色</label>
                                <select class="form-select" id="editRole">
                                    <option value="student">考生</option>
                                    <option value="expert">专家</option>
                                    <option value="grader">考评员</option>
                                    <option value="internal_supervisor">内部督导员</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editStatus" class="form-label">状态</label>
                                <select class="form-select" id="editStatus">
                                    <option value="pending">待审核</option>
                                    <option value="approved">审核通过</option>
                                    <option value="rejected">审核未通过</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editDepartment" class="form-label">部门</label>
                                <input type="text" class="form-control" id="editDepartment">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editPosition" class="form-label">职位</label>
                            <input type="text" class="form-control" id="editPosition">
                        </div>
                        
                        <!-- 申报相关字段 -->
                        <h6 class="mt-4 mb-3">申报信息</h6>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editDeclaredOccupation" class="form-label">申报职业(工种)</label>
                                <input type="text" class="form-control" id="editDeclaredOccupation">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editDeclaredLevel" class="form-label">申报级别</label>
                                <input type="text" class="form-control" id="editDeclaredLevel">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editExamType" class="form-label">考试类型</label>
                                <input type="text" class="form-control" id="editExamType">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editAssessmentSubject" class="form-label">考核科目</label>
                                <input type="text" class="form-control" id="editAssessmentSubject">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editQuestionBankName" class="form-label">题库名称</label>
                                <input type="text" class="form-control" id="editQuestionBankName">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editTestPaperId" class="form-label">试卷ID</label>
                                <input type="text" class="form-control" id="editTestPaperId">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="updateUser()">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div class="modal fade" id="batchImportModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-upload"></i> 批量导入用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <h6>导入说明：</h6>
                        <ul class="text-muted small">
                            <li>支持Excel文件(.xlsx, .xls)和CSV文件(.csv)</li>
                            <li>文件第一行必须为表头：用户名,邮箱,真实姓名,手机号,密码,角色,部门,职位</li>
                            <li>用户名和邮箱不能重复</li>
                            <li>角色可选值：student(学生), teacher(教师), admin(管理员)</li>
                            <li>密码为空时将使用默认密码：123456</li>
                        </ul>
                    </div>
                    
                    <div class="mb-3">
                        <label for="importFile" class="form-label">选择文件</label>
                        <input type="file" class="form-control" id="importFile" accept=".xlsx,.xls,.csv">
                    </div>
                    
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-primary" onclick="downloadTemplate()">
                            <i class="bi bi-download"></i> 下载模板文件
                        </button>
                    </div>
                    
                    <div id="importProgress" style="display: none;">
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="importStatus"></div>
                    </div>
                    
                    <div id="importResult" style="display: none;">
                        <div class="alert alert-info">
                            <h6>导入结果：</h6>
                            <div id="importSummary"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="startBatchImport()" id="importBtn">
                        <i class="bi bi-upload"></i> 开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量修改模态框 -->
    <div class="modal fade" id="batchEditModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-pencil-square"></i> 批量修改用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> 已选择 <span id="selectedCount">0</span> 个用户进行批量修改。只有填写的字段会被更新，空白字段将保持不变。
                    </div>
                    
                    <form id="batchEditForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="batchStatus" class="form-label">状态</label>
                                <select class="form-select" id="batchStatus">
                                    <option value="">-- 不修改 --</option>
                                    <option value="pending">待审核</option>
                                    <option value="approved">审核通过</option>
                                    <option value="rejected">审核未通过</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="batchDeclaredOccupation" class="form-label">申报职业(工种)</label>
                                <input type="text" class="form-control" id="batchDeclaredOccupation" placeholder="不修改请留空">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="batchDeclaredLevel" class="form-label">申报级别</label>
                                <input type="text" class="form-control" id="batchDeclaredLevel" placeholder="不修改请留空">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="batchExamType" class="form-label">考试类型</label>
                                <input type="text" class="form-control" id="batchExamType" placeholder="不修改请留空">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="batchAssessmentSubject" class="form-label">考核科目</label>
                                <input type="text" class="form-control" id="batchAssessmentSubject" placeholder="不修改请留空">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="batchQuestionBankName" class="form-label">题库名称</label>
                                <input type="text" class="form-control" id="batchQuestionBankName" placeholder="不修改请留空">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="batchTestPaperId" class="form-label">试卷ID</label>
                                <input type="text" class="form-control" id="batchTestPaperId" placeholder="不修改请留空">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="executeBatchEdit()">
                        <i class="bi bi-check-lg"></i> 确认修改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/user-management.js') }}"></script>
</body>
</html>