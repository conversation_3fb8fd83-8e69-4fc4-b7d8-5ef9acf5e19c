# 职业技能等级考试系统生产环境模拟操作指引

## 1. 系统概述

本操作指引适用于职业技能等级考试系统的生产环境模拟，涵盖系统部署、各角色操作流程、安全管理和应急处理等关键环节。

### 1.1 系统架构
- **主控台**：统一管理界面（端口8000）
- **API网关**：路由分发和认证（端口8080）
- **模块化服务**：各功能模块独立运行
- **网络环境**：局域网内运行，仅成绩上报模块可连接互联网

### 1.2 用户角色
- **系统管理员**：系统部署、用户管理、监控维护
- **考评员**：主观题阅卷评分
- **专家**：质量监督、评分校准
- **内督员**：考试监督、违规处理
- **考生**：参加考试、答题提交

## 2. 系统部署和环境配置

### 2.1 硬件环境要求

**服务器配置**：
- CPU：Intel i5及以上或同等性能
- 内存：8GB及以上
- 硬盘：500GB及以上可用空间
- 网络：千兆网卡
- 操作系统：Windows 10/11 或 Windows Server 2016及以上

**客户端配置**：
- CPU：Intel i3及以上或同等性能
- 内存：4GB及以上
- 硬盘：100GB及以上可用空间
- 网络：百兆网卡
- 浏览器：Chrome 90+、Firefox 88+、Edge 90+

### 2.2 网络配置

**局域网设置**：
```
网络段：***********/24
服务器IP：*************
客户端IP：*************-*************
网关：***********
DNS：***********
```

**防火墙配置**：
- 开放端口：8000（主控台）、8080（API网关）
- 禁止互联网访问（除成绩上报模块外）
- 启用局域网内通信

### 2.3 系统部署步骤

**步骤1：环境准备**
```bash
# 设置控制台编码
chcp 65001

# 设置环境变量
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
```

**步骤2：启动系统**
```bash
# 进入系统目录
cd d:\60-PHRL_OLE_SYS

# 启动主控台
python main_launcher.py
```

**步骤3：验证部署**
- 访问主控台：http://*************:8000
- 检查API网关：http://*************:8080/health
- 验证模块状态：所有模块显示"运行中"

## 3. 管理员操作流程

### 3.1 系统启动和初始化

**操作步骤**：
1. **启动系统服务**
   - 双击运行 `start.bat` 或执行 `python main_launcher.py`
   - 等待所有模块启动完成（约2-3分钟）
   - 检查主控台显示所有模块状态为"运行中"

2. **管理员登录**
   - 访问：http://*************:8000
   - 用户名：admin
   - 密码：admin123
   - 验证登录成功，进入主控台界面

3. **系统状态检查**
   - 点击"刷新状态"按钮
   - 确认所有模块正常运行
   - 检查系统资源使用情况

### 3.2 用户管理

**批量导入用户**：
1. 点击"用户管理"模块
2. 选择"批量导入"
3. 下载用户导入模板
4. 填写用户信息（姓名、工号、角色、密码等）
5. 上传Excel文件
6. 验证导入结果

**用户权限配置**：
```
角色权限映射：
- 管理员：所有模块访问权限
- 考评员：评分管理、成绩查询
- 专家：评分管理、质量监控
- 内督员：考试监控、审计日志
- 考生：考试参与、成绩查询
```

### 3.3 考试安排

**创建考试计划**：
1. 进入"考试管理"模块
2. 点击"新建考试"
3. 填写考试基本信息：
   - 考试名称
   - 考试时间
   - 考试时长
   - 参考人数
4. 配置考试参数：
   - 题目数量
   - 分值分布
   - 及格分数
5. 分配考生和考评员
6. 保存并发布考试

**题库管理**：
1. 进入"题库管理"模块
2. 批量导入题目（使用题库模板）
3. 设置组卷规则
4. 生成试卷并预览
5. 审核确认试卷内容

### 3.4 监控和维护

**实时监控**：
- 监控系统资源使用率
- 查看用户在线状态
- 检查考试进行情况
- 观察网络连接状态

**日志管理**：
- 查看系统运行日志
- 监控用户操作日志
- 分析异常事件记录
- 定期备份日志文件

**数据备份**：
```bash
# 手动备份数据库
python scripts/backup_database.py

# 备份用户文件
xcopy /s /e "user_data" "backup\user_data_%date%"
```

## 4. 考评员操作流程

### 4.1 登录和认证

**登录步骤**：
1. 访问系统主页：http://*************:8000
2. 输入考评员账号和密码
3. 系统验证身份和权限
4. 成功登录后进入考评员工作台

**权限验证**：
- 确认可访问"评分管理"模块
- 检查分配的评分任务
- 验证评分权限范围

### 4.2 主观题阅卷

**阅卷流程**：
1. **进入评分界面**
   - 点击"评分管理"模块
   - 选择待评分的考试
   - 查看分配给自己的考生列表

2. **开始阅卷**
   - 点击考生姓名进入答题详情
   - 查看考生答案和题目要求
   - 根据评分标准给出分数
   - 填写评分说明（可选）

3. **评分操作**
   ```
   评分界面要素：
   - 题目内容显示区
   - 考生答案显示区
   - 评分标准参考区
   - 分数输入框
   - 评语输入框
   - 提交/保存按钮
   ```

4. **提交评分**
   - 检查评分是否完整
   - 点击"提交评分"按钮
   - 确认提交操作
   - 系统自动保存评分记录

### 4.3 多评分员协作

**并发评分机制**：
- 系统自动分配不同考生给不同评分员
- 同一份答卷可由多个评分员独立评分
- 系统自动计算平均分作为最终成绩
- 分数差异过大时触发复评机制

**质量控制**：
- 定期校准评分标准
- 参与评分一致性检查
- 接受专家组质量监督
- 及时处理评分争议

### 4.4 注意事项

**评分规范**：
- 严格按照评分标准执行
- 保持评分的一致性和公正性
- 及时完成分配的评分任务
- 遇到疑问及时咨询专家组

**系统操作**：
- 定期保存评分进度
- 避免长时间占用评分界面
- 注意网络连接稳定性
- 及时报告系统异常

## 5. 专家和内督员操作流程

### 5.1 专家操作流程

**质量监督**：
1. **登录专家账号**
   - 使用专家权限账号登录
   - 进入质量监控界面

2. **评分质量检查**
   - 查看评分员评分情况
   - 抽查评分质量
   - 分析评分数据分布
   - 识别异常评分模式

3. **评分校准**
   - 组织评分标准讨论
   - 进行样卷评分校准
   - 统一评分尺度
   - 指导评分员改进

**操作界面**：
```
专家监控面板：
- 评分进度统计
- 评分质量分析
- 异常评分预警
- 评分员绩效统计
- 质量报告生成
```

### 5.2 内督员操作流程

**考试监督**：
1. **实时监控**
   - 监控考试进行状态
   - 查看考生在线情况
   - 检查异常行为记录
   - 处理考试违规事件

2. **违规处理**
   - 接收违规报告
   - 调查违规事实
   - 记录处理结果
   - 生成违规报告

3. **审计追踪**
   - 查看系统操作日志
   - 分析用户行为轨迹
   - 检查数据完整性
   - 生成审计报告

**监督工具**：
```
内督员工作台：
- 考试实时监控
- 违规事件处理
- 审计日志查询
- 监督报告生成
- 数据完整性检查
```

### 5.3 协作机制

**专家与内督员协作**：
- 共享质量监控数据
- 协同处理争议问题
- 联合制定改进措施
- 定期召开质量会议

**与管理员协作**：
- 报告系统问题
- 提出改进建议
- 协助系统维护
- 参与应急处理

## 6. 考生操作注意事项

### 6.1 考前准备

**设备检查**：
- 确保计算机正常运行
- 检查网络连接稳定
- 测试浏览器兼容性
- 准备备用设备（如有）

**环境准备**：
- 选择安静的考试环境
- 确保充足的照明
- 准备必要的考试用品
- 关闭无关应用程序

**账号确认**：
- 确认考生账号和密码
- 了解考试时间安排
- 熟悉考试界面操作
- 阅读考试须知

### 6.2 登录和考试

**登录流程**：
1. **访问考试系统**
   - 打开浏览器
   - 访问：http://*************:8000
   - 点击"考生登录"

2. **身份验证**
   - 输入准考证号
   - 输入登录密码
   - 完成身份验证
   - 进入考试界面

3. **开始考试**
   - 阅读考试说明
   - 确认考试信息
   - 点击"开始考试"
   - 进入答题界面

### 6.3 答题规范

**答题操作**：
```
答题界面功能：
- 题目导航栏
- 答题区域
- 时间倒计时
- 暂存答案按钮
- 提交答案按钮
- 标记题目功能
```

**答题注意事项**：
- 仔细阅读题目要求
- 及时保存答题进度
- 注意考试时间限制
- 检查答案完整性
- 确认提交前再次检查

**特殊题型处理**：
- **选择题**：点击选项选择答案
- **填空题**：在输入框中填写答案
- **主观题**：在文本区域输入详细答案
- **操作题**：按照要求完成实际操作

### 6.4 异常处理

**网络异常**：
- 检查网络连接
- 刷新页面重新登录
- 联系技术支持
- 使用备用设备

**系统异常**：
- 记录异常时间和现象
- 截图保存异常界面
- 立即报告监考人员
- 等待技术处理

**答题异常**：
- 答案无法保存：多次尝试保存
- 页面无响应：刷新页面
- 时间异常：联系监考人员
- 题目显示错误：报告技术支持

### 6.5 考试结束

**提交答案**：
1. 检查所有题目是否已答
2. 确认答案内容正确
3. 点击"提交试卷"按钮
4. 确认提交操作
5. 等待系统确认

**注意事项**：
- 提交后无法修改答案
- 确保在规定时间内提交
- 保存提交确认信息
- 按要求离开考场

## 7. 应急处理预案

### 7.1 系统故障处理

**服务器故障**：
```
处理步骤：
1. 立即启动备用服务器
2. 恢复最新数据备份
3. 通知所有用户系统状态
4. 记录故障原因和处理过程
5. 制定预防措施
```

**网络故障**：
```
处理步骤：
1. 检查网络设备状态
2. 重启网络设备
3. 检查网络配置
4. 联系网络服务商
5. 启用备用网络连接
```

**数据库故障**：
```
处理步骤：
1. 停止相关服务
2. 检查数据库完整性
3. 恢复数据库备份
4. 重启数据库服务
5. 验证数据完整性
```

### 7.2 考试异常处理

**大规模登录失败**：
- 检查认证服务状态
- 重启用户管理模块
- 批量重置用户密码
- 延长考试时间补偿

**答题数据丢失**：
- 立即停止相关操作
- 从备份恢复数据
- 通知受影响考生
- 安排补考或延时

**评分系统异常**：
- 暂停评分操作
- 备份已有评分数据
- 修复系统问题
- 恢复评分工作

### 7.3 安全事件处理

**未授权访问**：
```
处理流程：
1. 立即阻断可疑连接
2. 分析访问日志
3. 加强访问控制
4. 通知相关人员
5. 生成安全报告
```

**数据泄露风险**：
```
处理流程：
1. 评估泄露范围
2. 立即采取隔离措施
3. 通知相关部门
4. 调查泄露原因
5. 制定补救措施
```

### 7.4 通信和协调

**应急联系方式**：
```
角色联系信息：
- 系统管理员：[电话] [邮箱]
- 技术支持：[电话] [邮箱]
- 考务负责人：[电话] [邮箱]
- 网络管理员：[电话] [邮箱]
```

**信息发布渠道**：
- 系统公告栏
- 短信通知
- 邮件通知
- 电话通知
- 现场广播

## 8. 安全管理规范

### 8.1 访问控制

**身份认证**：
- 强制使用复杂密码
- 定期更换密码
- 启用账号锁定机制
- 记录登录日志

**权限管理**：
- 最小权限原则
- 定期权限审查
- 及时回收权限
- 权限变更审批

### 8.2 数据保护

**数据加密**：
- 传输数据加密
- 存储数据加密
- 密钥安全管理
- 定期更新加密算法

**数据备份**：
- 定期自动备份
- 多地备份存储
- 备份数据验证
- 快速恢复机制

### 8.3 审计监控

**日志记录**：
- 用户操作日志
- 系统运行日志
- 安全事件日志
- 数据访问日志

**监控告警**：
- 实时状态监控
- 异常行为检测
- 自动告警机制
- 告警处理流程

## 9. 性能优化建议

### 9.1 系统性能

**服务器优化**：
- 定期清理临时文件
- 优化数据库索引
- 调整内存分配
- 监控CPU使用率

**网络优化**：
- 优化网络带宽分配
- 减少网络延迟
- 启用数据压缩
- 负载均衡配置

### 9.2 用户体验

**界面优化**：
- 简化操作流程
- 优化页面加载速度
- 提供操作指引
- 改进错误提示

**响应优化**：
- 减少页面刷新
- 实现智能缓存
- 优化数据查询
- 提高并发处理能力

## 10. 总结

本操作指引涵盖了职业技能等级考试系统生产环境模拟的各个方面，从系统部署到各角色操作，从日常管理到应急处理，为系统的稳定运行提供了全面的指导。

**关键要点**：
- 严格遵循操作流程
- 重视安全管理
- 做好应急准备
- 持续优化改进

**成功要素**：
- 充分的前期准备
- 规范的操作流程
- 有效的监控机制
- 快速的响应能力

通过遵循本指引，可以确保考试系统在生产环境中稳定、安全、高效地运行，为职业技能等级考试提供可靠的技术保障。