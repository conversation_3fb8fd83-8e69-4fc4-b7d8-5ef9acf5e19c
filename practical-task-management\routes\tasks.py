# -*- coding: utf-8 -*-
"""
任务管理API路由
提供任务的增删改查、状态管理等功能
"""

from flask import Blueprint, request, current_app
from werkzeug.utils import secure_filename
import os
import json
from datetime import datetime
from services import PracticalTaskService
from auth.permissions import require_permission, Permission
from auth.audit_log import log_operation
from config.api_gateway_config import (
    APIResponse, api_response, validate_request, ValidationError,
    API_PREFIX, VALIDATION_RULES
)

# 创建蓝图
tasks_bp = Blueprint('tasks', __name__, url_prefix=f'{API_PREFIX}/tasks')
task_service = PracticalTaskService()

@tasks_bp.route('/list', methods=['GET'])
@require_permission(Permission.TASK_VIEW)
@log_operation('查看任务列表', 'task', 'READ')
@api_response
@validate_request({
    'page': 'page',
    'per_page': 'per_page',
    'sort_by': 'sort_by',
    'sort_order': 'sort_order'
})
def get_tasks():
    """
    获取实操任务列表
    
    查询参数:
        - category_id: 分类ID过滤
        - difficulty_level: 难度等级过滤
        - software_type: 软件类型过滤
        - status: 状态过滤 (active/inactive)
        - keyword: 关键词搜索
        - page: 页码 (默认1)
        - page_size: 每页数量 (默认20)
    
    返回:
        JSON格式的任务列表和分页信息
    """
    # 获取查询参数
    category_id = request.args.get('category_id', type=int)
    difficulty_level = request.args.get('difficulty_level')
    software_type = request.args.get('software_type')
    status = request.args.get('status', 'active')
    keyword = request.args.get('keyword')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    
    # 构建过滤条件
    filters = {}
    if category_id:
        filters['category_id'] = category_id
    if difficulty_level:
        filters['difficulty_level'] = difficulty_level
    if software_type:
        filters['software_type'] = software_type
    if status:
        filters['status'] = status
    if keyword:
        filters['keyword'] = keyword
        
    # 获取任务列表
    tasks = task_service.get_tasks_with_pagination(
        filters=filters,
        page=page,
        page_size=page_size
    )
    
    return APIResponse.success(tasks, '获取任务列表成功')

@tasks_bp.route('/<int:task_id>', methods=['GET'])
@require_permission(Permission.TASK_VIEW)
@log_operation('查看任务详情', 'task', 'READ')
@api_response
def get_task(task_id):
    """
    获取指定任务详情
    
    参数:
        task_id: 任务ID
    
    返回:
        JSON格式的任务详细信息
    """
    task = task_service.get_task_by_id(task_id)
    if not task:
        return APIResponse.error('任务不存在', 404)
        
    return APIResponse.success(task, '获取任务详情成功')

@tasks_bp.route('', methods=['POST'])
@require_permission(Permission.TASK_CREATE)
@log_operation('创建任务', 'task', 'CREATE')
@api_response
@validate_request({
    'title': 'task_title',
    'category_id': 'category_id',
    'difficulty_level': 'difficulty_level',
    'software_type': 'software_type',
    'description': 'description',
    'time_limit': 'time_limit',
    'requirements': 'requirements',
    'scoring_criteria': 'scoring_criteria',
    'tags': 'tags'
})
def create_task():
    """
    创建新的实操任务
    
    请求体:
        {
            "title": "任务标题",
            "description": "任务描述",
            "category_id": 1,
            "difficulty_level": "初级",
            "software_type": "Office",
            "time_limit": 60,
            "requirements": "任务要求",
            "scoring_criteria": "评分标准",
            "tags": ["标签1", "标签2"]
        }
    
    返回:
        JSON格式的创建结果
    """
    data = request.get_json()
    
    # 创建任务
    task_id = task_service.create_task(
        title=data['title'],
        description=data.get('description', ''),
        category_id=data['category_id'],
        difficulty_level=data['difficulty_level'],
        software_type=data['software_type'],
        time_limit=data.get('time_limit', 60),
        requirements=data.get('requirements', ''),
        scoring_criteria=data.get('scoring_criteria', ''),
        tags=data.get('tags', [])
    )
    
    return APIResponse.success({'task_id': task_id}, '任务创建成功', 201)

@tasks_bp.route('/<int:task_id>', methods=['PUT'])
@require_permission(Permission.TASK_EDIT)
@log_operation('更新任务', 'task', 'UPDATE')
@api_response
@validate_request({
    'title': 'task_title',
    'category_id': 'category_id',
    'difficulty_level': 'difficulty_level',
    'software_type': 'software_type',
    'description': 'description',
    'time_limit': 'time_limit',
    'requirements': 'requirements',
    'scoring_criteria': 'scoring_criteria',
    'tags': 'tags',
    'status': 'status'
})
def update_task(task_id):
    """
    更新实操任务
    
    参数:
        task_id: 任务ID
    
    请求体:
        {
            "title": "任务标题",
            "description": "任务描述",
            "category_id": 1,
            "difficulty_level": "初级",
            "software_type": "Office",
            "time_limit": 60,
            "requirements": "任务要求",
            "scoring_criteria": "评分标准",
            "tags": ["标签1", "标签2"],
            "status": "active"
        }
    
    返回:
        JSON格式的更新结果
    """
    data = request.get_json()
    
    # 更新任务
    success = task_service.update_task(
        task_id=task_id,
        title=data.get('title'),
        description=data.get('description'),
        category_id=data.get('category_id'),
        difficulty_level=data.get('difficulty_level'),
        software_type=data.get('software_type'),
        time_limit=data.get('time_limit'),
        requirements=data.get('requirements'),
        scoring_criteria=data.get('scoring_criteria'),
        tags=data.get('tags'),
        status=data.get('status')
    )
    
    if success:
        return APIResponse.success({}, '任务更新成功')
    else:
        return APIResponse.error('任务不存在', 404)

@tasks_bp.route('/<int:task_id>', methods=['DELETE'])
@require_permission(Permission.TASK_DELETE)
@log_operation('删除任务', 'task', 'DELETE')
@api_response
def delete_task(task_id):
    """
    删除实操任务
    
    参数:
        task_id: 任务ID
    
    返回:
        JSON格式的删除结果
    """
    # 检查任务是否存在
    existing_task = task_service.get_task_by_id(task_id)
    if not existing_task:
        return APIResponse.error('任务不存在', 404)
    
    # 检查是否有关联的执行记录
    if task_service.has_execution_records(task_id):
        return APIResponse.error('该任务还有执行记录，无法删除', 400)
    
    # 删除任务
    success = task_service.delete_task(task_id)
    
    if success:
        return APIResponse.success({}, '任务删除成功')
    else:
        return APIResponse.error('任务删除失败', 500)

@tasks_bp.route('/<int:task_id>/duplicate', methods=['POST'])
@require_permission(Permission.TASK_CREATE)
@log_operation('复制任务', 'task', 'CREATE')
@api_response
def duplicate_task(task_id):
    """
    复制实操任务
    
    参数:
        task_id: 源任务ID
    
    请求体:
        {
            "title": "新任务标题" (可选，默认为"复制 - 原标题")
        }
    
    返回:
        JSON格式的复制结果
    """
    # 检查原任务是否存在
    original_task = task_service.get_task_by_id(task_id)
    if not original_task:
        return APIResponse.error('原任务不存在', 404)
    
    # 复制任务
    new_task_id = task_service.duplicate_task(task_id)
    
    return APIResponse.success({'new_task_id': new_task_id}, '任务复制成功', 201)

@tasks_bp.route('/stats', methods=['GET'])
@require_permission(Permission.TASK_VIEW)
@log_operation('获取任务统计', 'task', 'READ')
@api_response
def get_task_stats():
    """
    获取任务统计信息
    
    返回:
        JSON格式的任务统计数据
    """
    stats = task_service.get_task_statistics()
    return APIResponse.success(stats, '获取统计信息成功')

@tasks_bp.route('/search', methods=['GET'])
@require_permission(Permission.TASK_VIEW)
@log_operation('搜索任务', 'task', 'READ')
@api_response
@validate_request({
    'keyword': 'keyword',
    'category_id': 'category_id',
    'difficulty_level': 'difficulty_level',
    'software_type': 'software_type',
    'status': 'status'
})
def search_tasks():
    """
    高级任务搜索
    
    请求体:
        {
            "keyword": "搜索关键词",
            "category_ids": [1, 2, 3],
            "difficulty_levels": ["初级", "中级"],
            "software_types": ["Office", "CAD"],
            "time_range": {"min": 30, "max": 120},
            "tags": ["标签1", "标签2"],
            "date_range": {
                "start": "2024-01-01",
                "end": "2024-12-31"
            }
        }
    
    返回:
        JSON格式的搜索结果
    """
    # 获取搜索参数
    keyword = request.args.get('keyword')
    category_id = request.args.get('category_id', type=int)
    difficulty_level = request.args.get('difficulty_level')
    software_type = request.args.get('software_type')
    status = request.args.get('status')
    
    # 搜索任务
    tasks = task_service.search_tasks(
        keyword=keyword,
        category_id=category_id,
        difficulty_level=difficulty_level,
        software_type=software_type,
        status=status
    )
    
    return APIResponse.success(tasks, '搜索任务成功')