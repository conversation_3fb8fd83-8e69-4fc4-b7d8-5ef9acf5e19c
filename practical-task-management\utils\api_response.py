# -*- coding: utf-8 -*-
"""
API响应工具模块
提供统一的API响应格式和装饰器
"""

import json
from functools import wraps
from flask import jsonify, request
import traceback
import logging

logger = logging.getLogger(__name__)

class APIResponse:
    """
    统一API响应格式类
    
    提供标准化的API响应格式，包括成功和错误响应
    """
    
    @staticmethod
    def success(data=None, message="操作成功", code=200):
        """
        成功响应
        
        参数:
            data: 响应数据
            message: 响应消息
            code: 状态码
            
        返回:
            Flask jsonify响应对象
        """
        response = {
            "code": code,
            "msg": message,
            "data": data if data is not None else {}
        }
        return jsonify(response), code
    
    @staticmethod
    def error(message="操作失败", code=500, data=None):
        """
        错误响应
        
        参数:
            message: 错误消息
            code: 错误状态码
            data: 错误详细数据
            
        返回:
            Flask jsonify响应对象
        """
        response = {
            "code": code,
            "msg": message,
            "data": data if data is not None else {}
        }
        return jsonify(response), code
    
    @staticmethod
    def validation_error(errors, message="参数验证失败"):
        """
        参数验证错误响应
        
        参数:
            errors: 验证错误详情
            message: 错误消息
            
        返回:
            Flask jsonify响应对象
        """
        return APIResponse.error(message, 400, {"validation_errors": errors})

def api_response(func):
    """
    API响应装饰器
    
    自动处理异常并返回统一格式的响应
    
    参数:
        func: 被装饰的函数
        
    返回:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            # 记录请求信息
            logger.info(f"API请求: {request.method} {request.path}")
            if request.is_json and request.get_json():
                logger.debug(f"请求数据: {request.get_json()}")
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 如果返回的是tuple，直接返回（已经是Flask响应格式）
            if isinstance(result, tuple):
                return result
            
            # 如果返回的是字典，包装成标准响应
            if isinstance(result, dict):
                return APIResponse.success(result)
            
            # 其他情况直接返回
            return result
            
        except ValueError as e:
            logger.warning(f"参数错误: {str(e)}")
            return APIResponse.error(f"参数错误: {str(e)}", 400)
        
        except PermissionError as e:
            logger.warning(f"权限错误: {str(e)}")
            return APIResponse.error(f"权限不足: {str(e)}", 403)
        
        except FileNotFoundError as e:
            logger.warning(f"资源不存在: {str(e)}")
            return APIResponse.error("资源不存在", 404)
        
        except Exception as e:
            logger.error(f"API异常: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return APIResponse.error("服务器内部错误", 500)
    
    return wrapper

def validate_request(schema):
    """
    请求参数验证装饰器
    
    参数:
        schema: 验证模式字典
        
    返回:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 获取请求数据
            if request.method in ['POST', 'PUT', 'PATCH']:
                data = request.get_json() or {}
            else:
                data = request.args.to_dict()
            
            # 验证参数
            errors = []
            
            for field, rules in schema.items():
                value = data.get(field)
                
                # 检查必填字段
                if rules.get('required', False) and (value is None or value == ''):
                    errors.append(f"字段 '{field}' 是必填的")
                    continue
                
                # 如果字段为空且非必填，跳过其他验证
                if value is None or value == '':
                    continue
                
                # 类型验证
                field_type = rules.get('type')
                if field_type == 'integer':
                    try:
                        int(value)
                    except (ValueError, TypeError):
                        errors.append(f"字段 '{field}' 必须是整数")
                elif field_type == 'number':
                    try:
                        float(value)
                    except (ValueError, TypeError):
                        errors.append(f"字段 '{field}' 必须是数字")
                elif field_type == 'string':
                    if not isinstance(value, str):
                        errors.append(f"字段 '{field}' 必须是字符串")
                elif field_type == 'array':
                    if not isinstance(value, list):
                        errors.append(f"字段 '{field}' 必须是数组")
                elif field_type == 'object':
                    if not isinstance(value, dict):
                        errors.append(f"字段 '{field}' 必须是对象")
                
                # 长度验证
                if 'min_length' in rules and len(str(value)) < rules['min_length']:
                    errors.append(f"字段 '{field}' 长度不能少于 {rules['min_length']} 个字符")
                
                if 'max_length' in rules and len(str(value)) > rules['max_length']:
                    errors.append(f"字段 '{field}' 长度不能超过 {rules['max_length']} 个字符")
                
                # 数值范围验证
                if 'min' in rules:
                    try:
                        if float(value) < rules['min']:
                            errors.append(f"字段 '{field}' 不能小于 {rules['min']}")
                    except (ValueError, TypeError):
                        pass
                
                if 'max' in rules:
                    try:
                        if float(value) > rules['max']:
                            errors.append(f"字段 '{field}' 不能大于 {rules['max']}")
                    except (ValueError, TypeError):
                        pass
                
                # 枚举值验证
                if 'enum' in rules and value not in rules['enum']:
                    errors.append(f"字段 '{field}' 的值必须是 {rules['enum']} 中的一个")
            
            # 如果有验证错误，返回错误响应
            if errors:
                return APIResponse.validation_error(errors)
            
            # 验证通过，执行原函数
            return func(*args, **kwargs)
        
        return wrapper
    return decorator