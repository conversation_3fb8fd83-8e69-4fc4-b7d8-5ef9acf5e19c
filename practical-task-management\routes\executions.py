# -*- coding: utf-8 -*-
"""
任务执行记录管理路由模块
提供任务执行记录和评分的API接口
"""

from flask import Blueprint, request
from services import TaskExecutionService, PracticalTaskService
from auth.permissions import require_permission, Permission
from auth.audit_log import log_operation
from utils.api_response import APIResponse, api_response, validate_request
from datetime import datetime
import json

# 创建蓝图
executions_bp = Blueprint('executions', __name__, url_prefix='/api/v1/executions')
execution_service = TaskExecutionService()
task_service = PracticalTaskService()

@executions_bp.route('', methods=['GET'])
@require_permission(Permission.EXECUTION_VIEW)
@log_operation('获取执行记录列表', 'execution', 'READ')
@api_response
@validate_request({
    'task_id': {'type': 'integer', 'required': False},
    'student_id': {'type': 'integer', 'required': False},
    'status': {'type': 'string', 'required': False, 'enum': ['pending', 'in_progress', 'completed', 'evaluated']},
    'page': {'type': 'integer', 'required': False, 'min': 1},
    'per_page': {'type': 'integer', 'required': False, 'min': 1, 'max': 100}
})
def get_executions():
    """
    获取任务执行记录列表
    
    查询参数:
        - task_id: 任务ID过滤
        - student_id: 学生ID过滤
        - exam_id: 考试ID过滤
        - status: 状态过滤 (pending/in_progress/completed/evaluated)
        - date_range: 日期范围过滤
        - page: 页码 (默认1)
        - page_size: 每页数量 (默认20)
    
    返回:
        JSON格式的执行记录列表和分页信息
    """
    # 获取查询参数
    task_id = request.args.get('task_id', type=int)
    student_id = request.args.get('student_id', type=int)
    status = request.args.get('status')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # 构建过滤条件
    filters = {}
    if task_id:
        filters['task_id'] = task_id
    if student_id:
        filters['student_id'] = student_id
    if status:
        filters['status'] = status
        
    # 获取执行记录列表
    executions, total = execution_service.get_executions(
        filters=filters,
        page=page,
        per_page=per_page
    )
    
    if executions is not None:
        return APIResponse.success({
            'executions': executions,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        }, '获取执行记录列表成功')
    else:
        return APIResponse.error('获取执行记录列表失败', 500)

@executions_bp.route('/<int:execution_id>', methods=['GET'])
@require_permission(Permission.EXECUTION_VIEW)
@log_operation('获取执行记录详情', 'execution', 'READ')
@api_response
def get_execution(execution_id):
    """
    获取指定执行记录详情
    
    参数:
        execution_id: 执行记录ID
    
    返回:
        JSON格式的执行记录详细信息
    """
    # 获取执行记录详情
    execution = execution_service.get_execution_by_id(execution_id)
    
    if not execution:
        return APIResponse.error('执行记录不存在', 404)
    
    return APIResponse.success(execution, '获取执行记录详情成功')

@executions_bp.route('/start', methods=['POST'])
@require_permission(Permission.TASK_EXECUTE)
@log_operation('开始任务执行', 'execution', 'CREATE')
@api_response
@validate_request({
    'task_id': {'type': 'integer', 'required': True},
    'student_id': {'type': 'integer', 'required': True},
    'execution_environment': {'type': 'object', 'required': False},
    'start_time': {'type': 'string', 'required': False}
})
def start_execution():
    """
    开始任务执行
    
    请求体:
        {
            "task_id": 1,
            "student_id": 123,
            "exam_id": 456,
            "environment_info": {
                "os": "Windows 10",
                "software_version": "Office 2019",
                "screen_resolution": "1920x1080"
            }
        }
    
    返回:
        JSON格式的执行开始结果
    """
    data = request.get_json()
    if not data:
        return APIResponse.error('请求数据不能为空', 400)
        
    # 验证必填字段
    required_fields = ['task_id', 'student_id']
    for field in required_fields:
        if not data.get(field):
            return APIResponse.error(f'缺少必填字段: {field}', 400)
    
    # 检查任务是否存在
    task = task_service.get_task_by_id(data['task_id'])
    if not task:
        return APIResponse.error('任务不存在', 404)
    
    # 创建执行记录
    execution_id = execution_service.start_execution(
        task_id=data['task_id'],
        student_id=data['student_id'],
        execution_environment=data.get('execution_environment', {}),
        start_time=data.get('start_time')
    )
    
    if execution_id:
        return APIResponse.success({'execution_id': execution_id}, '任务执行开始成功')
    else:
        return APIResponse.error('任务执行开始失败', 500)

@executions_bp.route('/<int:execution_id>/submit', methods=['POST'])
@require_permission(Permission.TASK_EXECUTE)
@log_operation('提交任务执行结果', 'execution', 'UPDATE')
@api_response
@validate_request({
    'submission_files': {'type': 'array', 'required': False},
    'submission_notes': {'type': 'string', 'required': False},
    'completion_time': {'type': 'string', 'required': False}
})
def submit_execution(execution_id):
    """
    提交任务执行结果
    
    参数:
        execution_id: 执行记录ID
    
    请求体:
        {
            "submission_files": [
                {
                    "filename": "result.docx",
                    "file_path": "/path/to/file",
                    "file_size": 1024,
                    "file_hash": "md5hash"
                }
            ],
            "submission_notes": "提交说明",
            "completion_time": "2024-01-01T10:30:00"
        }
    
    返回:
        JSON格式的提交结果
    """
    data = request.get_json()
    if not data:
        return APIResponse.error('请求数据不能为空', 400)
        
    # 检查执行记录是否存在
    existing_execution = execution_service.get_execution_by_id(execution_id)
    if not existing_execution:
        return APIResponse.error('执行记录不存在', 404)
    
    # 检查执行状态
    if existing_execution['status'] not in ['pending', 'in_progress']:
        return APIResponse.error('该执行记录已完成或已评价，无法重复提交', 400)
    
    # 提交执行结果
    success = execution_service.submit_execution(
        execution_id=execution_id,
        submission_files=data.get('submission_files', []),
        submission_notes=data.get('submission_notes', ''),
        completion_time=data.get('completion_time')
    )
    
    if success:
        return APIResponse.success({}, '任务提交成功')
    else:
        return APIResponse.error('任务提交失败', 500)

@executions_bp.route('/<int:execution_id>/evaluate', methods=['POST'])
@require_permission(Permission.EXECUTION_GRADE)
@log_operation('评价任务执行结果', 'execution', 'UPDATE')
@api_response
@validate_request({
    'score': {'type': 'number', 'required': True, 'min': 0},
    'max_score': {'type': 'number', 'required': True, 'min': 1},
    'evaluation_criteria': {'type': 'object', 'required': False},
    'feedback': {'type': 'string', 'required': False},
    'evaluator_id': {'type': 'integer', 'required': True}
})
def evaluate_execution(execution_id):
    """
    评价任务执行结果
    
    参数:
        execution_id: 执行记录ID
    
    请求体:
        {
            "score": 85,
            "max_score": 100,
            "evaluation_criteria": {
                "completeness": 20,
                "accuracy": 25,
                "efficiency": 20,
                "presentation": 20
            },
            "feedback": "评价反馈",
            "evaluator_id": 789
        }
    
    返回:
        JSON格式的评价结果
    """
    data = request.get_json()
    if not data:
        return APIResponse.error('请求数据不能为空', 400)
        
    # 验证必填字段
    required_fields = ['score', 'max_score', 'evaluator_id']
    for field in required_fields:
        if data.get(field) is None:
            return APIResponse.error(f'缺少必填字段: {field}', 400)
    
    # 检查执行记录是否存在
    existing_execution = execution_service.get_execution_by_id(execution_id)
    if not existing_execution:
        return APIResponse.error('执行记录不存在', 404)
    
    # 检查执行状态
    if existing_execution['status'] != 'completed':
        return APIResponse.error('该执行记录尚未完成，无法评价', 400)
    
    # 评价执行结果
    success = execution_service.evaluate_execution(
        execution_id=execution_id,
        score=data['score'],
        max_score=data['max_score'],
        evaluation_criteria=data.get('evaluation_criteria', {}),
        feedback=data.get('feedback', ''),
        evaluator_id=data['evaluator_id']
    )
    
    if success:
        return APIResponse.success({}, '任务评价成功')
    else:
        return APIResponse.error('任务评价失败', 500)

@executions_bp.route('/<int:execution_id>/report', methods=['GET'])
@require_permission('execution:read')
@log_operation('获取任务执行报告', 'execution', 'READ')
@api_response
def get_execution_report(execution_id):
    """
    获取任务执行报告
    
    参数:
        execution_id: 执行记录ID
    
    返回:
        JSON格式的执行报告
    """
    # 检查执行记录是否存在
    existing_execution = execution_service.get_execution_by_id(execution_id)
    if not existing_execution:
        return APIResponse.error('执行记录不存在', 404)
    
    # 生成执行报告
    report = execution_service.generate_execution_report(execution_id)
    
    if report:
        return APIResponse.success(report, '获取执行报告成功')
    else:
        return APIResponse.error('生成执行报告失败', 500)

@executions_bp.route('/stats', methods=['GET'])
@require_permission('execution:read')
@log_operation('获取执行统计信息', 'execution', 'READ')
@api_response
@validate_request({
    'task_id': {'type': 'integer', 'required': False},
    'date_range': {'type': 'string', 'required': False},
    'group_by': {'type': 'string', 'required': False, 'enum': ['task', 'student', 'date']}
})
def get_execution_stats():
    """
    获取执行统计信息
    
    查询参数:
        - task_id: 任务ID过滤
        - date_range: 日期范围过滤
        - group_by: 分组方式 (task/student/date)
    
    返回:
        JSON格式的执行统计数据
    """
    # 获取查询参数
    task_id = request.args.get('task_id', type=int)
    date_range = request.args.get('date_range')
    group_by = request.args.get('group_by', 'task')
    
    # 构建过滤条件
    filters = {}
    if task_id:
        filters['task_id'] = task_id
    if date_range:
        filters['date_range'] = date_range
        
    # 获取统计信息
    stats = execution_service.get_execution_statistics(filters, group_by)
    
    if stats is not None:
        return APIResponse.success(stats, '获取执行统计成功')
    else:
        return APIResponse.error('获取执行统计失败', 500)

@executions_bp.route('/batch-evaluate', methods=['POST'])
@require_permission(Permission.EXECUTION_GRADE)
@log_operation('批量评价任务执行结果', 'execution', 'UPDATE')
@api_response
@validate_request({
    'execution_ids': {'type': 'array', 'required': True, 'items': {'type': 'integer'}},
    'evaluation_template': {'type': 'object', 'required': True},
    'evaluator_id': {'type': 'integer', 'required': True}
})
def batch_evaluate_executions():
    """
    批量评价任务执行结果
    
    请求体:
        {
            "execution_ids": [1, 2, 3],
            "evaluation_template": {
                "max_score": 100,
                "evaluation_criteria": {
                    "completeness": 25,
                    "accuracy": 25,
                    "efficiency": 25,
                    "presentation": 25
                }
            },
            "evaluator_id": 789
        }
    
    返回:
        JSON格式的批量评价结果
    """
    data = request.get_json()
    if not data:
        return APIResponse.error('请求数据不能为空', 400)
        
    # 验证必填字段
    required_fields = ['execution_ids', 'evaluation_template', 'evaluator_id']
    for field in required_fields:
        if not data.get(field):
            return APIResponse.error(f'缺少必填字段: {field}', 400)
    
    # 批量评价
    results = execution_service.batch_evaluate_executions(
        execution_ids=data['execution_ids'],
        evaluation_template=data['evaluation_template'],
        evaluator_id=data['evaluator_id']
    )
    
    if results is not None:
        return APIResponse.success(results, '批量评价完成')
    else:
        return APIResponse.error('批量评价失败', 500)