# -*- coding: utf-8 -*-
"""
操作日志模块
记录系统中的各种操作行为，用于审计和追踪
"""

import sqlite3
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from flask import request, g
from functools import wraps

class AuditLogger:
    """
    审计日志记录器
    负责记录系统中的各种操作
    """
    
    def __init__(self, db_path: str = 'practical_tasks.db'):
        """
        初始化审计日志记录器
        
        Args:
            db_path (str): 数据库路径
        """
        self.db_path = db_path
    
    def create_tables(self):
        """
        创建审计日志相关的数据表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 创建操作日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    username VARCHAR(50),
                    action VARCHAR(100) NOT NULL,
                    resource_type VARCHAR(50) NOT NULL,
                    resource_id VARCHAR(100),
                    resource_name VARCHAR(200),
                    operation VARCHAR(20) NOT NULL,
                    old_data TEXT,
                    new_data TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    request_method VARCHAR(10),
                    request_path VARCHAR(500),
                    request_params TEXT,
                    response_code INTEGER,
                    execution_time REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    session_id VARCHAR(100),
                    module VARCHAR(50) DEFAULT 'practical-task'
                )
            ''')
            
            # 创建系统事件日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_event_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_type VARCHAR(50) NOT NULL,
                    event_level VARCHAR(20) NOT NULL,
                    event_message TEXT NOT NULL,
                    event_data TEXT,
                    source_module VARCHAR(50),
                    source_function VARCHAR(100),
                    error_code VARCHAR(50),
                    stack_trace TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建登录日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS login_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    username VARCHAR(50),
                    login_type VARCHAR(20) NOT NULL,
                    login_result VARCHAR(20) NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    logout_time TIMESTAMP,
                    session_duration INTEGER,
                    failure_reason VARCHAR(200)
                )
            ''')
            
            # 创建数据变更日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS data_change_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INTEGER NOT NULL,
                    operation VARCHAR(20) NOT NULL,
                    field_name VARCHAR(50),
                    old_value TEXT,
                    new_value TEXT,
                    user_id INTEGER,
                    username VARCHAR(50),
                    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    change_reason VARCHAR(200)
                )
            ''')
            
            # 创建索引以提高查询性能
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs(resource_type)')
            
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_event_logs_event_type ON system_event_logs(event_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_system_event_logs_created_at ON system_event_logs(created_at)')
            
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_login_logs_user_id ON login_logs(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_login_logs_login_time ON login_logs(login_time)')
            
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_change_logs_table_record ON data_change_logs(table_name, record_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_data_change_logs_changed_at ON data_change_logs(changed_at)')
            
            conn.commit()
            
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def log_operation(self, action: str, resource_type: str, operation: str,
                     resource_id: str = None, resource_name: str = None,
                     old_data: Dict = None, new_data: Dict = None,
                     response_code: int = 200, execution_time: float = None):
        """
        记录操作日志
        
        Args:
            action (str): 操作动作
            resource_type (str): 资源类型
            operation (str): 操作类型（CREATE/READ/UPDATE/DELETE）
            resource_id (str): 资源ID
            resource_name (str): 资源名称
            old_data (Dict): 旧数据
            new_data (Dict): 新数据
            response_code (int): 响应代码
            execution_time (float): 执行时间
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 获取当前用户信息
            current_user = getattr(g, 'current_user', None)
            user_id = current_user.get('user_id') if current_user else None
            username = current_user.get('username') if current_user else None
            
            # 获取请求信息
            ip_address = request.remote_addr if request else None
            user_agent = request.headers.get('User-Agent') if request else None
            request_method = request.method if request else None
            request_path = request.path if request else None
            
            # 获取请求参数
            request_params = None
            if request:
                params = {}
                if request.args:
                    params.update(request.args.to_dict())
                if request.form:
                    params.update(request.form.to_dict())
                if request.json:
                    params.update(request.json)
                request_params = json.dumps(params, ensure_ascii=False) if params else None
            
            # 序列化数据
            old_data_json = json.dumps(old_data, ensure_ascii=False) if old_data else None
            new_data_json = json.dumps(new_data, ensure_ascii=False) if new_data else None
            
            cursor.execute('''
                INSERT INTO audit_logs (
                    user_id, username, action, resource_type, resource_id, resource_name,
                    operation, old_data, new_data, ip_address, user_agent,
                    request_method, request_path, request_params, response_code, execution_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, username, action, resource_type, resource_id, resource_name,
                operation, old_data_json, new_data_json, ip_address, user_agent,
                request_method, request_path, request_params, response_code, execution_time
            ))
            
            conn.commit()
            
        except Exception as e:
            print(f"[!] 记录操作日志失败: {str(e)}")
        finally:
            conn.close()
    
    def log_system_event(self, event_type: str, event_level: str, event_message: str,
                        event_data: Dict = None, source_module: str = None,
                        source_function: str = None, error_code: str = None,
                        stack_trace: str = None):
        """
        记录系统事件日志
        
        Args:
            event_type (str): 事件类型
            event_level (str): 事件级别（INFO/WARNING/ERROR/CRITICAL）
            event_message (str): 事件消息
            event_data (Dict): 事件数据
            source_module (str): 源模块
            source_function (str): 源函数
            error_code (str): 错误代码
            stack_trace (str): 堆栈跟踪
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            event_data_json = json.dumps(event_data, ensure_ascii=False) if event_data else None
            
            cursor.execute('''
                INSERT INTO system_event_logs (
                    event_type, event_level, event_message, event_data,
                    source_module, source_function, error_code, stack_trace
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                event_type, event_level, event_message, event_data_json,
                source_module, source_function, error_code, stack_trace
            ))
            
            conn.commit()
            
        except Exception as e:
            print(f"[!] 记录系统事件日志失败: {str(e)}")
        finally:
            conn.close()
    
    def log_login(self, username: str, login_type: str, login_result: str,
                 user_id: int = None, failure_reason: str = None):
        """
        记录登录日志
        
        Args:
            username (str): 用户名
            login_type (str): 登录类型（WEB/API/MOBILE）
            login_result (str): 登录结果（SUCCESS/FAILURE）
            user_id (int): 用户ID
            failure_reason (str): 失败原因
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            ip_address = request.remote_addr if request else None
            user_agent = request.headers.get('User-Agent') if request else None
            
            cursor.execute('''
                INSERT INTO login_logs (
                    user_id, username, login_type, login_result,
                    ip_address, user_agent, failure_reason
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                user_id, username, login_type, login_result,
                ip_address, user_agent, failure_reason
            ))
            
            conn.commit()
            
        except Exception as e:
            print(f"[!] 记录登录日志失败: {str(e)}")
        finally:
            conn.close()
    
    def log_data_change(self, table_name: str, record_id: int, operation: str,
                       field_name: str = None, old_value: Any = None,
                       new_value: Any = None, change_reason: str = None):
        """
        记录数据变更日志
        
        Args:
            table_name (str): 表名
            record_id (int): 记录ID
            operation (str): 操作类型
            field_name (str): 字段名
            old_value (Any): 旧值
            new_value (Any): 新值
            change_reason (str): 变更原因
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            current_user = getattr(g, 'current_user', None)
            user_id = current_user.get('user_id') if current_user else None
            username = current_user.get('username') if current_user else None
            
            # 转换值为字符串
            old_value_str = str(old_value) if old_value is not None else None
            new_value_str = str(new_value) if new_value is not None else None
            
            cursor.execute('''
                INSERT INTO data_change_logs (
                    table_name, record_id, operation, field_name,
                    old_value, new_value, user_id, username, change_reason
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                table_name, record_id, operation, field_name,
                old_value_str, new_value_str, user_id, username, change_reason
            ))
            
            conn.commit()
            
        except Exception as e:
            print(f"[!] 记录数据变更日志失败: {str(e)}")
        finally:
            conn.close()
    
    def get_audit_logs(self, user_id: int = None, action: str = None,
                      resource_type: str = None, start_date: str = None,
                      end_date: str = None, page: int = 1, per_page: int = 50) -> Dict:
        """
        获取审计日志
        
        Args:
            user_id (int): 用户ID
            action (str): 操作动作
            resource_type (str): 资源类型
            start_date (str): 开始日期
            end_date (str): 结束日期
            page (int): 页码
            per_page (int): 每页数量
            
        Returns:
            Dict: 审计日志数据
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 构建查询条件
            conditions = []
            params = []
            
            if user_id:
                conditions.append('user_id = ?')
                params.append(user_id)
            
            if action:
                conditions.append('action LIKE ?')
                params.append(f'%{action}%')
            
            if resource_type:
                conditions.append('resource_type = ?')
                params.append(resource_type)
            
            if start_date:
                conditions.append('created_at >= ?')
                params.append(start_date)
            
            if end_date:
                conditions.append('created_at <= ?')
                params.append(end_date)
            
            where_clause = ' WHERE ' + ' AND '.join(conditions) if conditions else ''
            
            # 获取总数
            count_sql = f'SELECT COUNT(*) FROM audit_logs{where_clause}'
            cursor.execute(count_sql, params)
            total = cursor.fetchone()[0]
            
            # 获取数据
            offset = (page - 1) * per_page
            data_sql = f'''
                SELECT id, user_id, username, action, resource_type, resource_id,
                       resource_name, operation, ip_address, request_method,
                       request_path, response_code, execution_time, created_at
                FROM audit_logs{where_clause}
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            '''
            
            cursor.execute(data_sql, params + [per_page, offset])
            logs = cursor.fetchall()
            
            # 转换为字典列表
            log_list = []
            for log in logs:
                log_dict = {
                    'id': log[0],
                    'user_id': log[1],
                    'username': log[2],
                    'action': log[3],
                    'resource_type': log[4],
                    'resource_id': log[5],
                    'resource_name': log[6],
                    'operation': log[7],
                    'ip_address': log[8],
                    'request_method': log[9],
                    'request_path': log[10],
                    'response_code': log[11],
                    'execution_time': log[12],
                    'created_at': log[13]
                }
                log_list.append(log_dict)
            
            return {
                'logs': log_list,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            }
            
        except Exception as e:
            print(f"[!] 获取审计日志失败: {str(e)}")
            return {'logs': [], 'total': 0, 'page': page, 'per_page': per_page, 'pages': 0}
        finally:
            conn.close()
    
    def get_login_logs(self, user_id: int = None, username: str = None,
                      start_date: str = None, end_date: str = None,
                      page: int = 1, per_page: int = 50) -> Dict:
        """
        获取登录日志
        
        Args:
            user_id (int): 用户ID
            username (str): 用户名
            start_date (str): 开始日期
            end_date (str): 结束日期
            page (int): 页码
            per_page (int): 每页数量
            
        Returns:
            Dict: 登录日志数据
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 构建查询条件
            conditions = []
            params = []
            
            if user_id:
                conditions.append('user_id = ?')
                params.append(user_id)
            
            if username:
                conditions.append('username LIKE ?')
                params.append(f'%{username}%')
            
            if start_date:
                conditions.append('login_time >= ?')
                params.append(start_date)
            
            if end_date:
                conditions.append('login_time <= ?')
                params.append(end_date)
            
            where_clause = ' WHERE ' + ' AND '.join(conditions) if conditions else ''
            
            # 获取总数
            count_sql = f'SELECT COUNT(*) FROM login_logs{where_clause}'
            cursor.execute(count_sql, params)
            total = cursor.fetchone()[0]
            
            # 获取数据
            offset = (page - 1) * per_page
            data_sql = f'''
                SELECT id, user_id, username, login_type, login_result,
                       ip_address, login_time, failure_reason
                FROM login_logs{where_clause}
                ORDER BY login_time DESC
                LIMIT ? OFFSET ?
            '''
            
            cursor.execute(data_sql, params + [per_page, offset])
            logs = cursor.fetchall()
            
            # 转换为字典列表
            log_list = []
            for log in logs:
                log_dict = {
                    'id': log[0],
                    'user_id': log[1],
                    'username': log[2],
                    'login_type': log[3],
                    'login_result': log[4],
                    'ip_address': log[5],
                    'login_time': log[6],
                    'failure_reason': log[7]
                }
                log_list.append(log_dict)
            
            return {
                'logs': log_list,
                'total': total,
                'page': page,
                'per_page': per_page,
                'pages': (total + per_page - 1) // per_page
            }
            
        except Exception as e:
            print(f"[!] 获取登录日志失败: {str(e)}")
            return {'logs': [], 'total': 0, 'page': page, 'per_page': per_page, 'pages': 0}
        finally:
            conn.close()

# 全局审计日志记录器实例
audit_logger = AuditLogger()

def log_operation(action: str, resource_type: str, operation: str = 'READ'):
    """
    操作日志装饰器
    
    Args:
        action (str): 操作动作
        resource_type (str): 资源类型
        operation (str): 操作类型
        
    Returns:
        Callable: 装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = datetime.now()
            
            try:
                # 执行原函数
                result = f(*args, **kwargs)
                
                # 计算执行时间
                execution_time = (datetime.now() - start_time).total_seconds()
                
                # 记录操作日志
                audit_logger.log_operation(
                    action=action,
                    resource_type=resource_type,
                    operation=operation,
                    response_code=200,
                    execution_time=execution_time
                )
                
                return result
                
            except Exception as e:
                # 计算执行时间
                execution_time = (datetime.now() - start_time).total_seconds()
                
                # 记录错误日志
                audit_logger.log_operation(
                    action=action,
                    resource_type=resource_type,
                    operation=operation,
                    response_code=500,
                    execution_time=execution_time
                )
                
                # 记录系统事件
                audit_logger.log_system_event(
                    event_type='ERROR',
                    event_level='ERROR',
                    event_message=f'操作失败: {action}',
                    event_data={'error': str(e)},
                    source_module='practical-task',
                    source_function=f.__name__
                )
                
                raise e
        
        return decorated_function
    return decorator

def init_audit_system():
    """
    初始化审计系统
    """
    try:
        audit_logger.create_tables()
        print("[*] 审计系统初始化完成")
    except Exception as e:
        print(f"[!] 审计系统初始化失败: {str(e)}")
        raise