# 路由规则和权限控制策略设计文档

## 1. 路由架构概述

### 1.1 路由层次结构
```
┌─────────────────────────────────────────────────────────────────┐
│                       API网关路由层                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │  全局路由    │  │  版本路由    │  │  模块路由    │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                      权限控制层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │  认证检查    │  │  权限验证    │  │  资源控制    │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────────┐
│                      业务模块层                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │ 用户管理     │  │ 题库管理     │  │ 考试管理     │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 路由设计原则

#### RESTful设计原则
- **资源导向**：URL表示资源，HTTP方法表示操作
- **无状态**：每个请求包含完整的处理信息
- **统一接口**：标准化的HTTP方法和状态码
- **分层系统**：清晰的层次结构和职责分离

#### 版本控制原则
- **向后兼容**：新版本保持对旧版本的兼容
- **渐进升级**：支持多版本并存和平滑迁移
- **明确标识**：清晰的版本标识和文档

#### 安全优先原则
- **最小权限**：默认拒绝，明确授权
- **深度防御**：多层安全检查
- **审计追踪**：完整的访问日志和审计

## 2. 路由规则设计

### 2.1 全局路由规则

#### 基础路由结构
```
/api/{version}/{module}/{resource}[/{id}][/{sub-resource}][/{sub-id}]

示例：
- /api/v1/users/123                    # 获取用户123
- /api/v1/users/123/profile           # 获取用户123的资料
- /api/v1/exams/456/sessions          # 获取考试456的会话列表
- /api/v1/exams/456/sessions/789      # 获取考试456的会话789
- /api/v1/questions/banks/101/items   # 获取题库101的题目列表
```

#### 路由配置结构
```yaml
routing:
  global:
    prefix: "/api"
    default_version: "v1"
    supported_versions: ["v1", "v2"]
    
  versioning:
    strategy: "url_path"  # url_path, header, query_param
    header_name: "API-Version"
    query_param: "version"
    
  modules:
    user_management:
      prefix: "/users"
      service_name: "user-management"
      load_balancer: "round_robin"
      
    question_bank:
      prefix: "/questions"
      service_name: "question-bank"
      load_balancer: "weighted_round_robin"
      
    exam_management:
      prefix: "/exams"
      service_name: "exam-management"
      load_balancer: "least_connections"
      
    grade_management:
      prefix: "/grades"
      service_name: "grade-management"
      load_balancer: "round_robin"
      
    monitoring:
      prefix: "/monitor"
      service_name: "monitoring"
      load_balancer: "round_robin"
      
    audit:
      prefix: "/audit"
      service_name: "audit"
      load_balancer: "round_robin"
```

### 2.2 模块路由详细定义

#### 用户管理模块路由
```yaml
user_management_routes:
  # 用户基础操作
  - path: "/api/v1/users"
    methods: ["GET"]
    operation: "list_users"
    permission: "user:list:read:all"
    description: "获取用户列表"
    
  - path: "/api/v1/users"
    methods: ["POST"]
    operation: "create_user"
    permission: "user:create:write:all"
    description: "创建新用户"
    
  - path: "/api/v1/users/{user_id}"
    methods: ["GET"]
    operation: "get_user"
    permission: "user:profile:read:own|all"
    description: "获取用户详情"
    
  - path: "/api/v1/users/{user_id}"
    methods: ["PUT"]
    operation: "update_user"
    permission: "user:profile:write:own|all"
    description: "更新用户信息"
    
  - path: "/api/v1/users/{user_id}"
    methods: ["DELETE"]
    operation: "delete_user"
    permission: "user:delete:write:all"
    description: "删除用户"
    
  # 用户资料操作
  - path: "/api/v1/users/{user_id}/profile"
    methods: ["GET"]
    operation: "get_user_profile"
    permission: "user:profile:read:own|all"
    description: "获取用户资料"
    
  - path: "/api/v1/users/{user_id}/profile"
    methods: ["PUT"]
    operation: "update_user_profile"
    permission: "user:profile:write:own|all"
    description: "更新用户资料"
    
  # 用户角色操作
  - path: "/api/v1/users/{user_id}/roles"
    methods: ["GET"]
    operation: "get_user_roles"
    permission: "user:role:read:all"
    description: "获取用户角色"
    
  - path: "/api/v1/users/{user_id}/roles"
    methods: ["POST"]
    operation: "assign_user_role"
    permission: "user:role:write:all"
    description: "分配用户角色"
    
  - path: "/api/v1/users/{user_id}/roles/{role_id}"
    methods: ["DELETE"]
    operation: "revoke_user_role"
    permission: "user:role:write:all"
    description: "撤销用户角色"
    
  # 认证相关
  - path: "/api/v1/auth/login"
    methods: ["POST"]
    operation: "user_login"
    permission: "public"
    description: "用户登录"
    
  - path: "/api/v1/auth/logout"
    methods: ["POST"]
    operation: "user_logout"
    permission: "authenticated"
    description: "用户登出"
    
  - path: "/api/v1/auth/refresh"
    methods: ["POST"]
    operation: "refresh_token"
    permission: "authenticated"
    description: "刷新Token"
    
  - path: "/api/v1/auth/password/change"
    methods: ["POST"]
    operation: "change_password"
    permission: "user:password:write:own"
    description: "修改密码"
```

#### 题库管理模块路由
```yaml
question_bank_routes:
  # 题库操作
  - path: "/api/v1/questions/banks"
    methods: ["GET"]
    operation: "list_question_banks"
    permission: "question:bank:read:all"
    description: "获取题库列表"
    
  - path: "/api/v1/questions/banks"
    methods: ["POST"]
    operation: "create_question_bank"
    permission: "question:bank:write:all"
    description: "创建题库"
    
  - path: "/api/v1/questions/banks/{bank_id}"
    methods: ["GET"]
    operation: "get_question_bank"
    permission: "question:bank:read:own|all"
    description: "获取题库详情"
    
  - path: "/api/v1/questions/banks/{bank_id}"
    methods: ["PUT"]
    operation: "update_question_bank"
    permission: "question:bank:write:own|all"
    description: "更新题库"
    
  - path: "/api/v1/questions/banks/{bank_id}"
    methods: ["DELETE"]
    operation: "delete_question_bank"
    permission: "question:bank:delete:own|all"
    description: "删除题库"
    
  # 题目操作
  - path: "/api/v1/questions/banks/{bank_id}/items"
    methods: ["GET"]
    operation: "list_questions"
    permission: "question:item:read:own|all"
    description: "获取题目列表"
    
  - path: "/api/v1/questions/banks/{bank_id}/items"
    methods: ["POST"]
    operation: "create_question"
    permission: "question:item:write:own|all"
    description: "创建题目"
    
  - path: "/api/v1/questions/banks/{bank_id}/items/{question_id}"
    methods: ["GET"]
    operation: "get_question"
    permission: "question:item:read:own|all"
    description: "获取题目详情"
    
  - path: "/api/v1/questions/banks/{bank_id}/items/{question_id}"
    methods: ["PUT"]
    operation: "update_question"
    permission: "question:item:write:own|all"
    description: "更新题目"
    
  - path: "/api/v1/questions/banks/{bank_id}/items/{question_id}"
    methods: ["DELETE"]
    operation: "delete_question"
    permission: "question:item:delete:own|all"
    description: "删除题目"
    
  # 题目分类
  - path: "/api/v1/questions/categories"
    methods: ["GET"]
    operation: "list_categories"
    permission: "question:category:read:all"
    description: "获取题目分类"
    
  - path: "/api/v1/questions/categories"
    methods: ["POST"]
    operation: "create_category"
    permission: "question:category:write:all"
    description: "创建题目分类"
```

#### 考试管理模块路由
```yaml
exam_management_routes:
  # 考试操作
  - path: "/api/v1/exams"
    methods: ["GET"]
    operation: "list_exams"
    permission: "exam:list:read:all"
    description: "获取考试列表"
    
  - path: "/api/v1/exams"
    methods: ["POST"]
    operation: "create_exam"
    permission: "exam:create:write:all"
    description: "创建考试"
    
  - path: "/api/v1/exams/{exam_id}"
    methods: ["GET"]
    operation: "get_exam"
    permission: "exam:detail:read:assigned|all"
    description: "获取考试详情"
    
  - path: "/api/v1/exams/{exam_id}"
    methods: ["PUT"]
    operation: "update_exam"
    permission: "exam:update:write:own|all"
    description: "更新考试"
    
  - path: "/api/v1/exams/{exam_id}"
    methods: ["DELETE"]
    operation: "delete_exam"
    permission: "exam:delete:write:own|all"
    description: "删除考试"
    
  # 考试会话操作
  - path: "/api/v1/exams/{exam_id}/sessions"
    methods: ["GET"]
    operation: "list_exam_sessions"
    permission: "exam:session:read:assigned|all"
    description: "获取考试会话列表"
    
  - path: "/api/v1/exams/{exam_id}/sessions"
    methods: ["POST"]
    operation: "create_exam_session"
    permission: "exam:session:write:all"
    description: "创建考试会话"
    
  - path: "/api/v1/exams/{exam_id}/sessions/{session_id}"
    methods: ["GET"]
    operation: "get_exam_session"
    permission: "exam:session:read:assigned|all"
    description: "获取考试会话详情"
    
  - path: "/api/v1/exams/{exam_id}/sessions/{session_id}/join"
    methods: ["POST"]
    operation: "join_exam_session"
    permission: "exam:session:join:assigned"
    description: "加入考试会话"
    
  - path: "/api/v1/exams/{exam_id}/sessions/{session_id}/submit"
    methods: ["POST"]
    operation: "submit_exam"
    permission: "exam:answer:write:own"
    description: "提交考试答案"
    
  # 试卷操作
  - path: "/api/v1/exams/{exam_id}/papers"
    methods: ["GET"]
    operation: "list_exam_papers"
    permission: "exam:paper:read:assigned|all"
    description: "获取试卷列表"
    
  - path: "/api/v1/exams/{exam_id}/papers/{paper_id}"
    methods: ["GET"]
    operation: "get_exam_paper"
    permission: "exam:paper:read:own|assigned|all"
    description: "获取试卷内容"
```

### 2.3 动态路由管理

#### 路由管理器
```python
class RouteManager:
    def __init__(self):
        self.routes = {}
        self.route_patterns = {}
        self.middleware_stack = []
        self.permission_checker = PermissionChecker()
        
    def register_route(self, route_config):
        """
        注册路由
        
        Args:
            route_config: {
                'path': '/api/v1/users/{user_id}',
                'methods': ['GET', 'PUT'],
                'operation': 'get_user',
                'permission': 'user:profile:read:own|all',
                'service': 'user-management',
                'middleware': ['auth', 'rate_limit'],
                'timeout': 30,
                'retry_config': {...}
            }
        """
        route_key = self._generate_route_key(route_config['path'], route_config['methods'])
        
        # 编译路径模式
        pattern = self._compile_path_pattern(route_config['path'])
        
        # 存储路由配置
        self.routes[route_key] = {
            'config': route_config,
            'pattern': pattern,
            'path_params': self._extract_path_params(route_config['path'])
        }
        
        # 添加到模式匹配器
        self.route_patterns[pattern] = route_key
        
    def match_route(self, path, method):
        """
        匹配路由
        
        Args:
            path: 请求路径
            method: HTTP方法
            
        Returns:
            dict: 匹配的路由信息和路径参数
        """
        for pattern, route_key in self.route_patterns.items():
            match = pattern.match(path)
            if match:
                route_info = self.routes[route_key]
                if method in route_info['config']['methods']:
                    return {
                        'route_config': route_info['config'],
                        'path_params': match.groupdict(),
                        'route_key': route_key
                    }
                    
        return None
        
    def _compile_path_pattern(self, path):
        """编译路径模式为正则表达式"""
        import re
        
        # 将 {param} 转换为命名捕获组
        pattern = re.sub(r'\{([^}]+)\}', r'(?P<\1>[^/]+)', path)
        # 确保完全匹配
        pattern = f'^{pattern}$'
        
        return re.compile(pattern)
        
    def _extract_path_params(self, path):
        """提取路径参数名称"""
        import re
        return re.findall(r'\{([^}]+)\}', path)
        
    def _generate_route_key(self, path, methods):
        """生成路由键"""
        methods_str = '|'.join(sorted(methods))
        return f"{path}::{methods_str}"
        
    def get_route_config(self, route_key):
        """获取路由配置"""
        return self.routes.get(route_key, {}).get('config')
        
    def list_routes(self, service_name=None):
        """列出所有路由"""
        routes = []
        for route_key, route_info in self.routes.items():
            config = route_info['config']
            if service_name is None or config.get('service') == service_name:
                routes.append({
                    'path': config['path'],
                    'methods': config['methods'],
                    'operation': config['operation'],
                    'permission': config['permission'],
                    'service': config.get('service'),
                    'description': config.get('description')
                })
        return routes
```

## 3. 权限控制策略

### 3.1 权限模型设计

#### 权限层次结构
```
权限格式：{module}:{resource}:{action}:{scope}

模块 (module):
- user: 用户管理
- question: 题库管理
- exam: 考试管理
- grade: 成绩管理
- monitor: 监控管理
- audit: 审计管理
- system: 系统管理

资源 (resource):
- profile: 用户资料
- role: 角色
- bank: 题库
- item: 题目
- session: 考试会话
- paper: 试卷
- result: 成绩
- log: 日志

操作 (action):
- read: 读取
- write: 写入/修改
- delete: 删除
- create: 创建
- execute: 执行

范围 (scope):
- own: 自己的资源
- assigned: 分配给自己的资源
- department: 部门范围
- class: 班级范围
- all: 所有资源
```

#### 权限映射表
```yaml
permission_mappings:
  # 用户管理权限
  user_permissions:
    "user:profile:read:own": "读取自己的用户资料"
    "user:profile:read:all": "读取所有用户资料"
    "user:profile:write:own": "修改自己的用户资料"
    "user:profile:write:all": "修改所有用户资料"
    "user:list:read:all": "查看用户列表"
    "user:create:write:all": "创建用户"
    "user:delete:write:all": "删除用户"
    "user:role:read:all": "查看用户角色"
    "user:role:write:all": "管理用户角色"
    "user:password:write:own": "修改自己的密码"
    
  # 题库管理权限
  question_permissions:
    "question:bank:read:all": "查看所有题库"
    "question:bank:read:own": "查看自己创建的题库"
    "question:bank:write:all": "管理所有题库"
    "question:bank:write:own": "管理自己创建的题库"
    "question:bank:delete:own": "删除自己创建的题库"
    "question:item:read:all": "查看所有题目"
    "question:item:read:own": "查看自己创建的题目"
    "question:item:write:all": "管理所有题目"
    "question:item:write:own": "管理自己创建的题目"
    "question:item:delete:own": "删除自己创建的题目"
    "question:category:read:all": "查看题目分类"
    "question:category:write:all": "管理题目分类"
    
  # 考试管理权限
  exam_permissions:
    "exam:list:read:all": "查看所有考试"
    "exam:detail:read:assigned": "查看分配给自己的考试详情"
    "exam:detail:read:all": "查看所有考试详情"
    "exam:create:write:all": "创建考试"
    "exam:update:write:own": "修改自己创建的考试"
    "exam:update:write:all": "修改所有考试"
    "exam:delete:write:own": "删除自己创建的考试"
    "exam:session:read:assigned": "查看分配的考试会话"
    "exam:session:read:all": "查看所有考试会话"
    "exam:session:write:all": "管理考试会话"
    "exam:session:join:assigned": "参加分配的考试"
    "exam:paper:read:own": "查看自己的试卷"
    "exam:paper:read:assigned": "查看分配的试卷"
    "exam:paper:read:all": "查看所有试卷"
    "exam:answer:write:own": "提交自己的答案"
    
  # 成绩管理权限
  grade_permissions:
    "grade:result:read:own": "查看自己的成绩"
    "grade:result:read:assigned": "查看分配的成绩"
    "grade:result:read:class": "查看班级成绩"
    "grade:result:read:all": "查看所有成绩"
    "grade:manual:write:assigned": "手动阅卷分配的试卷"
    "grade:manual:write:all": "手动阅卷所有试卷"
    "grade:export:read:assigned": "导出分配的成绩"
    "grade:export:read:all": "导出所有成绩"
    
  # 监控管理权限
  monitor_permissions:
    "monitor:session:read:assigned": "监控分配的考试会话"
    "monitor:session:read:all": "监控所有考试会话"
    "monitor:violation:write:assigned": "记录分配会话的违规行为"
    "monitor:violation:write:all": "记录所有违规行为"
    "monitor:system:read:all": "查看系统监控信息"
    
  # 审计管理权限
  audit_permissions:
    "audit:log:read:own": "查看自己的操作日志"
    "audit:log:read:all": "查看所有操作日志"
    "audit:security:read:all": "查看安全审计日志"
    "audit:export:read:all": "导出审计日志"
    
  # 系统管理权限
  system_permissions:
    "system:config:read:all": "查看系统配置"
    "system:config:write:all": "修改系统配置"
    "system:backup:execute:all": "执行系统备份"
    "system:maintenance:execute:all": "执行系统维护"
```

### 3.2 角色权限配置

#### 预定义角色
```yaml
roles:
  # 超级管理员
  super_admin:
    name: "超级管理员"
    description: "系统最高权限管理员"
    permissions:
      - "*:*:*:*"  # 所有权限
    built_in: true
    deletable: false
    
  # 系统管理员
  system_admin:
    name: "系统管理员"
    description: "系统配置和用户管理"
    permissions:
      - "user:*:*:all"
      - "system:*:*:all"
      - "audit:log:read:all"
      - "monitor:system:read:all"
    parent_roles: []
    
  # 考试管理员
  exam_admin:
    name: "考试管理员"
    description: "考试和题库管理"
    permissions:
      - "exam:*:*:all"
      - "question:*:*:all"
      - "grade:result:read:all"
      - "grade:export:read:all"
      - "monitor:session:read:all"
      - "user:list:read:all"
      - "user:profile:read:all"
    parent_roles: []
    
  # 考评员
  examiner:
    name: "考评员"
    description: "考场监控和管理"
    permissions:
      - "exam:session:read:assigned"
      - "exam:session:write:assigned"
      - "monitor:session:read:assigned"
      - "monitor:violation:write:assigned"
      - "user:profile:read:own"
      - "user:password:write:own"
    parent_roles: []
    
  # 教师
  teacher:
    name: "教师"
    description: "阅卷和成绩管理"
    permissions:
      - "question:bank:read:all"
      - "question:item:read:all"
      - "question:bank:write:own"
      - "question:item:write:own"
      - "grade:manual:write:assigned"
      - "grade:result:read:assigned"
      - "grade:export:read:assigned"
      - "exam:detail:read:assigned"
      - "exam:paper:read:assigned"
      - "user:profile:read:own"
      - "user:password:write:own"
    parent_roles: []
    
  # 学生
  student:
    name: "学生"
    description: "参加考试"
    permissions:
      - "exam:session:join:assigned"
      - "exam:paper:read:own"
      - "exam:answer:write:own"
      - "grade:result:read:own"
      - "user:profile:read:own"
      - "user:profile:write:own"
      - "user:password:write:own"
    parent_roles: []
    
  # 只读用户
  readonly_user:
    name: "只读用户"
    description: "只能查看分配的信息"
    permissions:
      - "exam:detail:read:assigned"
      - "grade:result:read:assigned"
      - "user:profile:read:own"
    parent_roles: []
```

### 3.3 权限检查机制

#### 权限检查器
```python
class PermissionChecker:
    def __init__(self, user_service, role_service, cache_service):
        self.user_service = user_service
        self.role_service = role_service
        self.cache_service = cache_service
        self.cache_ttl = 300  # 5分钟缓存
        
    def check_permission(self, user_id, required_permission, context=None):
        """
        检查用户权限
        
        Args:
            user_id: 用户ID
            required_permission: 所需权限
            context: 权限检查上下文
            
        Returns:
            bool: 是否有权限
        """
        # 获取用户权限（带缓存）
        user_permissions = self._get_user_permissions(user_id)
        
        # 检查权限
        return self._check_permission_match(user_permissions, required_permission, context)
        
    def _get_user_permissions(self, user_id):
        """获取用户权限（带缓存）"""
        cache_key = f"user_permissions:{user_id}"
        
        # 尝试从缓存获取
        cached_permissions = self.cache_service.get(cache_key)
        if cached_permissions:
            return cached_permissions
            
        # 从数据库获取
        user = self.user_service.get_user(user_id)
        if not user:
            return []
            
        permissions = set()
        
        # 获取用户直接权限
        direct_permissions = self.user_service.get_user_permissions(user_id)
        permissions.update(direct_permissions)
        
        # 获取角色权限
        user_roles = self.user_service.get_user_roles(user_id)
        for role in user_roles:
            role_permissions = self.role_service.get_role_permissions(role.id)
            permissions.update(role_permissions)
            
            # 处理角色继承
            parent_permissions = self._get_inherited_permissions(role.id)
            permissions.update(parent_permissions)
            
        permissions_list = list(permissions)
        
        # 缓存权限
        self.cache_service.set(cache_key, permissions_list, self.cache_ttl)
        
        return permissions_list
        
    def _get_inherited_permissions(self, role_id):
        """获取继承的权限"""
        permissions = set()
        parent_roles = self.role_service.get_parent_roles(role_id)
        
        for parent_role in parent_roles:
            parent_permissions = self.role_service.get_role_permissions(parent_role.id)
            permissions.update(parent_permissions)
            
            # 递归获取父角色的权限
            inherited_permissions = self._get_inherited_permissions(parent_role.id)
            permissions.update(inherited_permissions)
            
        return permissions
        
    def _check_permission_match(self, user_permissions, required_permission, context):
        """检查权限匹配"""
        # 超级管理员权限
        if "*:*:*:*" in user_permissions:
            return True
            
        # 直接匹配
        if required_permission in user_permissions:
            return True
            
        # 通配符匹配
        for user_perm in user_permissions:
            if self._permission_wildcard_match(user_perm, required_permission, context):
                return True
                
        # 多权限匹配（OR逻辑）
        if '|' in required_permission:
            alternative_permissions = required_permission.split('|')
            for alt_perm in alternative_permissions:
                if self._check_permission_match(user_permissions, alt_perm.strip(), context):
                    return True
                    
        return False
        
    def _permission_wildcard_match(self, user_permission, required_permission, context):
        """权限通配符匹配"""
        user_parts = user_permission.split(':')
        required_parts = required_permission.split(':')
        
        if len(user_parts) != 4 or len(required_parts) != 4:
            return False
            
        # 逐部分检查
        for i, (user_part, required_part) in enumerate(zip(user_parts, required_parts)):
            if user_part == '*':
                continue
            elif user_part != required_part:
                # 特殊处理scope部分
                if i == 3:  # scope部分
                    return self._check_scope_permission(user_part, required_part, context)
                else:
                    return False
                    
        return True
        
    def _check_scope_permission(self, user_scope, required_scope, context):
        """检查权限范围"""
        scope_hierarchy = {
            'all': 4,
            'department': 3,
            'class': 2,
            'assigned': 2,
            'own': 1
        }
        
        user_level = scope_hierarchy.get(user_scope, 0)
        required_level = scope_hierarchy.get(required_scope, 0)
        
        # 用户权限级别必须大于等于所需级别
        if user_level < required_level:
            return False
            
        # 基于上下文的进一步检查
        if context:
            return self._check_scope_context(user_scope, required_scope, context)
            
        return True
        
    def _check_scope_context(self, user_scope, required_scope, context):
        """基于上下文检查权限范围"""
        user_id = context.get('user_id')
        target_user_id = context.get('target_user_id')
        resource_owner_id = context.get('resource_owner_id')
        user_department = context.get('user_department')
        target_department = context.get('target_department')
        user_class = context.get('user_class')
        target_class = context.get('target_class')
        assigned_resources = context.get('assigned_resources', [])
        resource_id = context.get('resource_id')
        
        if user_scope == 'own':
            # 检查是否为自己的资源
            if target_user_id:
                return user_id == target_user_id
            if resource_owner_id:
                return user_id == resource_owner_id
            return True
            
        elif user_scope == 'assigned':
            # 检查是否为分配的资源
            if resource_id:
                return resource_id in assigned_resources
            return True
            
        elif user_scope == 'class':
            # 检查是否为同班级
            if target_class:
                return user_class == target_class
            return True
            
        elif user_scope == 'department':
            # 检查是否为同部门
            if target_department:
                return user_department == target_department
            return True
            
        elif user_scope == 'all':
            return True
            
        return False
        
    def invalidate_user_permissions_cache(self, user_id):
        """清除用户权限缓存"""
        cache_key = f"user_permissions:{user_id}"
        self.cache_service.delete(cache_key)
        
    def batch_check_permissions(self, user_id, permissions_list, context=None):
        """批量检查权限"""
        results = {}
        user_permissions = self._get_user_permissions(user_id)
        
        for permission in permissions_list:
            results[permission] = self._check_permission_match(
                user_permissions, permission, context
            )
            
        return results
```

### 3.4 动态权限控制

#### 基于时间的权限控制
```python
class TimeBasedPermissionController:
    def __init__(self):
        self.time_based_permissions = {}
        
    def add_time_based_permission(self, user_id, permission, start_time, end_time, days_of_week=None, hours_of_day=None):
        """
        添加基于时间的权限
        
        Args:
            user_id: 用户ID
            permission: 权限
            start_time: 开始时间
            end_time: 结束时间
            days_of_week: 星期几（0-6，0为周一）
            hours_of_day: 小时范围（0-23）
        """
        if user_id not in self.time_based_permissions:
            self.time_based_permissions[user_id] = []
            
        self.time_based_permissions[user_id].append({
            'permission': permission,
            'start_time': start_time,
            'end_time': end_time,
            'days_of_week': days_of_week or [],
            'hours_of_day': hours_of_day or []
        })
        
    def check_time_based_permission(self, user_id, permission, check_time=None):
        """
        检查基于时间的权限
        
        Args:
            user_id: 用户ID
            permission: 权限
            check_time: 检查时间（默认为当前时间）
            
        Returns:
            bool: 是否有权限
        """
        if check_time is None:
            check_time = datetime.now()
            
        user_time_permissions = self.time_based_permissions.get(user_id, [])
        
        for time_perm in user_time_permissions:
            if time_perm['permission'] == permission:
                if self._is_time_valid(time_perm, check_time):
                    return True
                    
        return False
        
    def _is_time_valid(self, time_permission, check_time):
        """检查时间是否有效"""
        # 检查时间范围
        if check_time < time_permission['start_time'] or check_time > time_permission['end_time']:
            return False
            
        # 检查星期几
        if time_permission['days_of_week'] and check_time.weekday() not in time_permission['days_of_week']:
            return False
            
        # 检查小时范围
        if time_permission['hours_of_day'] and check_time.hour not in time_permission['hours_of_day']:
            return False
            
        return True
```

#### 基于资源的权限控制
```python
class ResourceBasedPermissionController:
    def __init__(self, database):
        self.db = database
        
    def grant_resource_permission(self, user_id, resource_type, resource_id, permissions):
        """
        授予资源权限
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            permissions: 权限列表
        """
        for permission in permissions:
            self.db.execute(
                "INSERT INTO resource_permissions (user_id, resource_type, resource_id, permission) VALUES (?, ?, ?, ?)",
                (user_id, resource_type, resource_id, permission)
            )
            
    def revoke_resource_permission(self, user_id, resource_type, resource_id, permission=None):
        """
        撤销资源权限
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            permission: 权限（如果为None则撤销所有权限）
        """
        if permission:
            self.db.execute(
                "DELETE FROM resource_permissions WHERE user_id=? AND resource_type=? AND resource_id=? AND permission=?",
                (user_id, resource_type, resource_id, permission)
            )
        else:
            self.db.execute(
                "DELETE FROM resource_permissions WHERE user_id=? AND resource_type=? AND resource_id=?",
                (user_id, resource_type, resource_id)
            )
            
    def check_resource_permission(self, user_id, resource_type, resource_id, permission):
        """
        检查资源权限
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            permission: 权限
            
        Returns:
            bool: 是否有权限
        """
        result = self.db.execute(
            "SELECT COUNT(*) FROM resource_permissions WHERE user_id=? AND resource_type=? AND resource_id=? AND permission=?",
            (user_id, resource_type, resource_id, permission)
        ).fetchone()
        
        return result[0] > 0
        
    def get_user_resource_permissions(self, user_id, resource_type=None):
        """
        获取用户的资源权限
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型（可选）
            
        Returns:
            list: 资源权限列表
        """
        if resource_type:
            results = self.db.execute(
                "SELECT resource_type, resource_id, permission FROM resource_permissions WHERE user_id=? AND resource_type=?",
                (user_id, resource_type)
            ).fetchall()
        else:
            results = self.db.execute(
                "SELECT resource_type, resource_id, permission FROM resource_permissions WHERE user_id=?",
                (user_id,)
            ).fetchall()
            
        return [{
            'resource_type': row[0],
            'resource_id': row[1],
            'permission': row[2]
        } for row in results]
```

## 4. 路由中间件

### 4.1 认证中间件

#### JWT认证中间件
```python
class JWTAuthenticationMiddleware:
    def __init__(self, token_manager, exempt_paths=None):
        self.token_manager = token_manager
        self.exempt_paths = exempt_paths or [
            '/api/v1/auth/login',
            '/api/v1/auth/register',
            '/health',
            '/docs'
        ]
        
    def process_request(self, request, route_config):
        """
        处理请求认证
        
        Args:
            request: 请求对象
            route_config: 路由配置
            
        Returns:
            dict: 认证结果
        """
        # 检查是否为免认证路径
        if self._is_exempt_path(request.path):
            return {'authenticated': False, 'user': None}
            
        # 检查路由是否需要认证
        required_permission = route_config.get('permission')
        if required_permission == 'public':
            return {'authenticated': False, 'user': None}
            
        # 提取Token
        token = self._extract_token(request)
        if not token:
            raise UnauthorizedError("Missing authentication token")
            
        # 验证Token
        try:
            payload = self.token_manager.verify_token(token)
        except Exception as e:
            raise UnauthorizedError(f"Invalid token: {e}")
            
        # 检查Token黑名单
        if self.token_manager.is_blacklisted(payload.get('jti')):
            raise UnauthorizedError("Token has been revoked")
            
        # 获取用户信息
        user_info = {
            'user_id': payload['user_id'],
            'username': payload['username'],
            'role': payload['role'],
            'permissions': payload.get('permissions', []),
            'session_id': payload.get('session_id')
        }
        
        return {'authenticated': True, 'user': user_info}
        
    def _is_exempt_path(self, path):
        """检查是否为免认证路径"""
        for exempt_path in self.exempt_paths:
            if path.startswith(exempt_path):
                return True
        return False
        
    def _extract_token(self, request):
        """从请求中提取Token"""
        auth_header = request.headers.get('Authorization', '')
        if auth_header.startswith('Bearer '):
            return auth_header[7:]
        return None
```

### 4.2 权限检查中间件

#### 权限验证中间件
```python
class PermissionCheckMiddleware:
    def __init__(self, permission_checker):
        self.permission_checker = permission_checker
        
    def process_request(self, request, route_config, auth_result):
        """
        处理权限检查
        
        Args:
            request: 请求对象
            route_config: 路由配置
            auth_result: 认证结果
            
        Returns:
            dict: 权限检查结果
        """
        required_permission = route_config.get('permission')
        
        # 公开接口，无需权限检查
        if required_permission == 'public':
            return {'authorized': True}
            
        # 仅需认证，无需特定权限
        if required_permission == 'authenticated':
            if auth_result['authenticated']:
                return {'authorized': True}
            else:
                raise UnauthorizedError("Authentication required")
                
        # 需要特定权限
        if not auth_result['authenticated']:
            raise UnauthorizedError("Authentication required")
            
        user = auth_result['user']
        
        # 构建权限检查上下文
        context = self._build_permission_context(request, route_config, user)
        
        # 检查权限
        has_permission = self.permission_checker.check_permission(
            user['user_id'], required_permission, context
        )
        
        if not has_permission:
            raise ForbiddenError(f"Insufficient permissions: {required_permission}")
            
        return {'authorized': True}
        
    def _build_permission_context(self, request, route_config, user):
        """构建权限检查上下文"""
        # 从路径参数中提取资源信息
        path_params = getattr(request, 'path_params', {})
        
        context = {
            'user_id': user['user_id'],
            'user_role': user['role'],
            'request_path': request.path,
            'request_method': request.method,
            'path_params': path_params
        }
        
        # 添加目标用户ID（如果存在）
        if 'user_id' in path_params:
            context['target_user_id'] = path_params['user_id']
            
        # 添加资源ID（如果存在）
        resource_id_fields = ['exam_id', 'bank_id', 'question_id', 'session_id']
        for field in resource_id_fields:
            if field in path_params:
                context['resource_id'] = path_params[field]
                context['resource_type'] = field.replace('_id', '')
                break
                
        # 从用户服务获取额外信息
        try:
            user_details = self._get_user_details(user['user_id'])
            context.update({
                'user_department': user_details.get('department_id'),
                'user_class': user_details.get('class_id'),
                'assigned_resources': user_details.get('assigned_resources', [])
            })
        except Exception:
            # 获取用户详情失败，使用默认值
            pass
            
        return context
        
    def _get_user_details(self, user_id):
        """获取用户详细信息"""
        # 这里应该调用用户服务获取详细信息
        # 为了示例，返回空字典
        return {}
```

### 4.3 限流中间件

#### 速率限制中间件
```python
class RateLimitMiddleware:
    def __init__(self, redis_client):
        self.redis = redis_client
        self.default_limits = {
            'requests_per_minute': 60,
            'requests_per_hour': 1000,
            'requests_per_day': 10000
        }
        
    def process_request(self, request, route_config, auth_result):
        """
        处理速率限制
        
        Args:
            request: 请求对象
            route_config: 路由配置
            auth_result: 认证结果
            
        Returns:
            dict: 限流检查结果
        """
        # 获取限流配置
        rate_limit_config = route_config.get('rate_limit', self.default_limits)
        
        # 确定限流键
        rate_limit_key = self._get_rate_limit_key(request, auth_result)
        
        # 检查各个时间窗口的限制
        for period, limit in rate_limit_config.items():
            if not self._check_rate_limit(rate_limit_key, period, limit):
                raise RateLimitExceededError(f"Rate limit exceeded: {period}")
                
        return {'rate_limit_passed': True}
        
    def _get_rate_limit_key(self, request, auth_result):
        """获取限流键"""
        if auth_result['authenticated']:
            # 已认证用户，使用用户ID
            return f"rate_limit:user:{auth_result['user']['user_id']}"
        else:
            # 未认证用户，使用IP地址
            client_ip = request.headers.get('X-Forwarded-For', request.remote_addr)
            return f"rate_limit:ip:{client_ip}"
            
    def _check_rate_limit(self, key, period, limit):
        """检查速率限制"""
        # 解析时间窗口
        if period == 'requests_per_minute':
            window_seconds = 60
        elif period == 'requests_per_hour':
            window_seconds = 3600
        elif period == 'requests_per_day':
            window_seconds = 86400
        else:
            return True  # 未知时间窗口，跳过检查
            
        # 使用滑动窗口算法
        now = int(time.time())
        window_start = now - window_seconds
        
        # 清理过期记录
        self.redis.zremrangebyscore(key, 0, window_start)
        
        # 获取当前窗口内的请求数
        current_requests = self.redis.zcard(key)
        
        if current_requests >= limit:
            return False
            
        # 记录当前请求
        self.redis.zadd(key, {str(now): now})
        self.redis.expire(key, window_seconds)
        
        return True
```

---

## 总结

本路由规则和权限控制策略设计文档为职业技能等级考试系统建立了完整的访问控制框架，包括：

### 🛣️ **路由管理**
1. **RESTful设计**：标准化的API路由结构
2. **版本控制**：支持多版本API并存
3. **动态路由**：灵活的路由注册和匹配机制
4. **模块化设计**：清晰的模块路由划分

### 🔐 **权限控制**
1. **RBAC模型**：基于角色的访问控制
2. **细粒度权限**：资源级别的权限控制
3. **动态权限**：基于时间和资源的权限控制
4. **权限继承**：支持角色继承和权限传递

### 🛡️ **安全保障**
- **多层验证**：认证、授权、限流三重保护
- **上下文感知**：基于请求上下文的权限检查
- **缓存优化**：高效的权限缓存机制
- **审计追踪**：完整的访问日志记录

### 🚀 **性能优化**
- **权限缓存**：减少数据库查询开销
- **智能路由**：高效的路由匹配算法
- **限流保护**：防止系统过载
- **中间件架构**：模块化的请求处理流程

本设计确保了系统的安全性、可扩展性和高性能，为考试系统提供了坚实的访问控制基础。