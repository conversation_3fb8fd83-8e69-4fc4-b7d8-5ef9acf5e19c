#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用配置模块

本模块定义了文件与项目管理工具的所有配置参数。
包括数据库配置、文件上传配置、安全配置等。

作者: 系统开发团队
创建时间: 2024-01-01
版本: v1.0
"""

import os
from datetime import timedelta

class Config:
    """
    基础配置类
    
    定义所有环境共用的基础配置参数。
    其他环境配置类应该继承此类。
    """
    
    # 应用基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'file-project-manager-secret-key-2024'
    
    # 数据库配置
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'connect_args': {
            'check_same_thread': False,  # SQLite特定配置
            'timeout': 20
        }
    }
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-key-2024'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)
    JWT_ALGORITHM = 'HS256'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {
        # Office文档
        'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
        # PDF文档
        'pdf',
        # 文本文档
        'txt', 'md', 'rtf',
        # 图片文件
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg',
        # 压缩文件
        'zip', 'rar', '7z', 'tar', 'gz',
        # 其他常用格式
        'csv', 'json', 'xml', 'html', 'css', 'js'
    }
    
    # 文件存储配置
    FILE_STORAGE_TYPE = 'local'  # local, oss, s3
    FILE_URL_EXPIRE_TIME = 3600  # 文件URL过期时间（秒）
    
    # 分页配置
    ITEMS_PER_PAGE = 20
    MAX_ITEMS_PER_PAGE = 100
    
    # 缓存配置
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs', 'app.log')
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # CORS配置
    CORS_ORIGINS = ['http://localhost:3000', 'http://127.0.0.1:3000']
    CORS_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    CORS_ALLOW_HEADERS = ['Content-Type', 'Authorization']
    
    # API配置
    API_VERSION = 'v1'
    API_PREFIX = f'/api/{API_VERSION}'
    
    # 安全配置
    BCRYPT_LOG_ROUNDS = 12
    PASSWORD_MIN_LENGTH = 6
    
    # 任务配置
    TASK_STATUS_OPTIONS = ['pending', 'in_progress', 'completed', 'cancelled']
    TASK_PRIORITY_OPTIONS = ['low', 'medium', 'high', 'urgent']
    
    # 项目配置
    PROJECT_STATUS_OPTIONS = ['active', 'completed', 'paused', 'cancelled']
    PROJECT_MEMBER_ROLES = ['owner', 'manager', 'member', 'viewer']
    
    # 文件分享配置
    SHARE_TOKEN_LENGTH = 32
    SHARE_DEFAULT_EXPIRE_DAYS = 7
    SHARE_MAX_DOWNLOADS = 100
    
    # 搜索配置
    SEARCH_RESULTS_LIMIT = 50
    SEARCH_MIN_QUERY_LENGTH = 2
    
    @staticmethod
    def init_app(app):
        """
        初始化应用配置
        
        参数:
            app: Flask应用实例
            
        示例:
            >>> from flask import Flask
            >>> app = Flask(__name__)
            >>> Config.init_app(app)
        """
        # 确保上传目录存在
        upload_folder = app.config.get('UPLOAD_FOLDER')
        if upload_folder and not os.path.exists(upload_folder):
            os.makedirs(upload_folder, exist_ok=True)
        
        # 确保日志目录存在
        log_file = app.config.get('LOG_FILE')
        if log_file:
            log_dir = os.path.dirname(log_file)
            if not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

class DevelopmentConfig(Config):
    """
    开发环境配置
    
    用于本地开发环境的配置参数。
    启用调试模式，使用SQLite数据库。
    """
    
    DEBUG = True
    TESTING = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'dev.db')
    
    # 日志配置
    LOG_LEVEL = 'DEBUG'
    
    # 开发环境允许所有来源的CORS请求
    CORS_ORIGINS = '*'
    
    @classmethod
    def init_app(cls, app):
        """
        初始化开发环境配置
        
        参数:
            app: Flask应用实例
        """
        Config.init_app(app)
        
        # 确保数据库目录存在
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        db_dir = os.path.dirname(db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)

class TestingConfig(Config):
    """
    测试环境配置
    
    用于单元测试和集成测试的配置参数。
    使用内存数据库，禁用CSRF保护。
    """
    
    TESTING = True
    DEBUG = True
    
    # 测试数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:'
    
    # 禁用CSRF保护
    WTF_CSRF_ENABLED = False
    
    # 测试环境使用较短的token过期时间
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(minutes=15)
    
    # 文件上传限制更小
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
    
    # 日志配置
    LOG_LEVEL = 'WARNING'

class ProductionConfig(Config):
    """
    生产环境配置
    
    用于生产部署的配置参数。
    使用更安全的配置，启用性能优化。
    """
    
    DEBUG = False
    TESTING = False
    
    # 生产数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'prod.db')
    
    # 安全配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'file-project-manager-prod-secret-key-2024'
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or SECRET_KEY
    
    # 日志配置
    LOG_LEVEL = 'WARNING'
    
    # 严格的CORS配置
    CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '').split(',')
    
    @classmethod
    def init_app(cls, app):
        """
        初始化生产环境配置
        
        参数:
            app: Flask应用实例
        """
        Config.init_app(app)
        
        # 生产环境日志配置
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug and not app.testing:
            # 文件日志处理器
            file_handler = RotatingFileHandler(
                app.config['LOG_FILE'],
                maxBytes=app.config['LOG_MAX_BYTES'],
                backupCount=app.config['LOG_BACKUP_COUNT']
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('File Project Manager startup')

# 配置字典
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config(config_name=None):
    """
    获取配置类
    
    参数:
        config_name (str): 配置名称，可选值: development, testing, production
        
    返回:
        Config: 配置类
        
    示例:
        >>> config_class = get_config('development')
        >>> app.config.from_object(config_class)
    """
    config_name = config_name or os.environ.get('FLASK_ENV', 'default')
    return config.get(config_name, DevelopmentConfig)