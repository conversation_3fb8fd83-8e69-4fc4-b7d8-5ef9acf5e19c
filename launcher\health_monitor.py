#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康监控器
职业技能等级考试系统 - 实时健康监控

功能特性：
- 实时模块状态监控
- 自动故障检测和告警
- 性能指标收集和分析
- 自动恢复机制
- 健康报告生成
- 监控数据可视化

作者：系统架构师
版本：2.0.0
日期：2024-01-15
"""

import os
import sys
import time
import json
import psutil
import requests
import threading
import subprocess
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging
from collections import deque, defaultdict


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"          # 健康
    WARNING = "warning"          # 警告
    CRITICAL = "critical"        # 严重
    UNKNOWN = "unknown"          # 未知
    OFFLINE = "offline"          # 离线


class AlertLevel(Enum):
    """告警级别枚举"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class HealthMetrics:
    """健康指标数据类"""
    timestamp: datetime
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_mb: float = 0.0
    response_time: float = 0.0
    status_code: int = 0
    is_responsive: bool = False
    error_count: int = 0
    uptime: float = 0.0


@dataclass
class Alert:
    """告警数据类"""
    timestamp: datetime
    module_name: str
    level: AlertLevel
    message: str
    metrics: Optional[HealthMetrics] = None
    resolved: bool = False
    resolve_time: Optional[datetime] = None


class HealthMonitor:
    """
    健康监控器
    
    负责监控所有模块的健康状态，包括：
    - CPU和内存使用率监控
    - HTTP响应时间监控
    - 进程状态监控
    - 自动故障检测
    - 告警和恢复机制
    """
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """初始化健康监控器"""
        self.config = config
        self.logger = logger or self._init_logger()
        
        # 监控配置
        monitor_config = config.get("monitoring", {})
        self.check_interval = monitor_config.get("interval", 30)
        self.response_timeout = monitor_config.get("timeout", 10)
        self.max_retries = monitor_config.get("retries", 3)
        self.metrics_history_size = monitor_config.get("history_size", 100)
        
        # 阈值配置
        thresholds = monitor_config.get("thresholds", {})
        self.cpu_warning_threshold = thresholds.get("cpu_warning", 70.0)
        self.cpu_critical_threshold = thresholds.get("cpu_critical", 90.0)
        self.memory_warning_threshold = thresholds.get("memory_warning", 70.0)
        self.memory_critical_threshold = thresholds.get("memory_critical", 90.0)
        self.response_warning_threshold = thresholds.get("response_warning", 5.0)
        self.response_critical_threshold = thresholds.get("response_critical", 10.0)
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        self.modules_info: Dict[str, Dict[str, Any]] = {}
        self.running_processes: Dict[str, subprocess.Popen] = {}
        
        # 监控数据
        self.health_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=self.metrics_history_size))
        self.current_status: Dict[str, HealthStatus] = {}
        self.alerts: List[Alert] = []
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # 统计数据
        self.start_time = datetime.now()
        self.total_checks = 0
        self.failed_checks = 0
        
        self.logger.info("健康监控器初始化完成")
    
    def _init_logger(self) -> logging.Logger:
        """初始化日志系统"""
        logger = logging.getLogger("health_monitor")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """添加告警回调函数"""
        self.alert_callbacks.append(callback)
    
    def _trigger_alert(self, alert: Alert):
        """触发告警"""
        self.alerts.append(alert)
        
        # 记录告警日志
        level_map = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }
        
        self.logger.log(level_map[alert.level], f"[{alert.level.value.upper()}] {alert.module_name}: {alert.message}")
        
        # 调用回调函数
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"告警回调执行失败: {e}")
    
    def _check_module_process(self, module_name: str, process: subprocess.Popen) -> Optional[HealthMetrics]:
        """检查模块进程状态"""
        try:
            # 检查进程是否还在运行
            if process.poll() is not None:
                return None  # 进程已退出
            
            # 获取进程信息
            ps_process = psutil.Process(process.pid)
            
            # 计算运行时间
            create_time = ps_process.create_time()
            uptime = time.time() - create_time
            
            # 获取CPU和内存使用率
            cpu_percent = ps_process.cpu_percent()
            memory_info = ps_process.memory_info()
            memory_percent = ps_process.memory_percent()
            memory_mb = memory_info.rss / 1024 / 1024
            
            metrics = HealthMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_mb=memory_mb,
                uptime=uptime
            )
            
            return metrics
            
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            self.logger.warning(f"无法获取模块 {module_name} 的进程信息: {e}")
            return None
        except Exception as e:
            self.logger.error(f"检查模块 {module_name} 进程状态时出错: {e}")
            return None
    
    def _check_module_http(self, module_name: str, module_info: Dict[str, Any]) -> Optional[float]:
        """检查模块HTTP响应"""
        try:
            port = module_info.get("port", 5000)
            health_check = module_info.get("health_check", "/api/health")
            url = f"http://localhost:{port}{health_check}"
            
            start_time = time.time()
            response = requests.get(url, timeout=self.response_timeout)
            response_time = (time.time() - start_time) * 1000  # 转换为毫秒
            
            if response.status_code == 200:
                return response_time
            else:
                self.logger.warning(f"模块 {module_name} HTTP检查返回状态码: {response.status_code}")
                return None
                
        except requests.exceptions.Timeout:
            self.logger.warning(f"模块 {module_name} HTTP检查超时")
            return None
        except requests.exceptions.ConnectionError:
            self.logger.warning(f"模块 {module_name} HTTP连接失败")
            return None
        except Exception as e:
            self.logger.error(f"检查模块 {module_name} HTTP状态时出错: {e}")
            return None
    
    def _check_single_module(self, module_name: str) -> HealthMetrics:
        """检查单个模块的健康状态"""
        self.total_checks += 1
        
        # 获取模块信息
        module_info = self.modules_info.get(module_name, {})
        process = self.running_processes.get(module_name)
        
        # 初始化指标
        metrics = HealthMetrics(timestamp=datetime.now())
        
        # 检查进程状态
        if process:
            process_metrics = self._check_module_process(module_name, process)
            if process_metrics:
                metrics.cpu_percent = process_metrics.cpu_percent
                metrics.memory_percent = process_metrics.memory_percent
                metrics.memory_mb = process_metrics.memory_mb
                metrics.uptime = process_metrics.uptime
            else:
                # 进程已退出
                metrics.is_responsive = False
                self.failed_checks += 1
                return metrics
        
        # 检查HTTP响应
        response_time = self._check_module_http(module_name, module_info)
        if response_time is not None:
            metrics.response_time = response_time
            metrics.is_responsive = True
            metrics.status_code = 200
        else:
            metrics.is_responsive = False
            metrics.status_code = 0
            self.failed_checks += 1
        
        return metrics
    
    def _analyze_health_status(self, module_name: str, metrics: HealthMetrics) -> HealthStatus:
        """分析健康状态"""
        if not metrics.is_responsive:
            return HealthStatus.OFFLINE
        
        # 检查各项指标
        issues = []
        
        # CPU使用率检查
        if metrics.cpu_percent >= self.cpu_critical_threshold:
            issues.append("CPU使用率严重过高")
        elif metrics.cpu_percent >= self.cpu_warning_threshold:
            issues.append("CPU使用率较高")
        
        # 内存使用率检查
        if metrics.memory_percent >= self.memory_critical_threshold:
            issues.append("内存使用率严重过高")
        elif metrics.memory_percent >= self.memory_warning_threshold:
            issues.append("内存使用率较高")
        
        # 响应时间检查
        if metrics.response_time >= self.response_critical_threshold * 1000:
            issues.append("响应时间严重过长")
        elif metrics.response_time >= self.response_warning_threshold * 1000:
            issues.append("响应时间较长")
        
        # 根据问题严重程度确定状态
        if any("严重" in issue for issue in issues):
            return HealthStatus.CRITICAL
        elif issues:
            return HealthStatus.WARNING
        else:
            return HealthStatus.HEALTHY
    
    def _check_for_alerts(self, module_name: str, metrics: HealthMetrics, status: HealthStatus):
        """检查是否需要发送告警"""
        current_time = datetime.now()
        
        # 状态变化告警
        previous_status = self.current_status.get(module_name, HealthStatus.UNKNOWN)
        if status != previous_status:
            if status == HealthStatus.OFFLINE:
                alert = Alert(
                    timestamp=current_time,
                    module_name=module_name,
                    level=AlertLevel.CRITICAL,
                    message=f"模块离线",
                    metrics=metrics
                )
                self._trigger_alert(alert)
            elif status == HealthStatus.CRITICAL:
                alert = Alert(
                    timestamp=current_time,
                    module_name=module_name,
                    level=AlertLevel.ERROR,
                    message=f"模块状态严重",
                    metrics=metrics
                )
                self._trigger_alert(alert)
            elif status == HealthStatus.WARNING:
                alert = Alert(
                    timestamp=current_time,
                    module_name=module_name,
                    level=AlertLevel.WARNING,
                    message=f"模块状态警告",
                    metrics=metrics
                )
                self._trigger_alert(alert)
            elif status == HealthStatus.HEALTHY and previous_status in [HealthStatus.WARNING, HealthStatus.CRITICAL, HealthStatus.OFFLINE]:
                alert = Alert(
                    timestamp=current_time,
                    module_name=module_name,
                    level=AlertLevel.INFO,
                    message=f"模块状态恢复正常",
                    metrics=metrics
                )
                self._trigger_alert(alert)
        
        # 性能指标告警
        if metrics.cpu_percent >= self.cpu_critical_threshold:
            alert = Alert(
                timestamp=current_time,
                module_name=module_name,
                level=AlertLevel.ERROR,
                message=f"CPU使用率过高: {metrics.cpu_percent:.1f}%",
                metrics=metrics
            )
            self._trigger_alert(alert)
        
        if metrics.memory_percent >= self.memory_critical_threshold:
            alert = Alert(
                timestamp=current_time,
                module_name=module_name,
                level=AlertLevel.ERROR,
                message=f"内存使用率过高: {metrics.memory_percent:.1f}% ({metrics.memory_mb:.1f}MB)",
                metrics=metrics
            )
            self._trigger_alert(alert)
        
        if metrics.response_time >= self.response_critical_threshold * 1000:
            alert = Alert(
                timestamp=current_time,
                module_name=module_name,
                level=AlertLevel.ERROR,
                message=f"响应时间过长: {metrics.response_time:.1f}ms",
                metrics=metrics
            )
            self._trigger_alert(alert)
    
    def _monitor_loop(self):
        """监控主循环"""
        self.logger.info("健康监控开始运行")
        
        while self.is_monitoring:
            try:
                # 检查所有模块
                for module_name in self.running_processes.keys():
                    if not self.is_monitoring:
                        break
                    
                    # 检查模块健康状态
                    metrics = self._check_single_module(module_name)
                    
                    # 分析健康状态
                    status = self._analyze_health_status(module_name, metrics)
                    
                    # 检查告警
                    self._check_for_alerts(module_name, metrics, status)
                    
                    # 更新状态和指标
                    self.current_status[module_name] = status
                    self.health_metrics[module_name].append(metrics)
                
                # 等待下次检查
                time.sleep(self.check_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(5)  # 出错后短暂等待
        
        self.logger.info("健康监控已停止")
    
    def start_monitoring(self, modules_info: Dict[str, Dict[str, Any]], running_processes: Dict[str, subprocess.Popen]):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("监控已在运行中")
            return
        
        self.modules_info = modules_info
        self.running_processes = running_processes
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info(f"开始监控 {len(running_processes)} 个模块")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        self.logger.info("健康监控已停止")
    
    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态报告"""
        current_time = datetime.now()
        uptime = current_time - self.start_time
        
        # 统计各状态的模块数量
        status_counts = defaultdict(int)
        for status in self.current_status.values():
            status_counts[status.value] += 1
        
        # 计算成功率
        success_rate = ((self.total_checks - self.failed_checks) / self.total_checks * 100) if self.total_checks > 0 else 0
        
        # 获取最新指标
        latest_metrics = {}
        for module_name, metrics_queue in self.health_metrics.items():
            if metrics_queue:
                latest = metrics_queue[-1]
                latest_metrics[module_name] = {
                    "timestamp": latest.timestamp.isoformat(),
                    "cpu_percent": latest.cpu_percent,
                    "memory_percent": latest.memory_percent,
                    "memory_mb": latest.memory_mb,
                    "response_time": latest.response_time,
                    "is_responsive": latest.is_responsive,
                    "uptime": latest.uptime,
                    "status": self.current_status.get(module_name, HealthStatus.UNKNOWN).value
                }
        
        # 获取最近的告警
        recent_alerts = []
        for alert in self.alerts[-10:]:  # 最近10条告警
            recent_alerts.append({
                "timestamp": alert.timestamp.isoformat(),
                "module_name": alert.module_name,
                "level": alert.level.value,
                "message": alert.message,
                "resolved": alert.resolved
            })
        
        return {
            "monitoring_status": "running" if self.is_monitoring else "stopped",
            "uptime": str(uptime),
            "total_modules": len(self.running_processes),
            "status_summary": dict(status_counts),
            "success_rate": round(success_rate, 2),
            "total_checks": self.total_checks,
            "failed_checks": self.failed_checks,
            "modules": latest_metrics,
            "recent_alerts": recent_alerts,
            "timestamp": current_time.isoformat()
        }
    
    def get_module_history(self, module_name: str, hours: int = 1) -> List[Dict[str, Any]]:
        """获取模块历史数据"""
        if module_name not in self.health_metrics:
            return []
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        history = []
        
        for metrics in self.health_metrics[module_name]:
            if metrics.timestamp >= cutoff_time:
                history.append({
                    "timestamp": metrics.timestamp.isoformat(),
                    "cpu_percent": metrics.cpu_percent,
                    "memory_percent": metrics.memory_percent,
                    "memory_mb": metrics.memory_mb,
                    "response_time": metrics.response_time,
                    "is_responsive": metrics.is_responsive
                })
        
        return history
    
    def generate_health_report(self) -> str:
        """生成健康报告"""
        status = self.get_health_status()
        
        report = []
        report.append("=" * 60)
        report.append("系统健康监控报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"监控状态: {status['monitoring_status']}")
        report.append(f"运行时间: {status['uptime']}")
        report.append(f"总模块数: {status['total_modules']}")
        report.append(f"检查成功率: {status['success_rate']}%")
        report.append("")
        
        # 状态汇总
        report.append("状态汇总:")
        for status_name, count in status['status_summary'].items():
            report.append(f"  {status_name}: {count}")
        report.append("")
        
        # 模块详情
        report.append("模块详情:")
        for module_name, metrics in status['modules'].items():
            report.append(f"  {module_name}:")
            report.append(f"    状态: {metrics['status']}")
            report.append(f"    CPU: {metrics['cpu_percent']:.1f}%")
            report.append(f"    内存: {metrics['memory_percent']:.1f}% ({metrics['memory_mb']:.1f}MB)")
            report.append(f"    响应时间: {metrics['response_time']:.1f}ms")
            report.append(f"    运行时间: {metrics['uptime']:.1f}秒")
            report.append("")
        
        # 最近告警
        if status['recent_alerts']:
            report.append("最近告警:")
            for alert in status['recent_alerts']:
                report.append(f"  [{alert['level'].upper()}] {alert['module_name']}: {alert['message']}")
                report.append(f"    时间: {alert['timestamp']}")
        
        report.append("=" * 60)
        
        return "\n".join(report)