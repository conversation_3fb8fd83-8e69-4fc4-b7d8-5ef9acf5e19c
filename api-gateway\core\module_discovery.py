#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
职业技能等级考试系统 - 模块自动发现机制
版本: 2.0.0
作者: SOLO Coding
更新日期: 2024-01-15

功能说明:
1. 自动扫描和发现所有模块
2. 解决题库管理模块未被发现的问题
3. 模块注册和健康检查
4. 配置文件解析和模块信息提取
5. 动态路由注册和负载均衡
"""

import os
import sys
import json
import yaml
import time
import logging
import threading
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/module_discovery.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class ModuleInfo:
    """
    模块信息数据类
    """
    name: str
    description: str
    type: str
    priority: int
    host: str
    port: int
    path: str
    main_file: str
    enabled: bool
    health_check_endpoint: str
    api_prefix: str
    dependencies: List[str]
    environment: Dict[str, str]
    status: str = "unknown"  # unknown, running, stopped, error
    last_health_check: Optional[datetime] = None
    health_status: str = "unknown"  # healthy, unhealthy, unknown
    process_id: Optional[int] = None
    start_time: Optional[datetime] = None
    error_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理datetime对象
        if self.last_health_check:
            data['last_health_check'] = self.last_health_check.isoformat()
        if self.start_time:
            data['start_time'] = self.start_time.isoformat()
        return data


class ModuleDiscovery:
    """
    模块自动发现和管理类
    """
    
    def __init__(self, project_root: str = None):
        """
        初始化模块发现器
        
        Args:
            project_root: 项目根目录路径
        """
        self.project_root = Path(project_root) if project_root else Path(__file__).parent.parent.parent
        self.modules: Dict[str, ModuleInfo] = {}
        self.config_cache = {}
        self.health_check_interval = 30  # 健康检查间隔（秒）
        self.health_check_thread = None
        self.running = False
        
        # 加载配置
        self._load_configurations()
        
        logger.info(f"模块发现器初始化完成，项目根目录: {self.project_root}")
    
    def _load_configurations(self):
        """
        加载配置文件
        """
        try:
            # 加载全局配置
            global_config_path = self.project_root / "config" / "global.yaml"
            if global_config_path.exists():
                with open(global_config_path, 'r', encoding='utf-8') as f:
                    self.global_config = yaml.safe_load(f)
                logger.info("全局配置加载成功")
            else:
                logger.warning(f"全局配置文件不存在: {global_config_path}")
                self.global_config = {}
            
            # 加载模块配置
            modules_config_path = self.project_root / "config" / "modules.yaml"
            if modules_config_path.exists():
                with open(modules_config_path, 'r', encoding='utf-8') as f:
                    self.modules_config = yaml.safe_load(f)
                logger.info("模块配置加载成功")
            else:
                logger.warning(f"模块配置文件不存在: {modules_config_path}")
                self.modules_config = {}
            
            # 加载API网关配置
            gateway_config_path = self.project_root / "api-gateway" / "config" / "gateway.yaml"
            if gateway_config_path.exists():
                with open(gateway_config_path, 'r', encoding='utf-8') as f:
                    self.gateway_config = yaml.safe_load(f)
                logger.info("API网关配置加载成功")
            else:
                logger.warning(f"API网关配置文件不存在: {gateway_config_path}")
                self.gateway_config = {}
                
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self.global_config = {}
            self.modules_config = {}
            self.gateway_config = {}
    
    def discover_modules(self) -> Dict[str, ModuleInfo]:
        """
        自动发现所有模块
        
        Returns:
            发现的模块信息字典
        """
        logger.info("开始自动发现模块...")
        
        # 清空现有模块信息
        self.modules.clear()
        
        # 从配置文件中发现模块
        self._discover_from_config()
        
        # 从文件系统中发现模块
        self._discover_from_filesystem()
        
        # 验证和补充模块信息
        self._validate_modules()
        
        logger.info(f"模块发现完成，共发现 {len(self.modules)} 个模块")
        
        # 打印发现的模块信息
        for module_name, module_info in self.modules.items():
            logger.info(f"发现模块: {module_name} - {module_info.description} ({module_info.host}:{module_info.port})")
        
        return self.modules
    
    def _discover_from_config(self):
        """
        从配置文件中发现模块
        """
        if not self.modules_config or 'modules' not in self.modules_config:
            logger.warning("模块配置为空或格式不正确")
            return
        
        modules_config = self.modules_config['modules']
        
        for module_name, config in modules_config.items():
            try:
                # 提取模块信息
                service_config = config.get('service', {})
                startup_config = config.get('startup', {})
                health_config = config.get('health_check', {})
                dependencies = config.get('dependencies', {})
                environment = config.get('environment', {})
                
                module_info = ModuleInfo(
                    name=config.get('name', module_name),
                    description=config.get('description', ''),
                    type=config.get('type', 'service'),
                    priority=config.get('priority', 5),
                    host=service_config.get('host', '127.0.0.1'),
                    port=service_config.get('port', 8000),
                    path=service_config.get('path', '.'),
                    main_file=service_config.get('main_file', 'app.py'),
                    enabled=service_config.get('enabled', True),
                    health_check_endpoint=health_config.get('endpoint', '/health'),
                    api_prefix=config.get('api_endpoints', ['/'])[0] if config.get('api_endpoints') else '/',
                    dependencies=dependencies.get('required', []) + dependencies.get('optional', []),
                    environment=environment
                )
                
                self.modules[module_name] = module_info
                logger.debug(f"从配置文件发现模块: {module_name}")
                
            except Exception as e:
                logger.error(f"解析模块配置失败 {module_name}: {e}")
    
    def _discover_from_filesystem(self):
        """
        从文件系统中发现模块
        """
        logger.info("从文件系统扫描模块...")
        
        # 定义要扫描的目录
        scan_directories = [
            self.project_root,
            self.project_root / "api-gateway",
            self.project_root / "user-management",
            self.project_root / "question_bank",
            self.project_root / "exam_score_reporting_interface",
            self.project_root / "file-project-manager",
            self.project_root / "file_project_management"
        ]
        
        for directory in scan_directories:
            if directory.exists() and directory.is_dir():
                self._scan_directory(directory)
    
    def _scan_directory(self, directory: Path):
        """
        扫描指定目录寻找模块
        
        Args:
            directory: 要扫描的目录
        """
        try:
            # 查找Python应用文件
            app_files = ['app.py', 'main.py', 'run.py', '__init__.py']
            
            for app_file in app_files:
                app_path = directory / app_file
                if app_path.exists():
                    module_name = directory.name
                    
                    # 如果模块已经从配置文件中发现，跳过
                    if module_name in self.modules:
                        continue
                    
                    # 尝试从文件中提取模块信息
                    module_info = self._extract_module_info(directory, app_file)
                    if module_info:
                        self.modules[module_name] = module_info
                        logger.debug(f"从文件系统发现模块: {module_name}")
                    break
                    
        except Exception as e:
            logger.error(f"扫描目录失败 {directory}: {e}")
    
    def _extract_module_info(self, directory: Path, main_file: str) -> Optional[ModuleInfo]:
        """
        从模块目录中提取模块信息
        
        Args:
            directory: 模块目录
            main_file: 主文件名
            
        Returns:
            模块信息对象
        """
        try:
            module_name = directory.name
            
            # 尝试从requirements.txt获取依赖信息
            requirements_file = directory / "requirements.txt"
            dependencies = []
            if requirements_file.exists():
                with open(requirements_file, 'r', encoding='utf-8') as f:
                    dependencies = [line.strip() for line in f if line.strip() and not line.startswith('#')]
            
            # 默认端口分配策略
            port_mapping = {
                'api-gateway': 8080,
                'user-management': 5001,
                'question_bank': 5002,
                'exam_score_reporting_interface': 5008,
                'file-project-manager': 5015,
                'file_project_management': 5015,
                'main': 8000,
                'monitoring': 5005
            }
            
            default_port = port_mapping.get(module_name, 8000)
            
            # 创建模块信息
            module_info = ModuleInfo(
                name=module_name.replace('_', ' ').replace('-', ' ').title(),
                description=f"自动发现的模块: {module_name}",
                type="service",
                priority=5,
                host="127.0.0.1",
                port=default_port,
                path=str(directory.relative_to(self.project_root)),
                main_file=main_file,
                enabled=True,
                health_check_endpoint="/health",
                api_prefix=f"/api/{module_name}",
                dependencies=dependencies,
                environment={}
            )
            
            return module_info
            
        except Exception as e:
            logger.error(f"提取模块信息失败 {directory}: {e}")
            return None
    
    def _validate_modules(self):
        """
        验证和补充模块信息
        """
        for module_name, module_info in self.modules.items():
            try:
                # 验证模块路径
                module_path = self.project_root / module_info.path
                if not module_path.exists():
                    logger.warning(f"模块路径不存在: {module_path}")
                    module_info.enabled = False
                    continue
                
                # 验证主文件
                main_file_path = module_path / module_info.main_file
                if not main_file_path.exists():
                    logger.warning(f"模块主文件不存在: {main_file_path}")
                    module_info.enabled = False
                    continue
                
                # 设置环境变量
                if not module_info.environment:
                    module_info.environment = {
                        'FLASK_ENV': 'development',
                        'FLASK_DEBUG': 'true',
                        f'{module_name.upper()}_PORT': str(module_info.port)
                    }
                
                logger.debug(f"模块验证通过: {module_name}")
                
            except Exception as e:
                logger.error(f"模块验证失败 {module_name}: {e}")
                module_info.enabled = False
    
    def check_module_health(self, module_name: str) -> bool:
        """
        检查单个模块的健康状态
        
        Args:
            module_name: 模块名称
            
        Returns:
            健康状态
        """
        if module_name not in self.modules:
            logger.warning(f"模块不存在: {module_name}")
            return False
        
        module_info = self.modules[module_name]
        
        try:
            # 构建健康检查URL
            health_url = f"http://{module_info.host}:{module_info.port}{module_info.health_check_endpoint}"
            
            # 发送健康检查请求
            response = requests.get(health_url, timeout=10)
            
            if response.status_code == 200:
                module_info.health_status = "healthy"
                module_info.last_health_check = datetime.now()
                module_info.error_count = 0
                logger.debug(f"模块健康检查通过: {module_name}")
                return True
            else:
                module_info.health_status = "unhealthy"
                module_info.error_count += 1
                logger.warning(f"模块健康检查失败: {module_name}, 状态码: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            module_info.health_status = "unhealthy"
            module_info.error_count += 1
            logger.warning(f"模块健康检查异常: {module_name}, 错误: {e}")
            return False
        except Exception as e:
            module_info.health_status = "unknown"
            module_info.error_count += 1
            logger.error(f"模块健康检查出错: {module_name}, 错误: {e}")
            return False
    
    def check_all_modules_health(self) -> Dict[str, bool]:
        """
        检查所有模块的健康状态
        
        Returns:
            模块健康状态字典
        """
        health_results = {}
        
        # 使用线程池并行检查
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_module = {
                executor.submit(self.check_module_health, module_name): module_name
                for module_name in self.modules.keys()
                if self.modules[module_name].enabled
            }
            
            for future in as_completed(future_to_module):
                module_name = future_to_module[future]
                try:
                    health_results[module_name] = future.result()
                except Exception as e:
                    logger.error(f"健康检查异常: {module_name}, 错误: {e}")
                    health_results[module_name] = False
        
        return health_results
    
    def start_health_monitoring(self):
        """
        启动健康监控线程
        """
        if self.health_check_thread and self.health_check_thread.is_alive():
            logger.warning("健康监控已经在运行")
            return
        
        self.running = True
        self.health_check_thread = threading.Thread(target=self._health_monitoring_loop, daemon=True)
        self.health_check_thread.start()
        logger.info("健康监控线程已启动")
    
    def stop_health_monitoring(self):
        """
        停止健康监控线程
        """
        self.running = False
        if self.health_check_thread:
            self.health_check_thread.join(timeout=5)
        logger.info("健康监控线程已停止")
    
    def _health_monitoring_loop(self):
        """
        健康监控循环
        """
        while self.running:
            try:
                logger.debug("执行健康检查...")
                health_results = self.check_all_modules_health()
                
                # 统计健康状态
                healthy_count = sum(1 for status in health_results.values() if status)
                total_count = len(health_results)
                
                logger.info(f"健康检查完成: {healthy_count}/{total_count} 个模块健康")
                
                # 等待下次检查
                time.sleep(self.health_check_interval)
                
            except Exception as e:
                logger.error(f"健康监控循环异常: {e}")
                time.sleep(5)  # 异常时短暂等待
    
    def get_module_status(self, module_name: str) -> Dict[str, Any]:
        """
        获取模块状态信息
        
        Args:
            module_name: 模块名称
            
        Returns:
            模块状态信息
        """
        if module_name not in self.modules:
            return {'error': f'模块不存在: {module_name}'}
        
        module_info = self.modules[module_name]
        return module_info.to_dict()
    
    def get_all_modules_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有模块的状态信息
        
        Returns:
            所有模块状态信息
        """
        return {
            module_name: module_info.to_dict()
            for module_name, module_info in self.modules.items()
        }
    
    def register_module(self, module_name: str, module_info: ModuleInfo) -> bool:
        """
        注册新模块
        
        Args:
            module_name: 模块名称
            module_info: 模块信息
            
        Returns:
            注册是否成功
        """
        try:
            self.modules[module_name] = module_info
            logger.info(f"模块注册成功: {module_name}")
            return True
        except Exception as e:
            logger.error(f"模块注册失败: {module_name}, 错误: {e}")
            return False
    
    def unregister_module(self, module_name: str) -> bool:
        """
        注销模块
        
        Args:
            module_name: 模块名称
            
        Returns:
            注销是否成功
        """
        try:
            if module_name in self.modules:
                del self.modules[module_name]
                logger.info(f"模块注销成功: {module_name}")
                return True
            else:
                logger.warning(f"模块不存在，无法注销: {module_name}")
                return False
        except Exception as e:
            logger.error(f"模块注销失败: {module_name}, 错误: {e}")
            return False
    
    def export_module_config(self, output_path: str = None) -> str:
        """
        导出模块配置到文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            导出的文件路径
        """
        if not output_path:
            output_path = self.project_root / "config" / "discovered_modules.json"
        
        try:
            config_data = {
                'discovery_time': datetime.now().isoformat(),
                'total_modules': len(self.modules),
                'modules': self.get_all_modules_status()
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"模块配置已导出到: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"导出模块配置失败: {e}")
            raise


def main():
    """
    主函数 - 用于测试模块发现功能
    """
    try:
        # 创建模块发现器
        discovery = ModuleDiscovery()
        
        # 发现模块
        modules = discovery.discover_modules()
        
        print(f"\n发现的模块数量: {len(modules)}")
        print("=" * 50)
        
        for module_name, module_info in modules.items():
            print(f"模块名称: {module_name}")
            print(f"描述: {module_info.description}")
            print(f"类型: {module_info.type}")
            print(f"地址: {module_info.host}:{module_info.port}")
            print(f"路径: {module_info.path}")
            print(f"主文件: {module_info.main_file}")
            print(f"启用状态: {module_info.enabled}")
            print("-" * 30)
        
        # 启动健康监控
        print("\n启动健康监控...")
        discovery.start_health_monitoring()
        
        # 等待一段时间进行健康检查
        time.sleep(5)
        
        # 检查所有模块健康状态
        print("\n检查模块健康状态...")
        health_results = discovery.check_all_modules_health()
        
        for module_name, is_healthy in health_results.items():
            status = "健康" if is_healthy else "不健康"
            print(f"{module_name}: {status}")
        
        # 导出配置
        config_file = discovery.export_module_config()
        print(f"\n配置已导出到: {config_file}")
        
        # 停止健康监控
        discovery.stop_health_monitoring()
        
    except Exception as e:
        logger.error(f"主函数执行失败: {e}")
        raise


if __name__ == "__main__":
    main()