#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证脚本
用于验证系统配置的一致性，确保设计文档与实际实现保持同步
"""

import os
import sys
import yaml
import json
import re
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass

@dataclass
class ValidationError:
    """验证错误信息"""
    category: str
    severity: str  # 'error', 'warning', 'info'
    message: str
    file_path: str = ""
    line_number: int = 0

class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.errors: List[ValidationError] = []
        self.warnings: List[ValidationError] = []
        self.info: List[ValidationError] = []
        
        # 预期的健康检查端点
        self.expected_health_endpoint = "/api/health"
        
        # 模块列表
        self.modules = [
            "user-management",
            "question-bank",
            "exam-management",
            "score-management",
            "monitoring",
            "auditing"
        ]
    
    def add_error(self, category: str, message: str, file_path: str = "", line_number: int = 0):
        """添加错误"""
        self.errors.append(ValidationError(category, "error", message, file_path, line_number))
    
    def add_warning(self, category: str, message: str, file_path: str = "", line_number: int = 0):
        """添加警告"""
        self.warnings.append(ValidationError(category, "warning", message, file_path, line_number))
    
    def add_info(self, category: str, message: str, file_path: str = "", line_number: int = 0):
        """添加信息"""
        self.info.append(ValidationError(category, "info", message, file_path, line_number))
    
    def load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """加载YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            self.add_error("file_load", f"无法加载配置文件 {file_path}: {e}", str(file_path))
            return {}
    
    def load_markdown_file(self, file_path: Path) -> str:
        """加载Markdown文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.add_error("file_load", f"无法加载文档文件 {file_path}: {e}", str(file_path))
            return ""
    
    def validate_health_endpoints(self):
        """验证健康检查端点一致性"""
        print("🔍 验证健康检查端点一致性...")
        
        # 1. 检查API网关配置
        self._validate_gateway_health_endpoints()
        
        # 2. 检查各模块配置
        self._validate_module_health_endpoints()
        
        # 3. 检查设计文档
        self._validate_design_doc_health_endpoints()
    
    def _validate_gateway_health_endpoints(self):
        """验证API网关健康检查端点"""
        gateway_config_path = self.root_path / "api-gateway" / "config" / "gateway.yaml"
        
        if gateway_config_path.exists():
            config = self.load_yaml_file(gateway_config_path)
            modules = config.get("modules", {})
            
            for module_name, module_config in modules.items():
                health_endpoint = module_config.get("health_check")
                if health_endpoint != self.expected_health_endpoint:
                    self.add_error(
                        "health_endpoint",
                        f"API网关配置中模块 {module_name} 健康检查端点不一致: {health_endpoint} (期望: {self.expected_health_endpoint})",
                        str(gateway_config_path)
                    )
                else:
                    self.add_info(
                        "health_endpoint",
                        f"API网关配置中模块 {module_name} 健康检查端点正确: {health_endpoint}",
                        str(gateway_config_path)
                    )
        else:
            self.add_warning("file_missing", f"API网关配置文件不存在: {gateway_config_path}")
    
    def _validate_module_health_endpoints(self):
        """验证各模块健康检查端点"""
        for module in self.modules:
            # 检查模块配置文件
            config_paths = [
                self.root_path / module / "config" / "base.yaml",
                self.root_path / module / "config.py",
                self.root_path / module / "app" / "api" / "health.py"
            ]
            
            for config_path in config_paths:
                if config_path.exists():
                    if config_path.suffix == '.yaml':
                        self._check_yaml_health_endpoint(module, config_path)
                    elif config_path.suffix == '.py':
                        self._check_python_health_endpoint(module, config_path)
    
    def _check_yaml_health_endpoint(self, module: str, config_path: Path):
        """检查YAML配置中的健康检查端点"""
        config = self.load_yaml_file(config_path)
        health_config = config.get("health", {})
        health_endpoint = health_config.get("endpoint")
        
        if health_endpoint:
            if health_endpoint != self.expected_health_endpoint:
                self.add_error(
                    "health_endpoint",
                    f"模块 {module} 健康检查端点不一致: {health_endpoint} (期望: {self.expected_health_endpoint})",
                    str(config_path)
                )
            else:
                self.add_info(
                    "health_endpoint",
                    f"模块 {module} 健康检查端点正确: {health_endpoint}",
                    str(config_path)
                )
    
    def _check_python_health_endpoint(self, module: str, config_path: Path):
        """检查Python文件中的健康检查端点"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找路由定义
            route_patterns = [
                r"@\w+\.route\(['\"]([^'\"]+)['\"].*\)",
                r"add_url_rule\(['\"]([^'\"]+)['\"].*\)"
            ]
            
            found_endpoints = []
            for pattern in route_patterns:
                matches = re.findall(pattern, content)
                found_endpoints.extend(matches)
            
            # 检查是否有正确的健康检查端点
            if self.expected_health_endpoint in found_endpoints:
                self.add_info(
                    "health_endpoint",
                    f"模块 {module} 实现了正确的健康检查端点: {self.expected_health_endpoint}",
                    str(config_path)
                )
            
            # 检查是否有错误的健康检查端点
            wrong_endpoints = [ep for ep in found_endpoints if ep in ["/health", "/api/health/check"]]
            for endpoint in wrong_endpoints:
                if endpoint != self.expected_health_endpoint:
                    self.add_warning(
                        "health_endpoint",
                        f"模块 {module} 可能使用了非标准健康检查端点: {endpoint}",
                        str(config_path)
                    )
                    
        except Exception as e:
            self.add_warning("file_parse", f"解析Python文件失败 {config_path}: {e}")
    
    def _validate_design_doc_health_endpoints(self):
        """验证设计文档中的健康检查端点"""
        doc_files = [
            "API网关架构设计文档.md",
            "API网关技术文档.md",
            "模块间通信协议设计文档.md"
        ]
        
        for doc_file in doc_files:
            doc_path = self.root_path / doc_file
            if doc_path.exists():
                content = self.load_markdown_file(doc_path)
                
                # 查找健康检查端点引用
                health_patterns = [
                    r"health_check:\s*['\"]([^'\"]+)['\"]?",
                    r"GET\s+([^\s]+).*健康检查",
                    r"健康检查.*?([/\w]+)"
                ]
                
                for pattern in health_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if "/health" in match and match != self.expected_health_endpoint:
                            self.add_warning(
                                "design_doc",
                                f"设计文档 {doc_file} 中可能包含非标准健康检查端点: {match}",
                                str(doc_path)
                            )
    
    def validate_port_conflicts(self):
        """验证端口冲突"""
        print("🔍 验证端口冲突...")
        
        ports = {}
        
        # 收集API网关端口
        gateway_config_path = self.root_path / "api-gateway" / "config" / "gateway.yaml"
        if gateway_config_path.exists():
            config = self.load_yaml_file(gateway_config_path)
            gateway_port = config.get("gateway", {}).get("port")
            if gateway_port:
                ports[gateway_port] = "api-gateway"
            
            # 收集模块端口
            modules = config.get("modules", {})
            for module_name, module_config in modules.items():
                port = module_config.get("port")
                if port:
                    if port in ports:
                        self.add_error(
                            "port_conflict",
                            f"端口冲突: {module_name} 和 {ports[port]} 都使用端口 {port}",
                            str(gateway_config_path)
                        )
                    else:
                        ports[port] = module_name
        
        # 检查各模块配置
        for module in self.modules:
            config_paths = [
                self.root_path / module / "config" / "base.yaml",
                self.root_path / module / "config.py",
                self.root_path / module / "run.py"
            ]
            
            for config_path in config_paths:
                if config_path.exists():
                    self._check_module_port(module, config_path, ports)
    
    def _check_module_port(self, module: str, config_path: Path, ports: Dict[int, str]):
        """检查模块端口配置"""
        if config_path.suffix == '.yaml':
            config = self.load_yaml_file(config_path)
            port = config.get("service", {}).get("port")
        else:
            # 解析Python文件中的端口配置
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                port_patterns = [
                    r"port\s*=\s*(\d+)",
                    r"PORT\s*=\s*(\d+)",
                    r"app\.run\([^)]*port\s*=\s*(\d+)"
                ]
                
                port = None
                for pattern in port_patterns:
                    match = re.search(pattern, content)
                    if match:
                        port = int(match.group(1))
                        break
            except Exception:
                port = None
        
        if port:
            if port in ports:
                self.add_error(
                    "port_conflict",
                    f"端口冲突: {module} 和 {ports[port]} 都使用端口 {port}",
                    str(config_path)
                )
            else:
                ports[port] = module
                self.add_info(
                    "port_config",
                    f"模块 {module} 使用端口 {port}",
                    str(config_path)
                )
    
    def validate_api_consistency(self):
        """验证API接口一致性"""
        print("🔍 验证API接口一致性...")
        
        # 检查API接口规范文档
        api_doc_path = self.root_path / "API接口规范文档.md"
        if api_doc_path.exists():
            content = self.load_markdown_file(api_doc_path)
            
            # 检查响应格式一致性
            self._check_response_format_consistency(content)
            
            # 检查状态码使用
            self._check_status_code_consistency(content)
    
    def _check_response_format_consistency(self, content: str):
        """检查响应格式一致性"""
        # 查找响应格式定义
        response_patterns = [
            r"\{[^}]*['\"]code['\"]\s*:[^}]*\}",
            r"\{[^}]*['\"]message['\"]\s*:[^}]*\}",
            r"\{[^}]*['\"]data['\"]\s*:[^}]*\}"
        ]
        
        for pattern in response_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                self.add_info(
                    "api_format",
                    f"发现 {len(matches)} 个响应格式定义",
                    "API接口规范文档.md"
                )
    
    def _check_status_code_consistency(self, content: str):
        """检查状态码使用一致性"""
        # 查找状态码定义
        status_code_pattern = r"(\d{3})\s*[:-]\s*([^\n]+)"
        matches = re.findall(status_code_pattern, content)
        
        standard_codes = {
            "200": "成功",
            "201": "创建成功",
            "400": "请求错误",
            "401": "未授权",
            "403": "禁止访问",
            "404": "未找到",
            "500": "服务器错误"
        }
        
        for code, description in matches:
            if code in standard_codes:
                self.add_info(
                    "status_code",
                    f"标准状态码 {code}: {description}",
                    "API接口规范文档.md"
                )
    
    def validate_security_config(self):
        """验证安全配置"""
        print("🔍 验证安全配置...")
        
        # 检查JWT配置
        self._check_jwt_config()
        
        # 检查CORS配置
        self._check_cors_config()
        
        # 检查敏感信息泄露
        self._check_sensitive_info()
    
    def _check_jwt_config(self):
        """检查JWT配置"""
        config_files = [
            self.root_path / "user-management" / "config.py",
            self.root_path / "api-gateway" / "config" / "gateway.yaml"
        ]
        
        for config_file in config_files:
            if config_file.exists():
                if config_file.suffix == '.py':
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        # 检查JWT密钥
                        if "JWT_SECRET_KEY" in content:
                            if "your-secret-key" in content or "secret" in content.lower():
                                self.add_warning(
                                    "security",
                                    "发现可能的默认JWT密钥，建议使用强密钥",
                                    str(config_file)
                                )
                    except Exception:
                        pass
    
    def _check_cors_config(self):
        """检查CORS配置"""
        # 检查是否允许所有来源
        config_files = list(self.root_path.rglob("*.py")) + list(self.root_path.rglob("*.yaml"))
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if '"*"' in content and "cors" in content.lower():
                    self.add_warning(
                        "security",
                        "CORS配置允许所有来源，生产环境建议限制来源",
                        str(config_file)
                    )
            except Exception:
                pass
    
    def _check_sensitive_info(self):
        """检查敏感信息泄露"""
        sensitive_patterns = [
            r"password\s*=\s*['\"][^'\"]+['\"]?",
            r"secret\s*=\s*['\"][^'\"]+['\"]?",
            r"key\s*=\s*['\"][^'\"]+['\"]?"
        ]
        
        config_files = list(self.root_path.rglob("*.py")) + list(self.root_path.rglob("*.yaml"))
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in sensitive_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    for match in matches:
                        if not any(placeholder in match.lower() for placeholder in ["your-", "example", "test", "demo"]):
                            self.add_warning(
                                "security",
                                f"可能包含敏感信息: {match[:50]}...",
                                str(config_file)
                            )
            except Exception:
                pass
    
    def run_validation(self):
        """运行所有验证"""
        print("🚀 开始配置验证...\n")
        
        # 运行各项验证
        self.validate_health_endpoints()
        self.validate_port_conflicts()
        self.validate_api_consistency()
        self.validate_security_config()
        
        # 输出结果
        self.print_results()
        
        # 返回是否有错误
        return len(self.errors) == 0
    
    def print_results(self):
        """打印验证结果"""
        print("\n" + "="*60)
        print("📊 配置验证结果")
        print("="*60)
        
        # 统计信息
        print(f"\n📈 统计信息:")
        print(f"  ❌ 错误: {len(self.errors)}")
        print(f"  ⚠️  警告: {len(self.warnings)}")
        print(f"  ℹ️  信息: {len(self.info)}")
        
        # 打印错误
        if self.errors:
            print(f"\n❌ 错误 ({len(self.errors)}):")
            for error in self.errors:
                print(f"  [{error.category}] {error.message}")
                if error.file_path:
                    print(f"    📁 文件: {error.file_path}")
        
        # 打印警告
        if self.warnings:
            print(f"\n⚠️  警告 ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  [{warning.category}] {warning.message}")
                if warning.file_path:
                    print(f"    📁 文件: {warning.file_path}")
        
        # 打印信息（仅显示前5条）
        if self.info:
            print(f"\nℹ️  信息 (显示前5条，共{len(self.info)}条):")
            for info in self.info[:5]:
                print(f"  [{info.category}] {info.message}")
        
        # 总结
        print("\n" + "="*60)
        if len(self.errors) == 0:
            print("✅ 配置验证通过！")
        else:
            print("❌ 配置验证失败，请修复上述错误。")
        print("="*60)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="配置验证脚本")
    parser.add_argument("--root", default=".", help="项目根目录路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    # 创建验证器
    validator = ConfigValidator(args.root)
    
    # 运行验证
    success = validator.run_validation()
    
    # 退出码
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()