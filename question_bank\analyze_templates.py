#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import json
from pathlib import Path

def analyze_template_fields():
    """分析新旧模板的字段差异"""
    
    # 文件路径
    old_question_template = "d:/61-PHRL_question_bank/uploads/Question-Bank-Template_SPCT-4-LL.xlsx"
    new_question_template = "d:/61-PHRL_question_bank/uploads/Question-Bank-Template_SPCT-4-LL_NEW.xlsx"
    old_paper_template = "d:/61-PHRL_question_bank/uploads/Paper-Compilation-Template_SPCT-4-LL.xlsx"
    new_paper_template = "d:/61-PHRL_question_bank/uploads/Paper-Compilation-Template_SPCT-4-LL_NEW.xlsx"
    
    print("=== 分析题库模板字段差异 ===")
    
    # 读取旧题库模板
    try:
        old_question_df = pd.read_excel(old_question_template, header=[0, 1])
        print(f"旧题库模板字段: {list(old_question_df.columns)}")
    except Exception as e:
        print(f"读取旧题库模板失败: {e}")
        return
    
    # 读取新题库模板
    try:
        new_question_df = pd.read_excel(new_question_template, header=[0, 1])
        print(f"新题库模板字段: {list(new_question_df.columns)}")
    except Exception as e:
        print(f"读取新题库模板失败: {e}")
        return
    
    print("\n=== 分析组卷模板字段差异 ===")
    
    # 读取旧组卷模板
    try:
        old_paper_df = pd.read_excel(old_paper_template, header=[0, 1])
        print(f"旧组卷模板字段: {list(old_paper_df.columns)}")
    except Exception as e:
        print(f"读取旧组卷模板失败: {e}")
        return
    
    # 读取新组卷模板
    try:
        new_paper_df = pd.read_excel(new_paper_template, header=[0, 1])
        print(f"新组卷模板字段: {list(new_paper_df.columns)}")
    except Exception as e:
        print(f"读取新组卷模板失败: {e}")
        return
    
    # 分析字段映射关系
    print("\n=== 字段映射分析 ===")
    
    # 题库模板字段映射
    newline = '\n'
    old_question_fields = [f"{col[0]}{newline}{col[1]}" for col in old_question_df.columns]
    new_question_fields = [f"{col[0]}{newline}{col[1]}" for col in new_question_df.columns]
    
    print("题库模板字段对比:")
    for i, (old_field, new_field) in enumerate(zip(old_question_fields, new_question_fields)):
        print(f"  {i+1}. 旧: {old_field} -> 新: {new_field}")
    
    # 组卷模板字段映射
    newline = '\n'
    old_paper_fields = [f"{col[0]}{newline}{col[1]}" for col in old_paper_df.columns]
    new_paper_fields = [f"{col[0]}{newline}{col[1]}" for col in new_paper_df.columns]
    
    print("\n组卷模板字段对比:")
    for i, (old_field, new_field) in enumerate(zip(old_paper_fields, new_paper_fields)):
        print(f"  {i+1}. 旧: {old_field} -> 新: {new_field}")
    
    # 生成新的字段配置
    print("\n=== 生成新的字段配置 ===")
    
    # 从新模板提取英文字段名（第二行）
    new_question_english_fields = [col[1] for col in new_question_df.columns]
    new_paper_english_fields = [col[1] for col in new_paper_df.columns]
    
    print("新题库模板英文字段:")
    for field in new_question_english_fields:
        print(f"  - {field}")
    
    print("\n新组卷模板英文字段:")
    for field in new_paper_english_fields:
        print(f"  - {field}")
    
    # 创建字段映射字典
    field_mapping = {}
    
    # 题库字段映射
    for old_field, new_field in zip(old_question_fields, new_question_fields):
        field_mapping[old_field] = new_field
    
    # 组卷字段映射（如果有不同的字段）
    for old_field, new_field in zip(old_paper_fields, new_paper_fields):
        if old_field not in field_mapping:
            field_mapping[old_field] = new_field
    
    print("\n=== 字段映射字典 ===")
    for old_field, new_field in field_mapping.items():
        print(f"  '{old_field}': '{new_field}'")
    
    return field_mapping

if __name__ == "__main__":
    analyze_template_fields()