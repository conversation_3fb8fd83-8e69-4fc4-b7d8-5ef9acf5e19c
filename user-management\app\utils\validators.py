import re
from typing import Dict, List, Any
from app.models import User<PERSON><PERSON>

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """验证手机号格式"""
    if not phone:
        return True  # 手机号可选
    pattern = r'^1[3-9]\d{9}$'
    return bool(re.match(pattern, phone))

def validate_username(username: str) -> bool:
    """验证用户名格式"""
    if not username or len(username) < 3 or len(username) > 50:
        return False
    pattern = r'^[a-zA-Z0-9_]+$'
    return bool(re.match(pattern, username))

def validate_password(password: str) -> bool:
    """验证密码强度"""
    if not password or len(password) < 6 or len(password) > 128:
        return False
    return True

def validate_role(role: str) -> bool:
    """验证用户角色"""
    valid_roles = [r.value for r in UserRole]
    return role in valid_roles

def validate_user_data(data: Dict) -> Dict[str, List[str]]:
    """验证用户数据"""
    errors = {}
    
    # 验证用户名
    username = data.get('username', '').strip()
    if not username:
        errors.setdefault('username', []).append('用户名不能为空')
    elif not validate_username(username):
        errors.setdefault('username', []).append('用户名格式不正确，只能包含字母、数字和下划线，长度3-50位')
    
    # 验证邮箱
    email = data.get('email', '').strip()
    if not email:
        errors.setdefault('email', []).append('邮箱不能为空')
    elif not validate_email(email):
        errors.setdefault('email', []).append('邮箱格式不正确')
    
    # 验证密码（仅在创建用户时）
    password = data.get('password')
    if password is not None:  # 如果提供了密码
        if not validate_password(password):
            errors.setdefault('password', []).append('密码长度必须在6-128位之间')
    
    # 验证真实姓名
    real_name = data.get('real_name', '').strip()
    if not real_name:
        errors.setdefault('real_name', []).append('真实姓名不能为空')
    elif len(real_name) > 100:
        errors.setdefault('real_name', []).append('真实姓名长度不能超过100位')
    
    # 验证手机号
    phone = data.get('phone', '').strip()
    if phone and not validate_phone(phone):
        errors.setdefault('phone', []).append('手机号格式不正确')
    
    # 验证角色
    role = data.get('role')
    if role and not validate_role(role):
        errors.setdefault('role', []).append('用户角色不正确')
    
    # 验证部门
    department = data.get('department', '').strip()
    if department and len(department) > 100:
        errors.setdefault('department', []).append('部门名称长度不能超过100位')
    
    # 验证职位
    position = data.get('position', '').strip()
    if position and len(position) > 100:
        errors.setdefault('position', []).append('职位名称长度不能超过100位')
    
    return errors

def validate_pagination_params(page: Any, page_size: Any) -> Dict[str, List[str]]:
    """验证分页参数"""
    errors = {}
    
    try:
        page = int(page) if page else 1
        if page < 1:
            errors.setdefault('page', []).append('页码必须大于0')
    except (ValueError, TypeError):
        errors.setdefault('page', []).append('页码必须是整数')
    
    try:
        page_size = int(page_size) if page_size else 20
        if page_size < 1:
            errors.setdefault('page_size', []).append('每页数量必须大于0')
        elif page_size > 100:
            errors.setdefault('page_size', []).append('每页数量不能超过100')
    except (ValueError, TypeError):
        errors.setdefault('page_size', []).append('每页数量必须是整数')
    
    return errors