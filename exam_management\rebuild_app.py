#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新构建app.py文件，修复所有语法错误
"""

import re
import os

def rebuild_app_file():
    """重新构建app.py文件"""
    try:
        # 备份原文件
        if os.path.exists('app.py'):
            os.rename('app.py', 'app.py.broken')
            print("已备份原文件为 app.py.broken")
        
        # 从备份文件读取内容
        backup_file = 'app.py.backup' if os.path.exists('app.py.backup') else 'app.py.broken'
        
        with open(backup_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"从 {backup_file} 读取内容")
        
        # 修复所有已知的问题
        fixes_count = 0
        
        # 1. 修复docstring问题
        docstring_fixes = [
            ('"""健康检查接?"""', '"""健康检查接口"""'),
            ('"""根路径接?"""', '"""根路径接口"""'),
            ('"""参与者管理页?"""', '"""参与者管理页面"""'),
        ]
        
        for old, new in docstring_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes_count += 1
                print(f"修复docstring: {old} -> {new}")
        
        # 2. 修复HTML中的截断文本
        html_fixes = [
            ('加载参与者列表失?', '加载参与者列表失败'),
            ('显示参与者列?', '显示参与者列表'),
            ('暂无参与者数?', '暂无参与者数据'),
            ('座位?', '座位号'),
            ('状?', '状态'),
            ('上一?', '上一页'),
            ('下一?', '下一页'),
            ('应用筛?', '应用筛选'),
            ('添加参与?', '添加参与者'),
            ('编辑参与?', '编辑参与者'),
            ('查看参与?', '查看参与者'),
            ('移除参与?', '移除参与者'),
            ('更新参与?', '更新参与者'),
            ('管理参与?', '管理参与者'),
            ('状态分?', '状态分布'),
        ]
        
        for old, new in html_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes_count += 1
                print(f"修复HTML文本: {old} -> {new}")
        
        # 3. 修复HTML标签问题
        tag_fixes = [
            ('座位号/th>', '座位号</th>'),
            ('状?/th>', '状态</th>'),
        ]
        
        for old, new in tag_fixes:
            if old in content:
                content = content.replace(old, new)
                fixes_count += 1
                print(f"修复HTML标签: {old} -> {new}")
        
        # 4. 修复JavaScript注释问题
        js_comment_pattern = r'// ([^\n]*?)\?([^\n]*?)\n'
        js_matches = re.findall(js_comment_pattern, content)
        if js_matches:
            content = re.sub(js_comment_pattern, r'// \1\n', content)
            fixes_count += len(js_matches)
            print(f"修复JS注释: {len(js_matches)}个")
        
        # 5. 确保所有三引号字符串正确配对
        triple_quotes = content.count('"""')
        if triple_quotes % 2 != 0:
            print(f"警告: 三引号数量不匹配 ({triple_quotes})")
            # 在文件末尾添加缺失的三引号
            content += '\n"""\n'
            fixes_count += 1
            print("在文件末尾添加了缺失的三引号")
        
        # 6. 修复缩进问题
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 修复特定的缩进问题
            if '# 测试数据库连接            with db_manager.get_connection()' in line:
                # 分割这一行
                parts = line.split('with db_manager.get_connection()')
                if len(parts) == 2:
                    fixed_lines.append(parts[0].rstrip())
                    fixed_lines.append('            with db_manager.get_connection()' + parts[1])
                    fixes_count += 1
                    print(f"修复第{i+1}行的缩进问题")
                else:
                    fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # 写入新文件
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"\n重建完成！")
        print(f"总共修复了 {fixes_count} 个问题")
        
        return True
        
    except Exception as e:
        print(f"重建过程中出错: {e}")
        return False

def test_syntax():
    """测试Python语法"""
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, 'app.py', 'exec')
        print("\n✓ Python语法检查通过！")
        return True
        
    except SyntaxError as e:
        print(f"\n✗ 语法错误: {e}")
        print(f"行号: {e.lineno}")
        print(f"位置: {e.offset}")
        return False
    except Exception as e:
        print(f"\n✗ 检查过程中出错: {e}")
        return False

if __name__ == "__main__":
    print("开始重建app.py文件...")
    
    if rebuild_app_file():
        print("\n测试语法...")
        test_syntax()
    else:
        print("重建失败")