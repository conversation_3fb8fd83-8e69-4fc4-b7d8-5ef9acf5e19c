#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统性能优化器
实现自动性能优化、资源管理和系统调优
"""

import os
import gc
import time
import yaml
import json
import psutil
import logging
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from collections import defaultdict, deque
from dataclasses import dataclass

@dataclass
class PerformanceMetric:
    """
    性能指标数据类
    """
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_io_read: int
    disk_io_write: int
    network_sent: int
    network_recv: int
    response_time: float = 0.0
    request_count: int = 0
    error_count: int = 0

@dataclass
class OptimizationAction:
    """
    优化动作数据类
    """
    action_type: str
    target: str
    parameters: Dict[str, Any]
    priority: int
    estimated_impact: float
    description: str

class PerformanceOptimizer:
    """
    性能优化器
    负责监控系统性能并自动执行优化策略
    """
    
    def __init__(self, config_path: str = None):
        """
        初始化性能优化器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path) if config_path else Path("config/performance.yaml")
        self.config = self._load_config()
        
        # 性能指标历史
        self.metrics_history: deque = deque(maxlen=1000)
        self.optimization_history: List[OptimizationAction] = []
        
        # 监控状态
        self.monitoring_active = False
        self.monitoring_thread = None
        self.optimization_thread = None
        
        # 性能统计
        self.performance_stats = {
            "total_optimizations": 0,
            "successful_optimizations": 0,
            "failed_optimizations": 0,
            "last_optimization_time": None,
            "average_cpu_usage": 0.0,
            "average_memory_usage": 0.0,
            "peak_cpu_usage": 0.0,
            "peak_memory_usage": 0.0
        }
        
        # 优化策略
        self.optimization_strategies = {
            "memory_optimization": self._optimize_memory,
            "cpu_optimization": self._optimize_cpu,
            "disk_optimization": self._optimize_disk,
            "network_optimization": self._optimize_network,
            "cache_optimization": self._optimize_cache,
            "gc_optimization": self._optimize_garbage_collection
        }
        
        # 设置日志
        self._setup_logging()
        
        # 初始化缓存管理器
        self.cache_manager = None
        try:
            from .cache_manager import CacheManager
            self.cache_manager = CacheManager()
        except ImportError:
            self.logger.warning("缓存管理器不可用")
    
    def _load_config(self) -> Dict:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        if not self.config_path.exists():
            return self._get_default_config()
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or self._get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict:
        """
        获取默认配置
        
        Returns:
            默认配置字典
        """
        return {
            "monitoring": {
                "metrics": {
                    "enabled": True,
                    "collection_interval": 30
                }
            },
            "optimization": {
                "auto_optimization": {
                    "enabled": True,
                    "optimization_interval": 300
                }
            },
            "resource_limits": {
                "cpu": {"max_usage_percent": 80},
                "memory": {"max_usage_mb": 2048}
            }
        }
    
    def _setup_logging(self):
        """
        设置日志配置
        """
        self.logger = logging.getLogger("PerformanceOptimizer")
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def start_monitoring(self):
        """
        启动性能监控
        """
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        
        # 启动监控线程
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        # 启动优化线程
        if self.config.get("optimization", {}).get("auto_optimization", {}).get("enabled", True):
            self.optimization_thread = threading.Thread(target=self._optimization_loop)
            self.optimization_thread.daemon = True
            self.optimization_thread.start()
        
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """
        停止性能监控
        """
        self.monitoring_active = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        if self.optimization_thread:
            self.optimization_thread.join(timeout=5)
        
        self.logger.info("性能监控已停止")
    
    def _monitoring_loop(self):
        """
        监控循环
        """
        interval = self.config.get("monitoring", {}).get("metrics", {}).get("collection_interval", 30)
        
        while self.monitoring_active:
            try:
                # 收集性能指标
                metric = self._collect_performance_metric()
                self.metrics_history.append(metric)
                
                # 更新统计信息
                self._update_performance_stats(metric)
                
                # 检查是否需要立即优化
                if self._should_immediate_optimize(metric):
                    self._trigger_immediate_optimization(metric)
                
                time.sleep(interval)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {e}")
                time.sleep(interval)
    
    def _optimization_loop(self):
        """
        优化循环
        """
        interval = self.config.get("optimization", {}).get("auto_optimization", {}).get("optimization_interval", 300)
        
        while self.monitoring_active:
            try:
                time.sleep(interval)
                
                if not self.monitoring_active:
                    break
                
                # 执行定期优化
                self._perform_scheduled_optimization()
                
            except Exception as e:
                self.logger.error(f"优化循环异常: {e}")
                time.sleep(interval)
    
    def _collect_performance_metric(self) -> PerformanceMetric:
        """
        收集当前性能指标
        
        Returns:
            性能指标对象
        """
        # 获取系统资源信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk_io = psutil.disk_io_counters()
        network_io = psutil.net_io_counters()
        
        return PerformanceMetric(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_mb=memory.used / 1024 / 1024,
            disk_io_read=disk_io.read_bytes if disk_io else 0,
            disk_io_write=disk_io.write_bytes if disk_io else 0,
            network_sent=network_io.bytes_sent if network_io else 0,
            network_recv=network_io.bytes_recv if network_io else 0
        )
    
    def _update_performance_stats(self, metric: PerformanceMetric):
        """
        更新性能统计信息
        
        Args:
            metric: 性能指标
        """
        # 更新平均值
        if len(self.metrics_history) > 1:
            total_cpu = sum(m.cpu_percent for m in self.metrics_history)
            total_memory = sum(m.memory_percent for m in self.metrics_history)
            count = len(self.metrics_history)
            
            self.performance_stats["average_cpu_usage"] = total_cpu / count
            self.performance_stats["average_memory_usage"] = total_memory / count
        
        # 更新峰值
        self.performance_stats["peak_cpu_usage"] = max(
            self.performance_stats["peak_cpu_usage"],
            metric.cpu_percent
        )
        self.performance_stats["peak_memory_usage"] = max(
            self.performance_stats["peak_memory_usage"],
            metric.memory_percent
        )
    
    def _should_immediate_optimize(self, metric: PerformanceMetric) -> bool:
        """
        判断是否需要立即优化
        
        Args:
            metric: 性能指标
            
        Returns:
            是否需要立即优化
        """
        limits = self.config.get("resource_limits", {})
        
        # CPU使用率过高
        cpu_limit = limits.get("cpu", {}).get("max_usage_percent", 80)
        if metric.cpu_percent > cpu_limit:
            return True
        
        # 内存使用率过高
        memory_limit = limits.get("memory", {}).get("max_usage_mb", 2048)
        if metric.memory_mb > memory_limit:
            return True
        
        return False
    
    def _trigger_immediate_optimization(self, metric: PerformanceMetric):
        """
        触发立即优化
        
        Args:
            metric: 性能指标
        """
        self.logger.warning(f"触发立即优化 - CPU: {metric.cpu_percent:.1f}%, 内存: {metric.memory_mb:.1f}MB")
        
        # 确定优化策略
        strategies = []
        
        if metric.cpu_percent > 80:
            strategies.append("cpu_optimization")
        
        if metric.memory_mb > 1024:
            strategies.extend(["memory_optimization", "gc_optimization"])
        
        # 执行优化
        for strategy in strategies:
            self._execute_optimization_strategy(strategy, urgent=True)
    
    def _perform_scheduled_optimization(self):
        """
        执行定期优化
        """
        if len(self.metrics_history) < 10:
            return
        
        self.logger.info("开始定期性能优化")
        
        # 分析性能趋势
        optimization_actions = self._analyze_performance_trends()
        
        # 执行优化动作
        for action in optimization_actions:
            self._execute_optimization_action(action)
        
        self.performance_stats["last_optimization_time"] = datetime.now().isoformat()
    
    def _analyze_performance_trends(self) -> List[OptimizationAction]:
        """
        分析性能趋势并生成优化动作
        
        Returns:
            优化动作列表
        """
        actions = []
        
        # 获取最近的指标
        recent_metrics = list(self.metrics_history)[-10:]
        
        # 分析CPU趋势
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        if avg_cpu > 60:
            actions.append(OptimizationAction(
                action_type="cpu_optimization",
                target="system",
                parameters={"threshold": avg_cpu},
                priority=2 if avg_cpu > 80 else 1,
                estimated_impact=0.2,
                description=f"CPU使用率过高 ({avg_cpu:.1f}%)，执行CPU优化"
            ))
        
        # 分析内存趋势
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        if avg_memory > 70:
            actions.append(OptimizationAction(
                action_type="memory_optimization",
                target="system",
                parameters={"threshold": avg_memory},
                priority=2 if avg_memory > 85 else 1,
                estimated_impact=0.3,
                description=f"内存使用率过高 ({avg_memory:.1f}%)，执行内存优化"
            ))
        
        # 分析缓存效率
        if self.cache_manager:
            cache_stats = self.cache_manager.get_stats()
            hit_rate = cache_stats.get("hit_rate", 0)
            if hit_rate < 0.8:
                actions.append(OptimizationAction(
                    action_type="cache_optimization",
                    target="cache",
                    parameters={"hit_rate": hit_rate},
                    priority=1,
                    estimated_impact=0.15,
                    description=f"缓存命中率较低 ({hit_rate:.1%})，执行缓存优化"
                ))
        
        # 按优先级排序
        actions.sort(key=lambda x: x.priority, reverse=True)
        
        return actions
    
    def _execute_optimization_action(self, action: OptimizationAction):
        """
        执行优化动作
        
        Args:
            action: 优化动作
        """
        try:
            self.logger.info(f"执行优化动作: {action.description}")
            
            # 执行对应的优化策略
            if action.action_type in self.optimization_strategies:
                success = self.optimization_strategies[action.action_type](action.parameters)
                
                if success:
                    self.performance_stats["successful_optimizations"] += 1
                    self.logger.info(f"优化动作执行成功: {action.action_type}")
                else:
                    self.performance_stats["failed_optimizations"] += 1
                    self.logger.warning(f"优化动作执行失败: {action.action_type}")
            else:
                self.logger.error(f"未知的优化策略: {action.action_type}")
                self.performance_stats["failed_optimizations"] += 1
            
            self.performance_stats["total_optimizations"] += 1
            self.optimization_history.append(action)
            
        except Exception as e:
            self.logger.error(f"执行优化动作异常: {e}")
            self.performance_stats["failed_optimizations"] += 1
    
    def _execute_optimization_strategy(self, strategy: str, urgent: bool = False):
        """
        执行优化策略
        
        Args:
            strategy: 策略名称
            urgent: 是否紧急
        """
        if strategy in self.optimization_strategies:
            try:
                self.optimization_strategies[strategy]({"urgent": urgent})
            except Exception as e:
                self.logger.error(f"执行优化策略 {strategy} 异常: {e}")
    
    def _optimize_memory(self, parameters: Dict) -> bool:
        """
        内存优化
        
        Args:
            parameters: 优化参数
            
        Returns:
            优化成功标志
        """
        try:
            self.logger.info("开始内存优化")
            
            # 强制垃圾回收
            collected = gc.collect()
            self.logger.info(f"垃圾回收释放了 {collected} 个对象")
            
            # 清理缓存
            if self.cache_manager:
                cleaned = self.cache_manager.cleanup_expired()
                self.logger.info(f"清理了 {cleaned} 个过期缓存项")
            
            # 优化内存使用
            if parameters.get("urgent", False):
                # 紧急情况下更激进的清理
                if self.cache_manager:
                    self.cache_manager.clear_cache()
                    self.logger.info("紧急清理：清空所有缓存")
            
            return True
            
        except Exception as e:
            self.logger.error(f"内存优化失败: {e}")
            return False
    
    def _optimize_cpu(self, parameters: Dict) -> bool:
        """
        CPU优化
        
        Args:
            parameters: 优化参数
            
        Returns:
            优化成功标志
        """
        try:
            self.logger.info("开始CPU优化")
            
            # 降低进程优先级
            current_process = psutil.Process()
            if current_process.nice() == 0:  # 正常优先级
                current_process.nice(1)  # 降低优先级
                self.logger.info("降低了进程优先级")
            
            # 优化线程数量
            thread_count = threading.active_count()
            if thread_count > 20:
                self.logger.warning(f"线程数量较多: {thread_count}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"CPU优化失败: {e}")
            return False
    
    def _optimize_disk(self, parameters: Dict) -> bool:
        """
        磁盘优化
        
        Args:
            parameters: 优化参数
            
        Returns:
            优化成功标志
        """
        try:
            self.logger.info("开始磁盘优化")
            
            # 清理临时文件
            temp_dir = Path("/tmp") if os.name != 'nt' else Path(os.environ.get("TEMP", "C:\\temp"))
            if temp_dir.exists():
                # 这里可以添加临时文件清理逻辑
                pass
            
            # 优化文件缓存
            if self.cache_manager:
                self.cache_manager.optimize_file_cache()
            
            return True
            
        except Exception as e:
            self.logger.error(f"磁盘优化失败: {e}")
            return False
    
    def _optimize_network(self, parameters: Dict) -> bool:
        """
        网络优化
        
        Args:
            parameters: 优化参数
            
        Returns:
            优化成功标志
        """
        try:
            self.logger.info("开始网络优化")
            
            # 这里可以添加网络连接池优化等逻辑
            
            return True
            
        except Exception as e:
            self.logger.error(f"网络优化失败: {e}")
            return False
    
    def _optimize_cache(self, parameters: Dict) -> bool:
        """
        缓存优化
        
        Args:
            parameters: 优化参数
            
        Returns:
            优化成功标志
        """
        try:
            self.logger.info("开始缓存优化")
            
            if not self.cache_manager:
                return False
            
            # 获取缓存统计
            stats = self.cache_manager.get_stats()
            hit_rate = stats.get("hit_rate", 0)
            
            # 根据命中率调整缓存策略
            if hit_rate < 0.5:
                # 命中率太低，增加缓存大小
                self.cache_manager.resize_cache(int(stats.get("max_size", 1000) * 1.5))
                self.logger.info("增加了缓存大小")
            elif hit_rate > 0.95:
                # 命中率很高，可以适当减少缓存大小
                self.cache_manager.resize_cache(int(stats.get("max_size", 1000) * 0.8))
                self.logger.info("减少了缓存大小")
            
            # 预热热点数据
            self.cache_manager.warmup_cache()
            
            return True
            
        except Exception as e:
            self.logger.error(f"缓存优化失败: {e}")
            return False
    
    def _optimize_garbage_collection(self, parameters: Dict) -> bool:
        """
        垃圾回收优化
        
        Args:
            parameters: 优化参数
            
        Returns:
            优化成功标志
        """
        try:
            self.logger.info("开始垃圾回收优化")
            
            # 调整垃圾回收阈值
            import gc
            
            # 获取当前阈值
            thresholds = gc.get_threshold()
            
            # 如果内存压力大，降低阈值以更频繁地回收
            if parameters.get("urgent", False):
                new_thresholds = (thresholds[0] // 2, thresholds[1] // 2, thresholds[2] // 2)
                gc.set_threshold(*new_thresholds)
                self.logger.info(f"调整垃圾回收阈值: {thresholds} -> {new_thresholds}")
            
            # 执行完整的垃圾回收
            for generation in range(3):
                collected = gc.collect(generation)
                if collected > 0:
                    self.logger.info(f"第{generation}代垃圾回收释放了 {collected} 个对象")
            
            return True
            
        except Exception as e:
            self.logger.error(f"垃圾回收优化失败: {e}")
            return False
    
    def get_performance_report(self) -> Dict:
        """
        获取性能报告
        
        Returns:
            性能报告字典
        """
        if not self.metrics_history:
            return {"error": "没有性能数据"}
        
        recent_metrics = list(self.metrics_history)[-10:]
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "monitoring_duration": len(self.metrics_history),
            "current_performance": {
                "cpu_percent": recent_metrics[-1].cpu_percent,
                "memory_percent": recent_metrics[-1].memory_percent,
                "memory_mb": recent_metrics[-1].memory_mb
            },
            "average_performance": {
                "cpu_percent": sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics),
                "memory_percent": sum(m.memory_percent for m in recent_metrics) / len(recent_metrics),
                "memory_mb": sum(m.memory_mb for m in recent_metrics) / len(recent_metrics)
            },
            "optimization_stats": self.performance_stats.copy(),
            "recent_optimizations": [
                {
                    "action_type": action.action_type,
                    "description": action.description,
                    "priority": action.priority
                }
                for action in self.optimization_history[-5:]
            ]
        }
        
        # 添加缓存统计
        if self.cache_manager:
            report["cache_stats"] = self.cache_manager.get_stats()
        
        return report
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        获取当前性能指标
        
        Returns:
            当前性能指标字典
        """
        current_metrics = self._collect_performance_metric()
        
        return {
            'timestamp': datetime.now().isoformat(),
            'metrics': {
                'cpu_percent': current_metrics.cpu_percent,
                'memory_percent': current_metrics.memory_percent,
                'memory_mb': current_metrics.memory_mb,
                'disk_io_read': current_metrics.disk_io_read,
                'disk_io_write': current_metrics.disk_io_write,
                'network_sent': current_metrics.network_sent,
                'network_recv': current_metrics.network_recv
            },
            'optimization_count': self.performance_stats["total_optimizations"],
            'last_optimization': self.performance_stats["last_optimization_time"],
            'auto_optimization_enabled': self.config.get("optimization", {}).get("auto_optimization", {}).get("enabled", True)
        }
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """
        获取当前性能指标（get_metrics的别名）
        
        Returns:
            当前性能指标字典
        """
        return self.get_metrics()
    
    def force_optimization(self, strategy: str = None) -> bool:
        """
        强制执行优化
        
        Args:
            strategy: 指定的优化策略，None表示执行所有策略
            
        Returns:
            优化成功标志
        """
        try:
            if strategy:
                if strategy in self.optimization_strategies:
                    return self.optimization_strategies[strategy]({"forced": True})
                else:
                    self.logger.error(f"未知的优化策略: {strategy}")
                    return False
            else:
                # 执行所有优化策略
                success_count = 0
                for strategy_name, strategy_func in self.optimization_strategies.items():
                    try:
                        if strategy_func({"forced": True}):
                            success_count += 1
                    except Exception as e:
                        self.logger.error(f"执行策略 {strategy_name} 失败: {e}")
                
                return success_count > 0
                
        except Exception as e:
            self.logger.error(f"强制优化失败: {e}")
            return False
    
    def generate_report(self) -> Dict[str, Any]:
        """
        生成性能报告
        
        Returns:
            性能报告字典
        """
        current_time = datetime.now()
        
        # 计算时间范围
        if self.metrics_history:
            start_time = min(m.timestamp for m in self.metrics_history)
            duration = (current_time - start_time).total_seconds()
        else:
            start_time = current_time
            duration = 0
        
        # 计算平均值
        if self.metrics_history:
            avg_cpu = sum(m.cpu_percent for m in self.metrics_history) / len(self.metrics_history)
            avg_memory = sum(m.memory_percent for m in self.metrics_history) / len(self.metrics_history)
            avg_memory_mb = sum(m.memory_mb for m in self.metrics_history) / len(self.metrics_history)
        else:
            avg_cpu = avg_memory = avg_memory_mb = 0
        
        # 获取当前指标
        current_metrics = self._collect_performance_metric()
        
        report = {
            'report_time': current_time.isoformat(),
            'monitoring_duration_seconds': duration,
            'total_metrics_collected': len(self.metrics_history),
            'optimization_count': self.performance_stats["total_optimizations"],
            'last_optimization': self.performance_stats["last_optimization_time"],
            'current_metrics': {
                'cpu_percent': current_metrics.cpu_percent,
                'memory_percent': current_metrics.memory_percent,
                'memory_mb': current_metrics.memory_mb,
                'disk_io_read': current_metrics.disk_io_read,
                'disk_io_write': current_metrics.disk_io_write,
                'network_sent': current_metrics.network_sent,
                'network_recv': current_metrics.network_recv
            },
            'average_metrics': {
                'cpu_percent': avg_cpu,
                'memory_percent': avg_memory,
                'memory_mb': avg_memory_mb
            },
            'optimization_history': [
                {
                    'action_type': action.action_type,
                    'description': action.description,
                    'priority': action.priority,
                    'estimated_impact': action.estimated_impact
                }
                for action in self.optimization_history[-10:]  # 最近10次优化
            ],
            'performance_stats': self.performance_stats.copy()
        }
        
        # 添加缓存统计
        if self.cache_manager:
            report['cache_stats'] = self.cache_manager.get_stats()
        
        return report
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """
        生成性能报告（generate_report的别名）
        
        Returns:
            性能报告字典
        """
        return self.generate_report()

def main():
    """
    主函数 - 用于测试
    """
    optimizer = PerformanceOptimizer()
    
    try:
        print("启动性能优化器...")
        optimizer.start_monitoring()
        
        # 运行一段时间
        time.sleep(60)
        
        # 获取性能报告
        report = optimizer.get_performance_report()
        print(json.dumps(report, indent=2, ensure_ascii=False))
        
    except KeyboardInterrupt:
        print("\n停止性能优化器...")
    finally:
        optimizer.stop_monitoring()

if __name__ == "__main__":
    main()