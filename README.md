# 用户管理模块

基于Flask的用户管理系统，提供用户注册、登录、权限管理等功能。

## 功能特性

- 🔐 **安全认证**：JWT令牌认证，密码加密存储
- 👥 **用户管理**：用户增删改查，批量导入导出
- 🛡️ **权限控制**：基于角色的权限管理
- 📊 **数据导入**：支持Excel批量导入用户
- 🔍 **验证码**：登录验证码防暴力破解
- 📝 **日志记录**：完整的操作和登录日志
- 🔒 **数据加密**：敏感信息AES加密存储

## 技术栈

- **后端框架**：Flask 2.3.3
- **数据库**：SQLite（开发环境）/ MySQL（生产环境，通过SQLAlchemy ORM）
- **认证**：Flask-JWT-Extended
- **数据处理**：Pandas + OpenPyXL
- **加密**：bcrypt + AES-256
- **验证码**：Pillow图像处理

## 快速开始

### 1. 环境要求

- Python 3.8+
- SQLite（开发环境）/ MySQL 5.7+（生产环境）

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd 60-PHRL_User_Management

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 数据库配置

**开发环境（SQLite）**：
无需额外配置，SQLite数据库文件会自动创建。

**生产环境（MySQL）**：
1. 创建MySQL数据库：
```sql
CREATE DATABASE user_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改 `config.py` 中的数据库连接配置：
```python
# 生产环境配置
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://username:password@localhost:3306/user_management'
```

### 4. 初始化数据库

```bash
# 开发环境：直接运行即可自动创建SQLite数据库
python app.py
```

首次运行会自动创建数据表和默认管理员账号：
- 用户名：`admin`
- 密码：`Admin123!`

### 5. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:5000` 启动。

## API接口

### 认证接口

| 方法 | 路径 | 说明 |
|------|------|------|
| POST | `/api/auth/captcha` | 获取验证码 |
| POST | `/api/auth/login` | 用户登录 |
| POST | `/api/auth/logout` | 用户登出 |
| POST | `/api/auth/refresh` | 刷新令牌 |

### 用户管理接口

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/users` | 查询用户列表 |
| GET | `/api/users/{id}` | 查询用户详情 |
| POST | `/api/users` | 创建用户 |
| PUT | `/api/users/{id}` | 更新用户信息 |
| DELETE | `/api/users/{id}` | 删除用户 |
| DELETE | `/api/users/batch` | 批量删除用户 |
| POST | `/api/users/import` | 批量导入用户 |
| GET | `/api/users/export` | 导出用户数据 |
| PUT | `/api/users/{id}/password` | 修改密码 |

## 用户角色

- **admin**：管理员，拥有所有权限
- **expert**：专家，拥有考试和题库管理权限
- **student**：考生，基础权限
- **grader**：考评员，拥有阅卷评分权限

## 数据导入

### Excel模板格式

| 用户名* | 真实姓名* | 角色* | 邮箱 | 手机号 | 身份证号 | 部门 | 备注 |
|---------|-----------|-------|------|--------|----------|------|------|
| zhangsan | 张三 | student | <EMAIL> | 13800138001 | 110101199001011234 | 计算机系 | 示例数据 |

*必填字段

### 导入步骤

1. 下载导入模板：`GET /api/users/template`
2. 填写用户数据
3. 上传Excel文件：`POST /api/users/import`
4. 查看导入结果

## 安全特性

### 密码安全
- 使用bcrypt加密存储
- 强密码策略：至少8位，包含大小写字母、数字
- 登录失败锁定机制

### 数据加密
- 身份证号AES-256加密存储
- JWT令牌安全传输
- 敏感数据脱敏显示

### 访问控制
- 基于角色的权限控制
- API接口权限验证
- 操作日志记录

## 配置说明

### 环境配置

在 `config.py` 中可配置：

```python
class DevelopmentConfig(Config):
    # 开发环境：SQLite数据库
    SQLALCHEMY_DATABASE_URI = 'sqlite:///user_management.db'
    DEBUG = True

class ProductionConfig(Config):
    # 生产环境：MySQL数据库
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://user:pass@localhost/db'
    
    # JWT配置
    JWT_SECRET_KEY = 'your-secret-key'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
    
    # 加密配置
    ENCRYPTION_KEY = 'your-encryption-key'
```

### 日志配置

日志文件存储在 `logs/` 目录：
- `app.log`：应用日志
- `error.log`：错误日志
- `access.log`：访问日志

## 开发指南

### 项目结构

```
60-PHRL_User_Management/
├── app.py                 # 主应用文件
├── config.py             # 配置文件
├── models.py             # 数据模型
├── requirements.txt      # 依赖包
├── README.md            # 项目说明
├── utils/               # 工具模块
│   ├── __init__.py
│   ├── auth.py          # 认证工具
│   ├── captcha.py       # 验证码工具
│   ├── crypto.py        # 加密工具
│   ├── excel_handler.py # Excel处理
│   ├── logger.py        # 日志工具
│   ├── response.py      # 响应格式
│   └── validators.py    # 数据验证
├── uploads/             # 上传文件目录
└── logs/               # 日志目录
```

### 添加新接口

1. 在 `app.py` 中定义路由
2. 使用认证装饰器保护接口
3. 添加数据验证
4. 记录操作日志
5. 返回统一格式响应

示例：
```python
@app.route('/api/users', methods=['POST'])
@token_required
@admin_required
def create_user():
    data = request.get_json()
    
    # 数据验证
    validation = validate_user_data(data)
    if not validation['valid']:
        return validation_error_response(validation['message'])
    
    # 业务逻辑
    user = User(**data)
    db.session.add(user)
    db.session.commit()
    
    # 记录日志
    log_operation(
        user_id=g.current_user.user_id,
        operation=Operations.USER_CREATE,
        resource_type=ResourceTypes.USER,
        resource_id=user.user_id
    )
    
    return success_response(user.to_dict(), '用户创建成功')
```

## 部署说明

### 生产环境部署

1. 使用Gunicorn作为WSGI服务器：
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

2. 配置Nginx反向代理
3. 设置环境变量
4. 配置SSL证书
5. 设置定时任务清理日志

### Docker部署

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
```

## 常见问题

### Q: 忘记管理员密码怎么办？
A: 可以直接修改数据库中admin用户的密码字段，或重新运行初始化脚本。

### Q: 如何修改默认端口？
A: 在 `app.py` 的最后一行修改 `port` 参数。

### Q: 导入用户时提示格式错误？
A: 请确保Excel文件格式正确，必填字段不能为空，数据格式符合验证规则。

### Q: 如何备份数据？
A: 定期备份MySQL数据库，同时备份上传的文件和日志。

## 许可证

MIT License

## 联系方式

如有问题请联系开发团队。