#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API网关主应用
实现统一入口、路由转发、认证授权、健康检查等功能
"""

import os
import sys
import time
import json
import logging
import threading
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple

import yaml
import requests
from flask import Flask, request, jsonify, g
from flask_cors import CORS
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from werkzeug.exceptions import HTTPException
from prometheus_client import Counter, Histogram, Gauge, generate_latest

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置werkzeug日志级别以显示HTTP请求
logging.getLogger('werkzeug').setLevel(logging.INFO)

class HealthChecker:
    """增强的健康检查器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.module_status = {}
        self.check_interval = config.get('health_check_interval', 30)
        self.timeout = config.get('health_check_timeout', 10)
        self.max_retries = config.get('health_check_retries', 3)
        self.running = False
        self.thread = None
        
        # 初始化模块状态
        for module_name, module_config in config.get('modules', {}).items():
            self.module_status[module_name] = {
                'status': 'unknown',
                'last_check': None,
                'consecutive_failures': 0,
                'response_time': None,
                'error_message': None
            }
    
    def start(self):
        """启动健康检查"""
        if not self.running:
            self.running = True
            self.thread = threading.Thread(target=self._health_check_loop, daemon=True)
            self.thread.start()
            logger.info("健康检查器已启动")
    
    def stop(self):
        """停止健康检查"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("健康检查器已停止")
    
    def _health_check_loop(self):
        """健康检查循环"""
        while self.running:
            try:
                self._check_all_modules()
                time.sleep(self.check_interval)
            except Exception as e:
                logger.error(f"健康检查循环异常: {e}")
                time.sleep(5)
    
    def _check_all_modules(self):
        """检查所有模块健康状态"""
        for module_name, module_config in self.config.get('modules', {}).items():
            try:
                self._check_module_health(module_name, module_config)
            except Exception as e:
                logger.error(f"检查模块 {module_name} 健康状态失败: {e}")
                self._update_module_status(module_name, 'error', str(e))
    
    def _check_module_health(self, module_name: str, module_config: Dict):
        """检查单个模块健康状态 - 多策略检查"""
        host = module_config.get('host', 'localhost')
        port = module_config.get('port')
        health_endpoint = module_config.get('health_check', '/api/health')
        
        start_time = time.time()
        
        # 策略1: 检查健康检查端点
        success, error_msg = self._check_health_endpoint(host, port, health_endpoint)
        
        if not success:
            # 策略2: 检查根路径
            success, error_msg = self._check_root_endpoint(host, port)
            
            if not success:
                # 策略3: 检查端口是否开放
                success, error_msg = self._check_port_availability(host, port)
        
        response_time = (time.time() - start_time) * 1000  # 转换为毫秒
        
        if success:
            self._update_module_status(module_name, 'healthy', None, response_time)
        else:
            self._update_module_status(module_name, 'unhealthy', error_msg, response_time)
    
    def _check_health_endpoint(self, host: str, port: int, endpoint: str) -> Tuple[bool, str]:
        """检查健康检查端点"""
        try:
            url = f"http://{host}:{port}{endpoint}"
            response = requests.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                # 尝试解析JSON响应
                try:
                    data = response.json()
                    if data.get('status') == 'healthy' or data.get('code') == 200:
                        return True, None
                    else:
                        return False, f"健康检查返回非健康状态: {data}"
                except json.JSONDecodeError:
                    # 如果不是JSON，但状态码是200，也认为是健康的
                    return True, None
            else:
                return False, f"健康检查端点返回状态码: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            return False, f"健康检查端点请求失败: {str(e)}"
    
    def _check_root_endpoint(self, host: str, port: int) -> Tuple[bool, str]:
        """检查根路径端点"""
        try:
            url = f"http://{host}:{port}/"
            response = requests.get(url, timeout=self.timeout)
            
            # 任何HTTP响应都认为服务是运行的
            if response.status_code in [200, 404, 302, 301]:
                return True, None
            else:
                return False, f"根路径返回状态码: {response.status_code}"
                
        except requests.exceptions.RequestException as e:
            return False, f"根路径请求失败: {str(e)}"
    
    def _check_port_availability(self, host: str, port: int) -> Tuple[bool, str]:
        """检查端口是否可用"""
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                return True, None
            else:
                return False, f"端口 {port} 不可达"
                
        except Exception as e:
            return False, f"端口检查失败: {str(e)}"
    
    def _update_module_status(self, module_name: str, status: str, error_message: str = None, response_time: float = None):
        """更新模块状态"""
        current_status = self.module_status.get(module_name, {})
        
        if status == 'healthy':
            consecutive_failures = 0
        else:
            consecutive_failures = current_status.get('consecutive_failures', 0) + 1
        
        self.module_status[module_name] = {
            'status': status,
            'last_check': datetime.now().isoformat(),
            'consecutive_failures': consecutive_failures,
            'response_time': response_time,
            'error_message': error_message
        }
        
        # 记录状态变化
        if current_status.get('status') != status:
            logger.info(f"模块 {module_name} 状态变更: {current_status.get('status', 'unknown')} -> {status}")
            if error_message:
                logger.warning(f"模块 {module_name} 错误信息: {error_message}")
    
    def get_module_status(self, module_name: str = None) -> Dict:
        """获取模块状态"""
        if module_name:
            return self.module_status.get(module_name, {})
        return self.module_status
    
    def is_module_healthy(self, module_name: str) -> bool:
        """检查模块是否健康"""
        status = self.module_status.get(module_name, {})
        return status.get('status') == 'healthy'

class APIGateway:
    """API网关主类"""
    
    def __init__(self, config_path: str = 'config/gateway.yaml'):
        self.app = Flask(__name__)
        self.app.secret_key = 'api-gateway-secret-key-2024'  # 设置session密钥
        CORS(self.app)
        
        # 记录启动时间
        self.start_time = time.time()
        
        # 处理配置文件路径
        if not os.path.isabs(config_path):
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, config_path)
        
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 初始化组件
        self.health_checker = HealthChecker(self.config)
        self.limiter = Limiter(
            app=self.app,
            key_func=get_remote_address,
            default_limits=["1000 per hour"]
        )
        
        # 初始化监控指标
        self._init_metrics()
        
        # 注册路由
        self._register_routes()
        
        # 注册错误处理器
        self._register_error_handlers()
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            # 返回默认配置
            return {
                'gateway': {'host': '0.0.0.0', 'port': 5000},
                'modules': {},
                'health_check_interval': 30
            }
    
    def _init_metrics(self):
        """初始化监控指标"""
        self.request_count = Counter('gateway_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
        self.request_duration = Histogram('gateway_request_duration_seconds', 'Request duration')
        self.module_health = Gauge('gateway_module_health', 'Module health status', ['module'])
    
    def _register_routes(self):
        """注册路由"""
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """网关健康检查"""
            module_statuses = self.health_checker.get_module_status()
            
            overall_healthy = all(
                status.get('status') == 'healthy' 
                for status in module_statuses.values()
            )
            
            return jsonify({
                'code': 200 if overall_healthy else 503,
                'message': 'healthy' if overall_healthy else 'unhealthy',
                'data': {
                    'gateway_status': 'healthy',
                    'modules': module_statuses,
                    'timestamp': datetime.now().isoformat()
                },
                'timestamp': datetime.now().isoformat()
            }), 200 if overall_healthy else 503
        
        @self.app.route('/api/health/modules', methods=['GET'])
        def modules_health():
            """模块健康状态详情"""
            return jsonify({
                'code': 200,
                'message': 'success',
                'data': self.health_checker.get_module_status(),
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/set_session', methods=['POST'])
        def set_session():
            """设置session信息（从主系统传递）"""
            try:
                from flask import session
                data = request.get_json()
                if not data:
                    return jsonify({
                        'code': 400,
                        'message': 'No data provided',
                        'timestamp': datetime.now().isoformat()
                    }), 400
                
                # 设置session信息
                if 'user' in data:
                    session['user'] = data['user']
                if 'session_id' in data:
                    session['session_id'] = data['session_id']
                
                return jsonify({
                    'code': 200,
                    'message': 'Session set successfully',
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"Set session error: {e}")
                return jsonify({
                    'code': 500,
                    'message': 'Internal server error',
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/metrics', methods=['GET'])
        def metrics():
            """Prometheus监控指标"""
            return generate_latest()
        
        @self.app.route('/', methods=['GET'])
        def index():
            """API网关管理界面"""
            from flask import render_template
            # 检查是否是API请求（通过Accept头判断）
            if request.headers.get('Accept', '').startswith('application/json'):
                return jsonify({
                    'code': 200,
                    'message': 'API Gateway is running',
                    'data': {
                        'service': 'API Gateway',
                        'version': '1.0.0',
                        'status': 'healthy',
                        'endpoints': {
                            'health': '/api/health',
                            'modules_health': '/api/health/modules',
                            'metrics': '/metrics'
                        },
                        'available_modules': list(self.config.get('modules', {}).keys())
                    },
                    'timestamp': datetime.now().isoformat()
                })
            else:
                # 返回HTML管理界面
                return render_template('dashboard.html')
        
        @self.app.route('/dashboard', methods=['GET'])
        def dashboard():
            """API网关管理控制台"""
            from flask import render_template
            return render_template('dashboard.html')
        
        @self.app.route('/api/modules/status', methods=['GET'])
        def api_modules_status():
            """获取所有模块状态API"""
            try:
                modules_status = {}
                module_statuses = self.health_checker.get_module_status()
                
                for module_id, status_info in module_statuses.items():
                    module_config = self.config.get('modules', {}).get(module_id, {})
                    modules_status[module_id] = {
                        'name': module_config.get('name', module_id),
                        'description': module_config.get('description', ''),
                        'status': 'running' if status_info.get('status') == 'healthy' else 'stopped',
                        'port': module_config.get('port'),
                        'last_check': status_info.get('last_check'),
                        'response_time': status_info.get('response_time'),
                        'error_message': status_info.get('error_message')
                    }
                
                return jsonify({
                    'code': 200,
                    'message': 'success',
                    'modules': modules_status,
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"获取模块状态失败: {e}")
                return jsonify({
                    'code': 500,
                    'message': 'Internal server error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/api/routes', methods=['GET'])
        def api_routes_info():
            """获取路由信息API"""
            try:
                routes = []
                route_rules = self.config.get('routes', [])
                
                for route in route_rules:
                    routes.append({
                        'pattern': route.get('pattern', ''),
                        'target': route.get('target', ''),
                        'methods': route.get('methods', ['GET']),
                        'auth_required': route.get('auth_required', False)
                    })
                
                return jsonify({
                    'code': 200,
                    'message': 'success',
                    'routes': routes,
                    'total': len(routes),
                    'timestamp': datetime.now().isoformat()
                })
            except Exception as e:
                logger.error(f"获取路由信息失败: {e}")
                return jsonify({
                    'code': 500,
                    'message': 'Internal server error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/api/stats', methods=['GET'])
        def api_statistics():
            """获取统计信息API"""
            try:
                # 这里可以添加更多统计信息
                stats = {
                    'total_requests': 0,  # 可以从监控指标中获取
                    'avg_response_time': 0,  # 可以从监控指标中获取
                    'active_modules': len([m for m in self.health_checker.get_module_status().values() if m.get('status') == 'healthy']),
                    'total_modules': len(self.config.get('modules', {})),
                    'uptime': time.time() - getattr(self, 'start_time', time.time())
                }
                
                return jsonify({
                    'code': 200,
                    'message': 'success',
                    'data': stats,
                    'timestamp': datetime.now().isoformat(),
                    **stats  # 直接展开统计数据到根级别
                })
            except Exception as e:
                logger.error(f"获取统计信息失败: {e}")
                return jsonify({
                    'code': 500,
                    'message': 'Internal server error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/api/logs', methods=['GET'])
        def api_logs():
            """获取日志信息API"""
            return jsonify({
                'code': 200,
                'message': 'Log viewing feature coming soon',
                'data': [],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/config/export', methods=['GET'])
        def api_export_config():
            """导出配置API"""
            from flask import Response
            try:
                config_json = json.dumps(self.config, indent=2, ensure_ascii=False)
                return Response(
                    config_json,
                    mimetype='application/json',
                    headers={'Content-Disposition': 'attachment; filename=gateway_config.json'}
                )
            except Exception as e:
                logger.error(f"导出配置失败: {e}")
                return jsonify({
                    'code': 500,
                    'message': 'Export failed',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }), 500
        
        @self.app.route('/<path:path>', methods=['GET', 'POST', 'PUT', 'DELETE', 'PATCH'])
        def proxy_request(path):
            """代理请求到后端模块"""
            start_time = time.time()
            
            # 添加明显的日志标记
            logger.info("=== PROXY REQUEST START ===")
            logger.info(f"收到请求: {request.method} /{path} from {request.remote_addr}")
            logger.info(f"请求头: {dict(request.headers)}")
            logger.info(f"请求cookies: {dict(request.cookies)}")
            logger.info("=== REQUEST INFO END ===")
            
            try:
                # 路由匹配
                target_module, target_path = self._route_request(path)
                
                if not target_module:
                    logger.warning(f"路由未找到: /{path}")
                    return jsonify({
                        'code': 404,
                        'message': 'Route not found',
                        'error': {'type': 'ROUTE_NOT_FOUND', 'details': f'No route found for path: /{path}'}
                    }), 404
                
                # 认证检查
                auth_result = self._check_authentication(f'/{path}', request.method)
                logger.info(f"认证检查结果: path=/{path}, auth_result={auth_result}")
                if not auth_result['allowed']:
                    logger.info(f"认证失败，返回401: {auth_result['reason']}")
                    return jsonify({
                        'code': 401,
                        'message': 'Authentication required',
                        'error': {'type': 'AUTHENTICATION_REQUIRED', 'details': auth_result['reason']}
                    }), 401
                
                # 检查模块健康状态
                if not self.health_checker.is_module_healthy(target_module):
                    return jsonify({
                        'code': 503,
                        'message': 'Service unavailable',
                        'error': {'type': 'SERVICE_UNAVAILABLE', 'details': f'Module {target_module} is unhealthy'}
                    }), 503
                
                # 转发请求
                response = self._forward_request(target_module, target_path)
                
                # 记录指标
                duration = time.time() - start_time
                self.request_duration.observe(duration)
                self.request_count.labels(
                    method=request.method,
                    endpoint=f'/{path}',
                    status=response.status_code
                ).inc()
                
                return response
                
            except Exception as e:
                logger.error(f"请求代理失败: {e}")
                duration = time.time() - start_time
                self.request_duration.observe(duration)
                self.request_count.labels(
                    method=request.method,
                    endpoint=f'/{path}',
                    status=500
                ).inc()
                
                return jsonify({
                    'code': 500,
                    'message': 'Internal server error',
                    'error': {'type': 'INTERNAL_ERROR', 'details': str(e)}
                }), 500
    
    def _route_request(self, path: str) -> Tuple[Optional[str], Optional[str]]:
        """路由请求到对应模块"""
        logger.info(f"路由匹配: 输入路径 = {path}")
        
        # API路由匹配 - 修复路径匹配逻辑
        if path.startswith('api/v1/user'):
            logger.info(f"匹配到用户管理模块: {path}")
            return 'user_management', path
        elif path.startswith('api/v1/question'):
            logger.info(f"匹配到题库管理模块: {path}")
            return 'question_bank', path
        elif path.startswith('api/v1/exam'):
            logger.info(f"匹配到考试管理模块: {path}")
            return 'exam_management', path
        elif path.startswith('api/v1/score-reporting'):
            logger.info(f"匹配到成绩上报接口模块: {path}")
            return 'exam_score_reporting_interface', path
        elif path.startswith('api/v1/score'):
            logger.info(f"匹配到成绩管理模块: {path}")
            return 'score_management', path
        elif path.startswith('api/v1/monitor'):
            logger.info(f"匹配到监控模块: {path}")
            return 'monitoring', path
        elif path.startswith('api/v1/audit'):
            logger.info(f"匹配到审计模块: {path}")
            return 'auditing', path
        elif path.startswith('api/v1/filemanager') or path.startswith('api/v1/files') or path.startswith('api/v1/projects') or path.startswith('api/v1/tasks'):
            logger.info(f"匹配到文件项目管理模块: {path}")
            return 'file_project_management', path
        
        # Web界面路由匹配 - 支持模块的Web界面访问
        elif path.startswith('user-management'):
            # 保留完整路径，让模块自己处理
            return 'user_management', path
        elif path.startswith('question-bank'):
            return 'question_bank', path
        elif path.startswith('exam-management'):
            return 'exam_management', path
        elif path.startswith('grade-management'):
            return 'score_management', path
        elif path.startswith('monitoring'):
            return 'monitoring', path
        elif path.startswith('audit'):
            return 'auditing', path
        elif path.startswith('file-project-management'):
            return 'file_project_management', path
        
        return None, None
    
    def _check_authentication(self, path: str, method: str) -> Dict:
        """检查请求是否需要认证以及认证状态"""
        # 检查路由规则中是否需要认证
        routes = self.config.get('routing', {}).get('rules', [])
        
        logger.info(f"检查认证: path={path}, method={method}")
        logger.info(f"总共加载了 {len(routes)} 条路由规则")
        
        # 打印所有路由规则用于调试
        for i, route in enumerate(routes):
            pattern = route.get('pattern')
            auth_required = route.get('auth_required')
            logger.info(f"路由规则 {i}: pattern={pattern}, auth_required={auth_required}")
            # 特别关注projects相关的规则
            if 'projects' in pattern:
                logger.info(f"发现projects相关规则: {route}")
        
        for route in routes:
            pattern = route.get('pattern', '')
            methods = route.get('methods', ['GET'])
            auth_required = route.get('auth_required', True)
            
            # 简单的路径匹配（支持通配符）
            match_result = self._match_pattern(path, pattern)
            logger.info(f"路径匹配: path={path}, pattern={pattern}, match={match_result}, method_match={method in methods}")
            
            # 特别调试 /api/v1/filemanager/** 规则
            if pattern == "/api/v1/filemanager/**":
                logger.info(f"特别调试 filemanager 规则: path={path}, pattern={pattern}, match={match_result}, auth_required={auth_required}")
            
            if match_result and method in methods:
                if not auth_required:
                    logger.info(f"无需认证的路径: {path}")
                    return {'allowed': True, 'reason': 'No authentication required'}
                
                # 通过转发请求到主系统验证session
                return self._verify_session_with_main_system()
        
        # 默认需要认证，通过主系统验证
        logger.info(f"未匹配到路由规则，使用默认认证: {path}")
        return self._verify_session_with_main_system()
    
    def _verify_session_with_main_system(self) -> Dict:
        """通过主系统验证session状态"""
        try:
            # 转发session验证请求到主系统
            cookies = request.cookies
            response = requests.get(
                'http://localhost:8000/api/verify_session',
                cookies=cookies,
                timeout=5
            )
            
            if response.status_code == 200:
                return {'allowed': True, 'reason': 'Session verified by main system'}
            else:
                return {'allowed': False, 'reason': 'Session verification failed'}
        except Exception as e:
            logger.error(f"Session verification error: {e}")
            return {'allowed': False, 'reason': 'Session verification error'}
    
    def _match_pattern(self, path: str, pattern: str) -> bool:
        """匹配路径模式（支持通配符）"""
        import re
        original_pattern = pattern
        # 将通配符转换为正则表达式
        # 先替换单个*，再替换**，避免冲突
        pattern = pattern.replace('*', '__SINGLE_WILDCARD__')
        pattern = pattern.replace('__SINGLE_WILDCARD____SINGLE_WILDCARD__', '.*')  # ** -> .*
        pattern = pattern.replace('__SINGLE_WILDCARD__', '[^/]*')  # * -> [^/]*
        regex_pattern = f'^{pattern}$'
        logger.info(f"路径匹配调试: path={path}, original_pattern={original_pattern}, regex_pattern={regex_pattern}")
        result = bool(re.match(regex_pattern, path))
        logger.info(f"路径匹配结果: {result}")
        return result
    
    def _forward_request(self, module_name: str, path: str):
        """转发请求到目标模块"""
        module_config = self.config.get('modules', {}).get(module_name)
        if not module_config:
            raise ValueError(f"Module {module_name} not configured")
        
        host = module_config.get('host', 'localhost')
        port = module_config.get('port')
        
        # 路径映射：将网关路径映射到模块内部路径
        target_path = self._map_path_to_module(module_name, path)
        
        url = f"http://{host}:{port}/{target_path}"
        
        # 准备转发的headers，排除host但保留其他重要headers
        forward_headers = {k: v for k, v in request.headers if k.lower() != 'host'}
        
        # 准备cookies - 确保session信息被正确转发
        cookies = request.cookies
        
        # 转发请求
        response = requests.request(
            method=request.method,
            url=url,
            headers=forward_headers,
            data=request.get_data(),
            params=request.args,
            cookies=cookies,  # 转发cookies以保持session
            timeout=module_config.get('timeout', 30),
            allow_redirects=False  # 不自动跟随重定向，让前端处理
        )
        
        # 创建Flask响应对象，保持原始响应的所有信息
        from flask import Response
        flask_response = Response(
            response.content,
            status=response.status_code,
            headers=dict(response.headers)
        )
        
        # 转发Set-Cookie headers以保持session状态
        if 'Set-Cookie' in response.headers:
            flask_response.headers['Set-Cookie'] = response.headers['Set-Cookie']
        
        return flask_response
    
    def _map_path_to_module(self, module_name: str, path: str) -> str:
        """将网关路径映射到模块内部路径"""
        if module_name == 'file_project_management':
            # 文件项目管理模块的路径映射
            if path == 'api/v1/filemanager/health':
                return 'api/health'
            elif path == 'api/v1/filemanager/info':
                return 'api/info'
            elif path.startswith('api/v1/filemanager/'):
                # 移除 /filemanager 前缀
                return path.replace('api/v1/filemanager/', 'api/v1/')
            elif path.startswith('api/v1/files/'):
                return path  # 保持原路径
            elif path.startswith('api/v1/projects/'):
                return path  # 保持原路径
            elif path.startswith('api/v1/tasks/'):
                return path  # 保持原路径
        elif module_name == 'exam_score_reporting_interface':
            # 成绩上报接口模块的路径映射
            if path == 'api/v1/score-reporting/health':
                return 'health'  # 映射到模块的 /health 端点
            elif path.startswith('api/v1/score-reporting/'):
                # 移除 /api/v1/score-reporting 前缀，保留后续路径
                return path.replace('api/v1/score-reporting/', '')
            elif path.startswith('api/v1/exam-score/'):
                # 处理 exam-score 路径
                return path.replace('api/v1/exam-score/', 'api/v1/')
        
        # 其他模块保持原路径
        return path
    
    def _register_error_handlers(self):
        """注册错误处理器"""
        
        @self.app.errorhandler(HTTPException)
        def handle_http_exception(e):
            return jsonify({
                'code': e.code,
                'message': e.description,
                'error': {'type': 'HTTP_ERROR', 'details': str(e)}
            }), e.code
        
        @self.app.errorhandler(Exception)
        def handle_exception(e):
            logger.error(f"未处理的异常: {e}")
            return jsonify({
                'code': 500,
                'message': 'Internal server error',
                'error': {'type': 'INTERNAL_ERROR', 'details': str(e)}
            }), 500
    
    def start(self):
        """启动网关"""
        # 启动健康检查
        self.health_checker.start()
        
        # 启动Flask应用
        gateway_config = self.config.get('gateway', {})
        host = gateway_config.get('host', '0.0.0.0')
        port = gateway_config.get('port', 5000)
        debug = gateway_config.get('debug', False)
        
        logger.info(f"API网关启动中... http://{host}:{port}")
        
        try:
            # 使用非阻塞模式启动，避免阻塞主启动器
            self.app.run(host=host, port=port, debug=debug, threaded=True, use_reloader=False)
        except KeyboardInterrupt:
            logger.info("收到停止信号")
        finally:
            self.health_checker.stop()
            logger.info("API网关已停止")
    
    def start_non_blocking(self):
        """非阻塞启动网关（用于主启动器调用）"""
        import threading
        
        def run_gateway():
            self.start()
        
        # 在单独线程中启动网关
        gateway_thread = threading.Thread(target=run_gateway, daemon=True)
        gateway_thread.start()
        
        # 等待一小段时间确保启动
        import time
        time.sleep(2)
        
        # 检查网关是否成功启动
        gateway_config = self.config.get('gateway', {})
        port = gateway_config.get('port', 5000)
        
        import socket
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            if result == 0:
                logger.info(f"API网关非阻塞启动成功，端口 {port} 可用")
                return True
            else:
                logger.error(f"API网关启动失败，端口 {port} 不可用")
                return False
        except Exception as e:
            logger.error(f"检查API网关启动状态时出错: {e}")
            return False

def main():
    """主函数"""
    gateway = APIGateway()
    gateway.start()

if __name__ == '__main__':
    main()