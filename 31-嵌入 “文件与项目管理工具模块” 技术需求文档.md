局域网在线考试系统嵌入 “文件与项目管理工具模块” 技术需求文档

1. 项目概述
1.1 文档目的
本文档明确局域网在线考试系统中嵌入 “文件与项目管理工具模块”（以下简称 “工具模块”）的技术需求，包括功能、接口、性能等要求，作为开发、测试和验收的依据。功能类似简单的项目进度管理+FTP文件传输管理功能的整合，无需考虑复杂的项目管理功能（如甘特图、资源分配）。
1.2 项目背景
现有局域网在线考试系统已支持理论考试与非集成型软件操作考试，用户群体（管理员、专家、考评员）和硬件环境（服务器、存储设备）固定。为提升系统资源利用率，减少用户跨工具操作成本，需嵌入轻量工具模块，实现文件管理与项目进度跟踪功能。
1.3 目标与范围
1.3.1 核心目标
1.	复用现有系统的用户权限、存储资源，实现文件的安全管理与项目进度的简易跟踪；
2.	与考试系统业务场景协同（如题库素材管理、考试任务进度跟踪），避免功能冗余。
1.3.2 范围界定

包含范围	排除范围
1. 基于角色的文件上传 / 下载 / 目录管理；2. 考试相关项目任务的创建、进度跟踪；3. 与现有系统的用户、存储接口集成。	1. 通用化办公文件协作（如多人实时编辑）；2. 复杂项目管理功能（如甘特图、资源分配）；3. 跨局域网文件传输。
2. 功能需求
2.1 模块定位
工具模块为独立功能模块，通过接口与现有系统集成，不影响考试核心流程（如答题、评分）。
2.2 文件管理功能
2.2.1 目录管理
3.	支持按 “业务类型” 自动创建一级目录：
1.	预设目录：/题库素材/、/实操任务资源/、/考试批次文件/、/项目文档/；
2.	管理员可新增二级目录（如/题库素材/2025电工中级/），支持重命名、删除。
4.	目录权限继承：子目录默认继承父目录的权限设置。
2.2.2 文件操作

功能	说明	权限控制
上传	支持单文件（≤100MB）和多文件批量上传，自动校验文件格式（禁止.exe 等风险格式）	管理员、专家可上传所有目录；考生仅可上传 “个人提交” 子目录
下载	支持单个文件下载或文件夹打包下载（zip 格式）	拥有目录访问权限的用户均可下载
预览	支持图片（.jpg/.png）、文档（.docx/.pdf）在线预览	同下载权限
删除	支持单个或批量删除文件，删除后移入 “回收站”（保留 7 天）	仅管理员、文件上传者可删除
2.2.3 版本管理
5.	同一文件重复上传时，自动生成新版本（保留最近 3 个版本）；
6.	支持查看版本历史（上传时间、上传者），可回滚至旧版本。
2.3 项目进度管理功能
2.3.1 任务创建与管理
7.	任务信息：包含名称、描述、负责人（从现有用户中选择）、截止时间、优先级（高 / 中 / 低）；
8.	任务类型：预设与考试相关的类型，不可自定义：
1.	题库类：题库更新、试题审核；
2.	考试类：试卷制作、考场布置、成绩审核。
2.3.2 进度跟踪
9.	任务状态：待办→进行中→待审核→已完成（状态变更需记录操作人）；
10.	进度关联：支持上传任务相关文件（如 “试卷制作” 任务关联试卷初稿.docx）；
11.	提醒功能：任务截止前 1 天，向负责人推送系统内消息（复用现有消息通知机制）。
3. 非功能需求
3.1 性能要求
12.	文件上传 / 下载速度：局域网内单文件（50MB）传输耗时≤10 秒；
13.	页面响应时间：目录加载、任务列表查询≤2 秒；
14.	并发支持：同时在线操作文件的用户≤50 人（匹配考试系统并发量）。
3.2 安全性要求
15.	文件加密：复用现有系统的文件加密机制（存储时生成唯一哈希值，防篡改）；
16.	操作日志：记录所有文件操作（上传 / 删除 / 下载）和任务状态变更，日志保留≥1 年；
17.	防越权：通过接口严格校验用户权限，禁止访问无权限的目录 / 文件。
3.3 兼容性要求
18.	浏览器兼容：支持 Chrome 90+、Edge 90+（与考试系统一致）；
19.	文件格式支持：常见办公格式（.docx/.xlsx/.pdf）、图片（.jpg/.png）、压缩包（.zip/.rar）、专业软件格式（.dwg/.prproj 等，与实操考试适配）。
4. 接口需求
工具模块需调用现有系统的以下接口，接口规范遵循《局域网在线考试系统接口规范手册》：

接口功能	调用模块	接口路径	用途
用户权限校验	用户管理模块	/api/v1/user/check-perm	验证用户身份及操作权限
文件存储	成果文件管理模块	/api/v1/file/upload	复用文件上传底层能力
文件下载授权	成果文件管理模块	/api/v1/file/download-url	获取带权限的文件下载链接
5. 数据需求
5.1 新增数据表
5.1.1 文件目录表（file_directory）

字段名	数据类型	说明
directory_id	字符串（32 位）	目录唯一 ID（主键）
parent_id	字符串（32 位）	父目录 ID（顶级目录为 0）
name	字符串（100 位）	目录名称
create_user	字符串（50 位）	创建人用户名
create_time	datetime	创建时间
5.1.2 项目任务表（project_task）

字段名	数据类型	说明
task_id	字符串（32 位）	任务唯一 ID（主键）
name	字符串（100 位）	任务名称
type	字符串（20 位）	任务类型（预设值）
description	文本	任务描述
负责人	字符串（50 位）	关联用户表 user_name
deadline	datetime	截止时间
status	字符串（20 位）	任务状态
6. 约束与假设
6.1 约束条件
20.	不得修改现有考试系统的核心模块代码（如考生答题、评分管理）；
21.	文件存储总量不超过服务器可用磁盘空间的 50%（预留考试数据存储）。
6.2 假设与依赖
22.	现有系统的 “用户管理模块”“成果文件管理模块” 接口稳定，可正常调用；
23.	局域网网络环境稳定（带宽≥100Mbps），支持大文件传输。
7. 交付物

交付物名称	说明
工具模块部署包	包含前端页面、后端服务代码
数据库脚本	新增数据表的创建脚本
接口调用文档	工具模块对外提供的接口说明（如任务创建接口）
用户操作手册	针对管理员、专家、考生的功能使用说明
版本号：V1.0
编制日期：2025 年 8 月 5 日
编制人：技术部
（注：文档部分内容
