{% extends "base.html" %}

{% block title %}错误 {{ error_code }} - 实操任务管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="text-center mt-5">
                <!-- 错误图标 -->
                <div class="error-icon mb-4">
                    {% if error_code == 404 %}
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 5rem;"></i>
                    {% elif error_code == 500 %}
                        <i class="bi bi-x-circle text-danger" style="font-size: 5rem;"></i>
                    {% else %}
                        <i class="bi bi-question-circle text-secondary" style="font-size: 5rem;"></i>
                    {% endif %}
                </div>

                <!-- 错误信息 -->
                <h1 class="display-1 fw-bold text-muted">{{ error_code }}</h1>
                <h2 class="mb-3">
                    {% if error_code == 404 %}
                        页面未找到
                    {% elif error_code == 500 %}
                        服务器内部错误
                    {% else %}
                        发生错误
                    {% endif %}
                </h2>
                <p class="lead text-muted mb-4">{{ error_msg }}</p>

                <!-- 错误详细说明 -->
                <div class="alert alert-light border" role="alert">
                    {% if error_code == 404 %}
                        <p class="mb-2"><strong>可能的原因：</strong></p>
                        <ul class="text-start mb-0">
                            <li>您访问的页面不存在或已被删除</li>
                            <li>URL地址输入错误</li>
                            <li>页面链接已过期</li>
                        </ul>
                    {% elif error_code == 500 %}
                        <p class="mb-2"><strong>可能的原因：</strong></p>
                        <ul class="text-start mb-0">
                            <li>服务器遇到了意外错误</li>
                            <li>数据库连接问题</li>
                            <li>系统正在维护中</li>
                        </ul>
                    {% else %}
                        <p class="mb-0">系统遇到了未知错误，请稍后重试。</p>
                    {% endif %}
                </div>

                <!-- 操作按钮 -->
                <div class="d-flex justify-content-center gap-3 mt-4">
                    <button type="button" class="btn btn-outline-primary" onclick="history.back()">
                        <i class="bi bi-arrow-left me-2"></i>返回上页
                    </button>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="bi bi-house me-2"></i>返回首页
                    </a>
                    <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise me-2"></i>刷新页面
                    </button>
                </div>

                <!-- 联系信息 -->
                <div class="mt-5 pt-4 border-top">
                    <p class="text-muted small mb-2">如果问题持续存在，请联系系统管理员</p>
                    <div class="d-flex justify-content-center gap-4 text-muted small">
                        <span><i class="bi bi-envelope me-1"></i><EMAIL></span>
                        <span><i class="bi bi-telephone me-1"></i>************</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 错误页面样式 -->
<style>
.error-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.display-1 {
    font-size: 6rem;
    line-height: 1;
}

@media (max-width: 768px) {
    .display-1 {
        font-size: 4rem;
    }
    
    .error-icon i {
        font-size: 3rem !important;
    }
}
</style>

<!-- 错误页面脚本 -->
<script>
// 自动记录错误信息（用于调试）
console.error('Error Page:', {
    code: {{ error_code }},
    message: '{{ error_msg }}',
    url: window.location.href,
    timestamp: new Date().toISOString()
});

// 如果是404错误，尝试提供相似页面建议
{% if error_code == 404 %}
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const suggestions = {
        '/task': '/tasks',
        '/category': '/categories',
        '/material': '/materials',
        '/execution': '/executions',
        '/admin': '/'
    };
    
    // 查找相似路径
    for (const [pattern, suggestion] of Object.entries(suggestions)) {
        if (currentPath.includes(pattern)) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-info mt-3';
            alertDiv.innerHTML = `
                <i class="bi bi-lightbulb me-2"></i>
                您是否要访问：<a href="${suggestion}" class="alert-link">${suggestion}</a>？
            `;
            
            const container = document.querySelector('.alert.alert-light');
            container.parentNode.insertBefore(alertDiv, container.nextSibling);
            break;
        }
    }
});
{% endif %}
</script>
{% endblock %}