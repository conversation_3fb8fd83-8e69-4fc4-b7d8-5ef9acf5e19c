{% extends "base.html" %}

{% block title %}分类管理 - 实操任务管理系统{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">分类管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-tags"></i> 分类管理
            </h1>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#categoryModal">
                <i class="bi bi-plus-circle"></i> 创建新分类
            </button>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.args.get('search', '') }}" placeholder="分类名称或描述">
                    </div>
                    <div class="col-md-3">
                        <label for="software_type" class="form-label">软件类型</label>
                        <select class="form-select" id="software_type" name="software_type">
                            <option value="">全部类型</option>
                            <option value="Word" {{ 'selected' if request.args.get('software_type') == 'Word' }}>Word</option>
                            <option value="Excel" {{ 'selected' if request.args.get('software_type') == 'Excel' }}>Excel</option>
                            <option value="PowerPoint" {{ 'selected' if request.args.get('software_type') == 'PowerPoint' }}>PowerPoint</option>
                            <option value="Photoshop" {{ 'selected' if request.args.get('software_type') == 'Photoshop' }}>Photoshop</option>
                            <option value="AutoCAD" {{ 'selected' if request.args.get('software_type') == 'AutoCAD' }}>AutoCAD</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="difficulty_level" class="form-label">难度等级</label>
                        <select class="form-select" id="difficulty_level" name="difficulty_level">
                            <option value="">全部难度</option>
                            <option value="初级" {{ 'selected' if request.args.get('difficulty_level') == '初级' }}>初级</option>
                            <option value="中级" {{ 'selected' if request.args.get('difficulty_level') == '中级' }}>中级</option>
                            <option value="高级" {{ 'selected' if request.args.get('difficulty_level') == '高级' }}>高级</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                            </button>
                            <a href="{{ url_for('web.categories') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 分类列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">分类列表 (共 {{ categories|length }} 个)</h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="expandAll()">
                        <i class="bi bi-arrows-expand"></i> 展开全部
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="collapseAll()">
                        <i class="bi bi-arrows-collapse"></i> 收起全部
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                {% if categories %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50">#</th>
                                <th>分类名称</th>
                                <th>软件类型</th>
                                <th>难度等级</th>
                                <th>任务数量</th>
                                <th>创建时间</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in categories %}
                            <tr>
                                <td>{{ category.id }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <button class="btn btn-sm btn-outline-secondary me-2" 
                                                onclick="toggleDescription({{ category.id }})" 
                                                id="toggleBtn{{ category.id }}">
                                            <i class="bi bi-chevron-right"></i>
                                        </button>
                                        <div>
                                            <strong>{{ category.name }}</strong>
                                            <div class="collapse" id="description{{ category.id }}">
                                                <small class="text-muted">{{ category.description or '暂无描述' }}</small>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if category.software_type %}
                                    <span class="badge bg-info">{{ category.software_type }}</span>
                                    {% else %}
                                    <span class="text-muted">通用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if category.difficulty_level %}
                                    <span class="badge bg-{{ 'success' if category.difficulty_level == '初级' else 'warning' if category.difficulty_level == '中级' else 'danger' }}">
                                        {{ category.difficulty_level }}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">通用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ category.task_count or 0 }}</span>
                                </td>
                                <td>{{ category.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="editCategory({{ category.id }})" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteCategory({{ category.id }}, '{{ category.name }}', {{ category.task_count or 0 }})" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-tags display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无分类</h4>
                    <p class="text-muted">点击上方按钮创建第一个分类</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 分类创建/编辑模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryModalTitle">
                    <i class="bi bi-plus-circle"></i> 创建新分类
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <input type="hidden" id="categoryId" name="category_id">
                    
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">分类名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="categoryName" name="name" required maxlength="100">
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">分类描述</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3" maxlength="500"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="categorySoftwareType" class="form-label">软件类型</label>
                                <select class="form-select" id="categorySoftwareType" name="software_type">
                                    <option value="">通用</option>
                                    <option value="Word">Word</option>
                                    <option value="Excel">Excel</option>
                                    <option value="PowerPoint">PowerPoint</option>
                                    <option value="Photoshop">Photoshop</option>
                                    <option value="AutoCAD">AutoCAD</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="categoryDifficultyLevel" class="form-label">难度等级</label>
                                <select class="form-select" id="categoryDifficultyLevel" name="difficulty_level">
                                    <option value="">通用</option>
                                    <option value="初级">初级</option>
                                    <option value="中级">中级</option>
                                    <option value="高级">高级</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="categoryTags" class="form-label">标签</label>
                        <input type="text" class="form-control" id="categoryTags" name="tags" maxlength="200">
                        <div class="form-text">多个标签用逗号分隔</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">
                    <i class="bi bi-check-circle"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 切换描述显示
function toggleDescription(categoryId) {
    const description = document.getElementById(`description${categoryId}`);
    const toggleBtn = document.getElementById(`toggleBtn${categoryId}`);
    const icon = toggleBtn.querySelector('i');
    
    if (description.classList.contains('show')) {
        description.classList.remove('show');
        icon.className = 'bi bi-chevron-right';
    } else {
        description.classList.add('show');
        icon.className = 'bi bi-chevron-down';
    }
}

// 展开全部
function expandAll() {
    document.querySelectorAll('[id^="description"]').forEach(element => {
        element.classList.add('show');
    });
    document.querySelectorAll('[id^="toggleBtn"] i').forEach(icon => {
        icon.className = 'bi bi-chevron-down';
    });
}

// 收起全部
function collapseAll() {
    document.querySelectorAll('[id^="description"]').forEach(element => {
        element.classList.remove('show');
    });
    document.querySelectorAll('[id^="toggleBtn"] i').forEach(icon => {
        icon.className = 'bi bi-chevron-right';
    });
}

// 编辑分类
function editCategory(categoryId) {
    fetch(`/api/v1/categories/${categoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                const category = data.data;
                
                document.getElementById('categoryId').value = category.id;
                document.getElementById('categoryName').value = category.name;
                document.getElementById('categoryDescription').value = category.description || '';
                document.getElementById('categorySoftwareType').value = category.software_type || '';
                document.getElementById('categoryDifficultyLevel').value = category.difficulty_level || '';
                document.getElementById('categoryTags').value = category.tags || '';
                
                document.getElementById('categoryModalTitle').innerHTML = '<i class="bi bi-pencil"></i> 编辑分类';
                
                const modal = new bootstrap.Modal(document.getElementById('categoryModal'));
                modal.show();
            } else {
                alert('获取分类信息失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('获取分类信息失败: ' + error.message);
        });
}

// 保存分类
function saveCategory() {
    const form = document.getElementById('categoryForm');
    const formData = new FormData(form);
    const categoryId = document.getElementById('categoryId').value;
    
    const data = {
        name: formData.get('name'),
        description: formData.get('description'),
        software_type: formData.get('software_type'),
        difficulty_level: formData.get('difficulty_level'),
        tags: formData.get('tags')
    };
    
    const url = categoryId ? `/api/v1/categories/${categoryId}` : '/api/v1/categories';
    const method = categoryId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            location.reload();
        } else {
            alert('保存失败: ' + data.msg);
        }
    })
    .catch(error => {
        alert('保存失败: ' + error.message);
    });
}

// 删除分类
function deleteCategory(categoryId, categoryName, taskCount) {
    if (taskCount > 0) {
        alert(`分类 "${categoryName}" 下还有 ${taskCount} 个任务，无法删除。请先删除或移动相关任务。`);
        return;
    }
    
    if (confirm(`确定要删除分类 "${categoryName}" 吗？此操作不可撤销。`)) {
        fetch(`/api/v1/categories/${categoryId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                location.reload();
            } else {
                alert('删除失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('删除失败: ' + error.message);
        });
    }
}

// 重置模态框
document.getElementById('categoryModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('categoryForm').reset();
    document.getElementById('categoryId').value = '';
    document.getElementById('categoryModalTitle').innerHTML = '<i class="bi bi-plus-circle"></i> 创建新分类';
});
</script>
{% endblock %}