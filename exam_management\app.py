# -*- coding: utf-8 -*-
"""
考试编排模块 - 主应用，提供考试管理的完整功能，包括考试创建、编辑、状态管理、参与者管理等
"""

from flask import Flask, request, jsonify, render_template_string
from datetime import datetime, timezone
import json
import os
from models import DatabaseManager, ExamModel, ParticipantModel, ExamLogModel, ExamStatus, ExamType, ParticipantRole
from notification_model import NotificationModel, NotificationStatus, NotificationType
from utils import (
    validate_exam_data, validate_participant_data, format_datetime,
    get_exam_status_display, get_exam_type_display, get_participant_role_display,
    create_api_response, handle_exception, paginate_results, safe_print,
    is_exam_time_valid, validate_exam_time_detailed, calculate_exam_duration, sanitize_input,
    is_valid_status_transition, generate_seat_number,
    get_role_display, clean_input_data, generate_token, hash_password,
    verify_password, require_auth, log_action, log_exam_operation,
    call_question_bank_service, call_practical_task_service,
    validate_question_bank_ids, validate_practical_task_ids,
    get_user_permissions
)
from flask_cors import CORS

# 导入新的安全和验证模块
from validators import DataValidator, ValidationError
from security import SecurityManager, PermissionManager, AuditLogger, require_permission, audit_log, sql_injection_check
from logger import ExamLogger, PerformanceMonitor, log_errors
from exception_handler import ExamException, DatabaseException, ValidationException, ErrorRecoveryManager, retry_on_exception, handle_exceptions
from transaction_manager import TransactionManager, transaction, transactional

# 创建Flask应用实例
app = Flask(__name__)
CORS(app)

# 初始化数据库
db_manager = DatabaseManager()
transaction_manager = TransactionManager(db_manager.db_path)
exam_model = ExamModel(db_manager, transaction_manager)
participant_model = ParticipantModel(db_manager, transaction_manager)
log_model = ExamLogModel(db_manager, transaction_manager)
notification_model = NotificationModel(db_manager, transaction_manager)

# 初始化新的安全和管理模块
data_validator = DataValidator()
security_manager = SecurityManager()
permission_manager = PermissionManager()
audit_logger = AuditLogger()
exam_logger = ExamLogger()
performance_monitor = PerformanceMonitor(exam_logger)
error_recovery_manager = ErrorRecoveryManager()

def get_utc_now_iso():
    return datetime.now(timezone.utc).isoformat()

@app.route('/health', methods=['GET'])
@audit_log('health_check', 'system')
@log_action('health_check')
@handle_exceptions
@performance_monitor.monitor
def health_check():
    """健康检查接口"""
    try:
        # 测试数据库连接
        with db_manager.get_connection() as conn:
            conn.execute("SELECT 1")
        
        # 记录日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info(f"健康检查通过")
        audit_logger.log_action(operator_id, 'health_check', '健康检查', {'status': 'healthy', 'database': 'connected'})
        
        return jsonify(create_api_response(200, "服务正常", {
            "service": "exam_management",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "database": "connected"
        }))
    except Exception as e:
        return jsonify(handle_exception(e))

@app.route('/', methods=['GET'])
@audit_log('root_access', 'system')
@log_action('root_access')
@handle_exceptions
@performance_monitor.monitor
def root():
    """根路径接口"""
    # 记录日志
    operator_id = request.headers.get('X-User-ID', 'system')
    exam_logger.log_info(f"用户访问考试编排模块根路径")
    audit_logger.log_action(operator_id, 'root', '访问考试编排模块根路径', {'service': 'exam_management'})
    
    return jsonify(create_api_response(200, "考试编排模块", {
        "service": "exam_management",
        "version": "3.0",
        "description": "考试编排模块 - 提供完整的考试管理功能",
        "features": [
            "考试创建和编辑",
            "考试状态管理",
            "参与者管理",
            "时间冲突检测",
            "考试结果统计",
            "Web管理界面"
        ],
        "endpoints": [
            "GET /health - 健康检查",
            "GET /web - Web管理界面",
            "GET /api/v1/exams - 获取考试列表",
            "POST /api/v1/exams - 创建考试",
            "GET /api/v1/exams/{id} - 获取考试详情",
            "PUT /api/v1/exams/{id} - 更新考试",
            "DELETE /api/v1/exams/{id} - 删除考试",
            "POST /api/v1/exams/{id}/participants - 添加参与者",
            "GET /api/v1/exams/{id}/participants - 获取参与者列表",
            "DELETE /api/v1/exams/{id}/participants/{user_id} - 移除参与者",
            "GET /api/v1/exams/{id}/statistics - 获取考试统计",
            "POST /api/v1/exams/{id}/status - 更新考试状态"
        ]
    }))

@app.route('/api/v1/exams', methods=['GET'])
@require_permission('exam:view')
def get_exams():
    """获取考试列表"""
    try:
        # 获取查询参数
        status = request.args.get('status')
        exam_type = request.args.get('exam_type')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        
        # 构建过滤条件
        filters = {}
        if status:
            filters['status'] = status
        if exam_type:
            filters['exam_type'] = exam_type
        if start_date:
            filters['start_date'] = start_date
        if end_date:
            filters['end_date'] = end_date
        
        # 获取考试列表
        exams = exam_model.get_exams(filters)
        
        # 格式化显示数据
        for exam in exams:
            exam['status_display'] = get_exam_status_display(exam['status'])
            exam['exam_type_display'] = get_exam_type_display(exam['exam_type'])
            if exam['start_time']:
                exam['start_time_display'] = format_datetime(exam['start_time'])
            if exam['end_time']:
                exam['end_time_display'] = format_datetime(exam['end_time'])
        
        # 分页处理
        paginated_result = paginate_results(exams, page, per_page)
        
        return jsonify(create_api_response(200, "获取成功", paginated_result))
        
    except Exception as e:
        return jsonify(handle_exception(e))

@app.route('/api/v1/exams/<int:exam_id>', methods=['GET'])
@require_permission('exam:view')
def get_exam(exam_id):
    """获取考试详情"""
    try:
        exam = exam_model.get_exam_by_id(exam_id)
        if not exam:
            return jsonify(create_api_response(404, "考试不存在")), 404
        
        # 格式化显示数据
        exam['status_display'] = get_exam_status_display(exam['status'])
        exam['exam_type_display'] = get_exam_type_display(exam['exam_type'])
        if exam['start_time']:
            exam['start_time_display'] = format_datetime(exam['start_time'])
        if exam['end_time']:
            exam['end_time_display'] = format_datetime(exam['end_time'])
        if exam['created_at']:
            exam['created_at_display'] = format_datetime(exam['created_at'])
        if exam['updated_at']:
            exam['updated_at_display'] = format_datetime(exam['updated_at'])
        
        # 获取参与者信息
        participants = participant_model.get_exam_participants(exam_id)
        exam['participants'] = participants
        exam['participant_count'] = len(participants)
        
        # 计算考试时长
        if exam['start_time'] and exam['end_time']:
            exam['calculated_duration'] = calculate_exam_duration(exam['start_time'], exam['end_time'])
        
        return jsonify(create_api_response(200, "获取成功", exam))
        
    except Exception as e:
        return jsonify(handle_exception(e))

@app.route('/api/v1/exams', methods=['POST'])
@require_permission('exam:create')
@audit_log('exam_create', 'exam')
@log_operation('create_exam')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def create_exam():
    """创建考试"""
    try:
        with transaction(transaction_manager):
            data = request.get_json()
            
            # 使用新的数据验证器进行全面验证
            try:
                # 必填字段验证
                data_validator.validate_required(data, 'name', '考试名称')
                data_validator.validate_required(data, 'exam_type', '考试类型')
                data_validator.validate_required(data, 'start_time', '开始时间')
                data_validator.validate_required(data, 'end_time', '结束时间')
                
                # 字符串长度验证
                data_validator.validate_string_length(data.get('name', ''), 1, 200, '考试名称')
                if data.get('description'):
                    data_validator.validate_string_length(data['description'], 0, 1000, '考试描述')
                
                # 时间格式验证
                data_validator.validate_datetime(data['start_time'], '开始时间')
                data_validator.validate_datetime(data['end_time'], '结束时间')
                
                # 整数验证
                if data.get('duration'):
                    data_validator.validate_integer(data['duration'], 1, 1440, '考试时长(分钟)')
                if data.get('max_participants'):
                    data_validator.validate_integer(data['max_participants'], 1, 10000, '最大参与人数')
                    
            except ValidationError as e:
                exam_logger.log_error('validation_error', str(e), {'data': data})
                return jsonify(create_api_response(400, str(e))), 400
        
            # 使用安全管理器清理输入数据
            cleaned_data = security_manager.sanitize_input(data)
            
            # 时间验证
            is_valid, time_errors = validate_exam_time_detailed(cleaned_data['start_time'], cleaned_data['end_time'])
            if not is_valid:
                exam_logger.log_warning('time_validation_failed', '考试时间验证失败', {
                    'errors': time_errors,
                    'start_time': cleaned_data['start_time'],
                    'end_time': cleaned_data['end_time']
                })
                return jsonify(create_api_response(400, "考试时间设置无效", {
                    "errors": time_errors
                })), 400
        
            # 验证题库ID（如果提供）
            if 'question_bank_ids' in cleaned_data and cleaned_data['question_bank_ids']:
                is_valid, error_msg = validate_question_bank_ids(cleaned_data['question_bank_ids'])
                if not is_valid:
                    exam_logger.log_error('question_bank_validation_failed', error_msg, {
                        'question_bank_ids': cleaned_data['question_bank_ids']
                    })
                    return jsonify(create_api_response(400, f"题库验证失败: {error_msg}")), 400
            
            # 验证实操任务ID（如果提供）
            if 'practical_task_ids' in cleaned_data and cleaned_data['practical_task_ids']:
                is_valid, error_msg = validate_practical_task_ids(cleaned_data['practical_task_ids'])
                if not is_valid:
                    exam_logger.log_error('practical_task_validation_failed', error_msg, {
                        'practical_task_ids': cleaned_data['practical_task_ids']
                    })
                    return jsonify(create_api_response(400, f"实操任务验证失败: {error_msg}")), 400
        
            # 检查时间冲突
            conflicts = exam_model.check_time_conflict(
                cleaned_data['start_time'], 
                cleaned_data['end_time']
            )
            if conflicts:
                conflict_names = [conflict['name'] for conflict in conflicts]
                exam_logger.log_warning('time_conflict_detected', '考试时间冲突', {
                    'conflicts': conflict_names,
                    'start_time': cleaned_data['start_time'],
                    'end_time': cleaned_data['end_time']
                })
                return jsonify(create_api_response(
                    400, 
                    f"时间与以下考试冲突: {', '.join(conflict_names)}"
                )), 400
            
            # 创建考试
            exam_id = exam_model.create_exam(cleaned_data)
        
            # 记录操作日志（使用新的日志系统）
            exam_logger.log_info('exam_created', '考试创建成功', {
                'exam_id': exam_id,
                'exam_name': cleaned_data['name'],
                'exam_type': cleaned_data['exam_type'],
                'start_time': cleaned_data['start_time'],
                'end_time': cleaned_data['end_time']
            })
            
            # 记录审计日志
            audit_logger.log_action(
                user_id=request.headers.get('X-User-ID', 'system'),
                action='create_exam',
                resource_type='exam',
                resource_id=str(exam_id),
                details={
                    'exam_name': cleaned_data['name'],
                    'exam_type': cleaned_data['exam_type']
                }
            )
            
            # 传统日志记录（保持兼容性）
            log_model.log_operation(
                exam_id=exam_id,
                operation='create',
                operator=request.headers.get('X-User-ID', 'system'),
                details=f"创建考试: {cleaned_data['name']}"
            )
            
            # 获取创建的考试详情
            exam = exam_model.get_exam_by_id(exam_id)
            
            return jsonify(create_api_response(201, "考试创建成功", exam)), 201

    except Exception as e:
        exam_logger.log_error('create_exam_error', '创建考试失败', {'error': str(e)})
        return jsonify(create_api_response(500, f"创建考试失败: {str(e)}", {})), 500

@app.route('/api/v1/exams/<int:exam_id>', methods=['PUT'])
@require_permission('exam:edit')
@audit_log('exam_update', 'exam')
@log_operation('update_exam')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def update_exam(exam_id):
    """更新考试"""
    try:
        with transaction(transaction_manager):
            # 检查考试是否存在
            existing_exam = exam_model.get_exam_by_id(exam_id)
            if not existing_exam:
                exam_logger.log_warning('exam_not_found', '尝试更新不存在的考试', {'exam_id': exam_id})
                return jsonify(create_api_response(404, "考试不存在")), 404
            
            data = request.get_json()
            
            # 使用新的数据验证器进行验证
            try:
                # 验证更新的字段
                if 'name' in data:
                    data_validator.validate_string_length(data['name'], 1, 200, '考试名称')
                if 'description' in data:
                    data_validator.validate_string_length(data['description'], 0, 1000, '考试描述')
                if 'start_time' in data:
                    data_validator.validate_datetime(data['start_time'], '开始时间')
                if 'end_time' in data:
                    data_validator.validate_datetime(data['end_time'], '结束时间')
                if 'duration' in data:
                    data_validator.validate_integer(data['duration'], 1, 1440, '考试时长(分钟)')
                if 'max_participants' in data:
                    data_validator.validate_integer(data['max_participants'], 1, 10000, '最大参与人数')
                    
            except ValidationError as e:
                exam_logger.log_error('validation_error', str(e), {'exam_id': exam_id, 'data': data})
                return jsonify(create_api_response(400, str(e))), 400
            
            # 使用安全管理器清理输入数据
            cleaned_data = security_manager.sanitize_input(data)
            
            # 状态转换验证
            if 'status' in cleaned_data:
                current_status = existing_exam['status']
                new_status = cleaned_data['status']
                if not is_valid_status_transition(current_status, new_status):
                    return jsonify(create_api_response(
                        400, 
                        f"无法从状态'{current_status}' 转换到'{new_status}'"
                    )), 400
            
            # 时间验证（如果更新了时间）
            if 'start_time' in cleaned_data or 'end_time' in cleaned_data:
                start_time = cleaned_data.get('start_time', existing_exam['start_time'])
                end_time = cleaned_data.get('end_time', existing_exam['end_time'])
                
                is_valid, time_errors = validate_exam_time_detailed(start_time, end_time)
                if not is_valid:
                    return jsonify(create_api_response(400, "考试时间设置无效", {
                        "errors": time_errors
                    })), 400
                
                # 检查时间冲突（排除当前考试）
                conflicts = exam_model.check_time_conflict(start_time, end_time, exclude_exam_id=exam_id)
                if conflicts:
                    conflict_names = [conflict['name'] for conflict in conflicts]
                    return jsonify(create_api_response(
                        400, 
                        f"时间与以下考试冲突: {', '.join(conflict_names)}"
                    )), 400
            
            # 验证题库ID（如果提供）
            if 'question_bank_ids' in cleaned_data and cleaned_data['question_bank_ids']:
                is_valid, error_msg = validate_question_bank_ids(cleaned_data['question_bank_ids'])
                if not is_valid:
                    return jsonify(create_api_response(400, f"题库验证失败: {error_msg}")), 400
            
            # 验证实操任务ID（如果提供）
            if 'practical_task_ids' in cleaned_data and cleaned_data['practical_task_ids']:
                is_valid, error_msg = validate_practical_task_ids(cleaned_data['practical_task_ids'])
                if not is_valid:
                    return jsonify(create_api_response(400, f"实操任务验证失败: {error_msg}")), 400
            
            # 更新考试
            success = exam_model.update_exam(exam_id, cleaned_data)
            if not success:
                return jsonify(create_api_response(500, "更新考试失败")), 500
            
            # 记录操作日志
            log_model.log_operation(
                exam_id=exam_id,
                operation='update',
                operator='system',  # 实际应用中应从认证信息获取
                details=f"更新考试: {existing_exam['name']}"
            )
            
            # 获取更新后的考试详情
            updated_exam = exam_model.get_exam_by_id(exam_id)
            
            return jsonify(create_api_response(200, "考试更新成功", updated_exam))
        
    except Exception as e:
        return jsonify(handle_exception(e))

@app.route('/api/v1/exams/<int:exam_id>', methods=['DELETE'])
@require_permission('exam:delete')
@audit_log('exam_delete', 'exam')
@log_operation('delete_exam')
@handle_exceptions
@performance_monitor.monitor
def delete_exam(exam_id):
    """删除考试"""
    try:
        with transaction(transaction_manager):
            # 检查考试是否存在
            existing_exam = exam_model.get_exam_by_id(exam_id)
            if not existing_exam:
                exam_logger.log_warning('exam_not_found', '尝试删除不存在的考试', {'exam_id': exam_id})
                return jsonify(create_api_response(404, "考试不存在")), 404
            
            # 检查考试状态，只有草稿状态的考试可以删除
            if existing_exam['status'] not in ['draft']:
                exam_logger.log_warning('invalid_delete_status', '尝试删除非草稿状态的考试', {
                    'exam_id': exam_id,
                    'current_status': existing_exam['status']
                })
                return jsonify(create_api_response(
                    400, 
                    f"无法删除状态为 '{existing_exam['status']}' 的考试，只能删除草稿状态的考试"
                )), 400
            
            # 删除考试
            success = exam_model.delete_exam(exam_id)
            if not success:
                return jsonify(create_api_response(500, "删除考试失败")), 500
            
            # 记录操作日志
            log_model.log_operation(
                exam_id=exam_id,
                operation='delete',
                operator='system',  # 实际应用中应从认证信息获取
                details=f"删除考试: {existing_exam['name']}"
            )
            
            return jsonify(create_api_response(200, "考试删除成功"))
            
    except Exception as e:
        return jsonify(handle_exception(e))

# 参与者管理接口
@app.route('/api/v1/exams/<int:exam_id>/participants', methods=['GET'])
@require_permission('participant:view')
@audit_log('get_participants', 'exam')
@log_operation('get_exam_participants')
@handle_exceptions
@performance_monitor.monitor
def get_exam_participants(exam_id):
    """获取考试参与者列表"""
    # 检查考试是否存在
    exam = exam_model.get_exam_by_id(exam_id)
    if not exam:
        exam_logger.log_warning('exam_not_found', '尝试获取不存在考试的参与者', {'exam_id': exam_id})
        return jsonify(create_api_response(404, "考试不存在")), 404
    
    # 获取参与者列表
    participants = participant_model.get_exam_participants(exam_id)
    
    # 格式化参与者数据
    for participant in participants:
        participant['role_display'] = get_participant_role_display(participant['role'])
        if participant['joined_at']:
            participant['joined_at_display'] = format_datetime(participant['joined_at'])
    
    exam_logger.log_info('participants_retrieved', '成功获取考试参与者列表', {
        'exam_id': exam_id,
        'participant_count': len(participants)
    })
    
    return jsonify(create_api_response(200, "获取成功", {
        'participants': participants,
        'total_count': len(participants)
    }))

@app.route('/api/v1/exams/<int:exam_id>/participants', methods=['POST'])
@require_permission('participant:add')
@audit_log('add_participant', 'exam')
@log_operation('add_exam_participant')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def add_exam_participant(exam_id):
    """添加考试参与者"""
    try:
        with transaction(transaction_manager):
            # 检查考试是否存在
            exam = exam_model.get_exam_by_id(exam_id)
            if not exam:
                exam_logger.log_warning('exam_not_found', '尝试向不存在的考试添加参与者', {'exam_id': exam_id})
                return jsonify(create_api_response(404, "考试不存在")), 404
            
            data = request.get_json()

            # 使用新的数据验证器进行验证
            try:
                data_validator.validate_required(data, 'user_id', '用户ID')
                data_validator.validate_string_length(data['user_id'], 1, 50, '用户ID')
                
                if 'user_name' in data:
                    data_validator.validate_string_length(data['user_name'], 1, 100, '用户姓名')
                if 'role' in data:
                    data_validator.validate_string_length(data['role'], 1, 20, '角色')
                    
            except ValidationError as e:
                exam_logger.log_error('validation_error', str(e), {'exam_id': exam_id, 'data': data})
                return jsonify(create_api_response(400, str(e))), 400

            # 使用安全管理器清理输入数据
            cleaned_data = security_manager.sanitize_input(data)
            
            # 检查参与者是否已存在
            existing_participant = participant_model.get_participant_by_user_id(
                exam_id, cleaned_data['user_id']
            )
            if existing_participant:
                exam_logger.log_warning('duplicate_participant', '尝试添加已存在的参与者', {
                    'exam_id': exam_id,
                    'user_id': cleaned_data['user_id']
                })
                return jsonify(create_api_response(400, "该用户已是考试参与者")), 400
    
            # 生成座位号
            seat_number = generate_seat_number(exam_id)
            cleaned_data['seat_number'] = seat_number
            
            # 添加参与者
            participant_id = participant_model.add_participant(exam_id, cleaned_data)
            
            # 记录新的日志和审计
            exam_logger.log_info('participant_added', '成功添加考试参与者', {
                'exam_id': exam_id,
                'participant_id': participant_id,
                'user_id': cleaned_data['user_id'],
                'seat_number': seat_number
            })
            
            # 记录审计日志
            operator_id = request.headers.get('X-User-ID', 'system')
            audit_logger.log_action(
                user_id=operator_id,
                action='add_participant',
                resource_type='exam_participant',
                resource_id=str(participant_id),
                details=f"添加参与者 {cleaned_data.get('user_name', cleaned_data['user_id'])} 到考试 {exam_id}"
            )
            
            # 保持兼容性的操作日志
            log_model.log_operation(
                exam_id=exam_id,
                operation='add_participant',
                operator=operator_id,
                details=f"添加参与者 {cleaned_data.get('user_name', cleaned_data['user_id'])}"
            )
            
            # 获取添加的参与者详情
            participant = participant_model.get_participant_by_id(participant_id)
            
            return jsonify(create_api_response(201, "参与者添加成功", participant)), 201
        
    except Exception as e:
        exam_logger.log_error('add_participant_error', '添加参与者失败', {
            'exam_id': exam_id,
            'error': str(e)
        })
        return jsonify(create_api_response(500, f"添加参与者失败: {str(e)}", {})), 500

@app.route('/api/v1/exams/<int:exam_id>/participants/<int:participant_id>', methods=['DELETE'])
@require_permission('participant:remove')
@audit_log('remove_participant', 'exam')
@log_operation('remove_exam_participant')
@handle_exceptions
@performance_monitor.monitor
def remove_exam_participant(exam_id, participant_id):
    """移除考试参与者"""
    with transaction(transaction_manager):
        # 检查考试是否存在
        exam = exam_model.get_exam_by_id(exam_id)
        if not exam:
            exam_logger.log_warning('exam_not_found', '尝试从不存在的考试移除参与者', {'exam_id': exam_id})
            return jsonify(create_api_response(404, "考试不存在")), 404
        
        # 检查参与者是否存在
        participant = participant_model.get_participant_by_id(participant_id)
        if not participant or participant['exam_id'] != exam_id:
            exam_logger.log_warning('participant_not_found', '尝试移除不存在的参与者', {
                'exam_id': exam_id,
                'participant_id': participant_id
            })
            return jsonify(create_api_response(404, "参与者不存在")), 404

        # 检查考试状态，进行中的考试不能移除参与者
        if exam['status'] in ['in_progress']:
            exam_logger.log_warning('invalid_remove_status', '尝试从进行中的考试移除参与者', {
                'exam_id': exam_id,
                'participant_id': participant_id,
                'exam_status': exam['status']
            })
            return jsonify(create_api_response(
                400, 
                "进行中的考试不能移除参与者"
            )), 400
        
        # 移除参与者
        success = participant_model.remove_participant(participant_id)
        if not success:
            exam_logger.log_error('remove_participant_failed', '移除参与者失败', {
                'exam_id': exam_id,
                'participant_id': participant_id
            })
            return jsonify(create_api_response(500, "移除参与者失败")), 500
        
        # 记录成功日志
        exam_logger.log_info('participant_removed', '参与者移除成功', {
            'exam_id': exam_id,
            'participant_id': participant_id,
            'user_name': participant.get('user_name', participant['user_id'])
        })
        
        # 记录审计日志
        operator_id = request.headers.get('X-User-ID', 'system')
        audit_logger.log_action(
            operator_id,
            'remove_participant',
            f"移除考试{exam_id}的参与者{participant.get('user_name', participant['user_id'])}",
            {'exam_id': exam_id, 'participant_id': participant_id}
        )
        
        # 记录操作日志（保持兼容性）
        log_model.log_operation(
            exam_id=exam_id,
            operation='remove_participant',
            operator=operator_id,
            details=f"移除参与者 {participant.get('user_name', participant['user_id'])}"
        )
        
        return jsonify(create_api_response(200, "参与者移除成功"))

@app.route('/api/v1/exams/<int:exam_id>/participants/batch', methods=['POST'])
@require_permission('participant:add')
@audit_log('batch_add_participants', 'exam')
@log_operation('batch_add_participants')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def batch_add_participants(exam_id):
        """批量添加考试参与者"""
        with transaction(transaction_manager):
            # 检查考试是否存在
            exam = exam_model.get_exam_by_id(exam_id)
            if not exam:
                exam_logger.log_warning('exam_not_found', '尝试向不存在的考试批量添加参与者', {'exam_id': exam_id})
                return jsonify(create_api_response(404, "考试不存在")), 404
            
            data = request.get_json()
            
            # 清理输入数据
            cleaned_data = security_manager.sanitize_input(data)
            participants_data = cleaned_data.get('participants', [])
            
            if not participants_data:
                exam_logger.log_warning('empty_participants_list', '批量添加参与者时列表为空', {'exam_id': exam_id})
                return jsonify(create_api_response(400, "参与者列表不能为空")), 400
            
            success_count = 0
            failed_count = 0
            errors = []
            
            for i, participant_data in enumerate(participants_data):
                try:
                    # 数据验证
                    participant_data['exam_id'] = exam_id
                    
                    # 使用新的数据验证器
                    validation_errors = []
                    
                    # 验证必填字段
                    if not data_validator.validate_required_field(participant_data.get('user_id')):
                        validation_errors.append('用户ID不能为空')
                    
                    # 验证字符串长度
                    if not data_validator.validate_string_length(participant_data.get('user_id', ''), 1, 50):
                        validation_errors.append('用户ID长度必须在1-50字符之间')
                    
                    if participant_data.get('user_name') and not data_validator.validate_string_length(participant_data.get('user_name'), 1, 100):
                        validation_errors.append('用户姓名长度必须在1-100字符之间')
                    
                    if participant_data.get('role') and not data_validator.validate_string_length(participant_data.get('role'), 1, 20):
                        validation_errors.append('角色长度必须在1-20字符之间')
                    
                    if validation_errors:
                        exam_logger.log_error('participant_validation_failed', f'第{i+1}个参与者数据验证失败', {
                            'exam_id': exam_id,
                            'participant_index': i+1,
                            'errors': validation_errors
                        })
                        errors.append(f"第{i+1}个参与者: {', '.join(validation_errors)}")
                        failed_count += 1
                        continue
                    
                    # 清理输入数据
                    cleaned_data = security_manager.sanitize_input(participant_data)
                    
                    # 检查参与者是否已存在
                    existing_participant = participant_model.get_participant_by_user_id(
                        exam_id, cleaned_data['user_id']
                    )
                    if existing_participant:
                        exam_logger.log_warning('participant_already_exists', f'第{i+1}个参与者已存在', {
                            'exam_id': exam_id,
                            'participant_index': i+1,
                            'user_id': cleaned_data['user_id']
                        })
                        errors.append(f"第{i+1}个参与者: 用户ID {cleaned_data['user_id']} 已是考试参与者")
                        failed_count += 1
                        continue
                    
                    # 生成座位号
                    seat_number = generate_seat_number(exam_id)
                    cleaned_data['seat_number'] = seat_number
                    
                    # 添加参与者
                    participant_model.add_participant(exam_id, cleaned_data)
                    success_count += 1
                    
                except Exception as e:
                    exam_logger.log_error('batch_add_participant_error', f'第{i+1}个参与者添加失败', {
                        'exam_id': exam_id,
                        'participant_index': i+1,
                        'error': str(e)
                    })
                    errors.append(f"第{i+1}个参与者: {str(e)}")
                    failed_count += 1
            
            # 记录成功日志
            exam_logger.log_info('batch_add_participants_completed', '批量添加参与者完成', {
                'exam_id': exam_id,
                'success_count': success_count,
                'failed_count': failed_count,
                'total_count': len(participants_data)
            })
            
            # 记录审计日志
            operator_id = request.headers.get('X-User-ID', 'system')
            audit_logger.log_action(
                operator_id,
                'batch_add_participants',
                f"批量添加考试{exam_id}的参与者: 成功{success_count}个, 失败{failed_count}个",
                {'exam_id': exam_id, 'success_count': success_count, 'failed_count': failed_count}
            )
            
            # 记录操作日志（保持兼容性）
            log_model.log_operation(
                exam_id=exam_id,
                operation='batch_add_participants',
                operator=operator_id,
                details=f"批量添加参与者: 成功{success_count}个, 失败{failed_count}个"
            )
            
            result = {
                'success_count': success_count,
                'failed_count': failed_count,
                'errors': errors
            }
            
            if failed_count > 0:
                return jsonify(create_api_response(207, "部分参与者添加成功", result)), 207
            else:
                return jsonify(create_api_response(200, "所有参与者添加成功", result)), 200

@app.route('/api/v1/exams/<int:exam_id>/participants/batch', methods=['DELETE'])
@require_permission('participant:remove')
@audit_log('batch_remove_participants', 'exam')
@log_operation('batch_remove_participants')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def batch_remove_participants(exam_id):
        """批量移除考试参与者"""
        with transaction(transaction_manager):
            # 检查考试是否存在
            exam = exam_model.get_exam_by_id(exam_id)
            if not exam:
                exam_logger.log_warning('exam_not_found', '尝试从不存在的考试批量移除参与者', {'exam_id': exam_id})
                return jsonify(create_api_response(404, "考试不存在")), 404
            
            # 检查考试状态，进行中的考试不能移除参与者
            if exam['status'] in ['in_progress']:
                exam_logger.log_warning('invalid_batch_remove_status', '尝试从进行中的考试批量移除参与者', {
                    'exam_id': exam_id,
                    'exam_status': exam['status']
                })
                return jsonify(create_api_response(
                    400, 
                    "进行中的考试不能移除参与者"
                )), 400
            
            data = request.get_json()
            
            # 清理输入数据
            cleaned_data = security_manager.sanitize_input(data)
            participant_ids = cleaned_data.get('participant_ids', [])
            
            if not participant_ids:
                exam_logger.log_warning('empty_participant_ids_list', '批量移除参与者时ID列表为空', {'exam_id': exam_id})
                return jsonify(create_api_response(400, "参与者ID列表不能为空")), 400
            
            success_count = 0
            failed_count = 0
            errors = []
            
            for participant_id in participant_ids:
                try:
                    # 检查参与者是否存在
                    participant = participant_model.get_participant_by_id(participant_id)
                    if not participant or participant['exam_id'] != exam_id:
                        exam_logger.log_warning('participant_not_found_in_batch', '批量移除时参与者不存在或不属于此考试', {
                            'exam_id': exam_id,
                            'participant_id': participant_id
                        })
                        errors.append(f"参与者ID {participant_id}: 不存在或不属于此考试")
                        failed_count += 1
                        continue
                    
                    # 移除参与者
                    success = participant_model.remove_participant(participant_id)
                    if success:
                        success_count += 1
                    else:
                        exam_logger.log_error('batch_remove_participant_failed', '批量移除参与者失败', {
                            'exam_id': exam_id,
                            'participant_id': participant_id
                        })
                        errors.append(f"参与者ID {participant_id}: 移除失败")
                        failed_count += 1
                        
                except Exception as e:
                    exam_logger.log_error('batch_remove_participant_exception', '批量移除参与者时发生异常', {
                        'exam_id': exam_id,
                        'participant_id': participant_id,
                        'error': str(e)
                    })
                    errors.append(f"参与者ID {participant_id}: {str(e)}")
                    failed_count += 1
            
            # 记录操作日志和审计日志
            operator_id = request.headers.get('X-User-ID', 'system')
            exam_logger.log_info('batch_remove_participants_completed', '批量移除参与者操作完成', {
                'exam_id': exam_id,
                'success_count': success_count,
                'failed_count': failed_count,
                'operator_id': operator_id
            })
            
            audit_logger.log_action(
                action='batch_remove_participants',
                resource_type='exam_participants',
                resource_id=exam_id,
                user_id=operator_id,
                details={
                    'success_count': success_count,
                    'failed_count': failed_count,
                    'participant_ids': participant_ids
                }
            )
            
            # 保持兼容性的日志记录
            log_model.log_operation(
                exam_id=exam_id,
                operation='batch_remove_participants',
                operator=operator_id,
                details=f"批量移除参与者: 成功{success_count}个, 失败{failed_count}个"
            )
            
            result = {
                'success_count': success_count,
                'failed_count': failed_count,
                'errors': errors
            }
            
            if failed_count > 0:
                return jsonify(create_api_response(207, "部分参与者移除成功", result)), 207
            else:
                return jsonify(create_api_response(200, "所有参与者移除成功", result)), 200

# 考试状态管理接口
@app.route('/api/v1/exams/<int:exam_id>/status', methods=['PUT'])
@require_permission('status:change')
@audit_log('update_exam_status', 'exam')
@log_operation('update_exam_status')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def update_exam_status(exam_id):
    """更新考试状态"""
    with transaction(transaction_manager):
        # 检查考试是否存在
        exam = exam_model.get_exam_by_id(exam_id)
        if not exam:
            exam_logger.log_warning('exam_not_found', '尝试更新不存在的考试状态', {'exam_id': exam_id})
            return jsonify(create_api_response(404, "考试不存在")), 404
        
        data = request.get_json()
        
        # 清理输入数据
        cleaned_data = security_manager.sanitize_input(data)
        new_status = cleaned_data.get('status')
        
        if not new_status:
            exam_logger.log_warning('missing_status_parameter', '更新考试状态时缺少状态参数', {'exam_id': exam_id})
            return jsonify(create_api_response(400, "缺少状态参数")), 400
            
        # 数据验证
        validation_errors = []
        if not data_validator.validate_required_field(new_status, '状态'):
            validation_errors.append('状态不能为空')
        if not data_validator.validate_string_length(new_status, 1, 20, '状态'):
            validation_errors.append('状态长度必须在1-20个字符之间')
        
        if validation_errors:
            exam_logger.log_error('status_validation_failed', '考试状态验证失败', {
                'exam_id': exam_id,
                'new_status': new_status,
                'errors': validation_errors
            })
            return jsonify(create_api_response(400, "数据验证失败", {'errors': validation_errors})), 400
            
        # 验证状态转换
        current_status = exam['status']
        if not is_valid_status_transition(current_status, new_status):
            exam_logger.log_warning('invalid_status_transition', '无效的状态转换', {
                'exam_id': exam_id,
                'current_status': current_status,
                'new_status': new_status
            })
            return jsonify(create_api_response(
                400, 
                f"无法从状态'{current_status}' 转换到'{new_status}'"
            )), 400
        
        # 更新状态
        success = exam_model.update_exam_status(exam_id, new_status)
        if not success:
            exam_logger.log_error('status_update_failed', '考试状态更新失败', {
                'exam_id': exam_id,
                'current_status': current_status,
                'new_status': new_status
            })
            return jsonify(create_api_response(500, "状态更新失败")), 500
            
        # 记录操作日志和审计日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info('exam_status_updated', '考试状态更新成功', {
            'exam_id': exam_id,
            'current_status': current_status,
            'new_status': new_status,
            'operator_id': operator_id
        })
        
        audit_logger.log_action(
            action='update_exam_status',
            resource_type='exam',
            resource_id=exam_id,
            user_id=operator_id,
            details={
                'previous_status': current_status,
                'new_status': new_status
            }
        )
        
        # 保持兼容性的日志记录
        log_model.log_operation(
            exam_id=exam_id,
            operation='status_change',
            operator=operator_id,
            details=f"状态从 '{current_status}' 更改为'{new_status}'"
        )
        
        # 获取更新后的考试信息
        updated_exam = exam_model.get_exam_by_id(exam_id)
        
        return jsonify(create_api_response(200, "状态更新成功", updated_exam))

# 题库和实操任务关联接口
@app.route('/api/v1/exams/<int:exam_id>/question-banks', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_exam_question_banks', 'exam')
@log_operation('get_exam_question_banks')
@handle_exceptions
@performance_monitor.monitor
def get_exam_question_banks(exam_id):
    """获取考试关联的题库列表"""
    # 检查考试是否存在
    exam = exam_model.get_exam_by_id(exam_id)
    if not exam:
        exam_logger.log_warning('exam_not_found', '尝试获取不存在的考试题库', {'exam_id': exam_id})
        return jsonify(create_api_response(404, "考试不存在")), 404
        
    question_bank_ids = exam.get('question_bank_ids', [])
    
    # 如果没有关联题库，返回空列表
    if not question_bank_ids:
        exam_logger.log_info('no_question_banks', '考试没有关联题库', {'exam_id': exam_id})
        return jsonify(create_api_response(200, "获取成功", []))
    
    # 调用题库服务获取详细信息
    question_banks = []
    for bank_id in question_bank_ids:
        try:
            bank_info = call_question_bank_service(bank_id)
            question_banks.append(bank_info)
        except Exception as e:
            exam_logger.log_error('get_question_bank_error', '获取题库信息失败', {
                'exam_id': exam_id,
                'bank_id': bank_id,
                'error': str(e)
            })
            # 记录操作日志
            operator_id = request.headers.get('X-User-ID', 'system')
            log_model.log_operation(
                exam_id=exam_id,
                operation='get_question_bank_error',
                operator=operator_id,
                details=f"获取题库{bank_id}信息失败: {str(e)}"
            )
            # 添加错误信息到结果中
            question_banks.append({
                'id': bank_id,
                'name': f'题库{bank_id}',
                'status': 'error',
                'error': str(e)
            })
    
    exam_logger.log_info('question_banks_retrieved', '成功获取考试题库列表', {
        'exam_id': exam_id,
        'bank_count': len(question_banks)
    })
    
    return jsonify(create_api_response(200, "获取成功", question_banks))

@app.route('/api/v1/exams/<int:exam_id>/question-banks', methods=['PUT'])
@require_permission('exam:edit')
@audit_log('update_exam_question_banks', 'exam')
@log_operation('update_exam_question_banks')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def update_exam_question_banks(exam_id):
    """更新考试关联的题库"""
    with transaction(transaction_manager):
        # 检查考试是否存在
        exam = exam_model.get_exam_by_id(exam_id)
        if not exam:
            exam_logger.log_warning('exam_not_found', '尝试更新不存在的考试题库关联', {'exam_id': exam_id})
            return jsonify(create_api_response(404, "考试不存在")), 404
        
        # 检查考试状态，进行中的考试不能修改题库
        if exam['status'] in ['in_progress', 'completed']:
            exam_logger.log_warning('invalid_update_status', '尝试修改进行中或已完成考试的题库关联', {
                'exam_id': exam_id,
                'exam_status': exam['status']
            })
            return jsonify(create_api_response(
                400, 
                "进行中或已完成的考试不能修改题库关联"
            )), 400
                
        data = request.get_json()
        
        # 清理输入数据
        cleaned_data = security_manager.sanitize_input(data)
        question_bank_ids = cleaned_data.get('question_bank_ids', [])
        
        # 数据验证
        validation_errors = []
        if not isinstance(question_bank_ids, list):
            validation_errors.append('题库ID列表格式错误')
        else:
            for bank_id in question_bank_ids:
                if not data_validator.validate_integer(bank_id, '题库ID'):
                    validation_errors.append(f'无效的题库ID: {bank_id}')
                elif bank_id <= 0:
                    validation_errors.append(f'题库ID必须大于0: {bank_id}')
        
        if validation_errors:
            exam_logger.log_error('question_bank_validation_failed', '题库ID验证失败', {
                'exam_id': exam_id,
                'question_bank_ids': question_bank_ids,
                'errors': validation_errors
            })
            return jsonify(create_api_response(400, "数据验证失败", {'errors': validation_errors})), 400
        
        # 更新考试的题库关联
        updated_exam = exam_model.update_exam(exam_id, {
            'question_bank_ids': question_bank_ids
        })
        
        if not updated_exam:
            exam_logger.log_error('question_bank_update_failed', '考试题库关联更新失败', {
                'exam_id': exam_id,
                'question_bank_ids': question_bank_ids
            })
            return jsonify(create_api_response(500, "更新失败")), 500
        
        # 记录成功日志和审计日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info('question_bank_updated', '考试题库关联更新成功', {
            'exam_id': exam_id,
            'question_bank_ids': question_bank_ids,
            'operator_id': operator_id
        })
        
        audit_logger.log_action(
            action='update_exam_question_banks',
            resource_type='exam',
            resource_id=exam_id,
            user_id=operator_id,
            details={
                'question_bank_ids': question_bank_ids,
                'previous_question_bank_ids': exam.get('question_bank_ids', [])
            }
        )
        
        # 保持兼容性的操作日志
        log_model.log_operation(
            exam_id=exam_id,
            operation='update_question_banks',
            operator=operator_id,
            details=f"更新题库关联: {question_bank_ids}"
        )
        
        return jsonify(create_api_response(200, "更新成功", {
            'question_bank_ids': updated_exam['question_bank_ids']
        }))

@app.route('/api/v1/exams/<int:exam_id>/practical-tasks', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_exam_practical_tasks', 'exam')
@log_operation('get_exam_practical_tasks')
@handle_exceptions
@performance_monitor.monitor
def get_exam_practical_tasks(exam_id):
    """获取考试关联的实操任务列表"""
    # 检查考试是否存在
    exam = exam_model.get_exam_by_id(exam_id)
    if not exam:
        exam_logger.log_warning('exam_not_found', '尝试获取不存在的考试实操任务', {'exam_id': exam_id})
        return jsonify(create_api_response(404, "考试不存在")), 404
    
    practical_task_ids = exam.get('practical_task_ids', [])
    
    # 如果没有关联实操任务，返回空列表
    if not practical_task_ids:
        exam_logger.log_info('no_practical_tasks', '考试没有关联实操任务', {'exam_id': exam_id})
        return jsonify(create_api_response(200, "获取成功", []))
    
    # 调用实操任务服务获取详细信息
    practical_tasks = []
    for task_id in practical_task_ids:
        try:
            task_info = call_practical_task_service(task_id)
            practical_tasks.append(task_info)
        except Exception as e:
            exam_logger.log_error('practical_task_service_error', '获取实操任务信息失败', {
                'exam_id': exam_id,
                'task_id': task_id,
                'error': str(e)
            })
            
            operator_id = request.headers.get('X-User-ID', 'system')
            log_model.log_operation(
                exam_id=exam_id,
                operation='get_practical_task_error',
                operator=operator_id,
                details=f"获取实操任务{task_id}信息失败: {str(e)}"
            )
            # 添加错误信息到结果中
            practical_tasks.append({
                'id': task_id,
                'name': f'实操任务{task_id}',
                'status': 'error',
                'error': str(e)
            })
    
    exam_logger.log_info('practical_tasks_retrieved', '成功获取考试实操任务列表', {
        'exam_id': exam_id,
        'task_count': len(practical_tasks)
    })
    
    return jsonify(create_api_response(200, "获取成功", practical_tasks))

@app.route('/api/v1/exams/<int:exam_id>/practical-tasks', methods=['PUT'])
@require_permission('exam:edit')
@audit_log('update_exam_practical_tasks', 'exam')
@log_operation('update_exam_practical_tasks')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def update_exam_practical_tasks(exam_id):
    """更新考试关联的实操任务"""
    with transaction(transaction_manager):
        # 检查考试是否存在
        exam = exam_model.get_exam_by_id(exam_id)
        if not exam:
            exam_logger.log_warning('exam_not_found', '尝试更新不存在的考试实操任务关联', {'exam_id': exam_id})
            return jsonify(create_api_response(404, "考试不存在")), 404
                
        # 检查考试状态，进行中的考试不能修改实操任务
        if exam['status'] in ['in_progress', 'completed']:
            exam_logger.log_warning('invalid_update_status', '尝试修改进行中或已完成考试的实操任务关联', {
                'exam_id': exam_id,
                'exam_status': exam['status']
            })
            return jsonify(create_api_response(
                400, 
                "进行中或已完成的考试不能修改实操任务关联"
            )), 400
        
        data = request.get_json()
        
        # 清理输入数据
        cleaned_data = security_manager.sanitize_input(data)
        practical_task_ids = cleaned_data.get('practical_task_ids', [])
                
        # 数据验证
        validation_errors = []
        if not isinstance(practical_task_ids, list):
            validation_errors.append('实操任务ID列表格式错误')
        else:
            for task_id in practical_task_ids:
                if not data_validator.validate_integer(task_id, '实操任务ID'):
                    validation_errors.append(f'无效的实操任务ID: {task_id}')
                elif task_id <= 0:
                    validation_errors.append(f'实操任务ID必须大于0: {task_id}')
        
        if validation_errors:
            exam_logger.log_error('practical_task_validation_failed', '实操任务ID验证失败', {
                'exam_id': exam_id,
                'practical_task_ids': practical_task_ids,
                'errors': validation_errors
            })
            return jsonify(create_api_response(400, "数据验证失败", {'errors': validation_errors})), 400
                
        # 更新考试的实操任务关联
        updated_exam = exam_model.update_exam(exam_id, {
            'practical_task_ids': practical_task_ids
        })
        
        if not updated_exam:
            exam_logger.log_error('practical_task_update_failed', '考试实操任务关联更新失败', {
                'exam_id': exam_id,
                'practical_task_ids': practical_task_ids
            })
            return jsonify(create_api_response(500, "更新失败")), 500
        
        # 记录成功日志和审计日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info('practical_task_updated', '考试实操任务关联更新成功', {
            'exam_id': exam_id,
            'practical_task_ids': practical_task_ids,
            'operator_id': operator_id
        })
                
        audit_logger.log_action(
            action='update_exam_practical_tasks',
            resource_type='exam',
            resource_id=exam_id,
            user_id=operator_id,
            details={
                'practical_task_ids': practical_task_ids,
                'previous_practical_task_ids': exam.get('practical_task_ids', [])
            }
        )
        
        # 保持兼容性的操作日志
        log_model.log_operation(
            exam_id=exam_id,
            operation='update_practical_tasks',
            operator=operator_id,
            details=f"更新实操任务关联: {practical_task_ids}"
        )
        
        return jsonify(create_api_response(200, "更新成功", {
            'practical_task_ids': updated_exam['practical_task_ids']
        }))

# 考试统计接口
@app.route('/api/v1/exams/<int:exam_id>/statistics', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_exam_statistics', 'exam')
@log_operation('get_exam_statistics')
@handle_exceptions
@performance_monitor.monitor
def get_exam_statistics(exam_id):
    """获取考试统计信息"""
    # 检查考试是否存在
    exam = exam_model.get_exam_by_id(exam_id)
    if not exam:
        exam_logger.log_warning('exam_not_found', '尝试获取不存在的考试统计信息', {'exam_id': exam_id})
        return jsonify(create_api_response(404, "考试不存在")), 404
        
    # 获取参与者统计
    participants = participant_model.get_exam_participants(exam_id)
    participant_count = len(participants)
    
    # 按角色统计
    role_stats = {}
    for participant in participants:
        role = participant['role']
        role_stats[role] = role_stats.get(role, 0) + 1
        
    # 考试时长统计
    duration_info = {}
    if exam['start_time'] and exam['end_time']:
        duration_info['planned_duration'] = calculate_exam_duration(
            exam['start_time'], exam['end_time']
        )
    
    # 获取题库和实操任务详细信息
    question_bank_stats = []
    if exam.get('question_bank_ids'):
        try:
            for bank_id in exam['question_bank_ids']:
                bank_info = call_question_bank_service(bank_id)
                question_bank_stats.append({
                    'id': bank_id,
                    'name': bank_info.get('name', f'题库{bank_id}'),
                    'question_count': bank_info.get('question_count', 0),
                    'status': bank_info.get('status', 'unknown')
                })
        except Exception as e:
            exam_logger.log_error('get_question_bank_stats_failed', '获取题库统计信息失败', {'exam_id': exam_id, 'error': str(e)})
        
    practical_task_stats = []
    if exam.get('practical_task_ids'):
        try:
            for task_id in exam['practical_task_ids']:
                task_info = call_practical_task_service(task_id)
                practical_task_stats.append({
                    'id': task_id,
                    'name': task_info.get('name', f'实操任务{task_id}'),
                    'difficulty': task_info.get('difficulty', 'unknown'),
                    'estimated_time': task_info.get('estimated_time', 0),
                    'status': task_info.get('status', 'unknown')
                })
        except Exception as e:
            exam_logger.log_error('get_practical_task_stats_failed', '获取实操任务统计信息失败', {'exam_id': exam_id, 'error': str(e)})
        
    # 获取操作日志统计
    recent_logs = log_model.get_exam_logs(exam_id, limit=10)
        
    # 构建统计数据
    statistics = {
        'exam_info': {
            'id': exam['id'],
            'name': exam['name'],
            'description': exam.get('description', ''),
            'status': exam['status'],
            'status_display': get_exam_status_display(exam['status']),
            'exam_type': exam['exam_type'],
            'exam_type_display': get_exam_type_display(exam['exam_type']),
            'exam_date': exam.get('exam_date'),
            'start_time': exam.get('start_time'),
            'end_time': exam.get('end_time'),
            'location': exam.get('location', ''),
            'created_at': exam.get('created_at'),
            'updated_at': exam.get('updated_at')
        },
        'participant_stats': {
            'total_count': participant_count,
            'by_role': role_stats,
            'participants': [{
                'id': p['id'],
                'user_id': p['user_id'],
                'role': p['role'],
                'role_display': get_participant_role_display(p['role']),
                'seat_number': p.get('seat_number'),
                'joined_at': p.get('joined_at')
            } for p in participants[:10]]  # 只显示前10个参与者
        },
        'time_info': duration_info,
        'content_stats': {
            'question_banks': question_bank_stats,
            'practical_tasks': practical_task_stats,
            'total_question_banks': len(exam.get('question_bank_ids', [])),
            'total_practical_tasks': len(exam.get('practical_task_ids', []))
        },
        'activity_logs': [{
            'operation': log['operation'],
            'operator': log['operator'],
            'timestamp': log['timestamp'],
            'details': log.get('details', '')
        } for log in recent_logs],
        'summary': {
            'is_ready': exam['status'] in ['published', 'in_progress', 'completed'],
            'has_participants': participant_count > 0,
            'has_content': len(exam.get('question_bank_ids', [])) > 0 or len(exam.get('practical_task_ids', [])) > 0,
            'completion_rate': 100 if exam['status'] == 'completed' else 0
        }
    }
        
    # 记录操作日志
    operator_id = request.headers.get('X-User-ID', 'system')
    exam_logger.log_info('exam_statistics_retrieved', '成功获取考试统计信息', {'exam_id': exam_id, 'operator_id': operator_id})
    audit_logger.log_action('get_exam_statistics', operator_id, {'exam_id': exam_id})
    log_model.log_operation(exam_id, 'view_statistics', operator_id)
    
    return jsonify(create_api_response(200, "获取成功", statistics))

@app.route('/api/v1/exams/<int:exam_id>/reports/summary', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_exam_summary_report', 'report')
@log_operation('get_exam_summary_report')
@handle_exceptions
@performance_monitor.monitor
def get_exam_summary_report(exam_id):
        """获取考试摘要报表"""
        exam = exam_model.get_exam_by_id(exam_id)
        if not exam:
            exam_logger.log_warning('exam_not_found', '尝试获取不存在的考试摘要报表', {'exam_id': exam_id})
            return jsonify(create_api_response(404, "考试不存在")), 404
            
        # 获取参与者统计
        participants = participant_model.get_exam_participants(exam_id)
        participant_count = len(participants)
        
        # 按角色统计
        role_stats = {}
        for participant in participants:
            role = participant['role']
            role_stats[role] = role_stats.get(role, 0) + 1
            
        # 时间信息
        duration_info = {}
        if exam['start_time'] and exam['end_time']:
            duration_info = calculate_exam_duration(exam['start_time'], exam['end_time'])
        
        # 构建摘要报表
        summary_report = {
            'exam_basic_info': {
                'id': exam['id'],
                'name': exam['name'],
                'description': exam.get('description', ''),
                'status': exam['status'],
                'status_display': get_exam_status_display(exam['status']),
                'exam_type': exam['exam_type'],
                'exam_type_display': get_exam_type_display(exam['exam_type']),
                'location': exam.get('location', ''),
                'exam_date': exam.get('exam_date'),
                'start_time': exam.get('start_time'),
                'end_time': exam.get('end_time'),
                'duration': duration_info
            },
            'participant_summary': {
                'total_participants': participant_count,
                'role_distribution': role_stats,
                'role_details': [{
                    'role': role,
                    'role_display': get_participant_role_display(role),
                    'count': count
                } for role, count in role_stats.items()]
            },
            'content_summary': {
                'question_banks_count': len(exam.get('question_bank_ids', [])),
                'practical_tasks_count': len(exam.get('practical_task_ids', [])),
                'has_content': len(exam.get('question_bank_ids', [])) > 0 or len(exam.get('practical_task_ids', [])) > 0
            },
            'status_summary': {
                'is_draft': exam['status'] == 'draft',
                'is_published': exam['status'] == 'published',
                'is_in_progress': exam['status'] == 'in_progress',
                'is_completed': exam['status'] == 'completed',
                'can_edit': exam['status'] in ['draft', 'published'],
                'can_start': exam['status'] == 'published' and participant_count > 0,
                'ready_for_publish': exam['status'] == 'draft' and len(exam.get('question_bank_ids', [])) > 0
            },
            'generated_at': datetime.now(timezone.utc).isoformat()
        }
            
        # 记录操作日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info('exam_summary_report_retrieved', '成功获取考试摘要报表', {'exam_id': exam_id, 'operator_id': operator_id})
        audit_logger.log_action('get_exam_summary_report', operator_id, {'exam_id': exam_id})
        log_exam_operation(exam_id, 'view_summary_report', operator_id)
        
        return jsonify(create_api_response(200, "获取成功", summary_report))

@app.route('/api/v1/exams/<int:exam_id>/reports/participants', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_exam_participants_report', 'report')
@log_operation('get_exam_participants_report')
@handle_exceptions
@performance_monitor.monitor
def get_exam_participants_report(exam_id):
    """获取考试参与者详细报表"""
    exam = exam_model.get_exam_by_id(exam_id)
    if not exam:
        exam_logger.log_warning('exam_not_found', '尝试获取不存在的考试参与者报表', {'exam_id': exam_id})
        return jsonify(create_api_response(404, "考试不存在")), 404
            
    # 获取参与者详细信息
    participants = participant_model.get_exam_participants(exam_id)
    
    # 格式化参与者信息
    formatted_participants = []
    for participant in participants:
        formatted_participants.append({
            'id': participant['id'],
            'user_id': participant['user_id'],
            'role': participant['role'],
            'role_display': get_participant_role_display(participant['role']),
            'seat_number': participant.get('seat_number'),
            'joined_at': participant.get('joined_at'),
            'joined_at_display': format_datetime(participant.get('joined_at')) if participant.get('joined_at') else None,
            'status': participant.get('status', 'active'),
            'notes': participant.get('notes', '')
        })
    
    # 按角色分组
    participants_by_role = {}
    for participant in formatted_participants:
        role = participant['role']
        if role not in participants_by_role:
            participants_by_role[role] = []
        participants_by_role[role].append(participant)
    
    # 构建参与者报表
    participants_report = {
        'exam_info': {
            'id': exam['id'],
            'name': exam['name'],
            'status': exam['status'],
            'status_display': get_exam_status_display(exam['status'])
        },
        'participants_summary': {
            'total_count': len(formatted_participants),
            'by_role_count': {role: len(participants) for role, participants in participants_by_role.items()}
        },
        'participants_detail': formatted_participants,
        'participants_by_role': participants_by_role,
        'generated_at': datetime.now(timezone.utc).isoformat()
    }
    
    # 记录操作日志
    operator_id = request.headers.get('X-User-ID', 'system')
    exam_logger.log_info('exam_participants_report_retrieved', '成功获取考试参与者报表', {'exam_id': exam_id, 'operator_id': operator_id})
    audit_logger.log_action('get_exam_participants_report', operator_id, {'exam_id': exam_id})
    log_exam_operation(exam_id, 'view_participants_report', operator_id)
    
    return jsonify(create_api_response(200, "获取成功", participants_report))

@app.route('/api/v1/reports/exams/overview', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_exams_overview_report', 'report')
@log_operation('get_exams_overview_report')
@handle_exceptions
@performance_monitor.monitor
def get_exams_overview_report():
        """获取考试总览报表"""
        try:
            # 获取查询参数
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            status = request.args.get('status')
            exam_type = request.args.get('exam_type')
            
            # 构建过滤条件
            filters = {}
            if start_date:
                filters['start_date'] = start_date
            if end_date:
                filters['end_date'] = end_date
            if status:
                filters['status'] = status
            if exam_type:
                filters['exam_type'] = exam_type
            
            # 获取考试列表
            exams = exam_model.get_exams(filters)
            
            # 统计信息
            total_exams = len(exams)
            status_stats = {}
            type_stats = {}
            monthly_stats = {}
            
            for exam in exams:
                # 状态统计
                status = exam['status']
                status_stats[status] = status_stats.get(status, 0) + 1
                
                # 类型统计
                exam_type = exam['exam_type']
                type_stats[exam_type] = type_stats.get(exam_type, 0) + 1
                
                # 月度统计
                if exam.get('exam_date'):
                    month_key = exam['exam_date'][:7]  # YYYY-MM
                    monthly_stats[month_key] = monthly_stats.get(month_key, 0) + 1
            
            # 构建总览报表
            overview_report = {
                'summary': {
                    'total_exams': total_exams,
                    'report_period': {
                        'start_date': start_date,
                        'end_date': end_date
                    },
                    'filters_applied': {
                        'status': status,
                        'exam_type': exam_type
                    }
                },
                'status_distribution': {
                    'stats': status_stats,
                    'details': [{
                        'status': status,
                        'status_display': get_exam_status_display(status),
                        'count': count,
                        'percentage': round((count / total_exams) * 100, 2) if total_exams > 0 else 0
                    } for status, count in status_stats.items()]
                },
                'type_distribution': {
                    'stats': type_stats,
                    'details': [{
                        'exam_type': exam_type,
                        'exam_type_display': get_exam_type_display(exam_type),
                        'count': count,
                        'percentage': round((count / total_exams) * 100, 2) if total_exams > 0 else 0
                    } for exam_type, count in type_stats.items()]
                },
                'monthly_distribution': {
                    'stats': monthly_stats,
                    'details': [{
                        'month': month,
                        'count': count
                    } for month, count in sorted(monthly_stats.items())]
                },
                'recent_exams': [{
                    'id': exam['id'],
                    'name': exam['name'],
                    'status': exam['status'],
                    'status_display': get_exam_status_display(exam['status']),
                    'exam_type': exam['exam_type'],
                    'exam_type_display': get_exam_type_display(exam['exam_type']),
                    'exam_date': exam.get('exam_date'),
                    'created_at': exam.get('created_at')
                } for exam in sorted(exams, key=lambda x: x.get('created_at', ''), reverse=True)[:10]],
                'generated_at': datetime.now(timezone.utc).isoformat()
            }
            
            # 记录操作日志
            operator_id = request.headers.get('X-User-ID', 'system')
            exam_logger.log_info('exams_overview_report_retrieved', '成功获取考试总览报表', {'operator_id': operator_id})
            audit_logger.log_action('get_exams_overview_report', operator_id, {})
            log_action('view_exams_overview_report', operator_id)
            
            return jsonify(create_api_response(200, "获取成功", overview_report))
        except Exception as e:
            exam_logger.log_error('get_exams_overview_report_failed', '获取考试总览报表失败', {'error': str(e), 'operator_id': request.headers.get('X-User-ID', 'system')})
            return jsonify(create_api_response(500, "获取报表失败")), 500

@app.route('/api/v1/reports/statistics', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_system_statistics', 'system')
@log_operation('get_system_statistics')
@handle_exceptions
@performance_monitor.monitor
def get_system_statistics():
    """获取系统统计数据"""
    try:
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        # 构建过滤条件
        filters = {}
        if start_date:
            filters['start_date'] = start_date
        if end_date:
            filters['end_date'] = end_date
        
        # 获取考试数据
        exams = exam_model.get_exams(filters)
        
        # 基础统计
        total_exams = len(exams)
        completed_exams = len([e for e in exams if e['status'] == 'completed'])
        in_progress_exams = len([e for e in exams if e['status'] == 'in_progress'])
        draft_exams = len([e for e in exams if e['status'] == 'draft'])
        
        # 获取参与者统计
        total_participants = 0
        active_participants = 0
        
        for exam in exams:
            participants = participant_model.get_exam_participants(exam['id'])
            total_participants += len(participants)
            active_participants += len([p for p in participants if p.get('status') == 'active'])
        
        # 类型分布
        type_distribution = {}
        for exam in exams:
            exam_type = exam['exam_type']
            type_distribution[exam_type] = type_distribution.get(exam_type, 0) + 1
        
        # 状态分布
        status_distribution = {}
        for exam in exams:
            status = exam['status']
            status_distribution[status] = status_distribution.get(status, 0) + 1
        
        # 最近7天的考试趋势
        from datetime import datetime, timedelta
        today = datetime.now().date()
        trend_data = []
        
        for i in range(7):
            date = today - timedelta(days=6-i)
            date_str = date.strftime('%Y-%m-%d')
            count = len([e for e in exams if e.get('exam_date', '').startswith(date_str)])
            trend_data.append({
                'date': date_str,
                'count': count
            })
        
        statistics = {
            'overview': {
                'total_exams': total_exams,
                'completed_exams': completed_exams,
                'in_progress_exams': in_progress_exams,
                'draft_exams': draft_exams,
                'total_participants': total_participants,
                'active_participants': active_participants
            },
            'distributions': {
                'by_type': [{
                    'type': exam_type,
                    'type_display': get_exam_type_display(exam_type),
                    'count': count,
                    'percentage': round((count / total_exams) * 100, 2) if total_exams > 0 else 0
                } for exam_type, count in type_distribution.items()],
                'by_status': [{
                    'status': status,
                    'status_display': get_exam_status_display(status),
                    'count': count,
                    'percentage': round((count / total_exams) * 100, 2) if total_exams > 0 else 0
                } for status, count in status_distribution.items()]
            },
            'trends': {
                'daily_exams': trend_data,
                'completion_rate': round((completed_exams / total_exams) * 100, 2) if total_exams > 0 else 0,
                'participation_rate': round((active_participants / total_participants) * 100, 2) if total_participants > 0 else 0
            },
            'generated_at': datetime.now().isoformat()
        }
        
        # 记录操作日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info('system_statistics_retrieved', '成功获取系统统计数据', {'operator_id': operator_id})
        audit_logger.log_action('get_system_statistics', operator_id, {})
        log_action('view_system_statistics', operator_id)
        
        return jsonify(create_api_response(200, "获取成功", statistics))
    except Exception as e:
        exam_logger.log_error('get_system_statistics_failed', '获取系统统计数据失败', {'error': str(e), 'operator_id': request.headers.get('X-User-ID', 'system')})
        return jsonify(create_api_response(500, "获取统计数据失败")), 500

@app.route('/api/v1/reports/export', methods=['POST'])
@require_permission('exam:export')
@audit_log('export_reports', 'report')
@log_operation('export_reports')
@sql_injection_check
@handle_exceptions
@performance_monitor.monitor
def export_reports():
        """导出报表数据"""
        data = request.get_json()
        
        try:
            # 清理输入数据
            cleaned_data = security_manager.sanitize_input(data)
            report_type = cleaned_data.get('report_type')  # 'overview', 'participants', 'statistics'
            export_format = cleaned_data.get('format', 'json')  # 'json', 'csv', 'excel'
            filters = cleaned_data.get('filters', {})
            
            # 数据验证
            if not report_type:
                exam_logger.log_error('export_reports_validation_failed', '缺少报表类型参数', {'operator_id': request.headers.get('X-User-ID', 'system')})
                return jsonify(create_api_response(400, "缺少报表类型参数")), 400
            
            # 验证报表类型
            valid_types = ['overview', 'participants', 'statistics']
            if report_type not in valid_types:
                exam_logger.log_error('export_reports_validation_failed', '无效的报表类型', {'report_type': report_type, 'operator_id': request.headers.get('X-User-ID', 'system')})
                return jsonify(create_api_response(400, "无效的报表类型")), 400
            
            # 验证导出格式
            valid_formats = ['json', 'csv', 'excel']
            if export_format not in valid_formats:
                exam_logger.log_error('export_reports_validation_failed', '无效的导出格式', {'export_format': export_format, 'operator_id': request.headers.get('X-User-ID', 'system')})
                return jsonify(create_api_response(400, "无效的导出格式")), 400
            
            # 根据报表类型获取数据
            if report_type == 'overview':
                # 获取考试总览数据
                exams = exam_model.get_exams(filters)
                export_data = {
                    'report_type': 'exam_overview',
                    'data': exams,
                    'generated_at': datetime.now().isoformat()
                }
            elif report_type == 'participants':
                # 获取参与者数据
                all_participants = []
                exams = exam_model.get_exams(filters)
                for exam in exams:
                    participants = participant_model.get_exam_participants(exam['id'])
                    for participant in participants:
                        participant['exam_name'] = exam['name']
                        participant['exam_type'] = exam['exam_type']
                        all_participants.append(participant)
                
                export_data = {
                    'report_type': 'participants',
                    'data': all_participants,
                    'generated_at': datetime.now().isoformat()
                }
            elif report_type == 'statistics':
                # 获取统计数据
                # 这里可以调用上面的统计接口获取数据
                export_data = {
                    'report_type': 'statistics',
                    'data': {},  # 实际应该包含统计数据
                    'generated_at': datetime.now().isoformat()
                }
            else:
                return jsonify(create_api_response(400, "不支持的报表类型")), 400
            
            # 根据格式处理数据
            if export_format == 'json':
                response_data = {
                    'download_url': f'/api/v1/reports/download/{report_type}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
                    'file_name': f'{report_type}_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json',
                    'file_size': len(str(export_data)),
                    'export_format': export_format
                }
            else:
                # 其他格式的处理逻辑
                response_data = {
                    'message': f'{export_format}格式导出功能开发中',
                    'export_format': export_format
                }
            
            # 记录操作日志
            operator_id = request.headers.get('X-User-ID', 'system')
            exam_logger.log_info('report_exported', f'成功导出{report_type}报表', {'report_type': report_type, 'export_format': export_format, 'operator_id': operator_id})
            audit_logger.log_action('export_reports', operator_id, {'report_type': report_type, 'export_format': export_format})
            log_action(f'export_report_{report_type}', operator_id)
            
            return jsonify(create_api_response(200, "导出成功", response_data))
        except Exception as e:
            exam_logger.log_error('export_reports_failed', '导出报表失败', {'error': str(e), 'operator_id': request.headers.get('X-User-ID', 'system')})
            return jsonify(create_api_response(500, "导出报表失败")), 500

@app.route('/api/v1/reports/dashboard', methods=['GET'])
@require_permission('exam:view')
@audit_log('get_dashboard_data', 'system')
@log_operation('get_dashboard_data')
@handle_exceptions
@performance_monitor.monitor
def get_dashboard_data():
    """获取仪表板数据"""
    try:
        # 获取最近30天的数据
        from datetime import datetime, timedelta
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=30)
        
        filters = {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        }
        
        # 获取考试数据
        exams = exam_model.get_exams(filters)
        
        # 快速统计
        quick_stats = {
            'total_exams': len(exams),
            'active_exams': len([e for e in exams if e['status'] in ['published', 'in_progress']]),
            'completed_exams': len([e for e in exams if e['status'] == 'completed']),
            'total_participants': sum(len(participant_model.get_exam_participants(e['id'])) for e in exams)
        }
        
        # 最近活动
        recent_activities = []
        for exam in sorted(exams, key=lambda x: x.get('created_at', ''), reverse=True)[:5]:
            recent_activities.append({
                'type': 'exam_created',
                'title': f'创建考试: {exam["name"]}',
                'time': exam.get('created_at'),
                'exam_id': exam['id']
            })
        
        # 状态分布
        status_chart = {}
        for exam in exams:
            status = exam['status']
            status_chart[status] = status_chart.get(status, 0) + 1
        
        # 类型分布
        type_chart = {}
        for exam in exams:
            exam_type = exam['exam_type']
            type_chart[exam_type] = type_chart.get(exam_type, 0) + 1
        
        dashboard_data = {
            'quick_stats': quick_stats,
            'recent_activities': recent_activities,
            'charts': {
                'status_distribution': [{
                    'name': get_exam_status_display(status),
                    'value': count,
                    'status': status
                } for status, count in status_chart.items()],
                'type_distribution': [{
                    'name': get_exam_type_display(exam_type),
                    'value': count,
                    'type': exam_type
                } for exam_type, count in type_chart.items()]
            },
            'alerts': [
                # 这里可以添加系统警告信息
            ],
            'generated_at': datetime.now().isoformat()
        }
        
        # 记录操作日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info('dashboard_data_retrieved', '成功获取仪表板数据', {'operator_id': operator_id})
        audit_logger.log_action('get_dashboard_data', operator_id, {})
        
        return jsonify(create_api_response(200, "获取成功", dashboard_data))
    except Exception as e:
        exam_logger.log_error('get_dashboard_data_failed', '获取仪表板数据失败', {'error': str(e), 'operator_id': request.headers.get('X-User-ID', 'system')})
        return jsonify(create_api_response(500, "获取仪表板数据失败")), 500

# Web界面路由
@app.route('/web', methods=['GET'])
@audit_log('web_interface', 'web')
@log_operation('web_interface')
@handle_exceptions
@performance_monitor.monitor
def web_interface():
    """Web管理界面主页"""
    try:
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试编排管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .nav-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .nav-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .nav-btn.primary {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .nav-btn.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }
        
        .nav-btn.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .card p {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: scale(1.05);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .nav-btn {
                width: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>考试编排管理系统</h1>
            <p>专业的考试管理解决方案 - 支持完整的考试生命周期管理</p>
        </div>
        
        <div class="nav-buttons">
            <a href="/web/exams" class="nav-btn primary">考试管理</a>
            <a href="/web/participants" class="nav-btn success">参与者管?/a>
            <a href="/web/reports" class="nav-btn warning">统计报表</a>
            <a href="/web/settings" class="nav-btn">系统设置</a>
        </div>
        
        <div class="main-content">
            <div class="section">
                <h2>系统概览</h2>
                <div class="stats" id="systemStats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalExams">-</div>
                        <div class="stat-label">总考试?/div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeExams">-</div>
                        <div class="stat-label">进行中考试</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalParticipants">-</div>
                        <div class="stat-label">总参与?/div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="completedExams">-</div>
                        <div class="stat-label">已完成考试</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>主要功能</h2>
                <div class="card-grid">
                    <div class="card">
                        <h3>考试创建与编?/h3>
                        <p>支持创建各类型考试，设置考试时间、参数和规则。提供完整的考试配置选项，包括题库关联和实操任务设置?/p>
                    </div>
                    <div class="card">
                        <h3>状态管?/h3>
                        <p>完整的考试状态流转管理，从草稿到发布、进行中到已完成。支持状态转换验证和权限控制。/p>
                    </div>
                    <div class="card">
                        <h3>参与者管?/h3>
                        <p>灵活的参与者管理功能，支持批量添加、角色分配、座位安排等。提供详细的参与者信息管理。/p>
                    </div>
                    <div class="card">
                        <h3>时间冲突检?/h3>
                        <p>智能的时间冲突检测机制，确保考试时间安排合理，避免资源冲突和时间重叠。/p>
                    </div>
                    <div class="card">
                        <h3>统计报表</h3>
                        <p>丰富的统计报表功能，提供考试概览、参与者分析、成绩统计等多维度数据展示。/p>
                    </div>
                    <div class="card">
                        <h3>权限控制</h3>
                        <p>基于角色的权限控制系统，确保不同用户只能访问授权的功能和数据。/p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <a href="http://localhost:8000" class="back-btn">返回主控?/a>
            <p style="margin-top: 15px; color: #7f8c8d;">考试编排管理系统 v3.0 - 专业?/p>
        </div>
    </div>
    
    <script>
        // 加载系统统计数据
        async function loadSystemStats() {
            try {
                const response = await fetch('/api/v1/reports/exams/overview');
                const data = await response.json();
                
                if (data.code === 200) {
                    const stats = data.data.summary;
                    const statusStats = data.data.status_distribution.stats;
                    
                    document.getElementById('totalExams').textContent = stats.total_exams || 0;
                    document.getElementById('activeExams').textContent = statusStats.in_progress || 0;
                    document.getElementById('completedExams').textContent = statusStats.completed || 0;
                    
                    // 计算总参与者数（这里使用模拟数据，实际应该从API获取）
                    document.getElementById('totalParticipants').textContent = stats.total_exams * 25 || 0;
                }
            } catch (error) {
                console.error('加载统计数据失败:', error);
                // 使用默认值
                document.getElementById('totalExams').textContent = '0';
                document.getElementById('activeExams').textContent = '0';
                document.getElementById('totalParticipants').textContent = '0';
                document.getElementById('completedExams').textContent = '0';
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadSystemStats();
        });
    </script>
    </body>
    </html>
    """
    
        # 记录日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info(f"用户访问Web管理界面主页")
        audit_logger.log_action(operator_id, 'web_interface', '访问Web管理界面主页', {'page': 'main'})
        
        return html_template
    except Exception as e:
        exam_logger.log_error(f"Web界面访问失败: {str(e)}")
        return f"<h1>系统错误</h1><p>无法加载页面: {str(e)}</p>", 500

@app.route('/web/exams', methods=['GET'])
@audit_log('web_exams', 'web')
@log_operation('web_exams')
@handle_exceptions
@performance_monitor.monitor
def web_exams():
    """考试管理页面"""
    html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试管理 - 考试编排管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }
        
        .btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .btn.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .exam-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .exam-table th,
        .exam-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .exam-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .exam-table tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }
        
        .status-draft {
            background: #f39c12;
            color: white;
        }
        
        .status-published {
            background: #3498db;
            color: white;
        }
        
        .status-in_progress {
            background: #27ae60;
            color: white;
        }
        
        .status-completed {
            background: #95a5a6;
            color: white;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .empty {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>考试管理</h1>
            <div>
                <a href="#" class="btn success" onclick="createExam()">创建考试</a>
                <a href="/web" class="back-btn">返回首页</a>
            </div>
        </div>
        
        <div class="main-content">
            <div class="filters">
                <div class="filter-group">
                    <label>状态/label>
                    <select id="statusFilter">
                        <option value="">全部状态/option>
                        <option value="draft">草稿</option>
                        <option value="published">已发?/option>
                        <option value="in_progress">进行?/option>
                        <option value="completed">已完?/option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>考试类型</label>
                    <select id="typeFilter">
                        <option value="">全部类型</option>
                        <option value="theory">理论考试</option>
                        <option value="practical">实操考试</option>
                        <option value="comprehensive">综合考试</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>开始日?/label>
                    <input type="date" id="startDateFilter">
                </div>
                <div class="filter-group">
                    <label>结束日期</label>
                    <input type="date" id="endDateFilter">
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="applyFilters()">筛?/button>
                </div>
            </div>
            
            <div id="examList">
                <div class="loading">正在加载考试列表...</div>
            </div>
            
            <div id="pagination" class="pagination" style="display: none;"></div>
        </div>
    </div>
    
    <script>
        let currentPage = 1;
        let totalPages = 1;
        
        // 加载考试列表
        async function loadExams(page = 1) {
            try {
                const statusFilter = document.getElementById('statusFilter').value;
                const typeFilter = document.getElementById('typeFilter').value;
                const startDateFilter = document.getElementById('startDateFilter').value;
                const endDateFilter = document.getElementById('endDateFilter').value;
                
                let url = `/api/v1/exams?page=${page}&per_page=20`;
                if (statusFilter) url += `&status=${statusFilter}`;
                if (typeFilter) url += `&exam_type=${typeFilter}`;
                if (startDateFilter) url += `&start_date=${startDateFilter}`;
                if (endDateFilter) url += `&end_date=${endDateFilter}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.code === 200) {
                    displayExams(data.data.exams);
                    updatePagination(data.data.pagination);
                } else {
                    document.getElementById('examList').innerHTML = `<div class="empty">加载失败: ${data.msg}</div>`;
                }
            } catch (error) {
                console.error('加载考试列表失败:', error);
                document.getElementById('examList').innerHTML = '<div class="empty">加载失败，请稍后重试</div>';
            }
        }
        
        // 显示考试列表
        function displayExams(exams) {
            if (exams.length === 0) {
                document.getElementById('examList').innerHTML = '<div class="empty">暂无考试数据</div>';
                return;
            }
            
            let html = `
                <table class="exam-table">
                    <thead>
                        <tr>
                            <th>考试名称</th>
                            <th>类型</th>
                            <th>状态</th>
                            <th>考试日期</th>
                            <th>参与者数</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            exams.forEach(exam => {
                html += `
                    <tr>
                        <td><strong>${exam.name}</strong></td>
                        <td>${exam.exam_type_display || exam.exam_type}</td>
                        <td><span class="status-badge status-${exam.status}">${exam.status_display || exam.status}</span></td>
                        <td>${exam.exam_date || '-'}</td>
                        <td>${exam.participant_count || 0}</td>
                        <td>${exam.created_at_display || exam.created_at}</td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-sm" onclick="viewExam(${exam.id})">查看</button>
                                <button class="btn btn-sm warning" onclick="editExam(${exam.id})">编辑</button>
                                <button class="btn btn-sm danger" onclick="deleteExam(${exam.id})">删除</button>
                            </div>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            document.getElementById('examList').innerHTML = html;
        }
        
        // 更新分页
        function updatePagination(pagination) {
            currentPage = pagination.current_page;
            totalPages = pagination.total_pages;
            
            if (totalPages <= 1) {
                document.getElementById('pagination').style.display = 'none';
                return;
            }
            
            let html = '';
            
            // 上一页            if (currentPage > 1) {
                html += `<button class="page-btn" onclick="loadExams(${currentPage - 1})">上一页/button>`;
            }
            
            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                html += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="loadExams(${i})">${i}</button>`;
            }
            
            // 下一页            if (currentPage < totalPages) {
                html += `<button class="page-btn" onclick="loadExams(${currentPage + 1})">下一页/button>`;
            }
            
            document.getElementById('pagination').innerHTML = html;
            document.getElementById('pagination').style.display = 'flex';
        }
        
        // 应用筛选        function applyFilters() {
            loadExams(1);
        }
        
        // 创建考试
        function createExam() {
            alert('创建考试功能开发中...');
        }
        
        // 查看考试
        function viewExam(examId) {
            window.open(`/web/exams/${examId}`, '_blank');
        }
        
        // 编辑考试
        function editExam(examId) {
            alert(`编辑考试 ${examId} 功能开发中...`);
        }
        
        // 删除考试
        function deleteExam(examId) {
            if (confirm('确定要删除这个考试吗？此操作不可恢复?)) {
                alert(`删除考试 ${examId} 功能开发中...`);
            }
        }
        
        // 页面加载完成后执行        document.addEventListener('DOMContentLoaded', function() {
            loadExams();
        });
    </script>
</body>
</html>
    """
    
    # 记录日志
    operator_id = request.headers.get('X-User-ID', 'system')
    exam_logger.log_info(f"用户访问考试管理页面")
    audit_logger.log_action(operator_id, 'web_exams', '访问考试管理页面', {'page': 'exams'})
    
    return html_template

    @app.route('/web/exams/<int:exam_id>', methods=['GET'])
    @audit_log('web_exam_detail', 'web')
    @log_operation('web_exam_detail')
    @handle_exceptions
    @performance_monitor.monitor
    def web_exam_detail(exam_id):
        """考试详情页面"""
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试详情 - 考试编排管理系统</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }}
        
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }}
        
        .header {{
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .header h1 {{
            color: #2c3e50;
            font-size: 2em;
        }}
        
        .btn {{
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 5px;
        }}
        
        .btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }}
        
        .btn.success {{
            background: linear-gradient(45deg, #27ae60, #229954);
        }}
        
        .btn.warning {{
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }}
        
        .btn.danger {{
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }}
        
        .back-btn {{
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }}
        
        .main-content {{
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }}
        
        .exam-info {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        
        .info-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }}
        
        .info-card h3 {{
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2em;
        }}
        
        .info-item {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }}
        
        .info-label {{
            font-weight: bold;
            color: #2c3e50;
        }}
        
        .info-value {{
            color: #7f8c8d;
        }}
        
        .status-badge {{
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
        }}
        
        .status-draft {{
            background: #f39c12;
            color: white;
        }}
        
        .status-published {{
            background: #3498db;
            color: white;
        }}
        
        .status-in_progress {{
            background: #27ae60;
            color: white;
        }}
        
        .status-completed {{
            background: #95a5a6;
            color: white;
        }}
        
        .tabs {{
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }}
        
        .tab {{
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }}
        
        .tab.active {{
            border-bottom-color: #3498db;
            color: #3498db;
            font-weight: bold;
        }}
        
        .tab-content {{
            display: none;
        }}
        
        .tab-content.active {{
            display: block;
        }}
        
        .loading {{
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }}
        
        .error {{
            text-align: center;
            padding: 40px;
            color: #e74c3c;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="examTitle">考试详情</h1>
            <div>
                <button class="btn warning" onclick="editExam()">编辑考试</button>
                <button class="btn success" onclick="manageParticipants()">管理参与者/button>
                <button class="btn" onclick="viewReports()">查看报表</button>
                <a href="/web/exams" class="btn back-btn">返回列表</a>
            </div>
        </div>
        
        <div class="main-content">
            <div id="examContent">
                <div class="loading">正在加载考试详情...</div>
            </div>
        </div>
    </div>
    
    <script>
        const examId = {exam_id};
        
        // 加载考试详情
        async function loadExamDetail() {{
            try {{
                const response = await fetch(`/api/v1/exams/${{examId}}`);
                const data = await response.json();
                
                if (data.code === 200) {{
                    displayExamDetail(data.data);
                }} else {{
                    document.getElementById('examContent').innerHTML = `<div class="error">加载失败: ${{data.msg}}</div>`;
                }}
            }} catch (error) {{
                console.error('加载考试详情失败:', error);
                document.getElementById('examContent').innerHTML = '<div class="error">加载失败，请稍后重试</div>';
            }}
        }}
        
        // 显示考试详情
        function displayExamDetail(exam) {{
            document.getElementById('examTitle').textContent = exam.name;
            
            const html = `
                <div class="exam-info">
                    <div class="info-card">
                        <h3>基本信息</h3>
                        <div class="info-item">
                            <span class="info-label">考试名称:</span>
                            <span class="info-value">${{exam.name}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">考试类型:</span>
                            <span class="info-value">${{exam.exam_type_display || exam.exam_type}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">状态</span>
                            <span class="status-badge status-${{exam.status}}">${{exam.status_display || exam.status}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">描述:</span>
                            <span class="info-value">${{exam.description || '无'}}</span>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>时间安排</h3>
                        <div class="info-item">
                            <span class="info-label">考试日期:</span>
                            <span class="info-value">${{exam.exam_date || '未设置}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">开始时间</span>
                            <span class="info-value">${{exam.start_time_display || exam.start_time || '未设置}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">结束时间:</span>
                            <span class="info-value">${{exam.end_time_display || exam.end_time || '未设置}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">持续时间:</span>
                            <span class="info-value">${{exam.duration_display || '未计算}}</span>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>考试配置</h3>
                        <div class="info-item">
                            <span class="info-label">最大参与者</span>
                            <span class="info-value">${{exam.max_participants || '无限制}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">当前参与者</span>
                            <span class="info-value">${{exam.participant_count || 0}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">题库数量:</span>
                            <span class="info-value">${{exam.question_bank_ids ? exam.question_bank_ids.length : 0}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">实操任务:</span>
                            <span class="info-value">${{exam.practical_task_ids ? exam.practical_task_ids.length : 0}}</span>
                        </div>
                    </div>
                    
                    <div class="info-card">
                        <h3>创建信息</h3>
                        <div class="info-item">
                            <span class="info-label">创建时间:</span>
                            <span class="info-value">${{exam.created_at_display || exam.created_at}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">更新时间:</span>
                            <span class="info-value">${{exam.updated_at_display || exam.updated_at}}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">创建者</span>
                            <span class="info-value">${{exam.created_by || '系统'}}</span>
                        </div>
                    </div>
                </div>
                
                <div class="tabs">
                    <div class="tab active" onclick="showTab('participants')">参与?/div>
                    <div class="tab" onclick="showTab('content')">考试内容</div>
                    <div class="tab" onclick="showTab('logs')">操作日志</div>
                </div>
                
                <div id="participants" class="tab-content active">
                    <div class="loading">正在加载参与者信息..</div>
                </div>
                
                <div id="content" class="tab-content">
                    <div class="loading">正在加载考试内容...</div>
                </div>
                
                <div id="logs" class="tab-content">
                    <div class="loading">正在加载操作日志...</div>
                </div>
            `;
            
            document.getElementById('examContent').innerHTML = html;
            
            // 加载默认标签页内
        }}
        
        // 切换标签
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {{
                content.classList.remove('active');
            }});
            
            // 移除所有标签页的激活状
                tab.classList.remove('active');
            }});
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 加载对应内容
            switch(tabName) {{
                case 'participants':
                    loadParticipants();
                    break;
                case 'content':
                    loadContent();
                    break;
                case 'logs':
                    loadLogs();
                    break;
            }}
        }}
        
        // 加载参与者信
            try {{
                const response = await fetch(`/api/v1/exams/${{examId}}/participants`);
                const data = await response.json();
                
                if (data.code === 200) {{
                    const participants = data.data.participants;
                    let html = '<h3>参与者列?/h3>';
                    
                    if (participants.length === 0) {{
                        html += '<p>暂无参与?/p>';
                    }} else {{
                        html += '<table class="exam-table" style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
                        html += '<thead><tr><th>用户ID</th><th>角色</th><th>座位号</th><th>加入时间</th><th>状态</th></tr></thead><tbody>';
                        
                        participants.forEach(participant => {{
                            html += `
                                <tr>
                                    <td>${{participant.user_id}}</td>
                                    <td>${{participant.role_display || participant.role}}</td>
                                    <td>${{participant.seat_number || '-'}}</td>
                                    <td>${{participant.joined_at_display || participant.joined_at || '-'}}</td>
                                    <td>${{participant.status || 'active'}}</td>
                                </tr>
                            `;
                        }});
                        
                        html += '</tbody></table>';
                    }}
                    
                    document.getElementById('participants').innerHTML = html;
                }} else {{
                    document.getElementById('participants').innerHTML = `<p class="error">加载失败: ${{data.msg}}</p>`;
                }}
            }} catch (error) {{
                document.getElementById('participants').innerHTML = '<p class="error">加载失败，请稍后重试</p>';
            }}
        }}
        
        // 加载考试内容
        async function loadContent() {{
            document.getElementById('content').innerHTML = '<h3>考试内容</h3><p>题库和实操任务信息加载中...</p>';
        }}
        
        // 加载操作日志
        async function loadLogs() {{
            document.getElementById('logs').innerHTML = '<h3>操作日志</h3><p>操作日志加载中..</p>';
        }}
        
        // 编辑考试
        function editExam() {{
            alert('编辑考试功能开发中...');
        }}
        
        // 管理参与者
        function manageParticipants() {{
            alert('管理参与者功能开发中...');
        }}
        
        // 查看报表
        function viewReports() {{
            window.open(`/web/reports?exam_id=${{examId}}`, '_blank');
        }}
        
        // 页面加载完成后执行        document.addEventListener('DOMContentLoaded', function() {{
            loadExamDetail();
        }});
    </script>
</body>
</html>
        """
        
        # 记录日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info(f"用户访问考试详情页面，考试ID: {exam_id}")
        audit_logger.log_action(operator_id, 'web_exam_detail', '访问考试详情页面', {'exam_id': exam_id, 'page': 'exam_detail'})
        
        return html_template

    @app.route('/web/reports', methods=['GET'])
    @audit_log('web_reports', 'web')
    @log_operation('web_reports')
    @handle_exceptions
    @performance_monitor.monitor
    def web_reports():
        """统计报表页面"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计报表 - 考试编排管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }
        
        .btn.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .chart-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .chart-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .chart-placeholder {
            height: 300px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .report-table th,
        .report-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .report-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .report-table tr:hover {
            background: #f8f9fa;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .tab.active {
            border-bottom-color: #3498db;
            color: #3498db;
            font-weight: bold;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>统计报表</h1>
            <div>
                <button class="btn success" onclick="exportReport()">导出报表</button>
                <button class="btn warning" onclick="refreshData()">刷新数据</button>
                <a href="/web" class="btn back-btn">返回首页</a>
            </div>
        </div>
        
        <div class="main-content">
            <div class="filters">
                <div class="filter-group">
                    <label>时间范围</label>
                    <select id="timeRange">
                        <option value="today">今天</option>
                        <option value="week">本周</option>
                        <option value="month" selected>本月</option>
                        <option value="quarter">本季?/option>
                        <option value="year">本年</option>
                        <option value="custom">自定?/option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>开始日?/label>
                    <input type="date" id="startDate">
                </div>
                <div class="filter-group">
                    <label>结束日期</label>
                    <input type="date" id="endDate">
                </div>
                <div class="filter-group">
                    <label>考试类型</label>
                    <select id="examTypeFilter">
                        <option value="">全部类型</option>
                        <option value="theory">理论考试</option>
                        <option value="practical">实操考试</option>
                        <option value="comprehensive">综合考试</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="applyFilters()">应用筛选/button>
                </div>
            </div>
            
            <div id="statsOverview">
                <div class="loading">正在加载统计数据...</div>
            </div>
            
            <div class="tabs">
                <div class="tab active" onclick="showTab('overview')">总览</div>
                <div class="tab" onclick="showTab('exams')">考试统计</div>
                <div class="tab" onclick="showTab('participants')">参与者统?/div>
                <div class="tab" onclick="showTab('performance')">成绩分析</div>
            </div>
            
            <div id="overview" class="tab-content active">
                <div class="chart-container">
                    <div class="chart-title">考试趋势?/div>
                    <div class="chart-placeholder">考试趋势图表 (开发中)</div>
                </div>
                
                <div class="chart-container">
                    <div class="chart-title">状态分布/div>
                    <div class="chart-placeholder">考试状态分布饼图(开发中)</div>
                </div>
            </div>
            
            <div id="exams" class="tab-content">
                <div class="chart-container">
                    <div class="chart-title">考试类型分布</div>
                    <div class="chart-placeholder">考试类型分布图表 (开发中)</div>
                </div>
                
                <div id="examsList">
                    <div class="loading">正在加载考试统计...</div>
                </div>
            </div>
            
            <div id="participants" class="tab-content">
                <div class="chart-container">
                    <div class="chart-title">参与者活跃度</div>
                    <div class="chart-placeholder">参与者活跃度图表 (开发中)</div>
                </div>
                
                <div id="participantsList">
                    <div class="loading">正在加载参与者统计..</div>
                </div>
            </div>
            
            <div id="performance" class="tab-content">
                <div class="chart-container">
                    <div class="chart-title">成绩分布</div>
                    <div class="chart-placeholder">成绩分布直方图(开发中)</div>
                </div>
                
                <div id="performanceData">
                    <div class="loading">正在加载成绩分析...</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 加载统计概览
        async function loadStatsOverview() {
            try {
                const response = await fetch('/api/v1/reports/overview');
                const data = await response.json();
                
                if (data.code === 200) {
                    displayStatsOverview(data.data);
                } else {
                    document.getElementById('statsOverview').innerHTML = `<div class="loading">加载失败: ${data.msg}</div>`;
                }
            } catch (error) {
                console.error('加载统计概览失败:', error);
                document.getElementById('statsOverview').innerHTML = '<div class="loading">加载失败，请稍后重试</div>';
            }
        }
        
        // 显示统计概览
        function displayStatsOverview(stats) {
            const html = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">${stats.total_exams || 0}</div>
                        <div class="stat-label">总考试?/div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-number">${stats.completed_exams || 0}</div>
                        <div class="stat-label">已完成考试</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-number">${stats.in_progress_exams || 0}</div>
                        <div class="stat-label">进行中考试</div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-number">${stats.total_participants || 0}</div>
                        <div class="stat-label">总参与者数</div>
                    </div>
                </div>
            `;
            
            document.getElementById('statsOverview').innerHTML = html;
        }
        
        // 切换标签
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签页的激活状
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 加载对应内容
            switch(tabName) {
                case 'overview':
                    // 总览已经在页面加载时加载
                    break;
                case 'exams':
                    loadExamStats();
                    break;
                case 'participants':
                    loadParticipantStats();
                    break;
                case 'performance':
                    loadPerformanceStats();
                    break;
            }
        }
        
        // 加载考试统计
        async function loadExamStats() {
            document.getElementById('examsList').innerHTML = '<div class="loading">正在加载考试统计数据...</div>';
        }
        
        // 加载参与者统
            document.getElementById('participantsList').innerHTML = '<div class="loading">正在加载参与者统计数据..</div>';
        }
        
        // 加载成绩统计
        async function loadPerformanceStats() {
            document.getElementById('performanceData').innerHTML = '<div class="loading">正在加载成绩分析数据...</div>';
        }
        
        // 应用筛选        function applyFilters() {
            loadStatsOverview();
            // 重新加载当前活跃的标签页内容
            const activeTab = document.querySelector('.tab.active');
            if (activeTab) {
                activeTab.click();
            }
        }
        
        // 导出报表
        function exportReport() {
            alert('导出报表功能开发中...');
        }
        
        // 刷新数据
        function refreshData() {
            loadStatsOverview();
            applyFilters();
        }
        
        // 时间范围变化处理
        document.getElementById('timeRange').addEventListener('change', function() {
            const value = this.value;
            const startDate = document.getElementById('startDate');
            const endDate = document.getElementById('endDate');
            
            const today = new Date();
            const formatDate = (date) => date.toISOString().split('T')[0];
            
            switch(value) {
                case 'today':
                    startDate.value = formatDate(today);
                    endDate.value = formatDate(today);
                    break;
                case 'week':
                    const weekStart = new Date(today);
                    weekStart.setDate(today.getDate() - today.getDay());
                    startDate.value = formatDate(weekStart);
                    endDate.value = formatDate(today);
                    break;
                case 'month':
                    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                    startDate.value = formatDate(monthStart);
                    endDate.value = formatDate(today);
                    break;
                case 'quarter':
                    const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
                    startDate.value = formatDate(quarterStart);
                    endDate.value = formatDate(today);
                    break;
                case 'year':
                    const yearStart = new Date(today.getFullYear(), 0, 1);
                    startDate.value = formatDate(yearStart);
                    endDate.value = formatDate(today);
                    break;
            }
        });
        
        // 页面加载完成后执行        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认时间范围
            document.getElementById('timeRange').dispatchEvent(new Event('change'));
            loadStatsOverview();
        });
    </script>
</body>
</html>
        """
        
        # 记录日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info(f"用户访问统计报表页面")
        audit_logger.log_action(operator_id, 'web_reports', '访问统计报表页面', {'page': 'reports'})
        
        return html_template

    @app.route('/web/participants', methods=['GET'])
    @audit_log('web_participants', 'web')
    @log_operation('web_participants')
    @handle_exceptions
    @performance_monitor.monitor
    def web_participants():
        """参与者管理页面"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参与者管?- 考试编排管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2em;
        }
        
        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }
        
        .btn.warning {
            background: linear-gradient(45deg, #f39c12, #e67e22);
        }
        
        .btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }
        
        .back-btn {
            background: linear-gradient(45deg, #95a5a6, #7f8c8d);
        }
        
        .main-content {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .filters {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .filter-group label {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .participant-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .participant-table th,
        .participant-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .participant-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .participant-table tr:hover {
            background: #f8f9fa;
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }
        
        .role-student {
            background: #3498db;
            color: white;
        }
        
        .role-teacher {
            background: #27ae60;
            color: white;
        }
        
        .role-admin {
            background: #e74c3c;
            color: white;
        }
        
        .role-proctor {
            background: #f39c12;
            color: white;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            display: inline-block;
            min-width: 60px;
        }
        
        .status-active {
            background: #27ae60;
            color: white;
        }
        
        .status-inactive {
            background: #95a5a6;
            color: white;
        }
        
        .status-suspended {
            background: #e74c3c;
            color: white;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .empty {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }
        
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 80%;
            max-width: 600px;
            position: relative;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 10px;
        }
        
        .close:hover {
            color: #000;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>参与者管?/h1>
            <div>
                <button class="btn success" onclick="addParticipant()">添加参与者/button>
                <button class="btn warning" onclick="batchImport()">批量导入</button>
                <a href="/web" class="btn back-btn">返回首页</a>
            </div>
        </div>
        
        <div class="main-content">
            <div class="filters">
                <div class="filter-group">
                    <label>考试</label>
                    <select id="examFilter">
                        <option value="">全部考试</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>角色</label>
                    <select id="roleFilter">
                        <option value="">全部角色</option>
                        <option value="student">学生</option>
                        <option value="teacher">教师</option>
                        <option value="proctor">监考员</option>
                        <option value="admin">管理?/option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>状态/label>
                    <select id="statusFilter">
                        <option value="">全部状态/option>
                        <option value="active">活跃</option>
                        <option value="inactive">非活?/option>
                        <option value="suspended">暂停</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>搜索</label>
                    <input type="text" id="searchInput" placeholder="用户ID或姓?>
                </div>
                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button class="btn" onclick="applyFilters()">筛?/button>
                </div>
            </div>
            
            <div id="participantList">
                <div class="loading">正在加载参与者列表..</div>
            </div>
            
            <div id="pagination" class="pagination" style="display: none;"></div>
        </div>
    </div>
    
    <!-- 添加/编辑参与者模态框 -->
    <div id="participantModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">添加参与者/h2>
            <form id="participantForm">
                <div class="form-group">
                    <label for="userId">用户ID</label>
                    <input type="text" id="userId" name="user_id" required>
                </div>
                <div class="form-group">
                    <label for="examId">考试</label>
                    <select id="examId" name="exam_id" required>
                        <option value="">请选择考试</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="role">角色</label>
                    <select id="role" name="role" required>
                        <option value="student">学生</option>
                        <option value="teacher">教师</option>
                        <option value="proctor">监考员</option>
                        <option value="admin">管理?/option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="seatNumber">座位号/label>
                    <input type="text" id="seatNumber" name="seat_number">
                </div>
                <div class="form-group">
                    <button type="submit" class="btn success">保存</button>
                    <button type="button" class="btn" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let editingParticipantId = null;
        
        // 加载参与者列
            try {
                const examFilter = document.getElementById('examFilter').value;
                const roleFilter = document.getElementById('roleFilter').value;
                const statusFilter = document.getElementById('statusFilter').value;
                const searchInput = document.getElementById('searchInput').value;
                
                let url = `/api/v1/participants?page=${page}&per_page=20`;
                if (examFilter) url += `&exam_id=${examFilter}`;
                if (roleFilter) url += `&role=${roleFilter}`;
                if (statusFilter) url += `&status=${statusFilter}`;
                if (searchInput) url += `&search=${encodeURIComponent(searchInput)}`;
                
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.code === 200) {
                    displayParticipants(data.data.participants);
                    updatePagination(data.data.pagination);
                } else {
                    document.getElementById('participantList').innerHTML = `<div class="empty">加载失败: ${data.msg}</div>`;
                }
            } catch (error) {
                console.error('加载参与者列表失败', error);
                document.getElementById('participantList').innerHTML = '<div class="empty">加载失败，请稍后重试</div>';
            }
        }
        
        // 显示参与者列表        function displayParticipants(participants) {
            if (participants.length === 0) {
                document.getElementById('participantList').innerHTML = '<div class="empty">暂无参与者数据/div>';
                return;
            }
            
            let html = `
                <table class="participant-table">
                    <thead>
                        <tr>
                            <th>用户ID</th>
                            <th>考试名称</th>
                            <th>角色</th>
                            <th>座位号</th>
                            <th>状态</th>
                            <th>加入时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            participants.forEach(participant => {
                html += `
                    <tr>
                        <td><strong>${participant.user_id}</strong></td>
                        <td>${participant.exam_name || '-'}</td>
                        <td><span class="role-badge role-${participant.role}">${participant.role_display || participant.role}</span></td>
                        <td>${participant.seat_number || '-'}</td>
                        <td><span class="status-badge status-${participant.status || 'active'}">${participant.status_display || participant.status || 'active'}</span></td>
                        <td>${participant.joined_at_display || participant.joined_at || '-'}</td>
                        <td>
                            <div class="actions">
                                <button class="btn btn-sm" onclick="viewParticipant(${participant.id})">查看</button>
                                <button class="btn btn-sm warning" onclick="editParticipant(${participant.id})">编辑</button>
                                <button class="btn btn-sm danger" onclick="removeParticipant(${participant.id})">移除</button>
                            </div>
                        </td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            document.getElementById('participantList').innerHTML = html;
        }
        
        // 更新分页
        function updatePagination(pagination) {
            currentPage = pagination.current_page;
            totalPages = pagination.total_pages;
            
            if (totalPages <= 1) {
                document.getElementById('pagination').style.display = 'none';
                return;
            }
            
            let html = '';
            
            // 上一页            if (currentPage > 1) {
                html += `<button class="page-btn" onclick="loadParticipants(${currentPage - 1})">上一页/button>`;
            }
            
            // 页码
            for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                html += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="loadParticipants(${i})">${i}</button>`;
            }
            
            // 下一页            if (currentPage < totalPages) {
                html += `<button class="page-btn" onclick="loadParticipants(${currentPage + 1})">下一页/button>`;
            }
            
            document.getElementById('pagination').innerHTML = html;
            document.getElementById('pagination').style.display = 'flex';
        }
        
        // 加载考试列表到下拉框
        async function loadExamOptions() {
            try {
                const response = await fetch('/api/v1/exams?per_page=100');
                const data = await response.json();
                
                if (data.code === 200) {
                    const examFilter = document.getElementById('examFilter');
                    const examId = document.getElementById('examId');
                    
                    data.data.exams.forEach(exam => {
                        const option1 = new Option(exam.name, exam.id);
                        const option2 = new Option(exam.name, exam.id);
                        examFilter.appendChild(option1);
                        examId.appendChild(option2);
                    });
                }
            } catch (error) {
                console.error('加载考试列表失败:', error);
            }
        }
        
        // 应用筛选        function applyFilters() {
            loadParticipants(1);
        }
        
        // 添加参与者        function addParticipant() {
            editingParticipantId = null;
            document.getElementById('modalTitle').textContent = '添加参与者;
            document.getElementById('participantForm').reset();
            document.getElementById('participantModal').style.display = 'block';
        }
        
        // 编辑参与者        function editParticipant(participantId) {
            editingParticipantId = participantId;
            document.getElementById('modalTitle').textContent = '编辑参与者;
            // 这里应该加载参与者详情并填充表单
            document.getElementById('participantModal').style.display = 'block';
        }
        
        // 查看参与者        function viewParticipant(participantId) {
            alert(`查看参与者${participantId} 功能开发中...`);
        }
        
        // 移除参与者        function removeParticipant(participantId) {
            if (confirm('确定要移除这个参与者吗?')) {
                alert(`移除参与者${participantId} 功能开发中...`);
            }
        }
        
        // 批量导入
        function batchImport() {
            alert('批量导入功能开发中...');
        }
        
        // 关闭模态框
        function closeModal() {
            document.getElementById('participantModal').style.display = 'none';
        }
        
        // 表单提交
        document.getElementById('participantForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            if (editingParticipantId) {
                // 更新参与者                alert('更新参与者功能开发中...');
            } else {
                // 添加参与者                alert('添加参与者功能开发中...');
            }
            
            closeModal();
        });
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('participantModal');
            if (event.target === modal) {
                closeModal();
            }
        }
        
        // 页面加载完成后执行        document.addEventListener('DOMContentLoaded', function() {
            loadExamOptions();
            loadParticipants();
        });
    </script>
</body>
</html>
        """
        
        # 记录日志
        operator_id = request.headers.get('X-User-ID', 'system')
        exam_logger.log_info(f"用户访问参与者管理页面")
        audit_logger.log_action(operator_id, 'web_participants', '访问参与者管理页面', {'page': 'participants'})
        
        return html_template

    # ==================== 通知管理接口 ====================
    
    @app.route('/api/v1/notifications', methods=['POST'])
    @require_permission('exam:manage')
    @audit_log('notification_create', 'notification')
    @log_operation('create_notification')
    @sql_injection_check
    @handle_exceptions
    @performance_monitor.monitor
    def create_notification():
        """创建考试通知"""
        with transaction(transaction_manager):
            data = request.get_json()
            
            try:
                # 数据验证
                data_validator.validate_required(data, 'exam_id', '考试ID')
                data_validator.validate_required(data, 'title', '通知标题')
                data_validator.validate_required(data, 'content', '通知内容')
                data_validator.validate_required(data, 'notification_type', '通知类型')
                
                # 字符串长度验证
                data_validator.validate_string_length(data.get('title', ''), 1, 200, '通知标题')
                data_validator.validate_string_length(data.get('content', ''), 1, 2000, '通知内容')
                
                # 验证考试是否存在
                exam = exam_model.get_exam_by_id(data['exam_id'])
                if not exam:
                    return jsonify(create_api_response(404, "考试不存在")), 404
                
                # 验证通知类型
                valid_types = [t.value for t in NotificationType]
                if data['notification_type'] not in valid_types:
                    return jsonify(create_api_response(400, f"无效的通知类型，支持的类型: {', '.join(valid_types)}")), 400
                
                # 获取操作员信息
                operator_id = request.headers.get('X-User-ID', 'system')
                
                # 创建通知
                notification_data = {
                    'exam_id': data['exam_id'],
                    'title': sanitize_input(data['title']),
                    'content': sanitize_input(data['content']),
                    'notification_type': data['notification_type'],
                    'created_by': operator_id,
                    'scheduled_time': data.get('scheduled_time'),
                    'priority': data.get('priority', 'normal')
                }
                
                notification_id = notification_model.create_notification(notification_data)
                
                # 记录操作日志
                log_action(operator_id, 'create_notification', f"创建考试通知: {data['title']}", {
                    'notification_id': notification_id,
                    'exam_id': data['exam_id'],
                    'notification_type': data['notification_type']
                })
                
                # 记录审计日志
                audit_logger.log_action(operator_id, 'create_notification', '创建考试通知', {
                    'notification_id': notification_id,
                    'exam_id': data['exam_id'],
                    'title': data['title'],
                    'notification_type': data['notification_type']
                })
                
                return jsonify(create_api_response(201, "通知创建成功", {
                    'notification_id': notification_id,
                    'exam_name': exam['name']
                }))
                
            except ValidationError as e:
                return jsonify(create_api_response(400, str(e))), 400
            except Exception as e:
                return jsonify(handle_exception(e))
    
    @app.route('/api/v1/notifications/<int:notification_id>/send', methods=['POST'])
    @require_permission('exam:manage')
    @audit_log('notification_send', 'notification')
    @log_operation('send_notification')
    @handle_exceptions
    @performance_monitor.monitor
    def send_notification(notification_id):
        """发送考试通知"""
        try:
            with transaction(transaction_manager):
                data = request.get_json() or {}
                
                # 获取通知详情
                notification = notification_model.get_notification_by_id(notification_id)
                if not notification:
                    return jsonify(create_api_response(404, "通知不存在")), 404
                
                # 检查通知状态
                if notification['status'] == NotificationStatus.SENT.value:
                    return jsonify(create_api_response(400, "通知已发送，无法重复发送")), 400
                
                # 获取接收者列表
                recipients = data.get('recipients', [])
                if not recipients:
                    # 如果没有指定接收者，获取考试的所有参与者
                    participants = participant_model.get_exam_participants(notification['exam_id'])
                    recipients = [p['user_id'] for p in participants if p['role'] == 'student']
                
                if not recipients:
                    return jsonify(create_api_response(400, "没有找到接收者")), 400
                
                # 获取操作员信息
                operator_id = request.headers.get('X-User-ID', 'system')
                
                # 发送通知
                result = notification_model.send_notification(notification_id, recipients, operator_id)
                
                # 记录操作日志
                log_action(operator_id, 'send_notification', f"发送考试通知: {notification['title']}", {
                    'notification_id': notification_id,
                    'recipients_count': len(recipients),
                    'exam_id': notification['exam_id']
                })
                
                # 记录审计日志
                audit_logger.log_action(operator_id, 'send_notification', '发送考试通知', {
                    'notification_id': notification_id,
                    'title': notification['title'],
                    'recipients_count': len(recipients),
                    'exam_id': notification['exam_id']
                })
                
                return jsonify(create_api_response(200, "通知发送成功", {
                    'notification_id': notification_id,
                    'recipients_count': len(recipients),
                    'sent_at': result.get('sent_at')
                }))
                
        except Exception as e:
            return jsonify(handle_exception(e))
    
@app.route('/api/v1/notifications', methods=['GET'])
@require_permission('exam:view')
def get_notifications():
    """获取通知列表"""
    try:
        # 获取查询参数
        exam_id = request.args.get('exam_id')
        status = request.args.get('status')
        notification_type = request.args.get('notification_type')
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        
        # 构建过滤条件
        filters = {}
        if exam_id:
            filters['exam_id'] = exam_id
        if status:
            filters['status'] = status
        if notification_type:
            filters['notification_type'] = notification_type
        
        # 获取通知列表
        notifications = notification_model.get_notifications(filters)
        
        # 格式化显示数据
        for notification in notifications:
            if notification['created_at']:
                notification['created_at_display'] = format_datetime(notification['created_at'])
            if notification['sent_at']:
                notification['sent_at_display'] = format_datetime(notification['sent_at'])
            if notification['scheduled_time']:
                notification['scheduled_time_display'] = format_datetime(notification['scheduled_time'])
        
        # 分页处理
        paginated_result = paginate_results(notifications, page, per_page)
        
        return jsonify(create_api_response(200, "获取成功", paginated_result))
        
    except Exception as e:
        return jsonify(handle_exception(e))
    
@app.route('/api/v1/notifications/<int:notification_id>', methods=['GET'], endpoint='get_notification_endpoint')
@require_permission('exam:view')
def get_notification(notification_id):
    """获取通知详情"""
    try:
        notification = notification_model.get_notification_details(notification_id)
        if not notification:
            return jsonify(create_api_response(404, "通知不存在")), 404
        
        # 格式化显示数据
        if notification['created_at']:
            notification['created_at_display'] = format_datetime(notification['created_at'])
        if notification['sent_at']:
            notification['sent_at_display'] = format_datetime(notification['sent_at'])
        if notification['scheduled_time']:
            notification['scheduled_time_display'] = format_datetime(notification['scheduled_time'])
        
        return jsonify(create_api_response(200, "获取成功", notification))
        
    except Exception as e:
        return jsonify(handle_exception(e))
    
@app.route('/api/v1/notifications/<int:notification_id>', methods=['DELETE'], endpoint='delete_notification_endpoint')
@require_permission('exam:manage')
@audit_log('notification_delete', 'notification')
@log_operation('delete_notification')
@handle_exceptions
@performance_monitor.monitor
def delete_notification(notification_id):
    """删除通知"""
    with transaction(transaction_manager):
        try:
            # 获取通知详情
            notification = notification_model.get_notification_by_id(notification_id)
            if not notification:
                return jsonify(create_api_response(404, "通知不存在")), 404
            
            # 检查通知状态
            if notification['status'] == NotificationStatus.SENT.value:
                return jsonify(create_api_response(400, "已发送的通知无法删除")), 400
            
            # 获取操作员信息
            operator_id = request.headers.get('X-User-ID', 'system')
            
            # 删除通知
            notification_model.delete_notification(notification_id)
            
            # 记录操作日志
            log_action(operator_id, 'delete_notification', f"删除考试通知: {notification['title']}", {
                'notification_id': notification_id,
                'exam_id': notification['exam_id']
            })
            
            # 记录审计日志
            audit_logger.log_action(operator_id, 'delete_notification', '删除考试通知', {
                'notification_id': notification_id,
                'title': notification['title'],
                'exam_id': notification['exam_id']
            })
            
            return jsonify(create_api_response(200, "通知删除成功"))
            
        except Exception as e:
            return jsonify(handle_exception(e))
    
@app.route('/api/v1/exams/<int:exam_id>/notifications', methods=['GET'])
@require_permission('exam:view')
def get_exam_notifications(exam_id):
    """获取指定考试的通知列表"""
    try:
        # 验证考试是否存在
        exam = exam_model.get_exam_by_id(exam_id)
        if not exam:
            return jsonify(create_api_response(404, "考试不存在")), 404
        
        # 获取通知列表
        notifications = notification_model.get_notifications({'exam_id': exam_id})
        
        # 格式化显示数据
        for notification in notifications:
            if notification['created_at']:
                notification['created_at_display'] = format_datetime(notification['created_at'])
            if notification['sent_at']:
                notification['sent_at_display'] = format_datetime(notification['sent_at'])
            if notification['scheduled_time']:
                notification['scheduled_time_display'] = format_datetime(notification['scheduled_time'])
        
        return jsonify(create_api_response(200, "获取成功", {
            'exam': {
                'id': exam['id'],
                'name': exam['name']
            },
            'notifications': notifications
        }))
        
    except Exception as e:
        return jsonify(handle_exception(e))

if __name__ == '__main__':
    port = 5003
    print("--- Exam Management Service (v3) ---")
    print(f"Starting server at: http://0.0.0.0:{port}")
    app.run(host='0.0.0.0', port=port, debug=True)
