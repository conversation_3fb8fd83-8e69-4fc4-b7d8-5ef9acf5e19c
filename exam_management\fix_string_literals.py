#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Python文件中未闭合的字符串字面量
"""

import re
import sys

def fix_string_literals(file_path):
    """修复文件中的字符串字面量问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_count = 0
        
        # 修复docstring中的问号问题
        docstring_patterns = [
            (r'"""([^"]*?)\?""([^"]*?)"""', r'"""\1"""'),  # 修复docstring中的问号
            (r'"""([^"]*?)\?""', r'"""\1"""'),  # 修复不完整的docstring
        ]
        
        for pattern, replacement in docstring_patterns:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                fixes_count += len(matches)
                print(f"修复docstring: {len(matches)}个")
        
        # 检查三引号字符串的配对
        triple_quote_pattern = r'"""'
        quotes = list(re.finditer(triple_quote_pattern, content))
        
        # 如果三引号数量是奇数，说明有未闭合的字符串
        if len(quotes) % 2 != 0:
            print(f"发现未配对的三引号，总数: {len(quotes)}")
            
            # 找到最后一个未配对的三引号位置
            last_quote_pos = quotes[-1].end()
            
            # 在文件末尾添加闭合的三引号
            content = content[:last_quote_pos] + '\n        """\n        return html_template' + content[last_quote_pos:]
            fixes_count += 1
            print("在末尾添加了闭合的三引号")
        
        # 修复JavaScript注释中的问题
        js_comment_patterns = [
            (r'// ([^\n]*?)\?([^\n]*?)\n', r'// \1\n'),  # 修复JS注释中的问号
            (r'// ([^\n]*?)\?\s*function', r'// \1\n        function'),  # 修复注释后直接跟函数的情况
        ]
        
        for pattern, replacement in js_comment_patterns:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                fixes_count += len(matches)
                print(f"修复JS注释: {len(matches)}个")
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\n字符串字面量修复完成！")
            print(f"已修复 {fixes_count} 个问题")
        else:
            print("没有发现需要修复的字符串字面量问题")
            
        return fixes_count
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        return 0

if __name__ == "__main__":
    file_path = "app.py"
    fix_string_literals(file_path)