<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成绩查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 24px;
            font-weight: 600;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logout-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input, .form-group select {
            padding: 10px;
            border: 2px solid #e1e1e1;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .results-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .results-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e1e1e1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .results-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .results-actions {
            display: flex;
            gap: 10px;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        .results-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .results-table th,
        .results-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e1e1e1;
        }
        
        .results-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .results-table tr:hover {
            background: #f8f9fa;
        }
        
        .score-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .score-excellent {
            background: #d4edda;
            color: #155724;
        }
        
        .score-good {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .score-average {
            background: #fff3cd;
            color: #856404;
        }
        
        .score-poor {
            background: #f8d7da;
            color: #721c24;
        }
        
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state img {
            width: 100px;
            opacity: 0.5;
            margin-bottom: 20px;
        }
        
        .alert {
            padding: 12px 20px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>成绩查询系统</h1>
            <div class="user-info">
                <span id="username">用户</span>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div id="alert" class="alert"></div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
            <form class="search-form" id="searchForm">
                <div class="form-group">
                    <label for="examName">考试名称</label>
                    <input type="text" id="examName" placeholder="请输入考试名称">
                </div>
                <div class="form-group">
                    <label for="studentName">学生姓名</label>
                    <input type="text" id="studentName" placeholder="请输入学生姓名">
                </div>
                <div class="form-group">
                    <label for="examDate">考试日期</label>
                    <input type="date" id="examDate">
                </div>
                <div class="form-group">
                    <label for="scoreRange">分数范围</label>
                    <select id="scoreRange">
                        <option value="">全部</option>
                        <option value="90-100">优秀 (90-100)</option>
                        <option value="80-89">良好 (80-89)</option>
                        <option value="70-79">中等 (70-79)</option>
                        <option value="60-69">及格 (60-69)</option>
                        <option value="0-59">不及格 (0-59)</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">查询</button>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                </div>
            </form>
        </div>
        
        <!-- 统计信息 -->
        <div class="stats-section" id="statsSection" style="display: none;">
            <div class="stat-card">
                <div class="stat-value" id="totalCount">0</div>
                <div class="stat-label">总人数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgScore">0</div>
                <div class="stat-label">平均分</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="passRate">0%</div>
                <div class="stat-label">及格率</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="excellentRate">0%</div>
                <div class="stat-label">优秀率</div>
            </div>
        </div>
        
        <!-- 结果区域 -->
        <div class="results-section">
            <div class="results-header">
                <div class="results-title">查询结果</div>
                <div class="results-actions">
                    <button class="btn btn-success" onclick="exportResults()" id="exportBtn" style="display: none;">
                        导出Excel
                    </button>
                    <button class="btn btn-primary" onclick="syncGrades()" id="syncBtn">
                        同步成绩
                    </button>
                </div>
            </div>
            
            <div id="resultsContent">
                <div class="empty-state">
                    <p>请输入查询条件并点击查询按钮</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentResults = [];
        
        // 页面加载时初始化
        window.addEventListener('load', function() {
            // 检查登录状态
            checkLoginStatus();
            // 加载初始数据
            loadInitialData();
        });
        
        // 检查登录状态
        async function checkLoginStatus() {
            try {
                const response = await fetch('/api/v1/user/info');
                if (response.ok) {
                    const data = await response.json();
                    if (data.code === 200) {
                        document.getElementById('username').textContent = data.data.username || '用户';
                    }
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
            }
        }
        
        // 加载初始数据
        async function loadInitialData() {
            // 可以在这里加载一些初始统计数据
        }
        
        // 搜索表单提交
        document.getElementById('searchForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await searchGrades();
        });
        
        // 查询成绩
        async function searchGrades() {
            const formData = {
                exam_name: document.getElementById('examName').value,
                student_name: document.getElementById('studentName').value,
                exam_date: document.getElementById('examDate').value,
                score_range: document.getElementById('scoreRange').value
            };
            
            showLoading();
            
            try {
                const response = await fetch('/api/v1/grades/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    currentResults = data.data.grades || [];
                    displayResults(currentResults);
                    displayStatistics(data.data.statistics || {});
                    document.getElementById('exportBtn').style.display = currentResults.length > 0 ? 'block' : 'none';
                } else {
                    showAlert(data.msg || '查询失败', 'error');
                    showEmptyState();
                }
            } catch (error) {
                console.error('查询错误:', error);
                showAlert('网络错误，请稍后重试', 'error');
                showEmptyState();
            }
        }
        
        // 显示结果
        function displayResults(results) {
            const content = document.getElementById('resultsContent');
            
            if (results.length === 0) {
                showEmptyState();
                return;
            }
            
            let html = `
                <div class="table-container">
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th>学生姓名</th>
                                <th>考试名称</th>
                                <th>考试日期</th>
                                <th>总分</th>
                                <th>得分</th>
                                <th>等级</th>
                                <th>排名</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            results.forEach(grade => {
                const scoreClass = getScoreClass(grade.score, grade.max_score);
                const percentage = grade.max_score > 0 ? ((grade.score / grade.max_score) * 100).toFixed(1) : 0;
                
                html += `
                    <tr>
                        <td>${grade.student_name}</td>
                        <td>${grade.exam_name}</td>
                        <td>${grade.exam_date}</td>
                        <td>${grade.max_score}</td>
                        <td>${grade.score}</td>
                        <td><span class="score-badge ${scoreClass}">${getScoreLevel(percentage)}</span></td>
                        <td>${grade.rank || '-'}</td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
            `;
            
            content.innerHTML = html;
        }
        
        // 显示统计信息
        function displayStatistics(stats) {
            document.getElementById('totalCount').textContent = stats.total_count || 0;
            document.getElementById('avgScore').textContent = stats.avg_score || 0;
            document.getElementById('passRate').textContent = (stats.pass_rate || 0) + '%';
            document.getElementById('excellentRate').textContent = (stats.excellent_rate || 0) + '%';
            document.getElementById('statsSection').style.display = 'grid';
        }
        
        // 获取分数等级样式
        function getScoreClass(score, maxScore) {
            const percentage = maxScore > 0 ? (score / maxScore) * 100 : 0;
            if (percentage >= 90) return 'score-excellent';
            if (percentage >= 80) return 'score-good';
            if (percentage >= 60) return 'score-average';
            return 'score-poor';
        }
        
        // 获取分数等级文字
        function getScoreLevel(percentage) {
            if (percentage >= 90) return '优秀';
            if (percentage >= 80) return '良好';
            if (percentage >= 70) return '中等';
            if (percentage >= 60) return '及格';
            return '不及格';
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('resultsContent').innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>正在查询成绩...</p>
                </div>
            `;
        }
        
        // 显示空状态
        function showEmptyState() {
            document.getElementById('resultsContent').innerHTML = `
                <div class="empty-state">
                    <p>未找到符合条件的成绩记录</p>
                </div>
            `;
            document.getElementById('statsSection').style.display = 'none';
            document.getElementById('exportBtn').style.display = 'none';
        }
        
        // 重置表单
        function resetForm() {
            document.getElementById('searchForm').reset();
            showEmptyState();
        }
        
        // 同步成绩
        async function syncGrades() {
            const syncBtn = document.getElementById('syncBtn');
            const originalText = syncBtn.textContent;
            
            syncBtn.textContent = '同步中...';
            syncBtn.disabled = true;
            
            try {
                const response = await fetch('/api/v1/grades/sync', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    showAlert('成绩同步成功', 'success');
                    // 重新查询当前结果
                    if (currentResults.length > 0) {
                        await searchGrades();
                    }
                } else {
                    showAlert(data.msg || '同步失败', 'error');
                }
            } catch (error) {
                console.error('同步错误:', error);
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                syncBtn.textContent = originalText;
                syncBtn.disabled = false;
            }
        }
        
        // 导出结果
        async function exportResults() {
            if (currentResults.length === 0) {
                showAlert('没有可导出的数据', 'error');
                return;
            }
            
            const exportBtn = document.getElementById('exportBtn');
            const originalText = exportBtn.textContent;
            
            exportBtn.textContent = '导出中...';
            exportBtn.disabled = true;
            
            try {
                const response = await fetch('/api/v1/grades/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        grades: currentResults
                    })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `成绩查询结果_${new Date().toISOString().split('T')[0]}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    showAlert('导出成功', 'success');
                } else {
                    const data = await response.json();
                    showAlert(data.msg || '导出失败', 'error');
                }
            } catch (error) {
                console.error('导出错误:', error);
                showAlert('网络错误，请稍后重试', 'error');
            } finally {
                exportBtn.textContent = originalText;
                exportBtn.disabled = false;
            }
        }
        
        // 退出登录
        async function logout() {
            if (confirm('确定要退出登录吗？')) {
                try {
                    const response = await fetch('/api/v1/logout', {
                        method: 'POST'
                    });
                    
                    if (response.ok) {
                        window.location.href = '/login';
                    } else {
                        showAlert('退出登录失败', 'error');
                    }
                } catch (error) {
                    console.error('退出登录错误:', error);
                    window.location.href = '/login';
                }
            }
        }
        
        // 显示提示信息
        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert alert-${type}`;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>