# 编码规范文档

## 概述

本文档定义了项目中处理字符编码的标准规范，特别是针对Windows环境下的GBK编码兼容性问题。

## 1. 字符编码基础规范

### 1.1 文件编码
- **强制要求**：所有Python代码文件必须使用UTF-8编码保存
- **文件头声明**：每个Python文件开头必须添加编码声明
```python
# -*- coding: utf-8 -*-
```

### 1.2 Windows兼容性处理

#### 问题描述
Windows控制台默认使用GBK编码，无法正确显示Unicode字符，会导致`UnicodeEncodeError`异常。

#### 解决方案
1. **避免使用Unicode字符**：代码中不直接使用emoji表情符号
2. **使用文本替代符号**：
   - 🎓 → [*]
   - ✅ → [+] 
   - ❌ → [!]
   - 🔧 → [*]
   - 🚀 → [*]
   - 📚 → [*]

## 2. 控制台输出处理

### 2.1 环境配置
```python
import os
import platform

if platform.system() == 'Windows':
    # 设置控制台代码页为UTF-8
    os.system('chcp 65001 >nul 2>&1')
    # 设置环境变量
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    # 设置控制台编码
    import ctypes
    ctypes.windll.kernel32.SetConsoleCP(65001)
    ctypes.windll.kernel32.SetConsoleOutputCP(65001)
```

### 2.2 安全打印函数
实现统一的安全打印函数处理编码异常：

```python
def safe_print(text):
    """安全打印函数，处理Unicode编码问题"""
    try:
        if platform.system() == 'Windows':
            try:
                print(text, flush=True)
            except UnicodeEncodeError:
                print(text.encode('utf-8', 'replace').decode('utf-8'), flush=True)
        else:
            print(text)
    except (UnicodeEncodeError, UnicodeDecodeError):
        # 移除Unicode字符的降级处理
        import re
        unicode_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags
            "]+", flags=re.UNICODE)
        clean_text = unicode_pattern.sub('', text)
        print(clean_text)
```

## 3. 实施指导

### 3.1 代码审查检查点
- [ ] 文件是否包含UTF-8编码声明
- [ ] 是否使用了emoji或特殊Unicode字符
- [ ] 控制台输出是否使用safe_print函数
- [ ] Windows环境下是否正确设置编码

### 3.2 常见问题处理

#### 问题1：UnicodeEncodeError: 'gbk' codec can't encode character
**解决方案**：
1. 检查代码中是否包含emoji字符
2. 使用文本替代符号
3. 确保使用safe_print函数

#### 问题2：控制台显示乱码
**解决方案**：
1. 设置控制台代码页：`chcp 65001`
2. 配置环境变量：`PYTHONIOENCODING=utf-8`
3. 使用ctypes设置控制台编码

### 3.3 测试验证
```python
# 测试编码兼容性
def test_encoding_compatibility():
    """测试编码兼容性"""
    test_strings = [
        "普通中文文本",
        "English text",
        "[*] 替代符号测试",
        "[+] 成功标识",
        "[!] 错误标识"
    ]
    
    for text in test_strings:
        try:
            safe_print(text)
            print(f"✓ 编码测试通过: {text}")
        except Exception as e:
            print(f"✗ 编码测试失败: {text}, 错误: {e}")
```

## 4. 最佳实践

### 4.1 日志记录
```python
import logging

# 配置日志编码
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
```

### 4.2 文件读写
```python
# 读取文件
with open('file.txt', 'r', encoding='utf-8') as f:
    content = f.read()

# 写入文件
with open('file.txt', 'w', encoding='utf-8') as f:
    f.write(content)
```

### 4.3 HTTP响应
```python
from flask import jsonify

# 确保JSON响应使用UTF-8编码
@app.route('/api/data')
def get_data():
    response = jsonify({"message": "中文数据"})
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    return response
```

## 5. 工具和脚本

### 5.1 编码检查脚本
```python
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re

def check_unicode_chars(directory):
    """检查目录中的Unicode字符"""
    unicode_pattern = re.compile(r'[\U0001F600-\U0001F6FF]')
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                filepath = os.path.join(root, file)
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    matches = unicode_pattern.findall(content)
                    if matches:
                        print(f"发现Unicode字符: {filepath} - {matches}")

if __name__ == '__main__':
    check_unicode_chars('.')
```

## 6. 总结

通过遵循本编码规范，可以确保：
1. 代码在不同操作系统下的兼容性
2. 避免Windows环境下的编码错误
3. 提高系统的稳定性和可维护性
4. 统一团队的编码标准

**重要提醒**：所有新增代码都必须遵循本规范，现有代码应逐步改造以符合规范要求。