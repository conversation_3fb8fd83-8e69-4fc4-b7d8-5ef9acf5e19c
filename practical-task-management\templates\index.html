{% extends "base.html" %}

{% block title %}首页 - 实操任务管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2"></i> 系统概览
        </h1>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_tasks or 0 }}</h4>
                        <p class="card-text">总任务数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-list-task fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_categories or 0 }}</h4>
                        <p class="card-text">任务分类</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-tags fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_executions or 0 }}</h4>
                        <p class="card-text">执行记录</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-play-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ stats.total_materials or 0 }}</h4>
                        <p class="card-text">素材文件</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-file-earmark fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i> 快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('web.task_create') }}" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-plus-circle"></i><br>
                            创建新任务
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('web.category_create') }}" class="btn btn-success btn-lg w-100">
                            <i class="bi bi-tag"></i><br>
                            创建分类
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('web.tasks') }}" class="btn btn-info btn-lg w-100">
                            <i class="bi bi-search"></i><br>
                            浏览任务
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('web.executions') }}" class="btn btn-warning btn-lg w-100">
                            <i class="bi bi-graph-up"></i><br>
                            查看报告
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i> 最近创建的任务
                </h5>
            </div>
            <div class="card-body">
                {% if recent_tasks %}
                    <div class="list-group list-group-flush">
                        {% for task in recent_tasks %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ task.title }}</h6>
                                <small class="text-muted">{{ task.software_type }} - {{ task.difficulty_level }}</small>
                            </div>
                            <small class="text-muted">{{ task.created_at.strftime('%m-%d %H:%M') }}</small>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('web.tasks') }}" class="btn btn-outline-primary btn-sm">查看全部任务</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center">暂无任务</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-activity"></i> 最近执行记录
                </h5>
            </div>
            <div class="card-body">
                {% if recent_executions %}
                    <div class="list-group list-group-flush">
                        {% for execution in recent_executions %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ execution.task_title }}</h6>
                                <small class="text-muted">学生ID: {{ execution.student_id }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{{ 'success' if execution.status == 'completed' else 'warning' if execution.status == 'in_progress' else 'secondary' }}">
                                    {{ execution.status }}
                                </span>
                                <br>
                                <small class="text-muted">{{ execution.created_at.strftime('%m-%d %H:%M') }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('web.executions') }}" class="btn btn-outline-primary btn-sm">查看全部记录</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center">暂无执行记录</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}