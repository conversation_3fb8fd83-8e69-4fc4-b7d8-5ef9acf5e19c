# 用户管理模块技术文档

## 一、模块概述

### 1. 模块定位

用户管理模块是系统的基础核心模块，负责所有用户的账号管理、身份认证、权限控制及信息维护，为其他模块（如题库管理、考试编排）提供统一的用户身份与权限支持。

### 2. 核心目标

- 实现用户全生命周期管理（创建、查询、更新、删除）
- 提供安全的身份认证机制
- 基于角色的权限控制（不同角色操作不同功能）
- 支持批量导入 / 导出用户数据（适配 Excel 格式）

## 二、功能范围

### 1. 核心功能清单

| 功能类别       | 具体功能点         | 说明                        |
| ---------- | ------------- | ------------------------- |
| **用户基础管理** | 单个用户创建        | 手动输入用户信息创建账号              |
|            | 批量用户导入（Excel） | 通过 Excel 表格批量创建用户（支持模板下载） |
|            | 用户信息查询        | 按用户名、部门、角色等条件筛选           |
|            | 用户信息编辑        | 修改用户姓名、手机号、部门等非敏感信息       |
|            | 用户状态管理        | 启用 / 禁用账号（禁用后无法登录系统）      |
|            | 用户删除          | 移除用户账号（支持批量删除）            |
| **身份认证**   | 用户登录          | 用户名 + 密码验证，返回认证令牌         |
|            | 密码重置          | 管理员重置用户密码（生成临时密码）         |
|            | 密码修改          | 用户自主修改密码（需验证原密码）          |
|            | 会话管理          | 记录登录状态，超时自动登出             |
| **角色与权限**  | 角色分配          | 为用户指定角色（考生 / 管理员 / 评卷员）   |
|            | 权限关联          | 基于角色绑定可操作的功能权限（如管理员可创建考试） |

## 三、数据设计

### 1. 核心数据实体

#### （1）用户表（user）

| 字段名             | 数据类型     | 长度  | 是否必填 | 约束与说明                                        |
| --------------- | -------- | --- | ---- | -------------------------------------------- |
| user_id         | 字符串      | 32  | 是    | 唯一标识（系统自动生成，如 UUID），主键                       |
| username        | 字符串      | 50  | 是    | 登录账号，唯一索引，支持字母 / 数字 / 下划线                    |
| real_name       | 字符串      | 50  | 是    | 用户真实姓名                                       |
| id_card         | 字符串      | 256 | 否    | 身份证号（AES-256 加密存储，可选填）                       |
| role            | 字符串      | 20  | 是    | 角色类型（枚举值）：student（考生）、admin（管理员）、grader（评卷员） |
| password        | 字符串      | 255 | 是    | bcrypt 加密存储，不存明文                             |
| email           | 字符串      | 100 | 否    | 邮箱地址（需符合格式校验），唯一索引                          |
| phone           | 字符串      | 11  | 否    | 手机号（11 位数字，可选填），索引                          |
| department      | 字符串      | 100 | 否    | 所属部门 / 单位，索引                                 |
| status          | 整数       | 1   | 是    | 账号状态（0 = 禁用，1 = 正常，2 = 锁定）                   |
| login_attempts  | 整数       | 2   | 是    | 登录失败次数，默认 0                                  |
| locked_until    | datetime | -   | 否    | 账号锁定截止时间                                     |
| created_by      | 字符串      | 32  | 否    | 创建人用户ID                                      |
| updated_by      | 字符串      | 32  | 否    | 最后更新人用户ID                                    |
| create_time     | datetime | -   | 是    | 账号创建时间（系统自动记录）                               |
| update_time     | datetime | -   | 是    | 最后更新时间                                       |
| last_login_time | datetime | -   | 否    | 最后登录时间（登录时更新）                                |
| deleted_at      | datetime | -   | 否    | 软删除时间（NULL 表示未删除）                            |

#### （2）角色权限关联表（role_permission）

|字段名|数据类型|长度|是否必填|说明|
|---|---|---|---|---|
|role|字符串|20|是|角色类型（同用户表的 role 字段）|
|permission|字符串|50|是|权限标识（如 “exam:create”= 创建考试权限）|

## 四、接口设计

### 1. 接口规范（遵循全局《接口规范手册》）

- 基础 URL：`/api/v1/user`
- 数据格式：JSON
- 认证方式：除登录接口外，均需在请求头携带令牌 `Authorization: Bearer {token}`

### 2. 核心接口清单

| 接口功能     | 请求方法 | 接口路径              | 输入参数（示例）                                                     | 输出参数（示例）                                                                                                                               |
| -------- | ---- | ----------------- | ------------------------------------------------------------ | -------------------------------------------------------------------------------------------------------------------------------------- |
| 用户登录     | POST | `/login`          | `{"username": "zhangsan", "password": "123456", "captcha": "abc123"}` | `{"code": 200, "msg": "登录成功", "data": {"token": "xxx", "refresh_token": "yyy", "user_id": "u123", "role": "student", "expires_in": 7200}}` |
| 令牌刷新     | POST | `/refresh-token`  | `{"refresh_token": "yyy"}`                                   | `{"code": 200, "msg": "刷新成功", "data": {"token": "new_xxx", "expires_in": 7200}}`                                                    |
| 用户登出     | POST | `/logout`         | `{"token": "xxx"}`                                           | `{"code": 200, "msg": "登出成功"}`                                                                                                         |
| 创建用户     | POST | `/create`         | `{"username": "lisi", "real_name": "李四", "role": "student", "email": "<EMAIL>"}` | `{"code": 200, "msg": "创建成功", "data": {"user_id": "u124", "temp_password": "Abc123"}}`                                              |
| 批量导入用户   | POST | `/batch-import`   | `{"file_url": "/upload/user_template.xlsx"}`（Excel 文件存储路径）   | `{"code": 200, "msg": "导入成功", "data": {"success_count": 50, "fail_count": 2, "fail_list": ["lisi"]}}`                                  |
| 查询用户列表   | GET  | `/list`           | Query 参数：`role=student&department=计算机系&page=1&pageSize=20`   | `{"code": 200, "msg": "查询成功", "data": {"total": 120, "list": [{"user_id": "u123", "username": "zhangsan", "real_name": "张三", ...}]}}` |
| 查询用户详情   | GET  | `/{user_id}`      | 路径参数：`user_id=u123`                                        | `{"code": 200, "msg": "查询成功", "data": {"user_id": "u123", "username": "zhangsan", "real_name": "张三", ...}}`                      |
| 更新用户信息   | PUT  | `/{user_id}`      | `{"real_name": "张三三", "email": "<EMAIL>", "phone": "13800138000"}` | `{"code": 200, "msg": "更新成功"}`                                                                                                         |
| 修改用户状态   | PUT  | `/status`         | `{"user_ids": ["u123", "u124"], "status": 0}`（0 = 禁用，1 = 启用） | `{"code": 200, "msg": "状态更新成功"}`                                                                                                       |
| 批量删除用户   | DELETE | `/batch-delete` | `{"user_ids": ["u123", "u124"]}`                            | `{"code": 200, "msg": "删除成功"}`                                                                                                         |
| 密码重置     | PUT  | `/reset-password` | `{"user_id": "u123"}`                                        | `{"code": 200, "msg": "密码重置成功", "data": {"temp_password": "Abc123"}}`                                                                  |
| 修改密码     | PUT  | `/change-password` | `{"old_password": "123456", "new_password": "654321"}`        | `{"code": 200, "msg": "密码修改成功"}`                                                                                                       |
| 获取验证码    | GET  | `/captcha`        | 无                                                          | `{"code": 200, "msg": "获取成功", "data": {"captcha_id": "cap123", "captcha_image": "base64_image"}}`                                    |

## 五、业务流程

### 1. 批量用户导入流程

```
1. 用户下载导入模板 → 2. 填写用户信息 → 3. 上传Excel文件 → 4. 系统校验数据格式
   ↓
5. 数据校验通过 → 6. 批量插入数据库 → 7. 返回导入结果（成功/失败统计）
   ↓（如有失败）
8. 生成错误报告 → 9. 用户下载错误文件 → 10. 修正后重新导入
```

### 2. 用户登录流程

```
1. 用户输入账号密码 → 2. 获取验证码 → 3. 提交登录请求 → 4. 验证码校验
   ↓
5. 账号密码验证 → 6. 检查账号状态 → 7. 检查登录失败次数 → 8. 生成JWT令牌
   ↓
9. 记录登录日志 → 10. 返回令牌和用户信息 → 11. 前端存储令牌 → 12. 跳转到主页
```

### 3. 权限验证流程

```
1. 前端发起请求 → 2. 携带JWT令牌 → 3. 后端验证令牌有效性 → 4. 解析用户角色
   ↓
5. 检查接口权限 → 6. 权限验证通过 → 7. 执行业务逻辑 → 8. 返回结果
```

## 六、非功能需求

### 1. 安全性

- 密码存储：采用 bcrypt 或 Argon2 加密算法，禁止明文存储
- JWT 令牌管理：设置合理的过期时间（2小时），支持令牌刷新和黑名单机制
- 登录安全：连续登录失败5次后锁定账号30分钟，支持图形验证码
- 权限控制：严格校验操作权限（如考生无法访问 "用户管理" 功能）
- 敏感数据保护：身份证号使用 AES-256 对称加密存储
- 日志审计：记录所有关键操作（登录、批量导入、密码重置），包含操作人、时间、IP 地址

### 2. 性能

- 响应时间：单用户操作（如登录、查询）≤1 秒；批量导入（1000 条数据）≤10 秒
- 并发支持：支持 50 人同时在线操作（查询、编辑用户）

### 3. 易用性

- 提供 Excel 导入模板（含字段说明），支持错误数据下载（标注失败原因）
- 用户列表支持导出 Excel（含用户基本信息，不含密码等敏感数据）

## 七、依赖与约束

### 1. 依赖模块

- 无前置依赖模块，可独立开发（为系统第一个开发的核心模块）

### 2. 外部约束

- 数据库：开发环境使用 SQLite（轻量级，无需额外配置）；生产环境可升级至 MySQL 8.0+
- 浏览器兼容性：支持 Chrome 90+、Edge 90+、Firefox 88+
- 数据导入：仅支持.xlsx 格式 Excel 文件（单个文件≤10MB）
- 权限关联：需与后续开发的 "权限管理模块" 对接（当前暂存角色信息）

## 八、交付物清单

1. 模块部署包（含可执行程序）
2. 用户操作手册（含管理员操作步骤、Excel 模板使用说明）
3. 接口文档（含所有接口的输入输出、参数说明）
4. 测试报告（含功能测试、性能测试结果）