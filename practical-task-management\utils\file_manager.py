# -*- coding: utf-8 -*-
"""
文件管理工具模块
提供文件上传、下载、版本控制等功能
"""

import os
import hashlib
import shutil
from datetime import datetime
from werkzeug.utils import secure_filename
from typing import Dict, List, Optional, Tuple
import mimetypes
import zipfile

class FileManager:
    """
    文件管理器类
    负责处理文件的上传、下载、存储和版本控制
    """
    
    def __init__(self, base_upload_path: str = 'uploads'):
        """
        初始化文件管理器
        
        参数:
            base_upload_path: 基础上传路径
        """
        self.base_upload_path = os.path.abspath(base_upload_path)
        self.allowed_extensions = {
            'document': {'doc', 'docx', 'pdf', 'txt', 'rtf'},
            'spreadsheet': {'xls', 'xlsx', 'csv'},
            'presentation': {'ppt', 'pptx'},
            'image': {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'},
            'video': {'mp4', 'avi', 'mov', 'wmv', 'flv'},
            'audio': {'mp3', 'wav', 'wma', 'aac'},
            'archive': {'zip', 'rar', '7z', 'tar', 'gz'},
            'code': {'py', 'java', 'cpp', 'c', 'js', 'html', 'css', 'sql'},
            'other': {'exe', 'msi', 'dmg', 'deb', 'rpm'}
        }
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        
        # 确保上传目录存在
        self._ensure_directory_exists(self.base_upload_path)
    
    def _ensure_directory_exists(self, directory: str) -> None:
        """
        确保目录存在，如果不存在则创建
        
        参数:
            directory: 目录路径
        """
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
    
    def _get_file_hash(self, file_path: str) -> str:
        """
        计算文件的MD5哈希值
        
        参数:
            file_path: 文件路径
        
        返回:
            文件的MD5哈希值
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ''
    
    def _get_file_type(self, filename: str) -> str:
        """
        根据文件扩展名获取文件类型
        
        参数:
            filename: 文件名
        
        返回:
            文件类型
        """
        if not filename or '.' not in filename:
            return 'other'
        
        extension = filename.rsplit('.', 1)[1].lower()
        
        for file_type, extensions in self.allowed_extensions.items():
            if extension in extensions:
                return file_type
        
        return 'other'
    
    def _get_mime_type(self, filename: str) -> str:
        """
        获取文件的MIME类型
        
        参数:
            filename: 文件名
        
        返回:
            MIME类型
        """
        mime_type, _ = mimetypes.guess_type(filename)
        return mime_type or 'application/octet-stream'
    
    def is_allowed_file(self, filename: str) -> bool:
        """
        检查文件是否允许上传
        
        参数:
            filename: 文件名
        
        返回:
            是否允许上传
        """
        if not filename or '.' not in filename:
            return False
        
        extension = filename.rsplit('.', 1)[1].lower()
        all_extensions = set()
        for extensions in self.allowed_extensions.values():
            all_extensions.update(extensions)
        
        return extension in all_extensions
    
    def get_upload_path(self, task_id: int, material_type: str = 'general') -> str:
        """
        获取上传路径
        
        参数:
            task_id: 任务ID
            material_type: 素材类型
        
        返回:
            上传路径
        """
        upload_path = os.path.join(
            self.base_upload_path,
            'tasks',
            str(task_id),
            material_type
        )
        self._ensure_directory_exists(upload_path)
        return upload_path
    
    def save_uploaded_file(self, file, task_id: int, material_type: str = 'general', 
                          version: str = '1.0') -> Dict:
        """
        保存上传的文件
        
        参数:
            file: 上传的文件对象
            task_id: 任务ID
            material_type: 素材类型
            version: 版本号
        
        返回:
            文件信息字典
        """
        try:
            # 验证文件
            if not file or not file.filename:
                raise ValueError('没有选择文件')
            
            if not self.is_allowed_file(file.filename):
                raise ValueError('不支持的文件类型')
            
            # 检查文件大小
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置到文件开头
            
            if file_size > self.max_file_size:
                raise ValueError(f'文件大小超过限制 ({self.max_file_size / 1024 / 1024:.1f}MB)')
            
            # 生成安全的文件名
            original_filename = file.filename
            secure_name = secure_filename(original_filename)
            
            # 添加时间戳避免文件名冲突
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            name_parts = secure_name.rsplit('.', 1)
            if len(name_parts) == 2:
                filename = f"{name_parts[0]}_{timestamp}_v{version}.{name_parts[1]}"
            else:
                filename = f"{secure_name}_{timestamp}_v{version}"
            
            # 获取上传路径
            upload_path = self.get_upload_path(task_id, material_type)
            file_path = os.path.join(upload_path, filename)
            
            # 保存文件
            file.save(file_path)
            
            # 计算文件哈希
            file_hash = self._get_file_hash(file_path)
            
            # 获取文件信息
            file_info = {
                'original_filename': original_filename,
                'stored_filename': filename,
                'file_path': file_path,
                'relative_path': os.path.relpath(file_path, self.base_upload_path),
                'file_size': file_size,
                'file_hash': file_hash,
                'file_type': self._get_file_type(original_filename),
                'mime_type': self._get_mime_type(original_filename),
                'version': version,
                'upload_time': datetime.now().isoformat()
            }
            
            return file_info
            
        except Exception as e:
            raise Exception(f'文件保存失败: {str(e)}')
    
    def get_file_info(self, file_path: str) -> Optional[Dict]:
        """
        获取文件信息
        
        参数:
            file_path: 文件路径
        
        返回:
            文件信息字典或None
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            filename = os.path.basename(file_path)
            
            return {
                'filename': filename,
                'file_path': file_path,
                'file_size': stat.st_size,
                'file_hash': self._get_file_hash(file_path),
                'file_type': self._get_file_type(filename),
                'mime_type': self._get_mime_type(filename),
                'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
            }
            
        except Exception:
            return None
    
    def delete_file(self, file_path: str) -> bool:
        """
        删除文件
        
        参数:
            file_path: 文件路径
        
        返回:
            是否删除成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                
                # 如果目录为空，删除目录
                directory = os.path.dirname(file_path)
                if os.path.exists(directory) and not os.listdir(directory):
                    os.rmdir(directory)
                
                return True
            return False
            
        except Exception:
            return False
    
    def copy_file(self, source_path: str, target_path: str) -> bool:
        """
        复制文件
        
        参数:
            source_path: 源文件路径
            target_path: 目标文件路径
        
        返回:
            是否复制成功
        """
        try:
            if not os.path.exists(source_path):
                return False
            
            # 确保目标目录存在
            target_dir = os.path.dirname(target_path)
            self._ensure_directory_exists(target_dir)
            
            shutil.copy2(source_path, target_path)
            return True
            
        except Exception:
            return False
    
    def create_backup(self, file_path: str) -> Optional[str]:
        """
        创建文件备份
        
        参数:
            file_path: 文件路径
        
        返回:
            备份文件路径或None
        """
        try:
            if not os.path.exists(file_path):
                return None
            
            # 生成备份文件名
            directory = os.path.dirname(file_path)
            filename = os.path.basename(file_path)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            name_parts = filename.rsplit('.', 1)
            if len(name_parts) == 2:
                backup_filename = f"{name_parts[0]}_backup_{timestamp}.{name_parts[1]}"
            else:
                backup_filename = f"{filename}_backup_{timestamp}"
            
            backup_path = os.path.join(directory, 'backups', backup_filename)
            
            # 创建备份
            if self.copy_file(file_path, backup_path):
                return backup_path
            
            return None
            
        except Exception:
            return None
    
    def get_file_versions(self, task_id: int, base_filename: str) -> List[Dict]:
        """
        获取文件的所有版本
        
        参数:
            task_id: 任务ID
            base_filename: 基础文件名
        
        返回:
            版本列表
        """
        try:
            versions = []
            upload_path = self.get_upload_path(task_id)
            
            if not os.path.exists(upload_path):
                return versions
            
            # 获取基础文件名（不含扩展名）
            base_name = base_filename.rsplit('.', 1)[0] if '.' in base_filename else base_filename
            
            # 遍历目录查找版本文件
            for filename in os.listdir(upload_path):
                if filename.startswith(base_name):
                    file_path = os.path.join(upload_path, filename)
                    file_info = self.get_file_info(file_path)
                    if file_info:
                        versions.append(file_info)
            
            # 按修改时间排序
            versions.sort(key=lambda x: x['modified_time'], reverse=True)
            return versions
            
        except Exception:
            return []
    
    def create_archive(self, file_paths: List[str], archive_path: str) -> bool:
        """
        创建压缩包
        
        参数:
            file_paths: 文件路径列表
            archive_path: 压缩包路径
        
        返回:
            是否创建成功
        """
        try:
            # 确保目标目录存在
            archive_dir = os.path.dirname(archive_path)
            self._ensure_directory_exists(archive_dir)
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in file_paths:
                    if os.path.exists(file_path):
                        # 使用相对路径作为压缩包内的文件名
                        arcname = os.path.basename(file_path)
                        zipf.write(file_path, arcname)
            
            return True
            
        except Exception:
            return False
    
    def extract_archive(self, archive_path: str, extract_path: str) -> bool:
        """
        解压压缩包
        
        参数:
            archive_path: 压缩包路径
            extract_path: 解压路径
        
        返回:
            是否解压成功
        """
        try:
            if not os.path.exists(archive_path):
                return False
            
            # 确保解压目录存在
            self._ensure_directory_exists(extract_path)
            
            with zipfile.ZipFile(archive_path, 'r') as zipf:
                zipf.extractall(extract_path)
            
            return True
            
        except Exception:
            return False
    
    def get_directory_size(self, directory: str) -> int:
        """
        获取目录大小
        
        参数:
            directory: 目录路径
        
        返回:
            目录大小（字节）
        """
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
        except Exception:
            pass
        
        return total_size
    
    def cleanup_old_files(self, directory: str, days: int = 30) -> int:
        """
        清理旧文件
        
        参数:
            directory: 目录路径
            days: 保留天数
        
        返回:
            删除的文件数量
        """
        deleted_count = 0
        try:
            current_time = datetime.now().timestamp()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        file_mtime = os.path.getmtime(file_path)
                        if file_mtime < cutoff_time:
                            if self.delete_file(file_path):
                                deleted_count += 1
        except Exception:
            pass
        
        return deleted_count