{% extends "base.html" %}

{% block title %}执行记录 - 实操任务管理系统{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">执行记录</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>
                <i class="bi bi-clipboard-check"></i> 执行记录管理
            </h1>
            <div>
                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#batchEvaluateModal">
                    <i class="bi bi-check2-all"></i> 批量评价
                </button>
                <button type="button" class="btn btn-primary" onclick="exportReport()">
                    <i class="bi bi-file-earmark-excel"></i> 导出报告
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-clipboard-data display-4 text-primary"></i>
                <h4 class="mt-2">{{ stats.total_executions or 0 }}</h4>
                <p class="text-muted mb-0">总执行记录</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-hourglass-split display-4 text-warning"></i>
                <h4 class="mt-2">{{ stats.pending_evaluations or 0 }}</h4>
                <p class="text-muted mb-0">待评价</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-check-circle display-4 text-success"></i>
                <h4 class="mt-2">{{ stats.completed_evaluations or 0 }}</h4>
                <p class="text-muted mb-0">已评价</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="bi bi-graph-up display-4 text-info"></i>
                <h4 class="mt-2">{{ '%.1f'|format(stats.average_score or 0) }}</h4>
                <p class="text-muted mb-0">平均分</p>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">搜索关键词</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ request.args.get('search', '') }}" placeholder="学生姓名或任务标题">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">执行状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            <option value="started" {{ 'selected' if request.args.get('status') == 'started' }}>进行中</option>
                            <option value="submitted" {{ 'selected' if request.args.get('status') == 'submitted' }}>已提交</option>
                            <option value="evaluated" {{ 'selected' if request.args.get('status') == 'evaluated' }}>已评价</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="task_id" class="form-label">任务</label>
                        <select class="form-select" id="task_id" name="task_id">
                            <option value="">全部任务</option>
                            {% for task in tasks %}
                            <option value="{{ task.id }}" {{ 'selected' if request.args.get('task_id')|int == task.id }}>{{ task.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_range" class="form-label">时间范围</label>
                        <select class="form-select" id="date_range" name="date_range">
                            <option value="">全部时间</option>
                            <option value="today" {{ 'selected' if request.args.get('date_range') == 'today' }}>今天</option>
                            <option value="week" {{ 'selected' if request.args.get('date_range') == 'week' }}>本周</option>
                            <option value="month" {{ 'selected' if request.args.get('date_range') == 'month' }}>本月</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="sort_by" class="form-label">排序方式</label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="start_time" {{ 'selected' if request.args.get('sort_by') == 'start_time' }}>开始时间</option>
                            <option value="submit_time" {{ 'selected' if request.args.get('sort_by') == 'submit_time' }}>提交时间</option>
                            <option value="score" {{ 'selected' if request.args.get('sort_by') == 'score' }}>得分</option>
                            <option value="duration" {{ 'selected' if request.args.get('sort_by') == 'duration' }}>用时</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i>
                            </button>
                            <a href="{{ url_for('web.executions') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i>
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 执行记录列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">执行记录列表 (共 {{ executions|length }} 条)</h5>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    <label class="form-check-label" for="selectAll">全选</label>
                </div>
            </div>
            <div class="card-body p-0">
                {% if executions %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th width="50">
                                    <input type="checkbox" class="form-check-input" id="headerCheckbox">
                                </th>
                                <th>学生信息</th>
                                <th>任务信息</th>
                                <th>执行状态</th>
                                <th>用时</th>
                                <th>得分</th>
                                <th>提交时间</th>
                                <th width="150">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for execution in executions %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="form-check-input execution-checkbox" value="{{ execution.id }}">
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ execution.student_name }}</strong>
                                        <br><small class="text-muted">ID: {{ execution.student_id }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ execution.task_title }}</strong>
                                        <br><small class="text-muted">{{ execution.task_category }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if execution.status == 'started' %}
                                    <span class="badge bg-warning">进行中</span>
                                    {% elif execution.status == 'submitted' %}
                                    <span class="badge bg-info">已提交</span>
                                    {% elif execution.status == 'evaluated' %}
                                    <span class="badge bg-success">已评价</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.duration_minutes %}
                                    {{ execution.duration_minutes }} 分钟
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.score is not none %}
                                    <span class="badge bg-{{ 'success' if execution.score >= 80 else 'warning' if execution.score >= 60 else 'danger' }}">
                                        {{ execution.score }}/{{ execution.max_score }}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">未评分</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if execution.submit_time %}
                                    {{ execution.submit_time.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                    <span class="text-muted">未提交</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="viewExecution({{ execution.id }})" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        {% if execution.status == 'submitted' %}
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="evaluateExecution({{ execution.id }})" title="评价">
                                            <i class="bi bi-check-circle"></i>
                                        </button>
                                        {% endif %}
                                        <button type="button" class="btn btn-outline-info" 
                                                onclick="downloadReport({{ execution.id }})" title="下载报告">
                                            <i class="bi bi-download"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-clipboard-check display-1 text-muted"></i>
                    <h4 class="text-muted mt-3">暂无执行记录</h4>
                    <p class="text-muted">学生开始执行任务后，记录将显示在这里</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 执行详情模态框 -->
<div class="modal fade" id="executionDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clipboard-check"></i> 执行详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="executionDetailContent">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 关闭
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 评价模态框 -->
<div class="modal fade" id="evaluateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-check-circle"></i> 任务评价
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="evaluateForm">
                    <input type="hidden" id="evaluateExecutionId" name="execution_id">
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="evaluateScore" class="form-label">得分 <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="evaluateScore" name="score" 
                                   min="0" max="100" required>
                        </div>
                        <div class="col-md-6">
                            <label for="evaluateMaxScore" class="form-label">满分</label>
                            <input type="number" class="form-control" id="evaluateMaxScore" name="max_score" 
                                   value="100" readonly>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="evaluateFeedback" class="form-label">评价反馈</label>
                        <textarea class="form-control" id="evaluateFeedback" name="feedback" rows="4" 
                                  placeholder="请输入对学生作业的评价和建议..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">评价标准</label>
                        <div id="evaluationCriteria">
                            <!-- 动态加载评价标准 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-success" onclick="submitEvaluation()">
                    <i class="bi bi-check-circle"></i> 提交评价
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量评价模态框 -->
<div class="modal fade" id="batchEvaluateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-check2-all"></i> 批量评价
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="batchEvaluateForm">
                    <div class="mb-3">
                        <label for="batchScore" class="form-label">统一得分 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="batchScore" name="score" 
                               min="0" max="100" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="batchFeedback" class="form-label">统一反馈</label>
                        <textarea class="form-control" id="batchFeedback" name="feedback" rows="3" 
                                  placeholder="请输入统一的评价反馈..."></textarea>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        将对所选的 <span id="selectedCount">0</span> 条记录进行批量评价
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-success" onclick="submitBatchEvaluation()">
                    <i class="bi bi-check2-all"></i> 批量评价
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.execution-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    
    updateSelectedCount();
}

// 更新选中数量
function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.execution-checkbox:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
}

// 查看执行详情
function viewExecution(executionId) {
    fetch(`/api/v1/executions/${executionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                const execution = data.data;
                const content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>学生信息</h6>
                            <table class="table table-sm">
                                <tr><td>姓名</td><td>${execution.student_name}</td></tr>
                                <tr><td>学号</td><td>${execution.student_id}</td></tr>
                            </table>
                            
                            <h6>任务信息</h6>
                            <table class="table table-sm">
                                <tr><td>任务标题</td><td>${execution.task_title}</td></tr>
                                <tr><td>任务分类</td><td>${execution.task_category}</td></tr>
                                <tr><td>满分</td><td>${execution.max_score}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>执行信息</h6>
                            <table class="table table-sm">
                                <tr><td>状态</td><td>${getStatusBadge(execution.status)}</td></tr>
                                <tr><td>开始时间</td><td>${new Date(execution.start_time).toLocaleString()}</td></tr>
                                <tr><td>提交时间</td><td>${execution.submit_time ? new Date(execution.submit_time).toLocaleString() : '未提交'}</td></tr>
                                <tr><td>用时</td><td>${execution.duration_minutes ? execution.duration_minutes + ' 分钟' : '未完成'}</td></tr>
                                <tr><td>得分</td><td>${execution.score !== null ? execution.score + '/' + execution.max_score : '未评分'}</td></tr>
                            </table>
                            
                            <h6>评价反馈</h6>
                            <p>${execution.feedback || '暂无反馈'}</p>
                        </div>
                    </div>
                    
                    ${execution.submission_data ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>提交内容</h6>
                            <div class="border p-3 bg-light">
                                <pre>${JSON.stringify(execution.submission_data, null, 2)}</pre>
                            </div>
                        </div>
                    </div>
                    ` : ''}
                `;
                document.getElementById('executionDetailContent').innerHTML = content;
                
                const modal = new bootstrap.Modal(document.getElementById('executionDetailModal'));
                modal.show();
            } else {
                alert('获取执行详情失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('获取执行详情失败: ' + error.message);
        });
}

// 评价执行记录
function evaluateExecution(executionId) {
    fetch(`/api/v1/executions/${executionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                const execution = data.data;
                
                document.getElementById('evaluateExecutionId').value = executionId;
                document.getElementById('evaluateMaxScore').value = execution.max_score;
                
                // 加载评价标准
                if (execution.scoring_criteria) {
                    const criteria = JSON.parse(execution.scoring_criteria);
                    let criteriaHtml = '';
                    criteria.forEach(criterion => {
                        criteriaHtml += `
                            <div class="mb-2">
                                <label class="form-label">${criterion.name} (${criterion.weight}%)</label>
                                <div class="form-text">${criterion.description}</div>
                            </div>
                        `;
                    });
                    document.getElementById('evaluationCriteria').innerHTML = criteriaHtml;
                }
                
                const modal = new bootstrap.Modal(document.getElementById('evaluateModal'));
                modal.show();
            } else {
                alert('获取执行信息失败: ' + data.msg);
            }
        })
        .catch(error => {
            alert('获取执行信息失败: ' + error.message);
        });
}

// 提交评价
function submitEvaluation() {
    const form = document.getElementById('evaluateForm');
    const formData = new FormData(form);
    const executionId = formData.get('execution_id');
    
    const data = {
        score: parseInt(formData.get('score')),
        feedback: formData.get('feedback')
    };
    
    fetch(`/api/v1/executions/${executionId}/evaluate`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            location.reload();
        } else {
            alert('评价失败: ' + data.msg);
        }
    })
    .catch(error => {
        alert('评价失败: ' + error.message);
    });
}

// 批量评价
function submitBatchEvaluation() {
    const selectedCheckboxes = document.querySelectorAll('.execution-checkbox:checked');
    const executionIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));
    
    if (executionIds.length === 0) {
        alert('请先选择要评价的记录');
        return;
    }
    
    const form = document.getElementById('batchEvaluateForm');
    const formData = new FormData(form);
    
    const data = {
        execution_ids: executionIds,
        score: parseInt(formData.get('score')),
        feedback: formData.get('feedback')
    };
    
    fetch('/api/v1/executions/batch-evaluate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            location.reload();
        } else {
            alert('批量评价失败: ' + data.msg);
        }
    })
    .catch(error => {
        alert('批量评价失败: ' + error.message);
    });
}

// 下载报告
function downloadReport(executionId) {
    window.open(`/api/v1/executions/${executionId}/report`, '_blank');
}

// 导出报告
function exportReport() {
    const params = new URLSearchParams(window.location.search);
    window.open(`/api/v1/executions/export?${params.toString()}`, '_blank');
}

// 获取状态徽章
function getStatusBadge(status) {
    const badges = {
        'started': '<span class="badge bg-warning">进行中</span>',
        'submitted': '<span class="badge bg-info">已提交</span>',
        'evaluated': '<span class="badge bg-success">已评价</span>'
    };
    return badges[status] || status;
}

// 监听复选框变化
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('execution-checkbox')) {
        updateSelectedCount();
    }
});

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    updateSelectedCount();
});
</script>
{% endblock %}