#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_excel_structure():
    """检查Excel文件的实际结构"""
    filepath = r'd:\61-PHRL_question_bank\uploads\Question-Bank-Template_SPCT-4-LL_UPDATED.xlsx'
    
    print(f"检查Excel文件: {filepath}")
    print("=" * 50)
    
    try:
        # 方法1: 读取前两行作为表头
        print("\n方法1: 使用header=[0,1]读取")
        df1 = pd.read_excel(filepath, header=[0, 1], engine='openpyxl')
        print(f"列名类型: {type(df1.columns)}")
        print(f"列名数量: {len(df1.columns)}")
        print("前5个列名:")
        for i, col in enumerate(df1.columns[:5]):
            print(f"  {i}: {col} (类型: {type(col)})")
            if isinstance(col, tuple):
                print(f"      第一部分: '{col[0]}' (类型: {type(col[0])})")
                print(f"      第二部分: '{col[1]}' (类型: {type(col[1])})")
        
        # 方法2: 只读取第一行作为表头
        print("\n方法2: 使用header=0读取")
        df2 = pd.read_excel(filepath, header=0, engine='openpyxl')
        print(f"列名类型: {type(df2.columns)}")
        print(f"列名数量: {len(df2.columns)}")
        print("前5个列名:")
        for i, col in enumerate(df2.columns[:5]):
            print(f"  {i}: '{col}' (类型: {type(col)})")
        
        # 方法3: 不指定表头，查看原始数据
        print("\n方法3: 不指定表头，查看原始前3行")
        df3 = pd.read_excel(filepath, header=None, engine='openpyxl')
        print("前3行数据:")
        for i in range(min(3, len(df3))):
            print(f"  第{i+1}行: {list(df3.iloc[i][:5])}")
        
        # 检查是否有'试题（题干）'相关的列
        print("\n查找包含'试题（题干）'的列:")
        for i, col in enumerate(df1.columns):
            if isinstance(col, tuple):
                if '试题' in str(col[0]) and '题干' in str(col[0]):
                    print(f"  找到: 位置{i}, 列名: {col}")
            elif isinstance(col, str):
                if '试题' in col and '题干' in col:
                    print(f"  找到: 位置{i}, 列名: '{col}'")
        
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_excel_structure()