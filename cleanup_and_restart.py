# -*- coding: utf-8 -*-
"""
系统清理和重启脚本
功能：停止所有相关Python进程，清理占用端口，重新启动主启动器
作者：系统管理员
日期：2024
"""

import os
import sys
import time
import psutil
import subprocess
import signal
from typing import List, Set

# 系统配置
SYSTEM_PORTS = [5001, 5002, 5003, 5004, 5005, 5006, 5007, 5008, 5009, 5010, 5011, 5012, 5013, 5014, 8000, 8080]
MODULE_NAMES = [
    'user-management', 'question_bank', 'exam_arrangement', 'scoring_management',
    'exam_score_reporting_interface', 'monitoring_audit', 'audit_log', 'api_gateway',
    'backup_recovery', 'notification_center', 'score_query', 'system_config',
    'practical_task_management', 'achievement_file_management', 'file_project_management_tools'
]

def safe_print(message: str) -> None:
    """安全打印函数，处理Windows控制台编码问题
    
    Args:
        message: 要打印的消息
    """
    try:
        print(message)
    except UnicodeEncodeError:
        # 移除或替换不兼容的Unicode字符
        safe_message = message.encode('ascii', 'ignore').decode('ascii')
        print(safe_message)

def get_python_processes() -> List[psutil.Process]:
    """获取所有Python进程
    
    Returns:
        Python进程列表
    """
    python_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                python_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return python_processes

def get_processes_on_ports(ports: List[int]) -> List[psutil.Process]:
    """获取占用指定端口的进程
    
    Args:
        ports: 端口列表
        
    Returns:
        占用端口的进程列表
    """
    processes = []
    for conn in psutil.net_connections():
        if conn.laddr and conn.laddr.port in ports:
            try:
                proc = psutil.Process(conn.pid)
                processes.append(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    return processes

def kill_process_safely(proc: psutil.Process) -> bool:
    """安全终止进程
    
    Args:
        proc: 要终止的进程
        
    Returns:
        是否成功终止
    """
    try:
        safe_print(f"正在终止进程: PID={proc.pid}, Name={proc.name()}")
        
        # 首先尝试优雅终止
        proc.terminate()
        
        # 等待进程终止
        try:
            proc.wait(timeout=5)
            safe_print(f"进程 {proc.pid} 已优雅终止")
            return True
        except psutil.TimeoutExpired:
            # 强制终止
            safe_print(f"强制终止进程 {proc.pid}")
            proc.kill()
            proc.wait(timeout=3)
            return True
            
    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
        safe_print(f"终止进程 {proc.pid} 时出错: {e}")
        return False

def cleanup_system_processes() -> None:
    """清理系统相关进程"""
    safe_print("[*] 开始清理系统进程...")
    
    # 获取所有Python进程
    python_processes = get_python_processes()
    safe_print(f"发现 {len(python_processes)} 个Python进程")
    
    # 获取占用系统端口的进程
    port_processes = get_processes_on_ports(SYSTEM_PORTS)
    safe_print(f"发现 {len(port_processes)} 个占用系统端口的进程")
    
    # 合并进程列表并去重
    all_processes = set()
    for proc in python_processes + port_processes:
        try:
            # 检查是否是系统相关进程
            cmdline = ' '.join(proc.cmdline())
            if any(module in cmdline for module in MODULE_NAMES) or 'main_launcher.py' in cmdline:
                all_processes.add(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    safe_print(f"需要清理 {len(all_processes)} 个相关进程")
    
    # 终止进程
    for proc in all_processes:
        kill_process_safely(proc)
    
    # 等待进程完全终止
    time.sleep(2)
    safe_print("[+] 进程清理完成")

def cleanup_ports() -> None:
    """清理占用的端口"""
    safe_print("[*] 检查端口占用情况...")
    
    occupied_ports = []
    for conn in psutil.net_connections():
        if conn.laddr and conn.laddr.port in SYSTEM_PORTS:
            occupied_ports.append(conn.laddr.port)
    
    if occupied_ports:
        safe_print(f"发现占用端口: {occupied_ports}")
        # 再次尝试清理占用端口的进程
        port_processes = get_processes_on_ports(occupied_ports)
        for proc in port_processes:
            kill_process_safely(proc)
        time.sleep(1)
    else:
        safe_print("[+] 所有系统端口已释放")

def start_main_launcher() -> subprocess.Popen:
    """启动主启动器
    
    Returns:
        主启动器进程对象
    """
    safe_print("[*] 启动主启动器...")
    
    # 设置启动参数
    creation_flags = 0
    if os.name == 'nt':  # Windows系统
        creation_flags = subprocess.CREATE_NO_WINDOW
    
    try:
        # 启动主启动器
        proc = subprocess.Popen(
            [sys.executable, 'main_launcher.py'],
            cwd=os.path.dirname(os.path.abspath(__file__)),
            creationflags=creation_flags,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        safe_print(f"[+] 主启动器已启动，PID: {proc.pid}")
        safe_print("[+] 主控台地址: http://localhost:8000")
        
        return proc
        
    except Exception as e:
        safe_print(f"[!] 启动主启动器失败: {e}")
        return None

def wait_for_system_ready() -> bool:
    """等待系统就绪
    
    Returns:
        系统是否就绪
    """
    safe_print("[*] 等待系统启动...")
    
    import requests
    
    for i in range(30):  # 最多等待30秒
        try:
            response = requests.get('http://localhost:8000', timeout=2)
            if response.status_code == 200:
                safe_print("[+] 系统启动成功！")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 0:
            safe_print(f"等待中... ({i+1}/30)")
    
    safe_print("[!] 系统启动超时")
    return False

def main() -> None:
    """主函数"""
    safe_print("="*50)
    safe_print("    局域网在线考试系统 - 清理重启工具")
    safe_print("="*50)
    
    try:
        # 步骤1: 清理进程
        cleanup_system_processes()
        
        # 步骤2: 清理端口
        cleanup_ports()
        
        # 步骤3: 等待清理完成
        safe_print("[*] 等待清理完成...")
        time.sleep(3)
        
        # 步骤4: 启动主启动器
        launcher_proc = start_main_launcher()
        if not launcher_proc:
            safe_print("[!] 启动失败，请检查系统配置")
            return
        
        # 步骤5: 等待系统就绪
        if wait_for_system_ready():
            safe_print("[+] 系统清理重启完成！")
            safe_print("[+] 请访问 http://localhost:8000 使用系统")
        else:
            safe_print("[!] 系统启动异常，请检查日志")
            
    except KeyboardInterrupt:
        safe_print("\n[!] 用户中断操作")
    except Exception as e:
        safe_print(f"[!] 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()