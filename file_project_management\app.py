#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件与项目管理工具模块 - 主应用入口

本模块提供文件管理和项目管理功能的Flask应用入口。
集成到局域网在线考试系统中，通过API网关统一访问。

作者: 系统开发团队
创建时间: 2024-01-01
版本: v1.0
"""

import os
import sys
from flask import Flask, jsonify, request
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入配置和扩展
from config import config
from app.extensions import db, jwt, cors

def create_app(config_name=None):
    """
    应用工厂函数
    
    Args:
        config_name (str): 配置环境名称
        
    Returns:
        Flask: 配置好的Flask应用实例
    """
    # 获取配置名称
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    cors.init_app(app)
    
    # 注册蓝图
    from app.api.files import files_bp
    from app.api.projects import projects_bp
    from app.api.tasks import tasks_bp
    
    app.register_blueprint(files_bp)
    app.register_blueprint(projects_bp)
    app.register_blueprint(tasks_bp)
    
    # 主页路由
    @app.route('/')
    def index():
        """主页路由，返回API测试页面"""
        return app.send_static_file('index.html')
    
    # 健康检查接口
    @app.route('/api/health', methods=['GET'])
    def health_check():
        """
        系统健康检查接口
        
        Returns:
            dict: 系统状态信息
        """
        return jsonify({
            'code': 200,
            'msg': 'success',
            'data': {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            }
        })
    
    # 系统信息接口
    @app.route('/api/info', methods=['GET'])
    def system_info():
        """
        获取系统信息接口
        
        Returns:
            dict: 系统详细信息
        """
        return jsonify({
            'code': 200,
            'msg': 'success',
            'data': {
                'name': '文件与项目管理工具',
                'version': '1.0.0',
                'description': '专注于OFFICE文件传输管理和简单项目进度管理的工具模块',
                'features': [
                    '文件上传下载管理',
                    '项目进度跟踪',
                    '任务分配管理',
                    '文件搜索和链接',
                    '简单的项目协作'
                ]
            }
        })
    
    # 全局错误处理
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'code': 404, 'msg': '接口不存在', 'data': {}}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return jsonify({'code': 500, 'msg': '服务器内部错误', 'data': {}}), 500
    
    @app.errorhandler(413)
    def file_too_large(error):
        return jsonify({'code': 413, 'msg': '文件大小超过限制', 'data': {}}), 413
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({'code': 400, 'msg': '请求参数错误', 'data': {}}), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({'code': 401, 'msg': '未授权访问', 'data': {}}), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        return jsonify({'code': 403, 'msg': '无权限访问', 'data': {}}), 403
    
    return app

def init_database(app):
    """
    初始化数据库
    
    创建所有数据库表，如果表已存在则跳过。
    
    参数:
        app (Flask): Flask应用实例
    """
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            print("数据库表创建成功")
            
            # 创建必要目录
            upload_folder = app.config.get('UPLOAD_FOLDER')
            if upload_folder and not os.path.exists(upload_folder):
                os.makedirs(upload_folder, exist_ok=True)
                print(f"上传目录创建成功: {upload_folder}")
                
            # 创建日志目录
            log_dir = os.path.dirname(app.config.get('LOG_FILE', 'logs/app.log'))
            if not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
                print(f"日志目录创建成功: {log_dir}")
                
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            raise

if __name__ == '__main__':
    # 创建应用
    app = create_app('development')
    
    # 注意：数据库初始化不应在每次启动时都运行。
    # 这是一个一次性的设置步骤，或者应该通过一个单独的CLI命令来管理。
    # init_database(app)
    
    port = 5015 # Port configuration for file project management module
    print("\n=== 文件与项目管理工具启动 ===")
    print(f"访问地址: http://127.0.0.1:{port}")
    print(f"健康检查: http://127.0.0.1:{port}/api/health")
    print(f"系统信息: http://127.0.0.1:{port}/api/info")
    print("================================\n")
    
    # 启动应用
    app.run(
        host='0.0.0.0',
        port=port,
        debug=True,
        threaded=True
    )
