from flask import request, current_app
from flask_restx import Namespace, Resource, fields
from flask_jwt_extended import create_access_token, create_refresh_token, jwt_required, get_jwt_identity
from sqlalchemy import text
from datetime import datetime
from app.models import db, User, UserStatus
from app.utils import success_response, error_response, validate_user_data

# 创建命名空间
auth_ns = Namespace('auth', description='用户认证相关接口')

# 定义请求模型
login_model = auth_ns.model('LoginRequest', {
    'username': fields.String(required=True, description='用户名或邮箱'),
    'password': fields.String(required=True, description='密码')
})

# 定义响应模型
login_response_model = auth_ns.model('LoginResponse', {
    'access_token': fields.String(description='访问令牌'),
    'refresh_token': fields.String(description='刷新令牌'),
    'user': fields.Raw(description='用户信息')
})

@auth_ns.route('/login')
class LoginAPI(Resource):
    @auth_ns.doc('user_login')
    @auth_ns.expect(login_model)
    def post(self):
        """用户登录"""
        try:
            data = request.get_json()
            if not data:
                return error_response("请求数据不能为空", 400), 400
            
            username = data.get('username', '').strip()
            password = data.get('password', '')
            
            if not username or not password:
                return error_response("用户名和密码不能为空", 400), 400
            
            # 查找用户（支持用户名或邮箱登录）
            user = User.query.filter(
                (User.username == username) | (User.email == username)
            ).first()
            
            if not user:
                return error_response("用户名或密码错误", 401), 401
            
            # 检查用户状态
            if user.status != UserStatus.APPROVED:
                return error_response("账户未通过审核，请联系管理员", 403), 403
            
            # 验证密码
            if not user.check_password(password):
                return error_response("用户名或密码错误", 401), 401
            
            # 更新登录信息
            user.last_login_time = datetime.utcnow()
            user.last_login_ip = request.remote_addr
            user.login_count = (user.login_count or 0) + 1
            db.session.commit()
            
            # 生成JWT令牌
            access_token = create_access_token(identity=user.id)
            refresh_token = create_refresh_token(identity=user.id)
            
            # 返回用户信息（不包含敏感信息）
            user_data = user.to_dict()
            
            response_data = {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'user': user_data
            }
            
            return success_response(response_data, "登录成功"), 200
            
        except Exception as e:
            current_app.logger.error(f"登录失败: {str(e)}")
            return error_response("登录失败，请稍后重试", 500), 500

@auth_ns.route('/refresh')
class RefreshAPI(Resource):
    @auth_ns.doc('refresh_token')
    @jwt_required(refresh=True)
    def post(self):
        """刷新访问令牌"""
        try:
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user or user.status != UserStatus.ACTIVE:
                return error_response("用户不存在或已被禁用", 401), 401
            
            # 生成新的访问令牌
            access_token = create_access_token(identity=user.id)
            
            response_data = {
                'access_token': access_token
            }
            
            return success_response(response_data, "令牌刷新成功"), 200
            
        except Exception as e:
            current_app.logger.error(f"令牌刷新失败: {str(e)}")
            return error_response("令牌刷新失败", 500), 500

@auth_ns.route('/me')
class UserInfoAPI(Resource):
    @auth_ns.doc('get_current_user')
    @jwt_required()
    def get(self):
        """获取当前用户信息"""
        try:
            current_user_id = get_jwt_identity()
            user = User.query.get(current_user_id)
            
            if not user:
                return error_response("用户不存在", 404), 404
            
            return success_response(user.to_dict(), "获取用户信息成功"), 200
            
        except Exception as e:
            current_app.logger.error(f"获取用户信息失败: {str(e)}")
            return error_response("获取用户信息失败", 500), 500

@auth_ns.route('/health')
class HealthAPI(Resource):
    @auth_ns.doc('health_check')
    def get(self):
        """健康检查接口"""
        try:
            # 检查数据库连接
            db.session.execute(text('SELECT 1'))
            
            return success_response({
                'status': 'healthy',
                'service': 'user-management',
                'version': '1.0.0'
            }, "服务正常"), 200
            
        except Exception as e:
            current_app.logger.error(f"健康检查失败: {str(e)}")
            return error_response("服务异常", 500), 500
