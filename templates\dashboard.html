{% extends "base.html" %}

{% block title %}主控台 - {{ system_config.name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h4 class="mb-1">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            主控台
                        </h4>
                        <p class="text-muted mb-0">欢迎使用 {{ system_config.name }}</p>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="d-flex gap-2 justify-content-end align-items-center flex-wrap">
                            <button class="btn btn-success" onclick="startAllModules()" title="启动所有已完成的模块">
                                <i class="fas fa-play me-1"></i>
                                全部启动
                            </button>
                            <button class="btn btn-primary" onclick="refreshAllModuleStatus(false)" title="手动刷新所有模块状态（调试用）">
                                <i class="fas fa-sync-alt me-1"></i>
                                手动刷新状态
                            </button>
                            
                            <!-- 成绩上报按钮 - 政府要求的唯一互联网连接模块 -->
                            {% if user.role in ['admin', 'teacher'] %}
                            <button class="btn btn-warning" onclick="openScoreReporting()" title="成绩上报 - 唯一互联网连接模块">
                                <i class="fas fa-upload me-1"></i>
                                成绩上报
                            </button>
                            {% endif %}

                            {% if user.role == 'admin' %}
                            <button class="btn btn-outline-warning btn-sm" onclick="clearCache()" title="清除状态缓存">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i>
                                    {{ user.username }}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><h6 class="dropdown-header">用户信息</h6></li>
                                    <li><span class="dropdown-item-text">角色: {{ user.role }}</span></li>
                                    <li><span class="dropdown-item-text">登录时间: {{ user.login_time[:19] }}</span></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                        <i class="fas fa-sign-out-alt me-1"></i>
                                        退出登录
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row" id="modules-container">
    {% for module_id, module in modules.items() %}
    <div class="col-md-6 col-lg-4 mb-4" data-module-id="{{ module_id }}">
        <div class="card h-100">
            <div class="card-body d-flex flex-column">
                <div class="text-center mb-3">
                    <i class="{{ module.icon }} text-primary" style="font-size: 3rem;"></i>
                </div>
                <h5 class="card-title text-center">{{ module.name }}</h5>
                <p class="card-text text-muted text-center flex-grow-1">{{ module.description }}</p>
                
                <div class="text-center" id="module-status-{{ module_id }}">
                    {% if module.development_status != 'completed' %}
                        <span class="badge bg-warning mb-2">
                            <i class="fas fa-code me-1"></i>
                            开发中
                        </span>
                        <div class="d-grid">
                            <button class="btn btn-outline-secondary" disabled>
                                <i class="fas fa-tools me-1"></i>
                                功能开发中
                            </button>
                        </div>
                    {% elif module.status == 'running' %}
                        <span class="badge bg-success mb-2">
                            <i class="fas fa-circle me-1"></i>
                            运行中
                        </span>
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('module_page', module_id=module_id) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-external-link-alt me-1"></i>
                                进入模块
                            </a>
                            <button class="btn btn-outline-danger btn-sm" 
                                    onclick="stopModule('{{ module_id }}')">
                                <i class="fas fa-stop me-1"></i>
                                停止模块
                            </button>
                        </div>
                    {% elif module.status == 'stopped' %}
                        <span class="badge bg-secondary mb-2">
                            <i class="fas fa-circle me-1"></i>
                            已停止
                        </span>
                        <div class="d-grid">
                            <button class="btn btn-success" 
                                    onclick="startModule('{{ module_id }}')">
                                <i class="fas fa-play me-1"></i>
                                启动模块
                            </button>
                        </div>
                    {% else %}
                        <span class="badge bg-warning mb-2">
                            <i class="fas fa-question-circle me-1"></i>
                            状态未知
                        </span>
                        <div class="d-grid">
                            <button class="btn btn-outline-primary" 
                                    onclick="checkModuleStatus('{{ module_id }}')">
                                <i class="fas fa-sync-alt me-1"></i>
                                检查状态
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- 模块操作的JavaScript -->
<script>
    let statusInterval = null;

    // 处理后端传递的返回信息
    {% if return_info %}
    const returnInfo = {{ return_info | tojson }};
    console.log('检测到模块返回信息:', returnInfo);
    
    // 如果是从成绩上报模块返回，显示提示信息
    if (returnInfo.from_module === 'score_reporting') {
        // 设置模块返回标记
        sessionStorage.setItem('returnedFromScoreReporting', 'true');
        
        // 显示返回提示
        setTimeout(() => {
            showAlert('info', '已从成绩上报模块返回主控台');
        }, 500);
        
        // 强制刷新模块状态
        setTimeout(() => {
            refreshAllModuleStatus(true);
        }, 1000);
    }
    {% endif %}

    // --- Page Load ---
    document.addEventListener('DOMContentLoaded', function() {
        // On every page load, start polling for status updates.
        // The backend now handles auto-starting services, so the frontend's
        // only job is to reflect the current state.
        startStatusPolling();
    });

    // --- Core Functions ---

    function startAllModules() {
        showAlert('info', '正在发送启动所有模块的指令...');
        fetch('/api/modules/start-all', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    // Visually update all modules that can be started
                    document.querySelectorAll('[data-module-id]').forEach(el => {
                        const statusElement = document.getElementById(`module-status-${el.dataset.moduleId}`);
                        if (statusElement && statusElement.querySelector('button.btn-success')) {
                             updateModuleVisualState(el.dataset.moduleId, 'starting');
                        }
                    });
                    startStatusPolling(); // Start polling to see the result
                } else {
                    showAlert('danger', '启动指令发送失败: ' + data.message);
                }
            }).catch(err => {
                showAlert('danger', '启动所有模块时出错: ' + err);
            });
    }

    function startModule(moduleId) {
        updateModuleVisualState(moduleId, 'starting');
        fetch(`/api/module/${moduleId}/start`, { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('info', data.message);
                    startStatusPolling(); // Start polling to see the result
                } else {
                    showAlert('danger', `启动模块 ${moduleId} 失败: ` + data.message);
                    refreshAllModuleStatus(true); // Refresh to show the real (failed) state
                }
            }).catch(err => {
                showAlert('danger', `启动模块 ${moduleId} 时出错: ` + err);
                refreshAllModuleStatus(true);
            });
    }

    function stopModule(moduleId) {
        if (!confirm('确定要停止此模块吗？')) return;
        updateModuleVisualState(moduleId, 'stopping');
        fetch(`/api/module/${moduleId}/stop`, { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message);
                    refreshAllModuleStatus(true);
                } else {
                    showAlert('danger', `停止模块 ${moduleId} 失败: ` + data.message);
                }
            }).catch(err => {
                showAlert('danger', `停止模块 ${moduleId} 时出错: ` + err);
            });
    }

    // 智能轮询策略
    let pollingInterval = 5000; // 初始轮询间隔5秒
    let consecutiveUnchangedPolls = 0; // 连续无变化的轮询次数
    let lastModuleStates = {}; // 上次模块状态缓存
    
    function startStatusPolling() {
        if (statusInterval) {
            console.log('Polling is already active.');
            return;
        }
        console.log('Starting intelligent status polling.');
        
        // 重置轮询参数
        pollingInterval = 5000;
        consecutiveUnchangedPolls = 0;
        
        refreshAllModuleStatus(true); // Poll immediately
        
        // 使用智能轮询间隔
        function scheduleNextPoll() {
            statusInterval = setTimeout(() => {
                if (shouldContinuePolling()) {
                    refreshAllModuleStatus(true);
                    scheduleNextPoll();
                } else {
                    console.log('停止轮询：所有模块状态稳定');
                    statusInterval = null;
                }
            }, pollingInterval);
        }
        
        scheduleNextPoll();
    }
    
    // 判断是否应该继续轮询
    function shouldContinuePolling() {
        // 如果页面不可见，暂停轮询
        if (!isPageVisible) {
            return false;
        }
        
        // 如果连续10次轮询无变化，停止轮询
        if (consecutiveUnchangedPolls >= 10) {
            return false;
        }
        
        // 如果有模块正在启动或停止，继续轮询
        for (const moduleId in lastModuleStates) {
            const status = lastModuleStates[moduleId];
            if (status === 'starting' || status === 'stopping' || status === 'unknown') {
                return true;
            }
        }
        
        return true;
    }
    
    // 动态调整轮询间隔
    function adjustPollingInterval(hasChanges) {
        if (hasChanges) {
            // 有变化时，重置计数器并使用较短间隔
            consecutiveUnchangedPolls = 0;
            pollingInterval = 3000; // 3秒
        } else {
            // 无变化时，增加计数器并逐渐延长间隔
            consecutiveUnchangedPolls++;
            if (consecutiveUnchangedPolls > 3) {
                pollingInterval = Math.min(pollingInterval * 1.5, 15000); // 最大15秒
            }
        }
        
        console.log(`轮询间隔调整为: ${pollingInterval}ms, 连续无变化次数: ${consecutiveUnchangedPolls}`);
    }

    // 页面焦点检测和智能刷新机制
    let lastFocusTime = Date.now();
    let lastUserAction = Date.now();
    let isPageVisible = true;
    let isFromModuleReturn = false;
    let refreshPending = false;
    
    // 用户操作感知 - 记录用户主动操作
    function recordUserAction() {
        lastUserAction = Date.now();
        isFromModuleReturn = false;
    }
    
    // 检测是否为从模块返回
    function detectModuleReturn() {
        const referrer = document.referrer;
        const currentUrl = window.location.href;
        
        console.log('检测模块返回:', {
            referrer: referrer,
            currentUrl: currentUrl
        });
        
        // 检查sessionStorage中是否有成绩上报模块跳转标记
        const jumpedToScoreReporting = sessionStorage.getItem('jumpedToScoreReporting');
        if (jumpedToScoreReporting === 'true') {
            console.log('检测到从成绩上报模块返回（sessionStorage标记）');
            isFromModuleReturn = true;
            recordUserAction(); // 记录用户操作
            
            // 清除sessionStorage标记
            sessionStorage.removeItem('jumpedToScoreReporting');
            
            // 延迟重置标记
            setTimeout(() => {
                isFromModuleReturn = false;
                console.log('模块返回标记已重置（成绩上报）');
            }, 15000); // 15秒后重置，给足够时间
            
            return true;
        }
        
        // 检查URL参数中是否有返回标记
        const urlParams = new URLSearchParams(window.location.search);
        const fromModule = urlParams.get('from_module');
        if (fromModule) {
            console.log(`检测到从模块返回: ${fromModule}`);
            isFromModuleReturn = true;
            recordUserAction(); // 记录用户操作
            
            // 清除URL参数，避免刷新时重复检测
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
            
            // 延迟重置标记
            setTimeout(() => {
                isFromModuleReturn = false;
                console.log('模块返回标记已重置');
            }, 15000); // 15秒后重置，给足够时间
            
            return true;
        }
        
        // 通过referrer检测（检查是否来自50xx端口的模块）
        if (referrer) {
            try {
                const referrerUrl = new URL(referrer);
                const port = parseInt(referrerUrl.port);
                
                // 检查是否来自模块端口（50xx系列）
                if (port >= 5000 && port <= 5099) {
                    console.log(`检测到从模块端口返回: ${port}`);
                    isFromModuleReturn = true;
                    recordUserAction(); // 记录用户操作
                    
                    // 延迟重置标记
                    setTimeout(() => {
                        isFromModuleReturn = false;
                        console.log('模块返回标记已重置（referrer检测）');
                    }, 15000); // 15秒后重置，给足够时间
                    
                    return true;
                }
                
                // 检查referrer是否包含模块路径
                if (referrer.includes('/module/')) {
                    console.log('检测到从模块路径返回');
                    isFromModuleReturn = true;
                    recordUserAction(); // 记录用户操作
                    
                    setTimeout(() => {
                        isFromModuleReturn = false;
                        console.log('模块返回标记已重置（路径检测）');
                    }, 15000);
                    
                    return true;
                }
            } catch (e) {
                console.warn('解析referrer URL失败:', e);
            }
        }
        
        return false;
    }
    
    // 智能刷新决策
    function shouldAutoRefresh(timeSinceLastFocus) {
        // 如果是从模块返回，绝对不进行自动刷新
        if (isFromModuleReturn) {
            console.log('从模块返回，跳过自动刷新');
            return false;
        }
        
        // 如果最近有用户操作（120秒内），不进行自动刷新
        const timeSinceUserAction = Date.now() - lastUserAction;
        if (timeSinceUserAction < 120000) {
            console.log(`最近有用户操作（${Math.round(timeSinceUserAction/1000)}秒前），跳过自动刷新`);
            return false;
        }
        
        // 如果已有刷新待处理，避免重复刷新
        if (refreshPending) {
            console.log('已有刷新待处理，跳过自动刷新');
            return false;
        }
        
        // 只有在页面失去焦点超过60秒后才进行自动刷新
        const shouldRefresh = timeSinceLastFocus > 60000;
        console.log(`自动刷新决策: ${shouldRefresh}, 失去焦点时间: ${Math.round(timeSinceLastFocus/1000)}秒`);
        return shouldRefresh;
    }
    
    // 监听页面可见性变化（主要处理标签页切换）
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            isPageVisible = true;
            
            // 检测是否从模块返回
            detectModuleReturn();
            
            const timeSinceLastFocus = Date.now() - lastFocusTime;
            
            if (shouldAutoRefresh(timeSinceLastFocus)) {
                console.log('页面重新可见，执行智能刷新');
                refreshPending = true;
                // 延迟刷新，避免频繁操作
                setTimeout(() => {
                    refreshAllModuleStatus(false, false, false);
                    refreshPending = false;
                }, 1000);
            }
            
            lastFocusTime = Date.now();
            
            // 重置模块返回标记（延迟重置，避免重复检测）
            setTimeout(() => {
                if (!isFromModuleReturn) {
                    // 只有在没有检测到模块返回时才重置
                    console.log('页面可见性变化：重置模块返回标记');
                }
            }, 3000);
        } else {
            isPageVisible = false;
        }
    });
    
    // 监听窗口焦点事件（处理窗口切换）
    window.addEventListener('focus', function() {
        // 检测是否从模块返回
        detectModuleReturn();
        
        const timeSinceLastFocus = Date.now() - lastFocusTime;
        
        // 只有在非页面可见性变化触发的情况下才处理
        if (isPageVisible && shouldAutoRefresh(timeSinceLastFocus)) {
            console.log('窗口重新获得焦点，执行智能刷新');
            refreshPending = true;
            // 延迟刷新，避免与visibilitychange重复
            setTimeout(() => {
                if (refreshPending) {
                    refreshAllModuleStatus(false, false, false);
                    refreshPending = false;
                }
            }, 1500);
        }
        
        lastFocusTime = Date.now();
    });
    
    // 监听窗口失去焦点事件
    window.addEventListener('blur', function() {
        lastFocusTime = Date.now();
    });
    
    // 为所有用户交互元素添加操作记录
    document.addEventListener('click', recordUserAction);
    document.addEventListener('keydown', recordUserAction);
    
    // 初始化模块状态缓存
         const moduleCards = document.querySelectorAll('.module-card');
         moduleCards.forEach(card => {
             const moduleId = card.getAttribute('data-module');
             if (moduleId) {
                 lastModuleStates[moduleId] = 'unknown';
             }
         });

    function stopStatusPolling() {
        if (statusInterval) {
            console.log('Stopping status polling.');
            clearTimeout(statusInterval); // 改为clearTimeout，因为现在使用setTimeout
            statusInterval = null;
        }
    }
    
    // 重启轮询（当有用户操作时）
    function restartPollingIfNeeded() {
        if (!statusInterval && isPageVisible) {
            console.log('重启状态轮询');
            startStatusPolling();
        }
    }

    // 状态缓存机制
    let statusCache = {
        data: null,
        timestamp: 0,
        ttl: 3000 // 缓存有效期3秒
    };
    
    // 模块状态缓存，用于检测状态变化（已在全局声明）
    
    function refreshAllModuleStatus(isAuto = false, forceRefresh = false, showLoading = false) {
        // 检查缓存是否有效（仅对自动刷新有效）
        if (isAuto && !forceRefresh && statusCache.data) {
            const cacheAge = Date.now() - statusCache.timestamp;
            if (cacheAge < statusCache.ttl) {
                console.log('使用缓存的模块状态数据');
                processModuleStatusData(statusCache.data, true);
                return;
            }
        }
        
        if (!isAuto) {
            showAlert('info', '正在手动刷新所有模块状态...');
            // 手动刷新时重启轮询
            restartPollingIfNeeded();
        }
        
        // 显示加载提示
        if (showLoading) {
            showLoadingIndicator('正在刷新模块状态...');
        }
        
        // 添加超时控制，避免长时间等待
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 增加超时时间到3秒
        
        const url = forceRefresh ? '/api/modules/status?force_refresh=true' : '/api/modules/status';
        fetch(url, {
            signal: controller.signal
        })
            .then(response => {
                clearTimeout(timeoutId);
                // 隐藏加载提示
                if (showLoading) {
                    hideLoadingIndicator();
                }
                if (response.status === 401) {
                    window.location.href = '/login';
                    return Promise.reject(new Error('Session expired'));
                }
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.modules) {
                    // 更新缓存
                    statusCache.data = data;
                    statusCache.timestamp = Date.now();
                    
                    processModuleStatusData(data, isAuto);
                } else {
                    console.warn('状态更新返回失败:', data);
                }
            }).catch(err => {
                clearTimeout(timeoutId);
                if (showLoading) {
                    hideLoadingIndicator();
                }
                if (err.name === 'AbortError') {
                    console.warn('状态更新超时');
                } else if (err.message !== 'Session expired') {
                    console.error('状态更新失败:', err);
                    // 减少错误提示频率，避免用户体验不佳
                    if (Math.random() < 0.1) { // 10%概率显示错误提示
                        showAlert('warning', '无法获取模块状态，请检查网络连接。');
                    }
                    stopStatusPolling();
                }
            });
    }
    
    // 处理模块状态数据
    function processModuleStatusData(data, isAuto) {
        let hasChanges = false;
        let allModulesSettled = true;
        
        // 检查状态变化
        Object.values(data.modules).forEach(module => {
            const previousStatus = lastModuleStates[module.id];
            if (previousStatus !== module.status) {
                hasChanges = true;
                console.log(`模块 ${module.id} 状态变化: ${previousStatus} -> ${module.status}`);
            }
            
            // 更新状态缓存
            lastModuleStates[module.id] = module.status;
            
            // 更新视觉状态
            updateModuleVisualState(module.id, module.status, module.development_status);
            
            // 检查是否有模块仍在变化中
            if (module.status === 'starting' || module.status === 'stopping' || module.status === 'unknown') {
                allModulesSettled = false;
            }
        });
        
        // 如果是自动轮询，调整轮询间隔
        if (isAuto) {
            adjustPollingInterval(hasChanges);
        }
        
        // 如果所有模块状态稳定，可以停止轮询
         if (allModulesSettled && isAuto) {
             console.log('所有模块状态稳定，将在下次检查后可能停止轮询');
         }
     }
     
     // 动态调整轮询间隔
     function adjustPollingInterval(hasChanges) {
         if (hasChanges) {
             // 有变化时缩短间隔
             currentPollingInterval = Math.max(3000, currentPollingInterval - 1000);
             console.log(`检测到状态变化，缩短轮询间隔至 ${currentPollingInterval}ms`);
         } else {
             // 无变化时延长间隔
             currentPollingInterval = Math.min(15000, currentPollingInterval + 1000);
             console.log(`状态稳定，延长轮询间隔至 ${currentPollingInterval}ms`);
         }
     }

    function updateModuleVisualState(moduleId, status, developmentStatus = null) {
        const statusElement = document.getElementById(`module-status-${moduleId}`);
        if (!statusElement) return;

        let html = '';
        const isRunning = status === 'running';
        const isStopped = status === 'stopped';
        const isStarting = status === 'starting';
        const isStopping = status === 'stopping';
        const isInDevelopment = developmentStatus && developmentStatus !== 'completed';
        const isCompleted = developmentStatus === 'completed';

        if (isInDevelopment) {
            html = `
                <span class="badge bg-warning mb-2"><i class="fas fa-code me-1"></i>开发中</span>
                <div class="d-grid">
                    <button class="btn btn-outline-secondary" disabled><i class="fas fa-tools me-1"></i>功能开发中</button>
                </div>`;
        } else if (isRunning) {
            const statusBadge = isCompleted ? 
                '<span class="badge bg-success mb-2"><i class="fas fa-check-circle me-1"></i>已完成 · 运行中</span>' :
                '<span class="badge bg-success mb-2"><i class="fas fa-circle me-1"></i>运行中</span>';
            html = `
                ${statusBadge}
                <div class="d-grid gap-2">
                    <a href="/module/${moduleId}" class="btn btn-primary"><i class="fas fa-external-link-alt me-1"></i>进入模块</a>
                    <button class="btn btn-outline-danger btn-sm" onclick="stopModule('${moduleId}')"><i class="fas fa-stop me-1"></i>停止模块</button>
                </div>`;
        } else if (isStopped) {
            const statusBadge = isCompleted ? 
                '<span class="badge bg-info mb-2"><i class="fas fa-check-circle me-1"></i>已完成 · 已停止</span>' :
                '<span class="badge bg-secondary mb-2"><i class="fas fa-circle me-1"></i>已停止</span>';
            html = `
                ${statusBadge}
                <div class="d-grid">
                    <button class="btn btn-success" onclick="startModule('${moduleId}')"><i class="fas fa-play me-1"></i>启动模块</button>
                </div>`;
        } else if (isStarting || isStopping) {
            const statusBadge = isCompleted ? 
                `<span class="badge bg-info mb-2"><i class="fas fa-check-circle me-1"></i>已完成 · <i class="fas fa-spinner fa-spin me-1"></i>${isStarting ? '启动中' : '停止中'}...</span>` :
                `<span class="badge bg-info mb-2"><i class="fas fa-spinner fa-spin me-1"></i>${isStarting ? '启动中' : '停止中'}...</span>`;
            html = `
                ${statusBadge}
                <div class="d-grid">
                    <button class="btn btn-outline-secondary" disabled><i class="fas fa-hourglass-half me-1"></i>请稍候</button>
                </div>`;
        } else { // unknown or error
            const statusBadge = isCompleted ? 
                '<span class="badge bg-warning mb-2"><i class="fas fa-check-circle me-1"></i>已完成 · <i class="fas fa-question-circle me-1"></i>状态未知</span>' :
                '<span class="badge bg-warning mb-2"><i class="fas fa-question-circle me-1"></i>状态未知</span>';
            html = `
                ${statusBadge}
                <div class="d-grid">
                    <button class="btn btn-outline-primary" onclick="refreshAllModuleStatus(false, true, true)"><i class="fas fa-sync-alt me-1"></i>检查状态</button>
                </div>`;
        }
        statusElement.innerHTML = html;
    }

    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 3000);
    }
    
    // 显示加载指示器
    function showLoadingIndicator(message = '加载中...') {
        // 移除已存在的加载指示器
        hideLoadingIndicator();
        
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loading-indicator';
        loadingDiv.className = 'alert alert-info d-flex align-items-center position-fixed';
        loadingDiv.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
        loadingDiv.innerHTML = `
            <div class="spinner-border spinner-border-sm me-2" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            ${message}
        `;
        
        document.body.appendChild(loadingDiv);
    }
    
    // 隐藏加载指示器
    function hideLoadingIndicator() {
        const loadingDiv = document.getElementById('loading-indicator');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }

    function clearCache(showMessage = true) {
        if (showMessage && !confirm('确定要清除模块状态缓存吗？')) {
            return;
        }
        
        // 清除状态缓存
        statusCache.data = null;
        statusCache.timestamp = 0;
        lastModuleStates = {};
        
        // 重置轮询间隔
         currentPollingInterval = 5000;
        
        // 重置用户操作记录
        lastUserAction = 0;
        lastFocusTime = Date.now();
        
        fetch('/api/modules/clear-cache', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (showMessage) showAlert('success', '缓存已清除，状态已重置');
                    // 强制刷新状态
                    refreshAllModuleStatus(false, true);
                } else {
                    if (showMessage) showAlert('danger', '清除缓存失败');
                }
            })
            .catch(err => {
                if (showMessage) showAlert('danger', '清除缓存时出错');
            });
    }

    function openScoreReporting() {
        // 记录用户操作
        recordUserAction();
        
        // 检查成绩上报模块是否运行
        fetch('/api/modules/status')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.modules && data.modules['exam_score_reporting_interface']) {
                    const module = data.modules['exam_score_reporting_interface'];
                    if (module.status === 'running') {
                        // 使用正确的模块跳转方式，确保返回时能被检测到
                        // 记录跳转到成绩上报模块，以便返回时跳过自动刷新
                        sessionStorage.setItem('jumpedToScoreReporting', 'true');
                        window.location.href = '/module/exam_score_reporting_interface';
                    } else {
                        // 模块未运行，提示启动
                        if (confirm('成绩上报模块未运行，是否启动该模块？')) {
                            startModule('exam_score_reporting_interface');
                            showAlert('info', '正在启动成绩上报模块，请稍后再试...');
                        }
                    }
                } else {
                    showAlert('danger', '无法获取成绩上报模块状态');
                }
            })
            .catch(err => {
                showAlert('danger', '检查成绩上报模块状态时出错: ' + err);
            });
    }




</script>
{% endblock %}