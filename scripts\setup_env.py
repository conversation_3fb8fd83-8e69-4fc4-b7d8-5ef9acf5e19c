#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一虚拟环境设置脚本
功能：创建虚拟环境、安装依赖、配置环境变量
作者：SOLO Coding
版本：1.0.0
日期：2024-01-15
"""

import os
import sys
import subprocess
import venv
import logging
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('logs/setup_env.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class VirtualEnvironmentSetup:
    """统一虚拟环境设置管理器"""
    
    def __init__(self):
        """初始化环境设置器"""
        self.project_root = Path(__file__).parent.parent
        self.venv_path = self.project_root / "venv"
        self.requirements_file = self.project_root / "requirements.txt"
        self.cache_dir = self.project_root / "cache"
        self.logs_dir = self.project_root / "logs"
        
        # 确保必要目录存在
        self._ensure_directories()
        
        # Windows编码设置
        self._setup_encoding()
        
        logger.info("虚拟环境设置器初始化完成")
    
    def _ensure_directories(self) -> None:
        """确保必要的目录存在"""
        directories = [self.cache_dir, self.logs_dir]
        for directory in directories:
            directory.mkdir(exist_ok=True)
        logger.info("目录结构检查完成")
    
    def _setup_encoding(self) -> None:
        """设置Windows编码兼容性"""
        try:
            # 设置环境变量以支持UTF-8
            os.environ["PYTHONIOENCODING"] = "utf-8"
            os.environ["PYTHONUTF8"] = "1"
            
            # Windows控制台编码设置
            if sys.platform.startswith('win'):
                try:
                    # 尝试设置控制台代码页为UTF-8
                    subprocess.run(["chcp", "65001"], 
                                 shell=True, 
                                 capture_output=True, 
                                 check=False)
                except Exception as e:
                    logger.warning(f"设置控制台编码失败: {e}")
            
            logger.info("编码环境配置完成")
            
        except Exception as e:
            logger.error(f"编码设置失败: {e}")
            raise
    
    def check_python_version(self) -> bool:
        """检查Python版本是否符合要求
        
        Returns:
            bool: 版本是否符合要求
        """
        try:
            version = sys.version_info
            required_version = (3, 8)
            
            if version >= required_version:
                logger.info(f"Python版本检查通过: {version.major}.{version.minor}.{version.micro}")
                return True
            else:
                logger.error(f"Python版本过低: {version.major}.{version.minor}.{version.micro}, 需要 >= 3.8")
                return False
                
        except Exception as e:
            logger.error(f"Python版本检查失败: {e}")
            return False
    
    def create_virtual_environment(self) -> bool:
        """创建虚拟环境
        
        Returns:
            bool: 创建是否成功
        """
        try:
            if self.venv_path.exists():
                logger.info("虚拟环境已存在，跳过创建")
                return True
            
            logger.info("开始创建虚拟环境...")
            start_time = time.time()
            
            # 创建虚拟环境
            venv.create(self.venv_path, with_pip=True, clear=False)
            
            end_time = time.time()
            logger.info(f"虚拟环境创建完成，耗时: {end_time - start_time:.2f}秒")
            
            # 验证虚拟环境
            if self._validate_virtual_environment():
                logger.info("虚拟环境验证成功")
                return True
            else:
                logger.error("虚拟环境验证失败")
                return False
                
        except Exception as e:
            logger.error(f"创建虚拟环境失败: {e}")
            return False
    
    def _validate_virtual_environment(self) -> bool:
        """验证虚拟环境是否正确创建
        
        Returns:
            bool: 验证是否成功
        """
        try:
            # 检查关键文件是否存在
            if sys.platform.startswith('win'):
                python_exe = self.venv_path / "Scripts" / "python.exe"
                pip_exe = self.venv_path / "Scripts" / "pip.exe"
            else:
                python_exe = self.venv_path / "bin" / "python"
                pip_exe = self.venv_path / "bin" / "pip"
            
            if not python_exe.exists():
                logger.error(f"Python可执行文件不存在: {python_exe}")
                return False
            
            if not pip_exe.exists():
                logger.error(f"pip可执行文件不存在: {pip_exe}")
                return False
            
            # 测试Python和pip是否可用
            try:
                result = subprocess.run(
                    [str(python_exe), "--version"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                logger.info(f"虚拟环境Python版本: {result.stdout.strip()}")
            except subprocess.CalledProcessError as e:
                logger.error(f"虚拟环境Python测试失败: {e}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"虚拟环境验证异常: {e}")
            return False
    
    def upgrade_pip(self) -> bool:
        """升级pip到最新版本
        
        Returns:
            bool: 升级是否成功
        """
        try:
            logger.info("开始升级pip...")
            
            if sys.platform.startswith('win'):
                pip_exe = self.venv_path / "Scripts" / "pip.exe"
            else:
                pip_exe = self.venv_path / "bin" / "pip"
            
            # 升级pip
            result = subprocess.run(
                [str(pip_exe), "install", "--upgrade", "pip"],
                capture_output=True,
                text=True,
                cwd=str(self.project_root)
            )
            
            if result.returncode == 0:
                logger.info("pip升级成功")
                return True
            else:
                logger.error(f"pip升级失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"pip升级异常: {e}")
            return False
    
    def install_dependencies(self) -> bool:
        """安装项目依赖
        
        Returns:
            bool: 安装是否成功
        """
        try:
            if not self.requirements_file.exists():
                logger.warning("requirements.txt文件不存在，跳过依赖安装")
                return True
            
            logger.info("开始安装项目依赖...")
            start_time = time.time()
            
            if sys.platform.startswith('win'):
                pip_exe = self.venv_path / "Scripts" / "pip.exe"
            else:
                pip_exe = self.venv_path / "bin" / "pip"
            
            # 安装依赖
            result = subprocess.run(
                [str(pip_exe), "install", "-r", str(self.requirements_file)],
                capture_output=True,
                text=True,
                cwd=str(self.project_root)
            )
            
            end_time = time.time()
            
            if result.returncode == 0:
                logger.info(f"依赖安装成功，耗时: {end_time - start_time:.2f}秒")
                
                # 保存安装日志
                self._save_installation_log(result.stdout, result.stderr)
                return True
            else:
                logger.error(f"依赖安装失败: {result.stderr}")
                self._save_installation_log(result.stdout, result.stderr)
                return False
                
        except Exception as e:
            logger.error(f"依赖安装异常: {e}")
            return False
    
    def _save_installation_log(self, stdout: str, stderr: str) -> None:
        """保存安装日志
        
        Args:
            stdout: 标准输出
            stderr: 标准错误输出
        """
        try:
            log_file = self.logs_dir / "pip_install.log"
            with open(log_file, 'w', encoding='utf-8') as f:
                f.write("=== PIP安装日志 ===\n")
                f.write(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("\n=== 标准输出 ===\n")
                f.write(stdout)
                f.write("\n=== 错误输出 ===\n")
                f.write(stderr)
            
            logger.info(f"安装日志已保存: {log_file}")
            
        except Exception as e:
            logger.warning(f"保存安装日志失败: {e}")
    
    def generate_activation_script(self) -> bool:
        """生成环境激活脚本
        
        Returns:
            bool: 生成是否成功
        """
        try:
            # Windows批处理脚本
            if sys.platform.startswith('win'):
                script_content = f"""@echo off
REM 激活虚拟环境脚本
REM 自动生成于: {time.strftime('%Y-%m-%d %H:%M:%S')}

echo 正在激活虚拟环境...
call "{self.venv_path}\\Scripts\\activate.bat"

REM 设置编码环境
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

REM 设置项目路径
set PYTHONPATH={self.project_root};%PYTHONPATH%

echo 虚拟环境已激活
echo 项目路径: {self.project_root}
echo 虚拟环境路径: {self.venv_path}

cmd /k
"""
                script_file = self.project_root / "activate_env.bat"
            else:
                # Linux/Mac shell脚本
                script_content = f"""#!/bin/bash
# 激活虚拟环境脚本
# 自动生成于: {time.strftime('%Y-%m-%d %H:%M:%S')}

echo "正在激活虚拟环境..."
source "{self.venv_path}/bin/activate"

# 设置编码环境
export PYTHONIOENCODING=utf-8
export PYTHONUTF8=1

# 设置项目路径
export PYTHONPATH="{self.project_root}:$PYTHONPATH"

echo "虚拟环境已激活"
echo "项目路径: {self.project_root}"
echo "虚拟环境路径: {self.venv_path}"

/bin/bash
"""
                script_file = self.project_root / "activate_env.sh"
            
            # 写入脚本文件
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            # 设置执行权限（Linux/Mac）
            if not sys.platform.startswith('win'):
                os.chmod(script_file, 0o755)
            
            logger.info(f"激活脚本已生成: {script_file}")
            return True
            
        except Exception as e:
            logger.error(f"生成激活脚本失败: {e}")
            return False
    
    def create_environment_info(self) -> bool:
        """创建环境信息文件
        
        Returns:
            bool: 创建是否成功
        """
        try:
            env_info = {
                "project_name": "职业技能等级考试系统",
                "project_root": str(self.project_root),
                "venv_path": str(self.venv_path),
                "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
                "platform": sys.platform,
                "created_at": time.strftime('%Y-%m-%d %H:%M:%S'),
                "encoding": "utf-8",
                "requirements_file": str(self.requirements_file),
                "status": "ready"
            }
            
            info_file = self.cache_dir / "env_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(env_info, f, indent=2, ensure_ascii=False)
            
            logger.info(f"环境信息文件已创建: {info_file}")
            return True
            
        except Exception as e:
            logger.error(f"创建环境信息文件失败: {e}")
            return False
    
    def run_environment_tests(self) -> bool:
        """运行环境测试
        
        Returns:
            bool: 测试是否通过
        """
        try:
            logger.info("开始运行环境测试...")
            
            if sys.platform.startswith('win'):
                python_exe = self.venv_path / "Scripts" / "python.exe"
            else:
                python_exe = self.venv_path / "bin" / "python"
            
            # 测试脚本
            test_script = """
import sys
import os
import json

# 测试编码
try:
    test_str = "测试中文编码"
    print(f"编码测试通过: {test_str}")
except Exception as e:
    print(f"编码测试失败: {e}")
    sys.exit(1)

# 测试导入常用模块
try:
    import flask
    import yaml
    import requests
    print("核心模块导入测试通过")
except ImportError as e:
    print(f"模块导入测试失败: {e}")
    sys.exit(1)

# 测试环境变量
if os.environ.get('PYTHONIOENCODING') == 'utf-8':
    print("环境变量测试通过")
else:
    print("环境变量测试失败")
    sys.exit(1)

print("所有环境测试通过")
"""
            
            # 运行测试
            result = subprocess.run(
                [str(python_exe), "-c", test_script],
                capture_output=True,
                text=True,
                cwd=str(self.project_root),
                env={**os.environ, "PYTHONIOENCODING": "utf-8", "PYTHONUTF8": "1"}
            )
            
            if result.returncode == 0:
                logger.info("环境测试通过")
                logger.info(f"测试输出: {result.stdout}")
                return True
            else:
                logger.error(f"环境测试失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"环境测试异常: {e}")
            return False
    
    def setup_complete_environment(self) -> bool:
        """完整的环境设置流程
        
        Returns:
            bool: 设置是否成功
        """
        try:
            logger.info("开始完整环境设置流程...")
            start_time = time.time()
            
            # 1. 检查Python版本
            if not self.check_python_version():
                return False
            
            # 2. 创建虚拟环境
            if not self.create_virtual_environment():
                return False
            
            # 3. 升级pip
            if not self.upgrade_pip():
                logger.warning("pip升级失败，继续后续步骤")
            
            # 4. 安装依赖
            if not self.install_dependencies():
                return False
            
            # 5. 生成激活脚本
            if not self.generate_activation_script():
                logger.warning("激活脚本生成失败，继续后续步骤")
            
            # 6. 创建环境信息
            if not self.create_environment_info():
                logger.warning("环境信息文件创建失败，继续后续步骤")
            
            # 7. 运行环境测试
            if not self.run_environment_tests():
                logger.warning("环境测试失败，但环境可能仍可用")
            
            end_time = time.time()
            logger.info(f"环境设置完成，总耗时: {end_time - start_time:.2f}秒")
            
            # 输出使用说明
            self._print_usage_instructions()
            
            return True
            
        except Exception as e:
            logger.error(f"环境设置失败: {e}")
            return False
    
    def _print_usage_instructions(self) -> None:
        """输出使用说明"""
        try:
            print("\n" + "="*60)
            print("🎉 虚拟环境设置完成！")
            print("="*60)
            print(f"📁 项目路径: {self.project_root}")
            print(f"🐍 虚拟环境路径: {self.venv_path}")
            print("\n📋 使用说明:")
            
            if sys.platform.startswith('win'):
                print("   1. 激活环境: 双击 activate_env.bat")
                print("   2. 手动激活: venv\\Scripts\\activate.bat")
            else:
                print("   1. 激活环境: ./activate_env.sh")
                print("   2. 手动激活: source venv/bin/activate")
            
            print("   3. 启动系统: python main_launcher.py")
            print("   4. 查看日志: logs/setup_env.log")
            print("\n🔧 环境配置:")
            print("   - 编码: UTF-8")
            print("   - Python版本: >= 3.8")
            print("   - 依赖管理: requirements.txt")
            print("\n" + "="*60)
            
        except Exception as e:
            logger.warning(f"输出使用说明失败: {e}")


def safe_print(message: str) -> None:
    """安全的打印函数，处理编码问题
    
    Args:
        message: 要打印的消息
    """
    try:
        print(message)
    except UnicodeEncodeError:
        # 如果编码失败，使用ASCII编码并忽略错误
        print(message.encode('ascii', 'ignore').decode('ascii'))
    except Exception:
        # 最后的备选方案
        print("[编码错误] 无法显示消息")


def main():
    """主函数"""
    try:
        safe_print("\n🚀 开始设置统一虚拟环境...")
        
        # 创建环境设置器
        setup = VirtualEnvironmentSetup()
        
        # 执行完整设置流程
        success = setup.setup_complete_environment()
        
        if success:
            safe_print("\n✅ 环境设置成功完成！")
            sys.exit(0)
        else:
            safe_print("\n❌ 环境设置失败，请检查日志文件")
            sys.exit(1)
            
    except KeyboardInterrupt:
        safe_print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        safe_print(f"\n💥 环境设置异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()