@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 设置环境变量
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1

echo ================================================
echo     局域网在线考试系统 - 一键清理重启工具
echo ================================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] Python未安装或不在PATH中
    echo 请确保Python已正确安装并添加到系统PATH
    pause
    exit /b 1
)

REM 检查必要的Python包
echo [信息] 检查依赖包...
python -c "import psutil, requests" >nul 2>&1
if errorlevel 1 (
    echo [警告] 缺少必要的Python包，正在安装...
    pip install psutil requests
    if errorlevel 1 (
        echo [错误] 安装依赖包失败
        pause
        exit /b 1
    )
)

REM 切换到脚本目录
cd /d "%~dp0"

REM 检查清理脚本是否存在
if not exist "cleanup_and_restart.py" (
    echo [错误] 找不到清理脚本 cleanup_and_restart.py
    echo 请确保脚本文件存在于当前目录
    pause
    exit /b 1
)

echo [信息] 开始执行系统清理和重启...
echo.

REM 执行清理脚本
python cleanup_and_restart.py

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo [错误] 清理重启过程中出现错误
    echo 请检查错误信息并重试
) else (
    echo.
    echo [成功] 系统清理重启完成！
    echo [提示] 请访问 http://localhost:8000 使用系统
)

echo.
echo 按任意键退出...
pause >nul