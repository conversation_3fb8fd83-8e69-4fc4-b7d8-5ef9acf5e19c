# -*- coding: utf-8 -*-
"""
文件与项目管理工具模块 - 应用包初始化

该文件将app目录标识为Python包，并提供应用的初始化功能。
"""

from flask import Flask
import os

# 导入扩展实例
from app.extensions import db, jwt, cors

def create_app(config_name='development'):
    """
    应用工厂函数
    
    Args:
        config_name (str): 配置名称，默认为'development'
        
    Returns:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)
    
    # 加载配置
    from config.config import get_config
    config = get_config(config_name)
    app.config.from_object(config)
    
    # 初始化扩展
    db.init_app(app)
    jwt.init_app(app)
    cors.init_app(app)
    
    # 注册蓝图
    from app.api.files import files_bp
    from app.api.projects import projects_bp
    from app.api.tasks import tasks_bp
    
    app.register_blueprint(files_bp, url_prefix='/api/v1/files')
    app.register_blueprint(projects_bp, url_prefix='/api/v1/projects')
    app.register_blueprint(tasks_bp, url_prefix='/api/v1/tasks')
    
    return app