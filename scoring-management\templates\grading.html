<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评分管理系统 - 阅卷评分</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header h1 {
            font-size: 24px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }
        
        .tab:hover {
            background-color: #f8f9fa;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .card h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .task-list {
            display: grid;
            gap: 15px;
        }
        
        .task-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
        }
        
        .task-item:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }
        
        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .task-title {
            font-weight: 600;
            color: #333;
        }
        
        .task-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-assigned {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-in-progress {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .task-info {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .task-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #5a6fd8;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #5a6268;
        }
        
        .grading-form {
            display: none;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .score-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .score-input input {
            width: 80px;
        }
        
        .alert {
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background-color: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .empty-state h4 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>评分管理系统</h1>
            <div class="user-info">
                <span>欢迎，{{ grader_name }}</span>
                <button class="logout-btn" onclick="logout()">退出登录</button>
            </div>
        </div>
    </div>
    
    <div class="container">
        <div id="alert" class="alert" style="display: none;"></div>
        
        <div class="tabs">
            <div class="tab active" onclick="switchTab('tasks')">我的评分任务</div>
            <div class="tab" onclick="switchTab('statistics')">评分统计</div>
        </div>
        
        <!-- 评分任务标签页 -->
        <div id="tasks-content" class="tab-content active">
            <div class="card">
                <h3>待评分任务</h3>
                <div id="tasks-loading" class="loading">
                    <div class="loading-spinner"></div>
                    正在加载评分任务...
                </div>
                <div id="tasks-list" class="task-list" style="display: none;"></div>
                <div id="tasks-empty" class="empty-state" style="display: none;">
                    <h4>暂无评分任务</h4>
                    <p>当前没有分配给您的评分任务</p>
                </div>
            </div>
        </div>
        
        <!-- 统计信息标签页 -->
        <div id="statistics-content" class="tab-content">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="total-tasks">0</div>
                    <div class="stat-label">总任务数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completed-tasks">0</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="pending-tasks">0</div>
                    <div class="stat-label">待评分</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completion-rate">0%</div>
                    <div class="stat-label">完成率</div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let currentTasks = [];
        const graderId = {{ grader_id }};
        
        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.getElementById('alert');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.style.display = 'block';
            
            setTimeout(() => {
                alertDiv.style.display = 'none';
            }, 3000);
        }
        
        // 切换标签页
        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 激活当前标签
            event.target.classList.add('active');
            document.getElementById(tabName + '-content').classList.add('active');
            
            // 加载对应数据
            if (tabName === 'tasks') {
                loadGradingTasks();
            } else if (tabName === 'statistics') {
                loadStatistics();
            }
        }
        
        // 加载评分任务
        async function loadGradingTasks() {
            const loadingDiv = document.getElementById('tasks-loading');
            const listDiv = document.getElementById('tasks-list');
            const emptyDiv = document.getElementById('tasks-empty');
            
            loadingDiv.style.display = 'block';
            listDiv.style.display = 'none';
            emptyDiv.style.display = 'none';
            
            try {
                const response = await fetch('/api/v1/grading-tasks');
                const data = await response.json();
                
                if (data.code === 200) {
                    currentTasks = data.data;
                    
                    if (currentTasks.length === 0) {
                        emptyDiv.style.display = 'block';
                    } else {
                        renderTasks(currentTasks);
                        listDiv.style.display = 'block';
                    }
                } else {
                    showAlert(data.msg || '加载评分任务失败', 'error');
                }
            } catch (error) {
                console.error('加载评分任务失败:', error);
                showAlert('网络连接失败', 'error');
            } finally {
                loadingDiv.style.display = 'none';
            }
        }
        
        // 渲染任务列表
        function renderTasks(tasks) {
            const listDiv = document.getElementById('tasks-list');
            
            listDiv.innerHTML = tasks.map(task => `
                <div class="task-item">
                    <div class="task-header">
                        <div class="task-title">考试 #${task.exam_id}</div>
                        <div class="task-status status-${task.status}">
                            ${getStatusText(task.status)}
                        </div>
                    </div>
                    <div class="task-info">
                        分配学生数: ${task.assigned_students.length} 人<br>
                        分配时间: ${formatDateTime(task.assigned_at)}
                        ${task.completed_at ? '<br>完成时间: ' + formatDateTime(task.completed_at) : ''}
                    </div>
                    <div class="task-actions">
                        ${task.status !== 'completed' ? 
                            `<button class="btn btn-primary" onclick="startGrading(${task.id})">开始评分</button>` : 
                            `<button class="btn btn-secondary" onclick="viewGrading(${task.id})">查看详情</button>`
                        }
                    </div>
                    <div id="grading-form-${task.id}" class="grading-form"></div>
                </div>
            `).join('');
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'assigned': '已分配',
                'in_progress': '评分中',
                'completed': '已完成'
            };
            return statusMap[status] || status;
        }
        
        // 格式化日期时间
        function formatDateTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
        
        // 开始评分
        async function startGrading(taskId) {
            const task = currentTasks.find(t => t.id === taskId);
            if (!task) return;
            
            const formDiv = document.getElementById(`grading-form-${taskId}`);
            
            if (formDiv.style.display === 'block') {
                formDiv.style.display = 'none';
                return;
            }
            
            // 获取考试详情和学生答案
            try {
                const response = await fetch(`/api/v1/exams/${task.exam_id}/scores`);
                const data = await response.json();
                
                if (data.code === 200) {
                    const scores = data.data.filter(score => 
                        task.assigned_students.includes(score.student_id)
                    );
                    
                    renderGradingForm(taskId, scores);
                    formDiv.style.display = 'block';
                } else {
                    showAlert(data.msg || '获取评分数据失败', 'error');
                }
            } catch (error) {
                console.error('获取评分数据失败:', error);
                showAlert('网络连接失败', 'error');
            }
        }
        
        // 渲染评分表单
        function renderGradingForm(taskId, scores) {
            const formDiv = document.getElementById(`grading-form-${taskId}`);
            
            formDiv.innerHTML = `
                <h4>主观题评分</h4>
                <div id="scores-list-${taskId}">
                    ${scores.map(score => `
                        <div class="score-item" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 15px; border-radius: 5px;">
                            <h5>学生 #${score.student_id}</h5>
                            <div class="form-group">
                                <label>题目ID</label>
                                <input type="number" id="question-id-${score.id}" placeholder="请输入题目ID" required>
                            </div>
                            <div class="form-group">
                                <label>评分</label>
                                <div class="score-input">
                                    <input type="number" id="score-${score.id}" min="0" step="0.5" placeholder="得分" required>
                                    <span>/</span>
                                    <input type="number" id="max-score-${score.id}" min="0" step="0.5" placeholder="满分" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>评分备注</label>
                                <textarea id="comments-${score.id}" placeholder="请输入评分备注（可选）"></textarea>
                            </div>
                            <button class="btn btn-primary" onclick="submitScore(${score.id})">提交评分</button>
                        </div>
                    `).join('')}
                </div>
            `;
        }
        
        // 提交评分
        async function submitScore(scoreId) {
            const questionId = document.getElementById(`question-id-${scoreId}`).value;
            const score = document.getElementById(`score-${scoreId}`).value;
            const maxScore = document.getElementById(`max-score-${scoreId}`).value;
            const comments = document.getElementById(`comments-${scoreId}`).value;
            
            if (!questionId || !score || !maxScore) {
                showAlert('请填写完整的评分信息', 'error');
                return;
            }
            
            if (parseFloat(score) > parseFloat(maxScore)) {
                showAlert('得分不能超过满分', 'error');
                return;
            }
            
            try {
                const response = await fetch(`/api/v1/scores/${scoreId}/subjective`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        question_id: parseInt(questionId),
                        score: parseFloat(score),
                        max_score: parseFloat(maxScore),
                        comments: comments
                    })
                });
                
                const data = await response.json();
                
                if (data.code === 200) {
                    showAlert('评分提交成功', 'success');
                    
                    // 清空表单
                    document.getElementById(`question-id-${scoreId}`).value = '';
                    document.getElementById(`score-${scoreId}`).value = '';
                    document.getElementById(`max-score-${scoreId}`).value = '';
                    document.getElementById(`comments-${scoreId}`).value = '';
                } else {
                    showAlert(data.msg || '评分提交失败', 'error');
                }
            } catch (error) {
                console.error('评分提交失败:', error);
                showAlert('网络连接失败', 'error');
            }
        }
        
        // 查看评分详情
        function viewGrading(taskId) {
            showAlert('查看评分详情功能开发中', 'info');
        }
        
        // 加载统计信息
        function loadStatistics() {
            const totalTasks = currentTasks.length;
            const completedTasks = currentTasks.filter(t => t.status === 'completed').length;
            const pendingTasks = totalTasks - completedTasks;
            const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
            
            document.getElementById('total-tasks').textContent = totalTasks;
            document.getElementById('completed-tasks').textContent = completedTasks;
            document.getElementById('pending-tasks').textContent = pendingTasks;
            document.getElementById('completion-rate').textContent = completionRate + '%';
        }
        
        // 退出登录
        async function logout() {
            if (confirm('确定要退出登录吗？')) {
                try {
                    const response = await fetch('/api/v1/logout', {
                        method: 'POST'
                    });
                    
                    const data = await response.json();
                    
                    if (data.code === 200) {
                        window.location.href = data.data.redirect_url || '/';
                    }
                } catch (error) {
                    console.error('退出登录失败:', error);
                    window.location.href = '/';
                }
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadGradingTasks();
        });
    </script>
</body>
</html>